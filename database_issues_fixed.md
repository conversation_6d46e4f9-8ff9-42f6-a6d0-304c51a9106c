# 数据库问题修正报告

## 主要问题及修正

### 1. **serial_number 字段长度不足** ⚠️ **关键问题**
- **问题**: `serial_number` 字段设置为 `VARCHAR(20)`，但代码中使用 `UUID.randomUUID().toString()` 生成36个字符的UUID
- **错误信息**: `Data too long for column 'serial_number' at row 1`
- **修正**: 将字段长度从 `VARCHAR(20)` 改为 `VARCHAR(50)`
- **影响**: 用户注册时会失败

### 2. **枚举值不匹配**
#### ProductStatus 枚举
- **问题**: 代码中有 `DELISTED` 状态，但SQL中缺失
- **修正**: 添加 `DELISTED` 到产品表和产品状态历史表的枚举中

#### MessageType 枚举  
- **问题**: 代码中有 `GROUP` 类型，但SQL中缺失
- **修正**: 添加 `GROUP` 到消息表的枚举中

#### MessageStatus 枚举
- **问题**: 代码中有 `FAILED` 和 `DELETED` 状态，但SQL中缺失
- **修正**: 添加 `FAILED` 和 `DELETED` 到消息表的枚举中

#### NotificationType 枚举
- **问题**: SQL中的枚举值与代码中的不匹配
- **修正**: 更新为完整的枚举值列表：
  - `NEW_COMMENT`, `REPLY_TO_COMMENT`, `POST_LIKE`, `MENTION_IN_POST`, `MENTION_IN_COMMENT`
  - `DEVELOPER_APP_APPROVED`, `DEVELOPER_APP_REJECTED`, `PRODUCT_APPROVED`, `PRODUCT_REJECTED`, `PRODUCT_DELISTED`

#### WithdrawalStatus 枚举
- **问题**: SQL中多了 `COMPLETED` 状态，但代码中没有
- **修正**: 移除 `COMPLETED` 状态，保持与代码一致

### 3. **Order表字段不匹配**
- **问题**: Order实体类中的字段与SQL表结构不匹配
- **修正**: 更新Order表结构以匹配实体类：
  - 添加 `out_trade_no` 字段 (VARCHAR(100))
  - 将 `amount` 改为 `total_fee`
  - 添加 `platform_fee` 和 `developer_revenue` 字段
  - 添加 `updated_at` 字段

### 4. **管理员用户密码哈希**
- **问题**: 使用了错误的BCrypt密码哈希
- **修正**: 更新为正确的BCrypt哈希 (密码: admin123)

## 修正后的文件
- `database_rebuild_script_fixed.sql` - 完整的修正版数据库重建脚本

## 验证建议
1. 运行修正后的SQL脚本重建数据库
2. 测试用户注册功能，确认serial_number字段可以正常存储UUID
3. 测试管理员登录 (用户名: KitolusAdmin, 密码: admin123)
4. 验证所有枚举类型的功能是否正常

## 其他检查项目
✅ 字符集设置 (utf8mb4)
✅ 外键约束
✅ 索引优化
✅ 字段类型匹配
✅ 初始数据完整性

所有主要问题已修正，数据库应该可以正常工作。
