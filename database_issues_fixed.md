# 数据库问题修正报告

## 主要问题及修正

### 1. **serial_number 字段长度不足** ⚠️ **关键问题**
- **问题**: `serial_number` 字段设置为 `VARCHAR(20)`，但代码中使用 `UUID.randomUUID().toString()` 生成36个字符的UUID
- **错误信息**: `Data too long for column 'serial_number' at row 1`
- **修正**: 将字段长度从 `VARCHAR(20)` 改为 `VARCHAR(50)`
- **影响**: 用户注册时会失败

### 2. **枚举值不匹配**
#### ProductStatus 枚举
- **问题**: 代码中有 `DELISTED` 状态，但SQL中缺失
- **修正**: 添加 `DELISTED` 到产品表和产品状态历史表的枚举中

#### MessageType 枚举  
- **问题**: 代码中有 `GROUP` 类型，但SQL中缺失
- **修正**: 添加 `GROUP` 到消息表的枚举中

#### MessageStatus 枚举
- **问题**: 代码中有 `FAILED` 和 `DELETED` 状态，但SQL中缺失
- **修正**: 添加 `FAILED` 和 `DELETED` 到消息表的枚举中

#### NotificationType 枚举
- **问题**: SQL中的枚举值与代码中的不匹配
- **修正**: 更新为完整的枚举值列表：
  - `NEW_COMMENT`, `REPLY_TO_COMMENT`, `POST_LIKE`, `MENTION_IN_POST`, `MENTION_IN_COMMENT`
  - `DEVELOPER_APP_APPROVED`, `DEVELOPER_APP_REJECTED`, `PRODUCT_APPROVED`, `PRODUCT_REJECTED`, `PRODUCT_DELISTED`

#### WithdrawalStatus 枚举
- **问题**: SQL中多了 `COMPLETED` 状态，但代码中没有
- **修正**: 移除 `COMPLETED` 状态，保持与代码一致

### 3. **Order表字段不匹配**
- **问题**: Order实体类中的字段与SQL表结构不匹配
- **修正**: 更新Order表结构以匹配实体类：
  - 添加 `out_trade_no` 字段 (VARCHAR(100))
  - 将 `amount` 改为 `total_fee`
  - 添加 `platform_fee` 和 `developer_revenue` 字段
  - 添加 `updated_at` 字段

### 4. **缺失支付账户字段** ⚠️ **新发现的关键问题**
- **问题**: User实体类中有 `alipayAccount` 和 `wechatAccount` 字段，但数据库表中缺失
- **错误信息**: `Unknown column 'alipay_account' in 'SELECT'`
- **修正**: 在user表中添加 `alipay_account` 和 `wechat_account` 字段
- **影响**: 用户资料查询和提现功能会失败

### 5. **缺失设备指纹字段** ⚠️ **新发现的关键问题**
- **问题**: LoginHistory实体类中有 `deviceFingerprint` 字段，但数据库表中缺失
- **错误信息**: `Unknown column 'device_fingerprint' in 'INSERT INTO'`
- **修正**: 在login_history表中添加 `device_fingerprint` 字段，并修正字段名为 `login_timestamp`
- **影响**: 用户登录历史记录功能会失败

### 6. **缺失通知内容预览字段** ⚠️ **新发现的关键问题**
- **问题**: Notification实体类中有 `contentPreview` 字段，但数据库表中缺失
- **修正**: 在notification表中添加 `content_preview` 字段
- **影响**: 通知功能会失败

### 7. **提现请求表结构不匹配** ⚠️ **新发现的关键问题**
- **问题**: WithdrawalRequest实体类的表名和字段与SQL不匹配
- **修正**:
  - 表名从 `withdrawal_request` 改为 `withdrawal_requests`
  - 添加 `channel` 枚举字段 (ALIPAY, WECHAT)
  - 将 `payment_account` 改为 `account_info`
  - 添加 `updated_at` 字段
- **影响**: 提现功能会完全失败

### 8. **开发者申请表结构不匹配** ⚠️ **新发现的关键问题**
- **问题**: DeveloperApplication实体类的表名和字段与SQL不匹配
- **修正**:
  - 表名从 `developer_application` 改为 `developer_applications`
  - 添加缺失的 `rejection_reason` 字段
- **影响**: 开发者申请功能会失败

### 9. **移除测试用户账号**
- **问题**: 不希望有测试用户账号
- **修正**: 移除了管理员测试用户的插入语句

## 修正后的文件
- `database_rebuild_script_fixed.sql` - 完整的修正版数据库重建脚本

## 修正总结

### 已修正的关键问题 (共9个)
1. ✅ **serial_number字段长度不足** - 从VARCHAR(20)改为VARCHAR(50)
2. ✅ **枚举值不匹配** - 修正所有枚举类型的值
3. ✅ **Order表字段不匹配** - 更新字段名和结构
4. ✅ **缺失支付账户字段** - 添加alipay_account和wechat_account
5. ✅ **缺失设备指纹字段** - 添加device_fingerprint到login_history表
6. ✅ **缺失通知内容预览字段** - 添加content_preview到notification表
7. ✅ **提现请求表结构不匹配** - 修正表名和字段结构
8. ✅ **开发者申请表结构不匹配** - 修正表名和添加rejection_reason字段
9. ✅ **移除测试用户账号** - 按用户要求移除

### 验证建议
1. 运行修正后的SQL脚本重建数据库
2. 测试用户注册功能，确认serial_number字段可以正常存储UUID
3. 测试用户登录功能，确认device_fingerprint字段正常工作
4. 测试用户资料查询，确认支付账户字段正常工作
5. 测试通知功能，确认content_preview字段正常工作
6. 测试提现功能，确认新的表结构正常工作
7. 测试开发者申请功能，确认rejection_reason字段正常工作
8. 验证所有枚举类型的功能是否正常

### 其他检查项目
✅ 字符集设置 (utf8mb4)
✅ 外键约束
✅ 索引优化
✅ 字段类型匹配
✅ 初始数据完整性
✅ 表名与实体类匹配
✅ 字段名与实体类匹配

**所有主要问题已修正，数据库应该可以正常工作。**
