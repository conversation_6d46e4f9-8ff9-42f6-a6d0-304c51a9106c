package com.kitolus.community.service;

import com.kitolus.community.entity.ProductStatus;
import com.kitolus.community.entity.ProductStatusHistory;
import java.util.List;

public interface ProductStatusHistoryService {
    
    /**
     * 记录产品状态变更
     */
    void recordStatusChange(Long productId, ProductStatus oldStatus, ProductStatus newStatus, 
                          String changedBy, String reason, String notes);
    
    /**
     * 获取产品的状态变更历史
     */
    List<ProductStatusHistory> getProductStatusHistory(Long productId);
}
