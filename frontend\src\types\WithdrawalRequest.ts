import { User } from './User';

export enum WithdrawalChannel {
  ALIPAY = 'ALIPAY',
  WECHAT = 'WECHAT',
}

export enum WithdrawalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export interface WithdrawalRequest {
  id: number;
  amount: number;
  channel: WithdrawalChannel;
  accountInfo: string;
  status: WithdrawalStatus;
  createdAt: string;
  reviewedAt?: string;
  notes?: string;
  userId: number;
  username?: string;
  userEmail?: string;
  reviewerId?: number;
  reviewerName?: string;
} 