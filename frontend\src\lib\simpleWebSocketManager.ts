/**
 * 简化的WebSocket管理器
 * 基于SessionManager的单一WebSocket连接管理
 */

import { sessionManager } from './sessionManager';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface WebSocketEventHandlers {
  [eventType: string]: ((data: any) => void)[];
}

class SimpleWebSocketManager {
  private ws: WebSocket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isDestroyed = false;
  private shouldReconnect = true;
  private eventHandlers: WebSocketEventHandlers = {};

  // 单例模式
  private static instance: SimpleWebSocketManager | null = null;

  private constructor() {
    if (typeof window !== 'undefined') {
      // 监听会话失效
      sessionManager.onSessionInvalidated(() => {
        console.log('🔌 会话失效，断开WebSocket连接');
        this.shouldReconnect = false;
        this.disconnect();
      });

      // 监听页面可见性变化
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && this.shouldReconnect && this.token) {
          setTimeout(() => {
            if (this.shouldReconnect && this.token && !this.isConnected) {
              console.log('🔌 页面可见性变化，检查WebSocket连接');
              this.connect();
            }
          }, 1000);
        }
      });
    }
  }

  static getInstance(): SimpleWebSocketManager {
    if (!SimpleWebSocketManager.instance) {
      SimpleWebSocketManager.instance = new SimpleWebSocketManager();
    }
    return SimpleWebSocketManager.instance;
  }

  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 设置WebSocket管理器
   */
  setup(token: string) {
    console.log('🌐 设置简化WebSocket管理器...');

    if (this.token !== token) {
      console.log('🔄 检测到新token，重新建立连接');
      this.token = token;
      this.shouldReconnect = true;
      this.disconnect();
      setTimeout(() => this.connect(), 1000);
    } else if (!this.isConnected && this.shouldReconnect && !this.isConnecting) {
      console.log('🔄 WebSocket未连接，尝试建立连接');
      this.connect();
    } else if (this.isConnected) {
      console.log('ℹ️ WebSocket已连接，跳过重复设置');
    } else if (this.isConnecting) {
      console.log('ℹ️ WebSocket正在连接中，跳过重复设置');
    }
  }

  /**
   * 建立WebSocket连接
   */
  private connect() {
    if (this.isConnecting || this.isDestroyed || !this.token || !this.shouldReconnect) {
      return;
    }

    // 检查会话是否有效
    if (!sessionManager.isSessionValid()) {
      console.log('⚠️ 会话无效，跳过WebSocket连接');
      return;
    }

    if (this.isConnected) {
      console.log('ℹ️ WebSocket已连接，跳过重复连接');
      return;
    }

    this.isConnecting = true;
    const wsUrl = `wss://kitolus.top/ws/messages?token=${this.token}`;
    
    console.log('🚀 简化WebSocket连接:', wsUrl.substring(0, 80) + '...');

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        console.log('✅ 简化WebSocket连接成功');
        this.emit('connected', {});
        this.startHeartbeat();
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 收到WebSocket消息:', message);
          this.emit(message.type, message);
        } catch (error) {
          console.error('❌ 解析WebSocket消息失败:', error);
        }
      };

      this.ws.onclose = (event) => {
        this.isConnecting = false;
        console.log('🔌 简化WebSocket连接关闭:', event.code, event.reason);
        this.emit('disconnected', { code: event.code, reason: event.reason });
        this.stopHeartbeat();

        if (this.shouldReconnect && !this.isDestroyed) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        this.isConnecting = false;
        console.error('❌ 简化WebSocket连接错误:', error);
        this.emit('error', error);
      };

    } catch (error) {
      this.isConnecting = false;
      console.error('❌ 创建WebSocket连接失败:', error);
    }
  }

  /**
   * 断开WebSocket连接
   */
  private disconnect() {
    if (this.ws) {
      console.log('🔌 简化WebSocket主动断开');
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.stopHeartbeat();
  }

  /**
   * 发送消息
   */
  send(message: any) {
    if (this.isConnected && this.ws) {
      const messageStr = JSON.stringify(message);
      console.log('📤 发送WebSocket消息:', message);
      this.ws.send(messageStr);
      return true;
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送消息');
      return false;
    }
  }

  /**
   * 添加事件监听器
   */
  on(eventType: string, handler: (data: any) => void) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(eventType: string, handler: (data: any) => void) {
    if (this.eventHandlers[eventType]) {
      const index = this.eventHandlers[eventType].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[eventType].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(eventType: string, data: any) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('❌ WebSocket事件处理器错误:', error);
        }
      });
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'PING', timestamp: Date.now() });
      }
    }, 30000); // 30秒心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ 达到最大重连次数，停止重连');
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    setTimeout(() => {
      if (this.shouldReconnect && !this.isDestroyed) {
        this.connect();
      }
    }, delay);
  }

  /**
   * 开始输入状态
   */
  startTyping(conversationId: string) {
    this.send({
      type: 'START_TYPING',
      conversationId,
      timestamp: Date.now()
    });
  }

  /**
   * 停止输入状态
   */
  stopTyping() {
    this.send({
      type: 'STOP_TYPING',
      timestamp: Date.now()
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('🌐 清理简化WebSocket连接');
    this.isDestroyed = true;
    this.shouldReconnect = false;
    this.disconnect();
    this.eventHandlers = {};
  }
}

// 导出单例实例
export const simpleWebSocketManager = SimpleWebSocketManager.getInstance();
