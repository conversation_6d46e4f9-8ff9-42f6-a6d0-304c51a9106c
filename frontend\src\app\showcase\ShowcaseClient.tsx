'use client';

import * as THREE from 'three';
import { useRef, useMemo, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Image as DreiImage, Text, OrbitControls, Stars, useTexture, Billboard, Preload } from '@react-three/drei';
import { EffectComposer, <PERSON>, Vignette, Scanline, Noise } from '@react-three/postprocessing';
import { BlendFunction } from 'postprocessing';
import projects from './projects.json';

const PROJECTS_RADIUS = 6;
const CAMERA_DEFAULT_POS = new THREE.Vector3(0, 0, 15);
const CAMERA_FAR_POS = new THREE.Vector3(0, 0, 450);
const CARD_FOCUS_DISTANCE = 6;

const getProjectPosition = (projectId: number) => {
    const projectIndex = projects.findIndex(p => p.id === projectId);
    if (projectIndex === -1) return null;

    const numCards = projects.length;
    const angle = (projectIndex / numCards) * Math.PI * 2;
    const x = PROJECTS_RADIUS * Math.cos(angle);
    const z = PROJECTS_RADIUS * Math.sin(angle);
    return new THREE.Vector3(x, 0, z);
};

function CardFrame() {
  const width = 4.0;
  const height = 2.5;
  const frameThickness = 0.1;
  const frameDepth = 0.1;

  const [
    colorMap,
    normalMap,
    roughnessMap,
    displacementMap,
  ] = useTexture([
    '/images/textures/tiles_0113_color_1k.webp',
    '/images/textures/tiles_0113_normal_opengl_1k.webp',
    '/images/textures/tiles_0113_roughness_1k.webp',
    '/images/textures/tiles_0113_height_1k.webp',
  ]);

  const geometry = useMemo(() => {
    const shape = new THREE.Shape();
    const halfW = width / 2;
    const halfH = height / 2;
    shape.moveTo(-halfW, -halfH);
    shape.lineTo(halfW, -halfH);
    shape.lineTo(halfW, halfH);
    shape.lineTo(-halfW, halfH);
    shape.lineTo(-halfW, -halfH);

    const holePath = new THREE.Path();
    const innerHalfW = halfW - frameThickness;
    const innerHalfH = halfH - frameThickness;
    holePath.moveTo(-innerHalfW, -innerHalfH);
    holePath.lineTo(innerHalfW, -innerHalfH);
    holePath.lineTo(innerHalfW, innerHalfH);
    holePath.lineTo(-innerHalfW, innerHalfH);
    holePath.lineTo(-innerHalfW, -innerHalfH);
    shape.holes.push(holePath);

    return new THREE.ExtrudeGeometry(shape, {
      steps: 2,
      depth: frameDepth,
      bevelEnabled: true,
      bevelThickness: 0.02,
      bevelSize: 0.01,
      bevelOffset: 0,
      bevelSegments: 2,
    });
  }, [width, height, frameThickness, frameDepth]);

  const innerW = width - frameThickness * 2;
  const innerH = height - frameThickness * 2;
  const tubeRadius = 0.01;
  const glowZ = frameDepth / 2 + 0.01; 

  const innerEdges = useMemo(() => [
    [new THREE.Vector3(-innerW / 2, -innerH / 2, glowZ), new THREE.Vector3(innerW / 2, -innerH / 2, glowZ)],
    [new THREE.Vector3(innerW / 2, -innerH / 2, glowZ), new THREE.Vector3(innerW / 2, innerH / 2, glowZ)],
    [new THREE.Vector3(innerW / 2, innerH / 2, glowZ), new THREE.Vector3(-innerW / 2, innerH / 2, glowZ)],
    [new THREE.Vector3(-innerW / 2, innerH / 2, glowZ), new THREE.Vector3(-innerW / 2, -innerH / 2, glowZ)],
  ], [innerW, innerH, glowZ]);

  return (
    <group>
        <mesh geometry={geometry}>
            <meshStandardMaterial
                map={colorMap}
                normalMap={normalMap}
                roughnessMap={roughnessMap}
                displacementMap={displacementMap}
                displacementScale={0.02}
            />
        </mesh>
        <group>
            {innerEdges.map((edge, i) => (
                <mesh key={i}>
                    <tubeGeometry args={[new THREE.LineCurve3(edge[0], edge[1]), 1, tubeRadius, 8, false]} />
                    <meshStandardMaterial emissive="#444444" emissiveIntensity={1.5} toneMapped={false} />
                </mesh>
            ))}
        </group>
    </group>
  );
}

type ProjectCardProps = {
  id: number;
  setActiveId: (id: number | null) => void;
  position: [number, number, number];
  imageUrl: string;
  title: string;
};

function ProjectCard({ id, setActiveId, position, imageUrl, title }: ProjectCardProps) {
  const ref = useRef<THREE.Group>(null!);
  const [hovered, setHovered] = useState(false);
  const backplateTexture = useTexture('/images/textures/tiles_0113_color_1k.webp');

  useFrame(() => {
    if (!ref.current) return;
    const targetScale = hovered ? 1.2 : 1;
    ref.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1);
  });
  
  return (
    <group
      ref={ref}
      position={position}
    >
      <Billboard>
        <CardFrame />
        <DreiImage 
            url={imageUrl} 
            scale={[4, 2.5]} 
            side={THREE.FrontSide} 
            onPointerOver={(e) => { e.stopPropagation(); setHovered(true); }}
            onPointerOut={(e) => { e.stopPropagation(); setHovered(false); }}
            onClick={(e) => { e.stopPropagation(); setActiveId(id); }}
        />
        <mesh position={[0, 0, -0.01]}>
            <planeGeometry args={[4, 2.5]} />
            <meshStandardMaterial map={backplateTexture} roughness={1} />
        </mesh>
        <Text font="/fonts/Oxanium-Regular.ttf" position={[0, -1.5, 0.1]} fontSize={0.25} color="white" anchorX="center" anchorY="middle">{title}</Text>
      </Billboard>
    </group>
  );
}

function Scene({ setActiveId, groupRef, isAnimating }: { 
    setActiveId: (id: number | null) => void, 
    groupRef: React.RefObject<THREE.Group>,
    isAnimating: boolean,
}) {
  useFrame((state, delta) => {
    // Natural rotation when not interacting
    if (!isAnimating && groupRef.current && state.camera.position.length() > 10) { // Only rotate in overview
      groupRef.current.rotation.y += delta * 0.05;
    }
  });

  return (
    <group ref={groupRef}>
      {projects.map((project, i) => {
          const numCards = projects.length;
          const radius = PROJECTS_RADIUS;
          const angle = (i / numCards) * Math.PI * 2;
          const x = radius * Math.cos(angle);
          const z = radius * Math.sin(angle);
    return (
              <ProjectCard
                  key={project.id}
                  id={project.id}
                  setActiveId={setActiveId}
                  position={[x, 0, z]}
                  imageUrl={project.imageUrl}
                  title={project.title}
              />
          );
      })}
    </group>
    );
  }

function SceneLogic({
    focusedId,
    setFocusedId,
    controlsRef,
    sceneGroupRef,
    returnFocusId,
    setReturnFocusId,
    isInteracting,
    needsToSnap,
    setNeedsToSnap,
    onInitialPanComplete,
    initialProjectId,
    setIsAnimating,
}: {
    focusedId: number | null;
    setFocusedId: (id: number | null) => void;
    controlsRef: React.RefObject<any>;
    sceneGroupRef: React.RefObject<THREE.Group>;
    returnFocusId: number | null;
    setReturnFocusId: (id: number | null) => void;
    isInteracting: boolean;
    needsToSnap: boolean;
    setNeedsToSnap: (snap: boolean) => void;
    onInitialPanComplete: (id: number) => void;
    initialProjectId: number;
    setIsAnimating: (isAnimating: boolean) => void;
}) {
    const animationStateRef = useRef<'idle' | 'toCard' | 'toOverview' | 'cardFocused' | 'snapping' | 'initial-pan'>('initial-pan');
    const snapTargetRef = useRef<{ position: THREE.Vector3; lookAt: THREE.Vector3 } | null>(null);
    const initialTargetId = useRef(initialProjectId);
    const animationPhaseRef = useRef<'start' | 'focus'>('start');

    useEffect(() => {
        if (isInteracting) {
            animationStateRef.current = 'idle';
            animationPhaseRef.current = 'start';
        }
    }, [isInteracting]);

    useEffect(() => {
        if (focusedId !== null && animationStateRef.current !== 'initial-pan') {
            animationStateRef.current = 'toCard';
        } else if (focusedId === null && animationStateRef.current === 'cardFocused') {
            animationStateRef.current = 'toOverview';
        }
    }, [focusedId]);

    useFrame((state, delta) => {
        if (!controlsRef.current || !sceneGroupRef.current) return;

        const animating = animationStateRef.current !== 'idle' && animationStateRef.current !== 'cardFocused';
        setIsAnimating(animating);

        if (animationStateRef.current === 'initial-pan' && !isInteracting) {
            const cardLocalPos = getProjectPosition(initialTargetId.current);
            if (cardLocalPos) {
                const cardWorldPos = sceneGroupRef.current.localToWorld(cardLocalPos.clone());
                const outwardDir = cardWorldPos.clone().normalize();

                if (animationPhaseRef.current === 'start') {
                    // 第一阶段：从远处开始
                    const startPos = outwardDir.multiplyScalar(450);
                    state.camera.position.lerp(startPos, 0.02);
                    controlsRef.current.target.lerp(cardWorldPos, 0.02);

                    if (state.camera.position.distanceTo(startPos) < 0.1) {
                        animationPhaseRef.current = 'focus';
                    }
                } else {
                    // 第二阶段：聚焦到卡片
                    const targetPos = cardWorldPos.clone().add(outwardDir.multiplyScalar(CARD_FOCUS_DISTANCE));
                    state.camera.position.lerp(targetPos, 0.02);
                    controlsRef.current.target.lerp(cardWorldPos, 0.02);

                    if (state.camera.position.distanceTo(targetPos) < 0.1) {
                        animationStateRef.current = 'cardFocused';
                        setFocusedId(initialTargetId.current);
                        onInitialPanComplete(initialTargetId.current);
                    }
                }
            } else {
                animationStateRef.current = 'idle';
            }
        } else if (animationStateRef.current === 'toCard' && !isInteracting) {
            const cardLocalPos = getProjectPosition(focusedId!);
            if (cardLocalPos) {
                const cardWorldPos = sceneGroupRef.current.localToWorld(cardLocalPos.clone());
                const outwardDir = cardWorldPos.clone().normalize();
                const targetPos = cardWorldPos.clone().add(outwardDir.multiplyScalar(CARD_FOCUS_DISTANCE));
                const targetLookAt = cardWorldPos;

                state.camera.position.lerp(targetPos, 0.02);
                controlsRef.current.target.lerp(targetLookAt, 0.02);

                if (state.camera.position.distanceTo(targetPos) < 0.1) {
                    animationStateRef.current = 'cardFocused';
                }
            } else {
                animationStateRef.current = 'idle';
                setFocusedId(null);
            }
        } else if (animationStateRef.current === 'toOverview' && !isInteracting) {
            let targetPos = CAMERA_DEFAULT_POS.clone();
            let targetLookAt = new THREE.Vector3(0, 0, 0);

            if (returnFocusId !== null) {
                const cardPos = getProjectPosition(returnFocusId);
                if (cardPos) {
                    const cardWorldPos = sceneGroupRef.current!.localToWorld(cardPos.clone());
                    targetLookAt = cardWorldPos;
                    const direction = cardWorldPos.clone().normalize();
                    targetPos = cardWorldPos.clone().add(direction.multiplyScalar(CARD_FOCUS_DISTANCE));
                }
            }

            state.camera.position.lerp(targetPos, 0.02);
            controlsRef.current.target.lerp(targetLookAt, 0.02);

            if (state.camera.position.distanceTo(targetPos) < 0.1 && controlsRef.current.target.distanceTo(targetLookAt) < 0.1) {
                animationStateRef.current = 'idle';
                setReturnFocusId(null);
            }
        } else if (animationStateRef.current === 'snapping' && !isInteracting) {
            if (snapTargetRef.current) {
                state.camera.position.lerp(snapTargetRef.current.position, 0.02);
                controlsRef.current.target.lerp(snapTargetRef.current.lookAt, 0.02);

                if (state.camera.position.distanceTo(snapTargetRef.current.position) < 0.05) {
                    animationStateRef.current = 'idle';
                    snapTargetRef.current = null;
                }
            } else {
                animationStateRef.current = 'idle';
            }
        }

        // 处理自动恢复到最近卡片
        if (needsToSnap && animationStateRef.current === 'idle' && !isInteracting) {
            setNeedsToSnap(false);

            const cameraDir = new THREE.Vector3(state.camera.position.x, 0, state.camera.position.z).normalize();
            let closestProjectId: number | null = null;
            let maxDot = -Infinity;

            projects.forEach(project => {
                const cardLocalPos = getProjectPosition(project.id);
                if (cardLocalPos) {
                    const cardWorldPos = sceneGroupRef.current!.localToWorld(cardLocalPos.clone());
                    const cardDir = new THREE.Vector3(cardWorldPos.x, 0, cardWorldPos.z).normalize();
                    const dot = cameraDir.dot(cardDir);
                    if (dot > maxDot) {
                        maxDot = dot;
                        closestProjectId = project.id;
                    }
                }
            });

            if (closestProjectId !== null) {
                const cardLocalPos = getProjectPosition(closestProjectId);
                if (cardLocalPos) {
                    const cardWorldPos = sceneGroupRef.current!.localToWorld(cardLocalPos.clone());
                    const outwardDir = cardWorldPos.clone().normalize();
                    const targetPos = cardWorldPos.clone().add(outwardDir.multiplyScalar(CARD_FOCUS_DISTANCE));
                    const targetLookAt = cardWorldPos;

                    snapTargetRef.current = { position: targetPos, lookAt: targetLookAt };
                    animationStateRef.current = 'snapping';
                }
            }
        }
    });

    return null;
}

function StarsAndLogic() {
    const starsRef = useRef<any>(null!);
    useFrame((state, delta) => {
        if(starsRef.current) {
            starsRef.current.rotation.y += delta * 0.1;
        }
    });
    return <Stars ref={starsRef} radius={100} depth={50} count={5000} factor={4} saturation={0} fade speed={0} />;
}

export type ShowcaseClientProps = {
    activeId: number | null;
    focusedId: number | null;
    setFocusedId: (id: number | null) => void;
    returnFocusId: number | null;
    setReturnFocusId: (id: number | null) => void;
    isInteracting: boolean;
    needsToSnap: boolean;
    setNeedsToSnap: (snap: boolean) => void;
    isAnimating: boolean;
    setIsAnimating: (isAnimating: boolean) => void;
    handleCardClick: (id: number | null) => void;
    handleInteractionStart: () => void;
    handleInteractionEnd: () => void;
    controlsRef: React.RefObject<any>;
    sceneGroupRef: React.RefObject<THREE.Group>;
    handleInitialPanComplete: (id: number) => void;
    initialProjectId: number;
};

// Main component for the 3D scene
const ShowcaseClient = ({
    activeId,
    focusedId,
    setFocusedId,
    returnFocusId,
    setReturnFocusId,
    isInteracting,
    needsToSnap,
    setNeedsToSnap,
    isAnimating,
    setIsAnimating,
    handleCardClick,
    handleInteractionStart,
    handleInteractionEnd,
    controlsRef,
    sceneGroupRef,
    handleInitialPanComplete,
    initialProjectId,
}: ShowcaseClientProps) => {
    const canvasKey = useMemo(() => Date.now(), []);
    const initRef = useRef(false);

    const handleControlsMount = (controls: any) => {
        if (!controls) return;
        
        if (controlsRef) {
            (controlsRef as React.MutableRefObject<any>).current = controls;
        }
        
        if (!initRef.current) {
            controls.object.position.copy(CAMERA_DEFAULT_POS);
            controls.target.set(0, 0, 0);
            controls.update();
            initRef.current = true;
        }
    };

    return (
        <div className={`absolute inset-0 transition-opacity duration-500 ${activeId !== null ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
            <Canvas key={canvasKey} camera={{ position: CAMERA_DEFAULT_POS, fov: 60 }}>
                <color attach="background" args={['#000000']} />
                <ambientLight intensity={0.2} />
                <pointLight position={[10, 10, 10]} intensity={1.5} />
                <StarsAndLogic />
                
                <SceneLogic
                    focusedId={focusedId}
                    setFocusedId={setFocusedId}
                    controlsRef={controlsRef}
                    sceneGroupRef={sceneGroupRef}
                    returnFocusId={returnFocusId}
                    setReturnFocusId={setReturnFocusId}
                    isInteracting={isInteracting}
                    needsToSnap={needsToSnap}
                    setNeedsToSnap={setNeedsToSnap}
                    onInitialPanComplete={handleInitialPanComplete}
                    initialProjectId={initialProjectId}
                    setIsAnimating={setIsAnimating}
                />
                <Scene setActiveId={handleCardClick} groupRef={sceneGroupRef} isAnimating={isAnimating} />

                <OrbitControls
                    ref={handleControlsMount}
                    enableZoom={true}
                    enablePan={false}
                    autoRotate={false}
                    onStart={handleInteractionStart}
                    onEnd={handleInteractionEnd}
                />

                <EffectComposer>
                    <Bloom luminanceThreshold={0.6} intensity={0.2} levels={8} mipmapBlur />
                    <Scanline blendFunction={BlendFunction.OVERLAY} density={1.5} opacity={0.05} />
                    <Noise premultiply blendFunction={BlendFunction.ADD} opacity={0.01} />
                    <Vignette eskil={false} offset={0.3} darkness={0.75} />
                </EffectComposer>
                <Preload all />
            </Canvas>
        </div>
    );
};

export default ShowcaseClient; 