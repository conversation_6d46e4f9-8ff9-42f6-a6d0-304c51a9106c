/**
 * 简单SSO解决方案 - 基于localStorage的跨标签页通信
 * 解决WebSocket连接冲突问题
 */

interface TabInfo {
  id: string;
  timestamp: number;
  isActive: boolean;
  userId?: number; // 添加用户ID
  userToken?: string; // 添加用户token的前缀用于识别
}

class SimpleSSO {
  private tabId: string;
  private isActive: boolean = false;
  private checkInterval: NodeJS.Timeout | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private currentUserId: number | null = null;
  private currentUserToken: string | null = null;

  constructor() {
    this.tabId = 'tab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    if (typeof window !== 'undefined') {
      console.log('🔧 初始化简单SSO，标签页ID:', this.tabId);
      
      // 监听localStorage变化
      window.addEventListener('storage', this.handleStorageChange.bind(this));
      
      // 监听页面可见性变化
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
      
      // 监听页面关闭
      window.addEventListener('beforeunload', this.cleanupOnUnload.bind(this));
      
      // 注册当前标签页
      this.registerTab();
      
      // 定期检查和清理
      this.startPeriodicCheck();
    }
  }

  private registerTab() {
    const tabs = this.getAllTabs();
    tabs[this.tabId] = {
      id: this.tabId,
      timestamp: Date.now(),
      isActive: document.visibilityState === 'visible',
      userId: this.currentUserId || undefined,
      userToken: this.currentUserToken?.substring(0, 20) || undefined
    };

    this.saveTabs(tabs);
    this.updateActiveStatus();
    console.log('📝 注册标签页:', this.tabId, '活跃状态:', this.isActive, '用户ID:', this.currentUserId);
  }

  private getAllTabs(): Record<string, TabInfo> {
    try {
      const stored = localStorage.getItem('sso-tabs');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  private saveTabs(tabs: Record<string, TabInfo>) {
    try {
      localStorage.setItem('sso-tabs', JSON.stringify(tabs));
    } catch (error) {
      console.error('❌ 保存标签页信息失败:', error);
    }
  }

  private updateActiveStatus() {
    const tabs = this.getAllTabs();
    const currentUserToken = this.currentUserToken?.substring(0, 20);

    // 如果当前用户信息为空，则按照传统方式选举（兼容性处理）
    if (!this.currentUserId || !currentUserToken) {
      console.log('⚠️ 用户信息为空，使用传统选举方式');
      const activeTabs = Object.values(tabs)
        .filter(tab => tab.isActive && Date.now() - tab.timestamp < 10000) // 10秒内活跃
        .sort((a, b) => a.timestamp - b.timestamp); // 按时间排序

      const wasActive = this.isActive;
      this.isActive = activeTabs.length > 0 && activeTabs[0].id === this.tabId;

      console.log('🔍 传统主标签页选举:', {
        tabId: this.tabId,
        activeTabs: activeTabs.map(t => ({ id: t.id, userId: t.userId })),
        wasActive,
        isActive: this.isActive
      });

      if (wasActive !== this.isActive) {
        console.log('🔄 标签页活跃状态变化:', this.tabId, '新状态:', this.isActive);
        this.emit('activeStatusChanged', { isActive: this.isActive, tabId: this.tabId });
      }
      return;
    }

    // 只考虑同账户的标签页进行主标签页选举
    const sameAccountTabs = Object.values(tabs)
      .filter(tab => {
        const isActive = tab.isActive && Date.now() - tab.timestamp < 10000; // 10秒内活跃
        const isSameAccount = tab.userToken === currentUserToken && tab.userId === this.currentUserId;
        return isActive && isSameAccount;
      })
      .sort((a, b) => a.timestamp - b.timestamp); // 按时间排序，最早的成为主标签页

    // 在同账户的标签页中，第一个活跃的标签页成为主标签页
    const wasActive = this.isActive;
    const shouldBeActive = sameAccountTabs.length > 0 && sameAccountTabs[0].id === this.tabId;

    // 只有在状态真正需要改变时才更新，避免频繁切换
    if (this.isActive !== shouldBeActive) {
      this.isActive = shouldBeActive;

      // 如果成为主标签页，更新时间戳确保优先级
      if (this.isActive) {
        const tabs = this.getAllTabs();
        if (tabs[this.tabId]) {
          tabs[this.tabId].isActive = true;
          tabs[this.tabId].timestamp = Date.now();
          this.saveTabs(tabs);
        }
      }
    }

    // 如果没有找到主标签页，但当前标签页是同账户的，强制成为主标签页
    if (!this.isActive && sameAccountTabs.length > 0) {
      const currentTabInList = sameAccountTabs.find(tab => tab.id === this.tabId);
      if (currentTabInList) {
        console.log('🔄 强制当前标签页成为主标签页');
        this.isActive = true;
        const tabs = this.getAllTabs();
        if (tabs[this.tabId]) {
          tabs[this.tabId].isActive = true;
          tabs[this.tabId].timestamp = Date.now();
          this.saveTabs(tabs);
        }
      }
    }

    console.log('🔍 基于账户的主标签页选举:', {
      tabId: this.tabId,
      currentUser: { userId: this.currentUserId, tokenPrefix: currentUserToken },
      sameAccountTabs: sameAccountTabs.map(t => ({ id: t.id, userId: t.userId, tokenPrefix: t.userToken })),
      wasActive,
      isActive: this.isActive
    });

    if (wasActive !== this.isActive) {
      console.log('🔄 标签页活跃状态变化:', this.tabId, '新状态:', this.isActive);
      this.emit('activeStatusChanged', { isActive: this.isActive, tabId: this.tabId });
    }
  }

  private handleStorageChange(event: StorageEvent) {
    console.log('📦 localStorage变化事件:', event.key, event.newValue?.substring(0, 100));

    if (event.key === 'sso-tabs') {
      this.updateActiveStatus();
    } else if (event.key?.startsWith('sso-command-')) {
      console.log('📨 检测到SSO命令事件');
      this.handleCommand(event.newValue);
    } else if (event.key === 'jwt_token') {
      // 处理token变化，但要避免影响当前标签页
      const newToken = event.newValue;
      const oldToken = event.oldValue;

      console.log('🔑 Token变化:', {
        oldToken: oldToken?.substring(0, 20) + '...',
        newToken: newToken?.substring(0, 20) + '...'
      });

      // 只有在token真正变化且不是当前标签页操作时才处理
      if (!newToken && oldToken && this.currentUserToken) {
        console.log('⚠️ 检测到token被清除，检查是否影响当前标签页');
        // 延迟检查，避免误判
        setTimeout(() => {
          const currentToken = localStorage.getItem('jwt_token');
          if (!currentToken) {
            console.log('🔔 确认token被清除，触发登出');
            this.emit('logout', { reason: 'token_removed' });
          }
        }, 500);
      }
    }
  }

  private handleVisibilityChange() {
    const tabs = this.getAllTabs();
    if (tabs[this.tabId]) {
      tabs[this.tabId].isActive = document.visibilityState === 'visible';
      tabs[this.tabId].timestamp = Date.now();
      // 更新用户信息（可能在标签页切换时用户信息已更新）
      tabs[this.tabId].userId = this.currentUserId || tabs[this.tabId].userId;
      tabs[this.tabId].userToken = this.currentUserToken?.substring(0, 20) || tabs[this.tabId].userToken;
      this.saveTabs(tabs);
      this.updateActiveStatus();
    }
  }

  private handleCommand(commandStr: string | null) {
    if (!commandStr) return;

    try {
      const command = JSON.parse(commandStr);
      console.log('📨 收到SSO命令:', command, '当前标签页:', this.tabId);

      // 防止处理自己发送的命令
      if (command.sourceTab === this.tabId) {
        console.log('ℹ️ 忽略自己发送的SSO命令');
        return;
      }

      // 检查是否是受保护的标签页
      if (command.protectedTab === this.tabId) {
        console.log('🛡️ 当前标签页受保护，忽略强制登出命令');
        return;
      }

      // 检查用户token是否匹配（避免不同用户间的误操作）
      if (command.currentUserToken && this.currentUserToken) {
        const commandTokenPrefix = command.currentUserToken;
        const currentTokenPrefix = this.currentUserToken.substring(0, 20);
        if (commandTokenPrefix !== currentTokenPrefix) {
          console.log('ℹ️ 用户token不匹配，忽略命令');
          return;
        }
      }

      if (command.type === 'logout' && command.excludeTab !== this.tabId) {
        console.log('🔔 收到登出命令，执行登出');
        this.emit('logout', command);
      } else if (command.type === 'forceLogout' && command.targetTab === this.tabId) {
        console.log('🔔 收到强制登出命令，执行登出');
        this.emit('forceLogout', command);
      } else if (command.type === 'forceLogout' && !command.targetTab && command.sourceTab !== this.tabId) {
        // 通用强制登出命令（没有指定目标标签页，且不是自己发送的）
        console.log('🔔 收到通用强制登出命令，执行登出');
        this.emit('forceLogout', command);
      } else {
        console.log('ℹ️ SSO命令不适用于当前标签页，忽略');
      }
    } catch (error) {
      console.error('❌ 解析SSO命令失败:', error);
    }
  }

  private startPeriodicCheck() {
    this.checkInterval = setInterval(() => {
      // 清理过期的标签页
      const tabs = this.getAllTabs();
      const now = Date.now();
      let hasChanges = false;

      for (const [tabId, tab] of Object.entries(tabs)) {
        // 增加过期时间到60秒，避免过于频繁的清理
        if (now - tab.timestamp > 60000) { // 60秒未更新则认为已关闭
          delete tabs[tabId];
          hasChanges = true;
          console.log('🧹 清理过期标签页:', tabId, '最后更新:', new Date(tab.timestamp).toLocaleTimeString());
        }
      }

      if (hasChanges) {
        this.saveTabs(tabs);
        this.updateActiveStatus();
      }

      // 只有在有用户信息时才更新当前标签页的时间戳
      if (tabs[this.tabId] && this.currentUserId && this.currentUserToken) {
        tabs[this.tabId].timestamp = now;
        tabs[this.tabId].userId = this.currentUserId;
        tabs[this.tabId].userToken = this.currentUserToken.substring(0, 20);
        tabs[this.tabId].isActive = document.visibilityState === 'visible';
        this.saveTabs(tabs);
      } else if (tabs[this.tabId] && (!this.currentUserId || !this.currentUserToken)) {
        // 检查是否真的已登出（避免在登录过程中误删除）
        const hasToken = localStorage.getItem('jwt_token');
        if (!hasToken) {
          console.log('🧹 确认已登出，清理标签页记录:', this.tabId);
          delete tabs[this.tabId];
          this.saveTabs(tabs);
        } else {
          console.log('ℹ️ 检测到token存在但用户信息为空，可能在登录过程中，保留标签页记录');
        }
      }

      // 清理过期的标签页记录（超过5分钟无活动）
      const expiredTabs = Object.entries(tabs).filter(([tabId, tab]) => {
        const isExpired = now - tab.timestamp > 5 * 60 * 1000; // 5分钟
        return tabId !== this.tabId && isExpired;
      });

      if (expiredTabs.length > 0) {
        console.log('🧹 清理过期标签页记录:', expiredTabs.map(([id]) => id));
        expiredTabs.forEach(([tabId]) => {
          delete tabs[tabId];
        });
        this.saveTabs(tabs);
      }
    }, 5000); // 每5秒检查一次
  }

  // 广播登出命令
  broadcastLogout() {
    console.log('🔄 广播登出命令');
    const command = {
      type: 'logout',
      timestamp: Date.now(),
      excludeTab: this.tabId, // 排除当前标签页
      sourceTab: this.tabId // 添加源标签页信息
    };

    try {
      localStorage.setItem('sso-command', JSON.stringify(command));
      // 立即删除，触发storage事件
      setTimeout(() => {
        localStorage.removeItem('sso-command');
      }, 100);
      console.log('✅ 登出命令广播成功');
    } catch (error) {
      console.error('❌ 登出命令广播失败:', error);
    }
  }

  // 强制登出特定标签页
  forceLogoutTab(targetTabId: string) {
    // 防止自我登出
    if (targetTabId === this.tabId) {
      console.warn('⚠️ 阻止自我登出命令，目标标签页ID与当前标签页相同:', targetTabId);
      return;
    }

    console.log('🔄 强制登出标签页:', targetTabId, '当前标签页:', this.tabId);
    const command = {
      type: 'forceLogout',
      timestamp: Date.now(),
      targetTab: targetTabId,
      sourceTab: this.tabId, // 添加源标签页信息
      currentUserToken: this.currentUserToken?.substring(0, 20), // 添加当前用户token标识
      protectedTab: this.tabId // 保护当前标签页不被登出
    };

    try {
      // 使用唯一的key避免冲突
      const commandKey = `sso-command-${targetTabId}-${Date.now()}`;
      localStorage.setItem(commandKey, JSON.stringify(command));

      // 延迟清理命令
      setTimeout(() => {
        localStorage.removeItem(commandKey);
      }, 5000);

      console.log('✅ 强制登出命令发送成功');
    } catch (error) {
      console.error('❌ 强制登出命令发送失败:', error);
    }
  }

  // 设置当前用户信息
  setUserInfo(userId: number, token: string, isRestore: boolean = false) {
    console.log('🔧 设置用户信息:', { userId, token: token.substring(0, 20) + '...', isRestore });

    const wasLoggedIn = !!this.currentUserId && !!this.currentUserToken;
    const isSameUser = this.currentUserId === userId;

    this.currentUserId = userId;
    this.currentUserToken = token;

    // 更新当前标签页的用户信息
    const tabs = this.getAllTabs();
    if (tabs[this.tabId]) {
      tabs[this.tabId].userId = userId;
      tabs[this.tabId].userToken = token.substring(0, 20);
      tabs[this.tabId].timestamp = Date.now();
      tabs[this.tabId].isActive = document.visibilityState === 'visible';
      this.saveTabs(tabs);
      console.log('✅ 用户信息已更新到标签页:', this.tabId);
    } else {
      // 如果标签页不存在，重新注册
      console.log('⚠️ 标签页不存在，重新注册');
      this.registerTab();
    }

    // 立即更新活跃状态
    this.updateActiveStatus();

    // 只有在真正的新登录时才强制登出其他标签页
    // 状态恢复或同一用户的token刷新不应该触发强制登出
    if (!isRestore && (!wasLoggedIn || !isSameUser)) {
      console.log('🔄 检测到新用户登录，准备强制登出其他标签页');
      // 延迟执行，确保当前标签页状态稳定
      setTimeout(() => {
        // 再次检查状态，避免竞争条件
        if (this.isMainTab() && this.currentUserToken === token) {
          console.log('🔄 主标签页恢复登录，强制登出同账户的其他标签页');
          this.forceLogoutOthers();
        } else {
          console.log('ℹ️ 状态已变化，取消强制登出操作');
        }
      }, 2000); // 增加延迟时间
    } else {
      console.log('ℹ️ 状态恢复或同用户token刷新，跳过强制登出');
    }
  }

  // 强制登出所有其他标签页（仅同账户）
  forceLogoutOthers() {
    console.log('🔄 强制登出所有其他标签页（仅同账户）');
    const tabs = this.getAllTabs();
    const currentUserToken = this.currentUserToken?.substring(0, 20);

    // 确保当前标签页信息是最新的
    if (!this.currentUserId || !currentUserToken) {
      console.log('⚠️ 当前标签页用户信息不完整，跳过强制登出:', {
        hasUserId: !!this.currentUserId,
        hasToken: !!currentUserToken,
        currentTabId: this.tabId
      });
      return;
    }

    // 更新当前标签页信息确保最新
    if (!tabs[this.tabId]) {
      console.log('🔧 当前标签页不存在，重新注册');
      this.registerTab();
      // 重新获取tabs
      const updatedTabs = this.getAllTabs();
      if (updatedTabs[this.tabId]) {
        updatedTabs[this.tabId].userId = this.currentUserId;
        updatedTabs[this.tabId].userToken = currentUserToken;
        this.saveTabs(updatedTabs);
      }
    }

    // 查找所有同账户的标签页（包括可能的旧token）
    const sameAccountTabs = Object.entries(tabs)
      .filter(([tabId, tab]) => {
        if (tabId === this.tabId) return false; // 排除当前标签页

        // 检查用户ID匹配
        const userIdMatch = tab.userId === this.currentUserId;

        // 检查token匹配（可能是旧token或新token）
        const tokenMatch = tab.userToken === currentUserToken;

        // 如果用户ID匹配，即使token不同也要登出（处理token刷新的情况）
        return userIdMatch || tokenMatch;
      })
      .map(([tabId]) => tabId);

    console.log('📋 发现同账户的其他标签页:', sameAccountTabs);
    console.log('🔍 当前用户信息:', {
      userId: this.currentUserId,
      tokenPrefix: currentUserToken,
      currentTabId: this.tabId,
      totalTabs: Object.keys(tabs).length,
      allTabs: Object.keys(tabs)
    });

    if (sameAccountTabs.length === 0) {
      console.log('ℹ️ 没有同账户的其他标签页需要登出');
      return;
    }

    // 发送强制登出命令给同账户的标签页
    for (const tabId of sameAccountTabs) {
      console.log('📤 发送强制登出命令给同账户标签页:', tabId);
      this.forceLogoutTab(tabId);
    }

    console.log('✅ 强制登出命令已发送给', sameAccountTabs.length, '个标签页');

    // 延迟清理已登出的标签页记录
    setTimeout(() => {
      console.log('🧹 清理已强制登出的标签页记录');
      const currentTabs = this.getAllTabs();
      let hasChanges = false;

      sameAccountTabs.forEach(tabId => {
        if (currentTabs[tabId]) {
          console.log('🗑️ 删除已登出的标签页记录:', tabId);
          delete currentTabs[tabId];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        this.saveTabs(currentTabs);
        console.log('✅ 已清理强制登出的标签页记录');
      }
    }, 2000); // 2秒后清理
  }

  // 事件监听
  on(event: string, listener: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }

  // 获取状态
  getStatus() {
    return {
      tabId: this.tabId,
      isActive: this.isActive,
      allTabs: this.getAllTabs()
    };
  }

  // 公共清理方法
  cleanup() {
    console.log('🧹 清理SSO标签页:', this.tabId);

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    const tabs = this.getAllTabs();
    delete tabs[this.tabId];
    this.saveTabs(tabs);

    // 清理用户信息
    this.currentUserId = null;
    this.currentUserToken = null;
    this.isActive = false;
  }

  // 私有清理方法（页面关闭时调用）
  private cleanupOnUnload() {
    this.cleanup();
  }

  // 检查是否为主标签页
  isMainTab(): boolean {
    return this.isActive;
  }
}

// 创建全局实例
export const simpleSSO = new SimpleSSO();
