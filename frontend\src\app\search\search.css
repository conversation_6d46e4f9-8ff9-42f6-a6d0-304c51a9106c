/* 搜索页面自定义样式 */

/* 反向旋转动画 */
@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

.animate-reverse {
  animation: spin-reverse 1s linear infinite;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 毛玻璃效果增强 */
.glass-effect {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 悬停发光效果 */
.glow-on-hover {
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 
    0 0 20px rgba(59, 130, 246, 0.3),
    0 0 40px rgba(147, 51, 234, 0.2),
    0 0 60px rgba(59, 130, 246, 0.1);
}

/* 搜索框特殊效果 */
.search-input-glow {
  position: relative;
}

.search-input-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #6b7280, #9ca3af, #6b7280);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.search-input-glow:hover::before {
  opacity: 0.5;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 卡片悬停效果 */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.card-hover-effect:hover {
  transform: translateY(-4px) scale(1.02);
}

/* 标签页特殊效果 */
.tab-glow {
  position: relative;
  overflow: hidden;
}

.tab-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.tab-glow:hover::before {
  left: 100%;
}

/* 脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 文字打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation: typewriter 2s steps(20) 1s forwards, blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    border-color: #3b82f6;
  }
  51%, 100% {
    border-color: transparent;
  }
}

/* 粒子效果背景 */
.particles-bg {
  position: relative;
  overflow: hidden;
}

.particles-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(107, 114, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(156, 163, 175, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(75, 85, 99, 0.1) 0%, transparent 50%);
  animation: particles-float 20s ease-in-out infinite;
}

@keyframes particles-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(20px) rotate(240deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-input-glow::before {
    display: none;
  }
  
  .card-hover-effect:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* 深色主题优化 */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(17, 24, 39, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
}
