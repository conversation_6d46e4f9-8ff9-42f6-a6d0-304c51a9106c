'use client';

import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import AvatarDisplay from '@/components/AvatarDisplay';
import { motion } from 'framer-motion';
import { Mail, Camera, Loader2 } from 'lucide-react';
import React, { useRef, useState } from 'react';
import apiService from '@/services/api';
import { toast } from 'sonner';
import { BannerCropModal } from './BannerCropModal';
import { ProfileDTO } from '@/types/ProfileDTO';
import { compressImageWithPreset, validateImageSize, validateImageType } from '@/lib/imageCompression';
import { format } from 'date-fns';
import { Calendar, MessageSquare, Edit, MessageCircle } from 'lucide-react';
import { useMessageStore } from '@/stores/messageStore';

interface ProfileOverviewProps {
  profile?: ProfileDTO;
  isPublic?: boolean;
  onBannerUpdate?: (newBannerUrl: string, bannerUrl?: string, bannerVersion?: number) => void;
  currentBannerUrl?: string;
  currentBannerVersion?: number;
}

const UserStats: React.FC<{ profile: ProfileDTO }> = ({ profile }) => (
    <div className="flex items-center justify-center sm:justify-start gap-4 mt-2 text-sm text-gray-400">
        <span className="flex items-center gap-1">
            <Calendar size={14} />
            加入于 {format(new Date(profile.createdAt), 'yyyy年MM月dd日')}
        </span>
        <span className="flex items-center gap-1">
            <Edit size={14} />
            {profile.postCount} 帖子
        </span>
        <span className="flex items-center gap-1">
            <MessageSquare size={14} />
            {profile.commentCount} 评论
        </span>
    </div>
);

const constructUrl = (base: string, path: string | null): string | null => {
    if (!path) return null;
    const cleanBase = base.endsWith('/') ? base.slice(0, -1) : base;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${cleanBase}${cleanPath}`;
};

const ProfileOverview: React.FC<ProfileOverviewProps> = ({
  profile: publicProfile,
  isPublic = false,
  onBannerUpdate,
  currentBannerUrl,
  currentBannerVersion
}) => {
  const { user: authUser, loading, updateUserBanner } = useAuth();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 获取消息系统的方法
  const { startNewConversation } = useMessageStore();

  const handleBannerUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
        // 使用新的验证函数
        if (!validateImageSize(file, 10)) {
            toast.error("横幅图片大小不能超过 10MB。");
            return;
        }
        if (!validateImageType(file, ['image/png', 'image/jpeg', 'image/webp', 'image/gif'])) {
            toast.error("只支持上传 PNG, JPG, WEBP 或 GIF 格式的图片。");
            return;
        }

        const reader = new FileReader();
        reader.onloadend = () => {
            setSelectedImage(reader.result as string);
            setIsModalOpen(true);
        };
        reader.readAsDataURL(file);
    }
    // Reset file input to allow re-selecting the same file
    if(fileInputRef.current) {
        fileInputRef.current.value = "";
    }
  };

  const handleBannerUpload = async (imageBlob: Blob) => {
    setIsUploading(true);
    try {
      // 压缩图片
      const compressedBlob = await compressImageWithPreset(imageBlob, 'banner');
      const file = new File([compressedBlob], "banner.jpg", { type: "image/jpeg" });

      const formData = new FormData();
      formData.append('banner', file);

      const response = await apiService.post('/api/user/banner', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      const { bannerUrl, bannerVersion } = response.data;

      if (bannerUrl && bannerVersion) {
        updateUserBanner(bannerUrl, bannerVersion);
        // 通知父组件更新背景和Banner
        if (onBannerUpdate) {
          const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
          const fullBannerUrl = constructUrl(apiUrl, bannerUrl);
          const newBannerImageUrl = fullBannerUrl
            ? `${fullBannerUrl}?v=${bannerVersion}`
            : '/images/background/background.webp';
          onBannerUpdate(newBannerImageUrl, fullBannerUrl, bannerVersion);
        }
      }

      toast.success('背景已更新！');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '上传失败，请重试。';
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
      setIsModalOpen(false);
      setSelectedImage(null);
    }
  };

  const handleSaveAndUpload = async (imageBlob: Blob) => {
    setIsSaving(true);
    setIsModalOpen(false);
    setIsUploading(true);

    try {
      // 压缩图片
      const compressedBlob = await compressImageWithPreset(imageBlob, 'banner');
      const file = new File([compressedBlob], "banner.jpg", { type: "image/jpeg" });

      const formData = new FormData();
      formData.append('banner', file);

      const response = await apiService.post('/api/user/banner', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      const { bannerUrl, bannerVersion } = response.data;

      if (bannerUrl && bannerVersion) {
        updateUserBanner(bannerUrl, bannerVersion);
        // 通知父组件更新背景和Banner
        if (onBannerUpdate) {
          const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
          const fullBannerUrl = constructUrl(apiUrl, bannerUrl);
          const newBannerImageUrl = fullBannerUrl
            ? `${fullBannerUrl}?v=${bannerVersion}`
            : '/images/background/background.webp';
          onBannerUpdate(newBannerImageUrl, fullBannerUrl, bannerVersion);
        }
      }

      toast.success('背景已更新！');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '上传失败，请重试。';
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
      setSelectedImage(null);
      setIsSaving(false);
    }
  };

  const profile = publicProfile || authUser;

  const renderSkeleton = () => (
    <div className="flex flex-col items-center text-center">
        <Skeleton className="h-36 w-36 rounded-full" />
        <Skeleton className="h-10 w-64 mt-6" />
        <Skeleton className="h-6 w-80 mt-4" />
        <Skeleton className="h-8 w-40 mt-4" />
    </div>
  );

  if (loading && !publicProfile) {
    return renderSkeleton();
  }

  if (!profile) {
    return <div className="text-center text-red-400">无法加载用户信息。</div>;
  }
  
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
  let finalAvatarUrl: string | null;
  let finalBannerUrl: string | null;
  let bannerVersion: number;

  if (isPublic && publicProfile) {
    finalAvatarUrl = constructUrl(apiUrl, publicProfile.avatarUrl);
    finalBannerUrl = constructUrl(apiUrl, publicProfile.bannerUrl);
    bannerVersion = publicProfile.bannerVersion;
  } else if (authUser) {
    finalAvatarUrl = authUser.fullAvatarUrl;
    // Use current banner URL if provided (for real-time updates), otherwise use auth user data
    if (currentBannerUrl && currentBannerVersion !== undefined) {
      finalBannerUrl = currentBannerUrl;
      bannerVersion = currentBannerVersion;
    } else {
      finalBannerUrl = authUser.fullBannerUrl;
      bannerVersion = authUser.bannerVersion || profile.bannerVersion;
    }
  } else {
    finalAvatarUrl = null;
    finalBannerUrl = null;
    bannerVersion = profile.bannerVersion;
  }

  const bannerImageUrl = finalBannerUrl
    ? `${finalBannerUrl}?v=${bannerVersion}`
    : '/images/background/background.webp';

  return (
    <div 
      className="relative flex flex-col items-center justify-center text-center text-white p-8 rounded-3xl overflow-hidden min-h-[300px] group"
      style={{
        backgroundImage: `url('${bannerImageUrl}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        transition: 'background-image 0.3s ease-in-out',
      }}
    >
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
      
      {/* --- UPLOAD OVERLAY --- */}
      {!isPublic && isUploading && (
        <div className="absolute inset-0 bg-black/70 backdrop-blur-md flex flex-col items-center justify-center z-30 transition-opacity duration-300">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="mt-4 text-lg font-semibold">上传中，请稍候...</p>
        </div>
      )}
      
      {/* Hidden file input */}
      {!isPublic && (
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept="image/png, image/jpeg, image/webp, image/gif"
        disabled={isUploading}
        aria-label="Upload banner image"
      />
      )}
      
      {/* Upload Button */}
      {!isPublic && (
      <button
        onClick={handleBannerUploadClick}
        disabled={isUploading}
        className="absolute top-4 right-4 z-20 p-2 bg-black/50 rounded-full text-white/70 hover:text-white hover:bg-black/70 transition-all duration-300 opacity-0 group-hover:opacity-100 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Change banner image"
      >
        {isUploading ? <Loader2 className="h-5 w-5 animate-spin" /> : <Camera size={20} />}
      </button>
      )}

      <div className="relative z-10 flex flex-col items-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: 'spring', stiffness: 260, damping: 20 }}
        >
          <AvatarDisplay 
            avatarUrl={finalAvatarUrl}
            serialNumber={profile.serialNumber}
            avatarVersion={profile.avatarVersion}
            size={144}
            className="h-36 w-36 rounded-full border-4 border-primary/50 shadow-lg"
            isLoading={loading && !publicProfile}
          />
        </motion.div>
        <h1 className="text-4xl font-bold mt-4">{profile.username}</h1>
        {isPublic && profile ? (
            <>
              <UserStats profile={profile as ProfileDTO} />
              {/* 私信按钮 */}
              <div className="mt-4">
                <button
                  onClick={() => {
                    if (!authUser) {
                      toast.error('请先登录');
                      router.push('/login');
                      return;
                    }
                    if (authUser.id === profile.id) {
                      toast.info('不能给自己发私信');
                      return;
                    }
                    // 使用messageStore打开消息中心并开始对话
                    startNewConversation(profile.id);
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-zinc-200 rounded-lg transition-colors duration-200"
                >
                  <MessageCircle size={16} />
                  发送私信
                </button>
              </div>
            </>
        ) : (
        <div className="flex items-center justify-center gap-2 mt-2 text-gray-300">
            <Mail size={16} />
                <span>{profile.email}</span>
        </div>
        )}
        <div className="flex items-center justify-center gap-2 mt-4">
          {profile.role === 'KitolusAdmin' && (
            <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-red-500/80 text-white">管理员</span>
          )}
          {profile.role === 'Developer' && (
            <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-sky-500/80 text-white">开发者</span>
          )}
          {profile.role === 'USER' && (
            <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-zinc-600/80 text-white">用户</span>
          )}
        </div>
      </div>
      
      {!isPublic && selectedImage && (
        <BannerCropModal
          isOpen={isModalOpen}
          onClose={() => {
            if (isSaving) return; // Prevent closing while processing
            setIsModalOpen(false);
            setSelectedImage(null);
          }}
          image={selectedImage}
          onSave={handleSaveAndUpload}
          isSaving={isSaving}
        />
      )}
    </div>
  );
};

export default ProfileOverview; 