'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MessageReactionsProps {
  messageId: number;
  onReact: (emoji: string) => void;
  onClose: () => void;
  existingReactions?: { emoji: string; count: number; userReacted: boolean }[];
  className?: string;
}

export const MessageReactions: React.FC<MessageReactionsProps> = ({
  messageId,
  onReact,
  onClose,
  existingReactions = [],
  className
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // 常用表情
  const quickEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🔥'];

  // 更多表情分类
  const emojiCategories = {
    faces: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰'],
    gestures: ['👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
    hearts: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓'],
    objects: ['🔥', '⭐', '🌟', '✨', '💫', '💥', '💢', '💨', '💦', '💤', '🎉', '🎊', '🎈']
  };

  const handleEmojiSelect = (emoji: string) => {
    onReact(emoji);
    onClose();
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 10 }}
        transition={{ duration: 0.2, ease: 'easeOut' }}
        className={cn(
          "absolute z-50 bg-background/95 backdrop-blur-lg border border-border rounded-lg shadow-xl",
          "min-w-[280px] max-w-[320px]",
          className
        )}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-3 border-b border-border">
          <h4 className="font-medium text-sm">添加反应</h4>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="w-6 h-6"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 现有反应 */}
        {existingReactions.length > 0 && (
          <div className="p-3 border-b border-border">
            <div className="flex flex-wrap gap-2">
              {existingReactions.map((reaction, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleEmojiSelect(reaction.emoji)}
                  className={cn(
                    "flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-all",
                    reaction.userReacted
                      ? "bg-primary/20 text-primary border border-primary/30"
                      : "bg-muted hover:bg-muted/80 border border-transparent"
                  )}
                >
                  <span className="text-sm">{reaction.emoji}</span>
                  <span className="font-medium">{reaction.count}</span>
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {/* 快速表情 */}
        <div className="p-3">
          <div className="grid grid-cols-8 gap-2">
            {quickEmojis.map((emoji, index) => (
              <motion.button
                key={emoji}
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.03 }}
                onClick={() => handleEmojiSelect(emoji)}
                className="w-8 h-8 flex items-center justify-center text-lg hover:bg-muted rounded-md transition-colors"
              >
                {emoji}
              </motion.button>
            ))}
          </div>
        </div>

        {/* 更多表情按钮 */}
        <div className="p-3 border-t border-border">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="w-full"
          >
            <Plus className="w-4 h-4 mr-2" />
            {showEmojiPicker ? '收起' : '更多表情'}
          </Button>
        </div>

        {/* 表情选择器 */}
        <AnimatePresence>
          {showEmojiPicker && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="border-t border-border overflow-hidden"
            >
              <div className="p-3 max-h-48 overflow-y-auto">
                {Object.entries(emojiCategories).map(([category, emojis]) => (
                  <div key={category} className="mb-3">
                    <h5 className="text-xs font-medium text-muted-foreground mb-2 capitalize">
                      {category}
                    </h5>
                    <div className="grid grid-cols-8 gap-1">
                      {emojis.map((emoji, index) => (
                        <motion.button
                          key={emoji}
                          initial={{ opacity: 0, scale: 0.5 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.01 }}
                          onClick={() => handleEmojiSelect(emoji)}
                          className="w-7 h-7 flex items-center justify-center text-sm hover:bg-muted rounded transition-colors"
                        >
                          {emoji}
                        </motion.button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};
