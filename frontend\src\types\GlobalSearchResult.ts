import { User } from './User';
import { CommunityPost } from './CommunityPost';
import { KnowledgeBaseArticle } from './KnowledgeBaseArticle';

export interface GlobalSearchResult {
  users: User[];
  posts: CommunityPost[];
  articles: KnowledgeBaseArticle[];
  totalResults: number;
  query: string;
}

export interface SearchSuggestion {
  type: 'user' | 'post' | 'article';
  id: string | number;
  title: string;
  subtitle?: string;
  avatarUrl?: string;
}
