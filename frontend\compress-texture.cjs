const imagemin = require('imagemin');
const imageminPngquant = require('imagemin-pngquant');
const fs = require('fs');
const path = require('path');

const inputDir = path.join(__dirname, 'public', 'images', 'textures');
const outputDir = path.join(__dirname, 'public', 'images', 'textures', 'compressed');

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

(async () => {
  const files = fs.readdirSync(inputDir).filter(f => f.endsWith('.png'));
  for (const file of files) {
    const inputPath = path.join(inputDir, file);
    const outputPath = path.join(outputDir, file);
    const before = fs.statSync(inputPath).size;
    const compressed = await imagemin([inputPath], {
      destination: outputDir,
      plugins: [imageminPngquant({ quality: [0.5, 0.7] })],
    });
    const after = fs.statSync(outputPath).size;
    console.log(`${file}: ${Math.round(before/1024/1024*100)/100}MB → ${Math.round(after/1024/1024*100)/100}MB`);
  }
})(); 