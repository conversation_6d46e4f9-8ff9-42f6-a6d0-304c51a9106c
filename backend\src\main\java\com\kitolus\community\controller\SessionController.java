package com.kitolus.community.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/session")
public class SessionController {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String TOKEN_SESSION_KEY_PREFIX = "token-session:";
    private static final String ACTIVE_SESSION_KEY_PREFIX = "user-session:active:";

    /**
     * 检查会话状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getSessionStatus(Authentication authentication) {
        if (authentication == null) {
            return ResponseEntity.ok(Map.of("authenticated", false));
        }

        String username = authentication.getName();
        String userSessionKey = ACTIVE_SESSION_KEY_PREFIX + username;
        
        Boolean hasActiveSession = redisTemplate.hasKey(userSessionKey);
        Long ttl = redisTemplate.getExpire(userSessionKey, TimeUnit.MINUTES);

        Map<String, Object> response = new HashMap<>();
        response.put("authenticated", true);
        response.put("username", username);
        response.put("hasActiveSession", hasActiveSession);
        response.put("sessionTtlMinutes", ttl);

        return ResponseEntity.ok(response);
    }

    /**
     * 刷新会话
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshSession(
            Authentication authentication,
            @RequestHeader("Authorization") String authHeader) {
        
        if (authentication == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "Not authenticated"));
        }

        String username = authentication.getName();
        String token = authHeader.substring(7); // 移除 "Bearer " 前缀
        
        String userSessionKey = ACTIVE_SESSION_KEY_PREFIX + username;
        String tokenSessionKey = TOKEN_SESSION_KEY_PREFIX + token;

        // 刷新会话TTL
        redisTemplate.expire(userSessionKey, 30, TimeUnit.MINUTES);
        redisTemplate.expire(tokenSessionKey, 24, TimeUnit.HOURS);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Session refreshed");

        return ResponseEntity.ok(response);
    }

    /**
     * 检查token是否有效
     */
    @PostMapping("/validate-token")
    public ResponseEntity<Map<String, Object>> validateToken(
            @RequestHeader("Authorization") String authHeader) {
        
        String token = authHeader.substring(7); // 移除 "Bearer " 前缀
        String tokenSessionKey = TOKEN_SESSION_KEY_PREFIX + token;
        
        Boolean isValid = redisTemplate.hasKey(tokenSessionKey);
        String username = null;
        
        if (isValid) {
            username = redisTemplate.opsForValue().get(tokenSessionKey);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        if (username != null) {
            response.put("username", username);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 主动登出（清理会话）
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout(
            Authentication authentication,
            @RequestHeader("Authorization") String authHeader) {
        
        if (authentication != null) {
            String username = authentication.getName();
            String token = authHeader.substring(7);
            
            String userSessionKey = ACTIVE_SESSION_KEY_PREFIX + username;
            String tokenSessionKey = TOKEN_SESSION_KEY_PREFIX + token;
            
            // 清理Redis中的会话信息
            redisTemplate.delete(userSessionKey);
            redisTemplate.delete(tokenSessionKey);
        }

        return ResponseEntity.ok(Map.of("success", true, "message", "Logged out successfully"));
    }
}
