'use client';

import React, { useRef } from "react";
import {
  PanelGroup,
  Panel,
} from "react-resizable-panels";
import Header from "@/components/Header";
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";
import StoreHero from "@/components/StoreHero";
import Link from 'next/link';
import { ArrowRight } from "lucide-react";

const ProductsPage = () => {
    const productsRef = useRef<HTMLDivElement>(null);
    const sceneRef = useRef<HTMLDivElement>(null);
    const journalRef = useRef<HTMLDivElement>(null);

    // --- 3D Scene Effect ---
    const sceneMouseX = useMotionValue(0);
    const sceneMouseY = useMotionValue(0);

    const springConfig = { damping: 20, stiffness: 100, mass: 0.5 };
    const springX = useSpring(sceneMouseX, springConfig);
    const springY = useSpring(sceneMouseY, springConfig);

    const rotateX = useTransform(springY, [-0.5, 0.5], ["-5deg", "5deg"]);
    const rotateY = useTransform(springX, [-0.5, 0.5], ["5deg", "-5deg"]);
    const scaleX = useTransform(springX, [-0.5, 0.5], [1.1, 1.05]);
    const scaleY = useTransform(springY, [-0.5, 0.5], [1.1, 1.05]);

    const handleSceneMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        if (!sceneRef.current) return;
        const { left, top, width, height } = sceneRef.current.getBoundingClientRect();
        const x = (e.clientX - left) / width - 0.5;
        const y = (e.clientY - top) / height - 0.5;
        sceneMouseX.set(x);
        sceneMouseY.set(y);
    };

    const handleSceneMouseLeave = () => {
        sceneMouseX.set(0);
        sceneMouseY.set(0);
    };

    // --- Magic Lantern Effect ---
    const lanternX = useMotionValue(0);
    const lanternY = useMotionValue(0);

    const handleJournalMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        if (!journalRef.current) return;
        const rect = journalRef.current.getBoundingClientRect();
        lanternX.set(e.clientX - rect.left);
        lanternY.set(e.clientY - rect.top);
    };

    const handleExploreClick = () => {
        // This can now navigate to the /products/all page or scroll to a specific section if needed
        // For now, we can just log it or have it do nothing if the button is repurposed.
    };

    return (
        <div className="w-full h-screen bg-stone-900 overflow-hidden">
            <PanelGroup direction="horizontal">
                {/* --- Left Pane --- */}
                <Panel defaultSize={15} collapsible={true}>
                    <div className="w-full h-full bg-pubs bg-cover bg-center" />
                </Panel>
                
                {/* --- Center Pane --- */}
                <Panel defaultSize={70}>
                    <div 
                        ref={journalRef}
                        onMouseMove={handleJournalMouseMove}
                        className="w-full h-full flex flex-col relative overflow-hidden bg-journal bg-stretch"
                    >
                        <motion.div
                            className="pointer-events-none absolute -inset-px z-20"
                            style={{
                                background: useTransform(
                                    [lanternX, lanternY],
                                    ([x, y]) => `radial-gradient(400px at ${x}px ${y}px, rgba(253, 246, 227, 0.4) 0%, transparent 80%)`
                                )
                            }}
                        />

                        <Header />
                        <main className="flex-grow overflow-y-auto journal-scrollbar p-8 md:p-12 relative z-10 flex flex-col items-center justify-center">
                                <StoreHero onExploreClick={handleExploreClick} />
                        </main>
                    </div>
                </Panel>

                {/* --- Right Pane --- */}
                <Panel defaultSize={15} collapsible={true}>
                    <div className="w-full h-full bg-pubs bg-cover bg-center" />
                </Panel>
            </PanelGroup>
        </div>
    );
};

export default ProductsPage; 