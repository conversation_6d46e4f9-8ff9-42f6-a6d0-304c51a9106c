'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Wifi, WifiOff, RefreshCw, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useConnectionStatus, useConnectionType, useConnectionStatusText, useMessageStore } from '@/stores/messageStore';
import { cn } from '@/lib/utils';

interface ConnectionStatusIndicatorProps {
  className?: string;
  showReconnectButton?: boolean;
  showDetails?: boolean;
}

export const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  className,
  showReconnectButton = false,
  showDetails = false
}) => {
  const connectionStatus = useConnectionStatus();
  const connectionType = useConnectionType();
  const connectionStatusText = useConnectionStatusText();
  const { reconnectConnection } = useMessageStore();

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: CheckCircle2,
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/20',
          label: '已连接',
          description: '消息系统运行正常'
        };
      case 'connecting':
        return {
          icon: RefreshCw,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500/20',
          label: '连接中',
          description: '正在建立连接...'
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10',
          borderColor: 'border-gray-500/20',
          label: '未连接',
          description: '消息系统未连接'
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/20',
          label: '连接错误',
          description: '连接出现问题'
        };
      default:
        return {
          icon: WifiOff,
          color: 'text-gray-400',
          bgColor: 'bg-gray-400/10',
          borderColor: 'border-gray-400/20',
          label: '未知',
          description: '状态未知'
        };
    }
  };

  const getTypeLabel = () => {
    switch (connectionType) {
      case 'websocket':
        return 'WebSocket';
      case 'sse':
        return 'SSE';
      case 'polling':
        return '轮询';
      case 'mock':
        return '模拟';
      default:
        return '未知';
    }
  };

  const statusConfig = getStatusConfig();
  const IconComponent = statusConfig.icon;

  const handleReconnect = async () => {
    try {
      await reconnectConnection();
    } catch (error) {
      console.error('Reconnection failed:', error);
    }
  };

  if (!showDetails) {
    // 简单模式 - 只显示图标
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn(
              "flex items-center justify-center w-6 h-6 rounded-full",
              statusConfig.bgColor,
              statusConfig.borderColor,
              "border",
              className
            )}>
              <IconComponent 
                className={cn(
                  "w-3 h-3",
                  statusConfig.color,
                  connectionStatus === 'connecting' && "animate-spin"
                )} 
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <div className="font-medium">{connectionStatusText}</div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // 详细模式 - 显示完整状态信息
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "flex items-center gap-3 p-3 rounded-lg border",
        statusConfig.bgColor,
        statusConfig.borderColor,
        className
      )}
    >
      <div className="flex items-center gap-2">
        <IconComponent 
          className={cn(
            "w-4 h-4",
            statusConfig.color,
            connectionStatus === 'connecting' && "animate-spin"
          )} 
        />
        <div className="flex flex-col">
          <span className="text-sm font-medium">{statusConfig.label}</span>
          <span className="text-xs text-muted-foreground">
            {statusConfig.description}
          </span>
        </div>
      </div>

      <div className="flex items-center gap-2 ml-auto">
        <Badge variant="outline" className="text-xs">
          {getTypeLabel()}
        </Badge>
        
        {showReconnectButton && (connectionStatus === 'disconnected' || connectionStatus === 'error') && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleReconnect}
            className="h-7 px-2 text-xs"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            重连
          </Button>
        )}
      </div>
    </motion.div>
  );
};

// 简化的连接状态点
export const ConnectionDot: React.FC<{ className?: string }> = ({ className }) => {
  const connectionStatus = useConnectionStatus();
  
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500 animate-pulse';
      case 'disconnected':
        return 'bg-gray-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div className={cn(
      "w-2 h-2 rounded-full",
      getStatusColor(),
      className
    )} />
  );
};

export default ConnectionStatusIndicator;
