'use client';

import { useState, useEffect, useCallback } from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/ui/data-table";
import { getDeveloperProducts, deleteProduct, updateProduct } from '@/services/api';
import { Product, ProductStatus } from '@/types/Product';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, MoreHorizontal, Trash2, PackageX, Eye } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import EditProductForm from './EditProductForm';
import { toast } from 'sonner';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import { Skeleton } from '@/components/ui/skeleton';

const MyProductsDashboard = () => {
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isViewOnly, setIsViewOnly] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState<Product | null>(null);
    const [pendingProducts, setPendingProducts] = useState<Product[]>([]);
    const [processedProducts, setProcessedProducts] = useState<Product[]>([]);
    const [editingProduct, setEditingProduct] = useState<Product | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isViewModalOpen, setIsViewModalOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

    const fetchProducts = useCallback(async () => {
        setIsLoading(true);
        try {
            const fetchedProducts: Product[] = await getDeveloperProducts();
            const pending = fetchedProducts.filter((p: Product) => p.status === 'PENDING_APPROVAL');
            const processed = fetchedProducts.filter((p: Product) =>
                p.status !== 'PENDING_APPROVAL' && p.status !== 'DELISTED'
            );
            setPendingProducts(pending);
            setProcessedProducts(processed);
        } catch (error) {
            console.error("Failed to fetch developer products", error);
            toast.error('获取产品列表失败');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchProducts();
    }, [fetchProducts]);

    const handleProductUpdated = () => {
        setIsEditModalOpen(false);
        setEditingProduct(null);
        fetchProducts();
    };

    const handleEditClick = (product: Product) => {
        setEditingProduct(product);
        setIsViewOnly(false); // It's an edit action
        setIsEditModalOpen(true);
    };

    const handleViewClick = (product: Product) => {
        setEditingProduct(product);
        setIsViewOnly(true); // It's a view action
        setIsEditModalOpen(true);
    };

    const handleDeleteClick = (product: Product) => {
        setProductToDelete(product);
        setIsDeleteDialogOpen(true);
    };

    const handleConfirmDelete = async () => {
        if (!productToDelete) return;

        const isDelist = productToDelete.status === ProductStatus.APPROVED;
        
        try {
            // 开发者统一使用删除产品API，后端会根据产品状态决定是下架还是删除
            await deleteProduct(productToDelete.id);
            if (isDelist) {
                toast.success(`产品 "${productToDelete.name}" 已成功下架。`);
            } else {
                toast.success(`产品 "${productToDelete.name}" 已成功删除。`);
            }
            fetchProducts();
        } catch (error: any) {
            toast.error(error.response?.data?.message || '操作失败，请重试。');
        } finally {
            setIsDeleteDialogOpen(false);
            setProductToDelete(null);
        }
    };

    const handleViewDetailsClick = (product: Product) => {
        setSelectedProduct(product);
        setIsViewModalOpen(true);
    };

    const statusBadgeMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: 'bg-yellow-500 hover:bg-yellow-600',
        [ProductStatus.APPROVED]: 'bg-green-500 hover:bg-green-600',
        [ProductStatus.REJECTED]: 'bg-red-500 hover:bg-red-600',
        [ProductStatus.DELISTED]: 'bg-gray-500 hover:bg-gray-600',
    };
    
    const statusTextMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: '审核中',
        [ProductStatus.APPROVED]: '已上架',
        [ProductStatus.REJECTED]: '已驳回',
        [ProductStatus.DELISTED]: '已下架',
    };
    
    const pendingColumns: ColumnDef<Product>[] = [
        {
            accessorKey: "name",
            header: "名称",
            cell: ({ row }) => <div className="font-medium">{row.getValue("name")}</div>
        },
        {
            accessorKey: "createdAt",
            header: "提交时间",
            cell: ({ row }) => new Date(row.getValue("createdAt")).toLocaleString(),
        },
        {
            accessorKey: "status",
            header: "状态",
            cell: ({ row }) => {
                const status = row.getValue("status") as ProductStatus;
                return <Badge className={`${statusBadgeMap[status]} text-white`}>{statusTextMap[status]}</Badge>;
            },
        },
        {
            accessorKey: "updatedAt",
            header: "最后更新于",
            cell: ({ row }) => {
                const date = row.getValue("updatedAt");
                return date ? new Date(date as string).toLocaleString() : 'N/A';
            }
        },
        {
            accessorKey: "rejectionReason",
            header: "审核信息",
            cell: ({ row }) => {
                const product = row.original;
                if (product.status === ProductStatus.REJECTED) {
                    return (
                        <div className="text-sm max-w-md">
                            <div className="flex items-center gap-2 flex-wrap">
                                <Badge variant="destructive" className="text-xs">驳回</Badge>
                                {product.reviewedBy && (
                                    <span className="text-xs text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                                {product.reviewedAt && (
                                    <span className="text-xs text-muted-foreground">
                                        • {new Date(product.reviewedAt).toLocaleDateString()}
                                    </span>
                                )}
                                <span className="text-xs text-muted-foreground line-clamp-1 flex-1" title={product.rejectionReason}>
                                    • {product.rejectionReason || '无原因'}
                                </span>
                            </div>
                        </div>
                    );
                }
                if (product.status === ProductStatus.APPROVED) {
                    return (
                        <div className="text-sm max-w-md">
                            <div className="flex items-center gap-2 flex-wrap">
                                <Badge variant="default" className="text-xs bg-green-500">通过</Badge>
                                {product.reviewedBy && (
                                    <span className="text-xs text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                                {product.reviewedAt && (
                                    <span className="text-xs text-muted-foreground">
                                        • {new Date(product.reviewedAt).toLocaleDateString()}
                                    </span>
                                )}
                                <span className="text-xs text-muted-foreground line-clamp-1 flex-1" title={product.approvalNotes}>
                                    • {product.approvalNotes || '无备注'}
                                </span>
                            </div>
                        </div>
                    );
                }
                return (
                    <div className="text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">等待审核</Badge>
                            <span className="text-xs text-muted-foreground">
                                {new Date(product.createdAt).toLocaleDateString()}
                            </span>
                        </div>
                    </div>
                );
            }
        },
        {
            header: "操作",
            id: "actions",
            cell: ({ row }) => {
                const product = row.original;
                const isApproved = product.status === ProductStatus.APPROVED;

                return (
                    <div className="flex items-center space-x-2">
                        {isApproved ? (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewDetailsClick(product)}
                                aria-label="查看产品详情"
                            >
                                <Eye className="h-4 w-4" />
                            </Button>
                        ) : (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditClick(product)}
                                aria-label="编辑产品"
                            >
                                <Edit className="h-4 w-4" />
                            </Button>
                        )}
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteClick(product)}
                            aria-label={product.status === ProductStatus.APPROVED ? '下架产品' : '删除产品'}
                        >
                            {product.status === ProductStatus.APPROVED ? <PackageX className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                            <span className="ml-2 hidden sm:inline">{product.status === ProductStatus.APPROVED ? '下架' : '删除'}</span>
                        </Button>
                    </div>
                );
            },
        }
    ];

    const processedColumns: ColumnDef<Product>[] = [
        {
            accessorKey: "name",
            header: "名称",
            cell: ({ row }) => (
                <div className="font-medium">{row.getValue("name")}</div>
            )
        },
        {
            accessorKey: "status",
            header: "状态",
            cell: ({ row }) => {
                const status = row.getValue("status") as ProductStatus;
                const badgeClass = statusBadgeMap[status] || 'bg-gray-400';
                const statusText = statusTextMap[status] || '未知状态';
                return <Badge className={`${badgeClass} text-white`}>{statusText}</Badge>;
            },
        },
        {
            accessorKey: "updatedAt",
            header: "最后更新于",
            cell: ({ row }) => {
                const date = row.getValue("updatedAt");
                return date ? new Date(date as string).toLocaleString() : 'N/A';
            }
        },
        {
            accessorKey: "reviewedAt",
            header: "审核时间",
            cell: ({ row }) => {
                const product = row.original;
                return product.reviewedAt ? new Date(product.reviewedAt).toLocaleString() : '-';
            }
        },
        {
            accessorKey: "rejectionReason",
            header: "审核信息",
            cell: ({ row }) => {
                const product = row.original;
                if (product.status === ProductStatus.REJECTED) {
                    return (
                        <div className="text-sm max-w-md">
                            <div className="flex items-center gap-2 flex-wrap">
                                <Badge variant="destructive" className="text-xs">驳回</Badge>
                                {product.reviewedBy && (
                                    <span className="text-xs text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                                <span className="text-xs text-muted-foreground line-clamp-1 flex-1" title={product.rejectionReason}>
                                    • {product.rejectionReason || '无原因'}
                                </span>
                            </div>
                        </div>
                    );
                }
                if (product.status === ProductStatus.APPROVED) {
                    return (
                        <div className="text-sm max-w-md">
                            <div className="flex items-center gap-2 flex-wrap">
                                <Badge variant="default" className="text-xs bg-green-500">通过</Badge>
                                {product.reviewedBy && (
                                    <span className="text-xs text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                                <span className="text-xs text-muted-foreground line-clamp-1 flex-1" title={product.approvalNotes}>
                                    • {product.approvalNotes || '无备注'}
                                </span>
                            </div>
                        </div>
                    );
                }
                if (product.status === ProductStatus.DELISTED) {
                    return (
                        <div className="text-sm">
                            <Badge variant="outline" className="text-xs">已下架</Badge>
                        </div>
                    );
                }
                return <span className="text-sm text-muted-foreground">-</span>;
            }
        },
        {
            header: "操作",
            id: "actions",
            cell: ({ row }) => {
                const product = row.original;
                const isApproved = product.status === ProductStatus.APPROVED;
                const isDelisted = product.status === ProductStatus.DELISTED;

                return (
                    <div className="flex items-center space-x-2">
                         {isApproved || isDelisted ? (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewClick(product)}
                                aria-label="查看产品详情"
                            >
                                <Eye className="h-4 w-4" />
                            </Button>
                        ) : (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditClick(product)}
                                aria-label="编辑产品"
                            >
                                <Edit className="h-4 w-4" />
                            </Button>
                        )}
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteClick(product)}
                            aria-label={product.status === ProductStatus.APPROVED ? '下架产品' : '删除产品'}
                        >
                            {product.status === ProductStatus.APPROVED ? <PackageX className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                        </Button>
                    </div>
                );
            },
        }
    ];

    if (isLoading) {
        return (
            <div className="space-y-8">
                <div>
                    <Skeleton className="h-8 w-1/4 mb-4" />
                    <Skeleton className="h-4 w-1/2" />
                </div>
                <Skeleton className="h-48 w-full" />
                <div>
                    <Skeleton className="h-8 w-1/4 mb-4" />
                    <Skeleton className="h-4 w-1/2" />
                </div>
                <Skeleton className="h-48 w-full" />
            </div>
        );
    }

    return (
        <div>
            <div className="mb-8">
                <h2 className="text-2xl font-bold mb-2">等待审核的产品</h2>
                <p className="text-muted-foreground">
                    这些产品正在等待管理员审核。在此期间您可以编辑或删除它们。
                </p>
                <div className="mt-4">
                    <DataTable columns={pendingColumns} data={pendingProducts} noResultsMessage="没有等待审核的产品。" />
                </div>
            </div>

            <div>
                <h2 className="text-2xl font-bold tracking-tight text-white mb-4 mt-8">已处理的产品</h2>
                <p className="text-muted-foreground mb-6">这些产品已被批准、驳回或下架。</p>
                <div className="mt-4">
                    <DataTable columns={processedColumns} data={processedProducts} noResultsMessage="没有已处理的产品。" />
                </div>
            </div>

            {/* Edit Product Modal */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent className="max-w-4xl h-full max-h-[90vh]">
                    <DialogHeader>
                        <DialogTitle>
                            {isViewOnly ? '查看产品详情' : '编辑产品'}
                        </DialogTitle>
                        <DialogDescription>
                            {isViewOnly ? '查看产品的详细信息' : '修改产品信息并保存更改'}
                        </DialogDescription>
                    </DialogHeader>
                     {editingProduct && (
                        <EditProductForm
                            product={editingProduct}
                            onProductUpdated={handleProductUpdated}
                            isViewOnly={isViewOnly}
                        />
                    )}
                </DialogContent>
            </Dialog>

            {/* View Product Details Modal */}
            <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>产品详情</DialogTitle>
                    </DialogHeader>
                    {selectedProduct && (
                        <div className="space-y-4 max-h-[80vh] overflow-y-auto p-4">
                            {selectedProduct.imageUrl && (
                                <div className="relative w-full h-48 rounded-lg overflow-hidden">
                                    <Image
                                        src={selectedProduct.imageUrl}
                                        alt={selectedProduct.name}
                                        layout="fill"
                                        objectFit="cover"
                                    />
                                </div>
                            )}
                            <h2 className="text-2xl font-bold">{selectedProduct.name}</h2>
                            <div className="flex items-center gap-4">
                                <Badge className={`${statusBadgeMap[selectedProduct.status]} text-white`}>
                                    {statusTextMap[selectedProduct.status]}
                                </Badge>
                                <span className="font-bold text-xl">¥{selectedProduct.price.toFixed(2)}</span>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-2">描述</h3>
                                <div className="prose prose-invert max-w-none">
                                    <ReactMarkdown>{selectedProduct.description}</ReactMarkdown>
                                </div>
                            </div>
                            {(selectedProduct.status === 'REJECTED' && selectedProduct.rejectionReason) && (
                                <div>
                                    <h3 className="font-semibold text-red-400 mb-2">驳回原因</h3>
                                    <p className="text-muted-foreground p-3 bg-red-900/20 rounded-md">
                                        {selectedProduct.rejectionReason}
                                    </p>
                                </div>
                            )}
                             {(selectedProduct.status === 'APPROVED' && selectedProduct.approvalNotes) && (
                                <div>
                                    <h3 className="font-semibold text-green-400 mb-2">审核备注</h3>
                                    <p className="text-muted-foreground p-3 bg-green-900/20 rounded-md">
                                        {selectedProduct.approvalNotes}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                    <div className="pt-4 border-t border-border">
                        <Button onClick={() => setIsViewModalOpen(false)}>关闭</Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>
                            {productToDelete?.status === ProductStatus.APPROVED ? '下架产品' : '删除产品'}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            {productToDelete?.status === ProductStatus.APPROVED 
                                ? '您确定要下架此产品吗？此操作会将其从商店中移除，但不会删除产品数据。'
                                : '您确定要永久删除此产品吗？此操作不可撤销。'}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleConfirmDelete()} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                            确认
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default MyProductsDashboard; 