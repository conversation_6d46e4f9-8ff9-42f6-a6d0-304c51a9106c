package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

@Data
@TableName("orders")
public class Order {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private Long productId;
    private String outTradeNo;
    private BigDecimal totalFee;
    private String status;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    private Timestamp paidAt;
    private BigDecimal platformFee;
    private BigDecimal developerRevenue;
} 