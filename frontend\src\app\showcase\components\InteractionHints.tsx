'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Move, MousePointer2, X, RotateCcw, Smartphone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useInteractionHints } from '../hooks/useInteractionHints';

interface InteractionHintsProps {
  className?: string;
}

export const InteractionHints: React.FC<InteractionHintsProps> = ({
  className = ''
}) => {
  const { isVisible, hasInteracted, hideHints, resetHints } = useInteractionHints({
    storageKey: 'showcase-hints-seen',
    autoHideDelay: 3000,
    initialDelay: 2000
  });

  if (!isVisible) return null;

  return (
    <>
      {/* 主提示面板 */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -20 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
            className={`fixed top-4 right-4 sm:top-6 sm:right-6 z-50 ${className}`}
          >
            <div className="bg-black/70 backdrop-blur-lg border border-white/20 rounded-xl p-3 sm:p-4 shadow-2xl max-w-xs sm:max-w-sm">
              {/* 关闭按钮 */}
              <button
                onClick={hideHints}
                className="absolute top-2 right-2 w-6 h-6 flex items-center justify-center text-white/60 hover:text-white/90 hover:bg-white/10 rounded-full transition-colors"
                aria-label="关闭提示"
              >
                <X size={14} />
              </button>

              {/* 标题 */}
              <div className="mb-3">
                <h3 className="text-white font-semibold text-sm mb-1">交互指南</h3>
                <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
              </div>

              {/* 提示内容 */}
              <div className="space-y-3 text-sm">
                {/* 拖动提示 */}
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center gap-3 text-white/80"
                >
                  <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Move size={16} className="text-blue-400" />
                  </div>
                  <div>
                    <div className="font-medium">拖动旋转</div>
                    <div className="text-xs text-white/60">鼠标拖拽可旋转视角</div>
                  </div>
                </motion.div>

                {/* 滚轮提示 */}
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="flex items-center gap-3 text-white/80"
                >
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MousePointer2 size={16} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="font-medium">滚轮缩放</div>
                    <div className="text-xs text-white/60">滚轮可缩放查看作品</div>
                  </div>
                </motion.div>

                {/* 点击提示 */}
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex items-center gap-3 text-white/80"
                >
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MousePointer2 size={16} className="text-green-400" />
                  </div>
                  <div>
                    <div className="font-medium">点击查看</div>
                    <div className="text-xs text-white/60">点击作品卡片查看详情</div>
                  </div>
                </motion.div>
              </div>

              {/* 底部提示 */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-4 pt-3 border-t border-white/10"
              >
                <div className="text-xs text-white/50 text-center">
                  开始交互后此提示将自动隐藏
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 重新显示提示的按钮（开发时使用） */}
      {process.env.NODE_ENV === 'development' && !isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed bottom-6 right-6 z-40"
        >
          <Button
            onClick={resetHints}
            variant="outline"
            size="sm"
            className="bg-black/50 border-white/20 text-white/70 hover:text-white hover:bg-white/10"
          >
            <RotateCcw size={14} className="mr-2" />
            重置提示
          </Button>
        </motion.div>
      )}

      {/* 浮动指示器（当提示隐藏后显示） */}
      <AnimatePresence>
        {!isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ delay: 1, duration: 0.3 }}
            className="fixed top-6 right-6 z-40"
          >
            <button
              onClick={resetHints}
              className="w-10 h-10 bg-black/50 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white/60 hover:text-white/90 hover:bg-white/10 transition-all duration-200 group"
              aria-label="显示交互提示"
            >
              <MousePointer2 size={16} className="group-hover:scale-110 transition-transform" />
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// 简化版本的提示组件，用于移动设备
export const MobileInteractionHints: React.FC<InteractionHintsProps> = ({
  className = ''
}) => {
  const { isVisible, hideHints } = useInteractionHints({
    storageKey: 'showcase-mobile-hints-seen',
    autoHideDelay: 4000,
    initialDelay: 1500
  });

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.3 }}
          className={`fixed bottom-6 left-4 right-4 z-50 ${className}`}
        >
          <div className="bg-black/80 backdrop-blur-lg border border-white/20 rounded-lg p-4 shadow-2xl">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Smartphone size={16} className="text-blue-400" />
                <h3 className="text-white font-medium text-sm">触摸操作</h3>
              </div>
              <button
                onClick={hideHints}
                className="text-white/60 hover:text-white/90"
                aria-label="关闭提示"
              >
                <X size={16} />
              </button>
            </div>
            <div className="text-xs text-white/70 space-y-1">
              <div>• 单指拖动：旋转视角</div>
              <div>• 双指缩放：放大缩小</div>
              <div>• 点击作品：查看详情</div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
