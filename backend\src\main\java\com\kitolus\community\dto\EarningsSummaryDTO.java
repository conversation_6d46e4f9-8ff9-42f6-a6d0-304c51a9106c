package com.kitolus.community.dto;

import com.kitolus.community.entity.Product;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.kitolus.community.dto.OrderDTO;

import java.util.List;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EarningsSummaryDTO {
    // For global view
    private double totalRevenue;
    private double communityRevenue;
    private long totalSales;
    private List<MonthlyRevenueDTO> monthlyRevenue;
    private List<DailyRevenueDTO> dailyRevenue;
    private ProductDTO topSellingProduct;
    private List<ProductDTO> topSellingProducts;
    private double averageOrderValue;
    private long developerCount;
    private long productCount;
    private List<OrderDTO> recentOrders;

    // For developer view
    private double developerRevenue;
    private BigDecimal availableBalance;

    // Constructor for global summary
    public EarningsSummaryDTO(double totalRevenue, double communityRevenue, long totalSales, List<MonthlyRevenueDTO> monthlyRevenue, List<DailyRevenueDTO> dailyRevenue, ProductDTO topSellingProduct, double averageOrderValue, long developerCount, long productCount, List<OrderDTO> recentOrders) {
        this.totalRevenue = totalRevenue;
        this.communityRevenue = communityRevenue;
        this.totalSales = totalSales;
        this.monthlyRevenue = monthlyRevenue;
        this.dailyRevenue = dailyRevenue;
        this.topSellingProduct = topSellingProduct;
        this.averageOrderValue = averageOrderValue;
        this.developerCount = developerCount;
        this.productCount = productCount;
        this.recentOrders = recentOrders;
    }

    // Constructor for developer summary
    public EarningsSummaryDTO(double developerRevenue, long totalSales, List<MonthlyRevenueDTO> monthlyRevenue,
                              List<DailyRevenueDTO> dailyRevenue, ProductDTO topSellingProduct, double averageOrderValue,
                              long developerCount, long productCount, List<OrderDTO> recentOrders) {
        this.developerRevenue = developerRevenue;
        this.totalSales = totalSales;
        this.monthlyRevenue = monthlyRevenue;
        this.dailyRevenue = dailyRevenue;
        this.topSellingProduct = topSellingProduct;
        this.averageOrderValue = averageOrderValue;
        this.developerCount = developerCount;
        this.productCount = productCount;
        this.recentOrders = recentOrders;
    }

    // Adding a new constructor for developer summary that includes availableBalance
    public EarningsSummaryDTO(double developerRevenue, BigDecimal availableBalance, long totalSales, List<MonthlyRevenueDTO> monthlyRevenue,
                              List<DailyRevenueDTO> dailyRevenue, ProductDTO topSellingProduct, double averageOrderValue,
                              long developerCount, long productCount, List<OrderDTO> recentOrders) {
        this(developerRevenue, totalSales, monthlyRevenue, dailyRevenue, topSellingProduct, averageOrderValue, developerCount, productCount, recentOrders);
        this.availableBalance = availableBalance;
    }
} 