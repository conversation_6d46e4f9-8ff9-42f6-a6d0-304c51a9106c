/**
 * 微服务客户端 - 支持gRPC-Web和GraphQL
 * 优势：类型安全、代码生成、更好的性能、实时订阅
 */

import { createClient } from '@connectrpc/connect';
import { createGrpcWebTransport } from '@connectrpc/connect-web';
import { ApolloClient, InMemoryCache, createHttpLink, split } from '@apollo/client';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { getMainDefinition } from '@apollo/client/utilities';
import { createClient as createWSClient } from 'graphql-ws';

// gRPC服务定义（需要从.proto文件生成）
interface MessageService {
  sendMessage(request: any): Promise<any>;
  getMessages(request: any): Promise<any>;
  subscribeToMessages(request: any): AsyncIterable<any>;
}

interface UserService {
  getUserProfile(request: any): Promise<any>;
  updateUserStatus(request: any): Promise<any>;
}

interface NotificationService {
  sendNotification(request: any): Promise<any>;
  getNotificationSettings(request: any): Promise<any>;
}

// gRPC客户端配置
const grpcTransport = createGrpcWebTransport({
  baseUrl: process.env.NEXT_PUBLIC_GRPC_ENDPOINT || 'http://localhost:8080',
  interceptors: [
    // 认证拦截器
    (next) => async (req) => {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (token) {
        req.header.set('authorization', `Bearer ${token}`);
      }
      return await next(req);
    },
    // 错误处理拦截器
    (next) => async (req) => {
      try {
        return await next(req);
      } catch (error) {
        console.error('gRPC Error:', error);
        throw error;
      }
    },
  ],
});

// GraphQL客户端配置
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT || 'http://localhost:4000/graphql',
  credentials: 'include',
});

const wsLink = new GraphQLWsLink(
  createWSClient({
    url: process.env.NEXT_PUBLIC_GRAPHQL_WS_ENDPOINT || 'ws://localhost:4000/graphql',
    connectionParams: () => ({
      authorization: `Bearer ${localStorage.getItem('token')}`,
    }),
  })
);

// 根据操作类型分割链接
const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    );
  },
  wsLink,
  httpLink
);

const apolloClient = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache({
    typePolicies: {
      Message: {
        fields: {
          // 消息列表的缓存合并策略
          messages: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming];
            },
          },
        },
      },
      Conversation: {
        fields: {
          messages: {
            merge(existing = [], incoming) {
              const merged = [...existing];
              incoming.forEach((newMsg: any) => {
                const existingIndex = merged.findIndex(msg => msg.id === newMsg.id);
                if (existingIndex >= 0) {
                  merged[existingIndex] = newMsg;
                } else {
                  merged.push(newMsg);
                }
              });
              return merged.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            },
          },
        },
      },
    },
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    query: {
      errorPolicy: 'all',
    },
  },
});

// 服务客户端类 - 简化版本
class MicroserviceClient {
  private messageService: any;
  private userService: any;
  private notificationService: any;

  constructor() {
    // 初始化gRPC客户端 - 暂时使用占位符
    this.messageService = null; // createClient(MessageService, grpcTransport);
    this.userService = null; // createClient(UserService, grpcTransport);
    this.notificationService = null; // createClient(NotificationService, grpcTransport);
  }

  // 消息服务方法 - 简化版本
  async sendMessage(data: {
    content: string;
    receiverId?: number;
    conversationId?: string;
    type: string;
  }) {
    // TODO: 实现gRPC调用
    console.log('Microservice sendMessage called with:', data);
    return Promise.resolve({ success: true });
  }

  async getMessages(params: {
    conversationId?: string;
    limit?: number;
    offset?: number;
    type?: string;
  }) {
    // TODO: 实现gRPC调用
    console.log('Microservice getMessages called with:', params);
    return Promise.resolve({ messages: [], total: 0 });
  }

  // 实时消息订阅 - 简化版本
  async *subscribeToMessages(conversationId?: string) {
    // TODO: 实现gRPC流
    console.log('Microservice subscribeToMessages called with:', conversationId);
    yield { type: 'connected', data: null };
  }

  // 用户服务方法 - 简化版本
  async getUserProfile(userId: number) {
    console.log('Microservice getUserProfile called with:', userId);
    return Promise.resolve({ id: userId, username: 'user' });
  }

  async updateUserStatus(status: 'online' | 'away' | 'busy' | 'offline') {
    console.log('Microservice updateUserStatus called with:', status);
    return Promise.resolve({ success: true });
  }

  // 通知服务方法 - 简化版本
  async sendNotification(data: {
    userId: number;
    type: string;
    title: string;
    content: string;
    priority?: string;
  }) {
    console.log('Microservice sendNotification called with:', data);
    return Promise.resolve({ success: true });
  }

  // GraphQL查询方法 - 简化版本
  async executeGraphQLQuery(query: string, variables?: any) {
    console.log('GraphQL query called with:', query, variables);
    return Promise.resolve({});
  }

  async executeGraphQLMutation(mutation: string, variables?: any) {
    console.log('GraphQL mutation called with:', mutation, variables);
    return Promise.resolve({});
  }

  // GraphQL订阅 - 简化版本
  subscribeToGraphQL(subscription: string, variables?: any) {
    console.log('GraphQL subscription called with:', subscription, variables);
    return {
      subscribe: () => ({ unsubscribe: () => {} })
    };
  }

  // 错误处理 - 简化版本
  private handleGrpcError(error: any) {
    console.error('gRPC Error:', error);
    return new Error(`gRPC Error: ${error?.message || 'Unknown error'}`);
  }

  private handleGraphQLError(error: any) {
    console.error('GraphQL Error:', error);
    return new Error(`GraphQL Error: ${error?.message || 'Unknown error'}`);
  }

  private getCurrentUserId(): number {
    // 从认证上下文获取当前用户ID
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user.id || 0;
  }

  // 健康检查
  async healthCheck() {
    try {
      // 检查各个服务的健康状态
      const checks = await Promise.allSettled([
        fetch(`${process.env.NEXT_PUBLIC_GRPC_ENDPOINT}/health`),
        fetch(`${process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT}/health`),
      ]);

      return {
        grpc: checks[0].status === 'fulfilled',
        graphql: checks[1].status === 'fulfilled',
      };
    } catch (error) {
      return {
        grpc: false,
        graphql: false,
      };
    }
  }

  // 获取服务指标
  async getMetrics() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_GRPC_ENDPOINT}/metrics`);
      return await response.json();
    } catch (error) {
      return null;
    }
  }
}

// 全局客户端实例
export const microserviceClient = new MicroserviceClient();

// React Hook
export const useMicroserviceClient = () => {
  return microserviceClient;
};

// GraphQL Apollo Client导出
export { apolloClient };
