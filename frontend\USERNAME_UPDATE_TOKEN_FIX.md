# 用户名修改后500错误修复

## 🐛 问题描述

用户修改用户名后跳转到新页面时出现500错误，显示"无法加载此用户的个人资料。用户可能不存在或发生了网络错误。"

## 🔍 错误分析

### 错误日志
```
GET https://kitolus.top/api/user/profile/Kitolus12 500 (Internal Server Error)
GET https://kitolus.top/api/session/status 500 (Internal Server Error)
GET https://kitolus.top/api/notifications/unread-count 500 (Internal Server Error)
GET https://kitolus.top/api/messages/unread/count 500 (Internal Server Error)
```

### 根本原因
1. **JWT Token失效**：用户名变更后，后端生成了新的JWT token
2. **前端未处理**：前端没有接收和使用新的token
3. **会话不一致**：旧token中包含旧用户名，导致后续API调用失败

### 问题流程
```
用户修改用户名
↓
后端更新数据库并生成新JWT token
↓
前端只接收ProfileDTO，忽略新token ❌
↓
页面跳转到新用户名URL
↓
使用旧token调用API
↓
服务器验证失败，返回500错误 ❌
```

## ✅ 解决方案

### 1. 修改API响应处理
更新`updateUserProfile`函数以处理可能返回的新token：

```typescript
// 修复前
export const updateUserProfile = async (profileData: UpdateProfileRequestDTO): Promise<ProfileDTO> => {
    const response = await apiService.put<ProfileDTO>('/api/user/profile', profileData);
    return response.data; // ❌ 只返回ProfileDTO，忽略token
};

// 修复后
export const updateUserProfile = async (profileData: UpdateProfileRequestDTO): Promise<{ profile: ProfileDTO; token?: string }> => {
    const response = await apiService.put('/api/user/profile', profileData);
    
    // 检查响应是否包含新token（用户名变更时会返回）
    if (response.data.token) {
        return {
            profile: profileData as ProfileDTO,
            token: response.data.token // ✅ 返回新token
        };
    } else {
        return {
            profile: profileData as ProfileDTO
        };
    }
};
```

### 2. 更新前端处理逻辑
在AccountSettings组件中处理新token：

```typescript
// 修复前
const updatedProfile = await updateUserProfile({...});
setUser({ ...user, ...updatedProfile }); // ❌ 没有处理token

// 修复后
const result = await updateUserProfile({...});

// 如果返回了新token，更新本地存储和请求头
if (result.token) {
    localStorage.setItem('token', result.token);
    apiService.defaults.headers.common['Authorization'] = `Bearer ${result.token}`;
}

setUser({ ...user, username: newUsernameValue, ... }); // ✅ 正确更新用户信息
```

## 🔧 具体修复

### API服务修复
```typescript
// 新的返回类型
interface UpdateProfileResult {
    profile: ProfileDTO;
    token?: string;
}

// 处理后端响应
if (response.data.token) {
    return {
        profile: profileData as ProfileDTO,
        token: response.data.token
    };
}
```

### 用户名更新修复
```typescript
const result = await updateUserProfile({
    username: newUsername.trim(),
    alipayAccount: user?.alipayAccount,
    wechatAccount: user?.wechatAccount
});

// 关键修复：更新token
if (result.token) {
    localStorage.setItem('token', result.token);
    apiService.defaults.headers.common['Authorization'] = `Bearer ${result.token}`;
}
```

### 支付账户更新修复
```typescript
const result = await updateUserProfile({
    username: user?.username || '',
    alipayAccount: paymentAccounts.alipayAccount,
    wechatAccount: paymentAccounts.wechatAccount,
});

// 安全检查：虽然支付账户更新不会改变用户名，但还是检查token
if (result.token) {
    localStorage.setItem('token', result.token);
    apiService.defaults.headers.common['Authorization'] = `Bearer ${result.token}`;
}
```

## 🎯 修复效果

### 修复前
- ❌ 用户名修改后出现500错误
- ❌ 无法加载新用户名的个人资料
- ❌ 所有API调用失败
- ❌ 需要重新登录才能恢复

### 修复后
- ✅ 用户名修改后自动更新token
- ✅ 成功跳转到新用户名页面
- ✅ 所有API调用正常
- ✅ 无需重新登录

## 🔄 完整流程

### 新的正确流程
```
用户修改用户名
↓
后端更新数据库并生成新JWT token
↓
前端接收ProfileDTO和新token ✅
↓
更新localStorage中的token ✅
↓
更新API请求头中的Authorization ✅
↓
页面跳转到新用户名URL ✅
↓
使用新token调用API ✅
↓
成功加载用户资料 ✅
```

## 🛡️ 安全考虑

### 1. Token同步
确保localStorage和API请求头中的token保持同步。

### 2. 错误处理
如果token更新失败，应该有适当的错误处理机制。

### 3. 会话一致性
新token确保用户会话与数据库中的用户信息保持一致。

## 🧪 测试要点

### 功能测试
- [ ] 修改用户名后成功跳转到新页面
- [ ] 新页面正常加载用户资料
- [ ] Header和其他组件正常显示
- [ ] 所有API调用返回正确数据

### Token测试
- [ ] localStorage中的token正确更新
- [ ] API请求头中的Authorization正确更新
- [ ] 新token包含正确的用户名信息
- [ ] 旧token失效，新token有效

### 边界情况测试
- [ ] 网络错误时的处理
- [ ] 服务器返回错误时的处理
- [ ] 用户名未实际改变时的处理
- [ ] 并发请求的处理

## 📊 后端响应格式

### 用户名变更时
```json
{
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "message": "用户资料更新成功"
}
```

### 其他更新时
```json
{
    "message": "用户资料更新成功"
}
```

## 🔍 调试信息

### 检查token更新
```javascript
// 在浏览器控制台检查
console.log('旧token:', localStorage.getItem('token'));
// 修改用户名后
console.log('新token:', localStorage.getItem('token'));
```

### 检查API请求头
```javascript
// 检查axios默认请求头
console.log('Authorization:', apiService.defaults.headers.common['Authorization']);
```

现在用户修改用户名后应该能正常跳转和加载页面了！
