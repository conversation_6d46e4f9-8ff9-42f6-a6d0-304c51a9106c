'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  X,
  Search,
  Users,
  ChevronLeft,
  ChevronRight,
  Maximize2,
  Minimize2,
  MessageSquare,
  Expand,
  Shrink,
  Settings,
  UserPlus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { MessageStats } from '@/types/Message';
import { useMessageStore, useUnreadCount, useConnectionStatus, useConnectionType } from '@/stores/messageStore';
import { ConnectionStatusIndicator } from '@/components/ConnectionStatusIndicator';
import { useMessageStats, useRealtimeSync } from '@/hooks/useMessageQueries';
import { useWebSocketDebugInfo } from '@/hooks/useWebSocketStatus';
import { ConversationList } from './ConversationList';
import { RealTimeChatInterface } from './RealTimeChatInterface';
import { WebSocketStatusIndicator } from './WebSocketStatusIndicator';
import { MessageSettings } from './MessageSettings';
import { NewConversationDialog } from './NewConversationDialog';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

// 消息系统状态类型
type MessageSystemState = 'collapsed' | 'semi-expanded' | 'fully-expanded';

interface MessageSystemProps {
  className?: string;
  defaultState?: MessageSystemState;
  allowResize?: boolean;
  minWidth?: number;
  maxWidth?: number;
  startConversationUserId?: number | null;
  onConversationStarted?: () => void;
}

export const MessageSystem: React.FC<MessageSystemProps> = ({
  className,
  defaultState = 'collapsed',
  allowResize = true,
  minWidth = 320,
  maxWidth = 1200,
  startConversationUserId,
  onConversationStarted
}) => {
  const { user, isAuthenticated } = useAuth();

  // 使用现代化状态管理
  const {
    isOpen,
    setOpen,
    activeTab,
    setActiveTab,
    selectedConversationId,
    setSelectedConversation,
    initializeConnection
  } = useMessageStore();

  const storeUnreadCount = useUnreadCount();
  const connectionStatus = useConnectionStatus();
  const connectionType = useConnectionType();

  // 添加强制刷新状态和本地未读数量状态
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [localUnreadCount, setLocalUnreadCount] = useState(0);

  // 使用本地状态或store状态
  const totalUnread = localUnreadCount >= 0 ? localUnreadCount : storeUnreadCount;

  // 同步store状态到本地状态
  useEffect(() => {
    setLocalUnreadCount(storeUnreadCount);
  }, [storeUnreadCount]);

  // 直接清除对话未读消息的函数
  const clearConversationUnread = useCallback((conversationId: string) => {
    // 简化处理：假设有未读消息，直接清除
    setLocalUnreadCount(prev => {
      const estimatedUnreadCount = Math.min(prev, 10); // 假设最多10条未读

      if (estimatedUnreadCount > 0) {
        // 更新store状态
        const { updateUnreadStats, unreadStats } = useMessageStore.getState();
        if (unreadStats) {
          const newStats = {
            ...unreadStats,
            totalUnread: Math.max(0, unreadStats.totalUnread - estimatedUnreadCount),
            privateUnread: Math.max(0, unreadStats.privateUnread - estimatedUnreadCount)
          };
          updateUnreadStats(newStats);
        }

        return Math.max(0, prev - estimatedUnreadCount);
      }

      return prev;
    });

    // 触发ConversationList清除UI状态
    window.dispatchEvent(new CustomEvent('directClearUnread', {
      detail: { conversationId }
    }));

    // 延迟刷新UI，避免与其他刷新冲突
    setTimeout(() => {
      setRefreshTrigger(prev => prev + 1);
    }, 50);
  }, []);

  // 统一的清除未读消息函数
  const clearUnreadMessages = useCallback((conversationId: string, unreadCount: number) => {
    // 1. 立即更新本地未读数量显示
    if (unreadCount > 0) {
      setLocalUnreadCount(prev => Math.max(0, prev - unreadCount));
    }

    // 2. 同时更新store状态（异步）
    if (unreadCount > 0) {
      const { updateUnreadStats, unreadStats } = useMessageStore.getState();
      if (unreadStats) {
        const newStats = {
          ...unreadStats,
          totalUnread: Math.max(0, unreadStats.totalUnread - unreadCount),
          privateUnread: Math.max(0, unreadStats.privateUnread - unreadCount)
        };
        updateUnreadStats(newStats);
      }
    }

    // 3. 触发ConversationList清除UI状态
    window.dispatchEvent(new CustomEvent('clearConversationUnread', {
      detail: { conversationId }
    }));

    // 4. 延迟刷新UI，避免频繁刷新
    setTimeout(() => {
      setRefreshTrigger(prev => prev + 1);
    }, 100);
  }, []);

  const [searchQuery, setSearchQuery] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isUltraWide, setIsUltraWide] = useState(false);

  // 新增状态管理
  const [systemState, setSystemState] = useState<MessageSystemState>(defaultState);
  const [showSettings, setShowSettings] = useState(false);
  const [showNewConversation, setShowNewConversation] = useState(false);

  // 使用React Query获取数据
  const { data: messageStats, isLoading: statsLoading } = useMessageStats();

  // 启用实时同步和WebSocket状态监控
  useRealtimeSync();
  const webSocketDebugInfo = useWebSocketDebugInfo();

  // 检测设备类型和屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 768);
      setIsTablet(width > 768 && width <= 1024);
      setIsUltraWide(width >= 1920);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 确保连接已初始化
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      const token = localStorage.getItem('token');

      if (token && connectionStatus === 'disconnected') {
        initializeConnection(token, user.id);
      }
    }
  }, [isAuthenticated, user?.id, connectionStatus, initializeConnection]);

  // 同步Header按钮的isOpen状态与本地systemState状态
  useEffect(() => {
    if (isOpen && systemState === 'collapsed') {
      // Header按钮打开消息系统时，设置为半展开状态
      setSystemState('semi-expanded');
    } else if (!isOpen && systemState !== 'collapsed') {
      // Header按钮关闭消息系统时，设置为折叠状态
      setSystemState('collapsed');
    }
  }, [isOpen, systemState]);

  // 加载用户偏好设置
  useEffect(() => {
    const savedState = localStorage.getItem('messageSystemState');

    if (savedState && ['collapsed', 'semi-expanded', 'fully-expanded'].includes(savedState)) {
      setSystemState(savedState as MessageSystemState);
    }
  }, []);

  // 保存用户偏好设置
  useEffect(() => {
    localStorage.setItem('messageSystemState', systemState);
  }, [systemState]);

  // 监听选中对话的变化，自动展开界面
  useEffect(() => {
    if (selectedConversationId && systemState === 'collapsed') {
      // 如果有选中的对话且当前是折叠状态，展开到全屏
      setSystemState('fully-expanded');
    }
  }, [selectedConversationId]);

  // 监听会话删除事件
  useEffect(() => {
    const handleConversationDeleted = (event: CustomEvent) => {
      const { conversationId } = event.detail;
      // 如果当前选中的会话被删除，清除选中状态
      if (selectedConversationId === conversationId) {
        console.log(`清除已删除会话的选中状态: ${conversationId}`);
        setSelectedConversation(null);
        // 如果是全屏状态，返回到半屏状态
        if (systemState === 'fully-expanded') {
          setSystemState('semi-expanded');
        }
      }
    };

    window.addEventListener('conversationDeleted', handleConversationDeleted as EventListener);

    return () => {
      window.removeEventListener('conversationDeleted', handleConversationDeleted as EventListener);
    };
  }, [selectedConversationId, systemState]);

  // 键盘快捷键 - 只保留 Escape 键关闭功能
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 移除 Ctrl/Cmd + M 快捷键 - 用户只希望从Header按钮打开消息系统

      // Escape 键关闭或收起
      if (e.key === 'Escape' && systemState !== 'collapsed') {
        e.preventDefault();
        if (systemState === 'fully-expanded') {
          collapseToSemi();
        } else {
          collapseCompletely();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [systemState]);

  // 处理标签切换
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setSelectedConversation(null);
  };

  // 处理会话选择 - 直接回调方式
  const handleConversationSelect = async (conversationId: string, unreadCount?: number) => {
    // 立即设置选中状态
    setSelectedConversation(conversationId);

    // 如果提供了未读数量，使用统一的清除函数
    if (conversationId && user && typeof unreadCount === 'number') {
      clearUnreadMessages(conversationId, unreadCount);
    }

    if (isMobile) {
      // 在移动端，选择会话后可能需要特殊处理
    }
  };



  // 状态切换函数
  const toggleState = () => {
    switch (systemState) {
      case 'collapsed':
        setSystemState('semi-expanded');
        setOpen(true);
        break;
      case 'semi-expanded':
        setSystemState('fully-expanded');
        break;
      case 'fully-expanded':
        setSystemState('collapsed');
        setOpen(false);
        break;
    }
  };

  const expandToFull = () => {
    setSystemState('fully-expanded');
    setOpen(true);
  };

  const collapseToSemi = () => {
    setSystemState('semi-expanded');
    setSelectedConversation(null);
  };

  const collapseCompletely = () => {
    setSystemState('collapsed');
    setOpen(false);
    setSelectedConversation(null);
  };

  // 新建对话处理函数
  const handleNewConversation = () => {
    setShowNewConversation(true);
  };

  // 监听对话已读响应事件
  useEffect(() => {
    const handleConversationReadResponse = (event: CustomEvent) => {
      const { conversationId, clearedUnreadCount, source } = event.detail;

      if (clearedUnreadCount > 0) {
        try {
          const { updateUnreadStats, unreadStats } = useMessageStore.getState();
          if (unreadStats) {
            const newStats = {
              ...unreadStats,
              totalUnread: Math.max(0, unreadStats.totalUnread - clearedUnreadCount),
              privateUnread: Math.max(0, unreadStats.privateUnread - clearedUnreadCount)
            };

            updateUnreadStats(newStats);

            // 强制触发重新渲染
            setRefreshTrigger(prev => prev + 1);
          }
        } catch (error) {
          console.error('MessageSystem: 更新全局未读统计失败:', error);
        }
      }
    };

    window.addEventListener('conversationReadResponse', handleConversationReadResponse as EventListener);

    return () => {
      window.removeEventListener('conversationReadResponse', handleConversationReadResponse as EventListener);
    };
  }, []);

  // 开始与用户对话
  const handleStartConversation = (userId: string) => {
    const conversationId = `conv_${userId}`;
    setSelectedConversation(conversationId);
    setShowNewConversation(false);

    // 如果当前是半展开状态，自动展开到全屏
    if (systemState === 'semi-expanded') {
      setSystemState('fully-expanded');
    }
  };

  // 处理外部传入的开始对话请求
  useEffect(() => {
    if (startConversationUserId) {
      const conversationId = `conv_${startConversationUserId}`;
      setSelectedConversation(conversationId);
      setSystemState('fully-expanded');

      // 通知父组件对话已开始
      if (onConversationStarted) {
        onConversationStarted();
      }
    }
  }, [startConversationUserId, onConversationStarted]);



  if (!isAuthenticated) {
    return null;
  }



  // 获取当前状态的样式配置 - 使用标准化宽度
  const getStateConfig = () => {
    switch (systemState) {
      case 'collapsed':
        return {
          width: 0,
          leftWidth: 0,
          showChat: false,
          showList: false
        };
      case 'semi-expanded':
        if (isMobile) {
          return {
            width: window.innerWidth,
            leftWidth: window.innerWidth,
            showChat: false,
            showList: true
          };
        } else if (isTablet) {
          return {
            width: Math.min(380, window.innerWidth * 0.4),
            leftWidth: Math.min(380, window.innerWidth * 0.4),
            showChat: false,
            showList: true
          };
        } else if (isUltraWide) {
          return {
            width: Math.min(480, window.innerWidth * 0.25),
            leftWidth: Math.min(480, window.innerWidth * 0.25),
            showChat: false,
            showList: true
          };
        } else {
          return {
            width: Math.min(420, window.innerWidth * 0.35),
            leftWidth: Math.min(420, window.innerWidth * 0.35),
            showChat: false,
            showList: true
          };
        }
      case 'fully-expanded':
        if (isMobile) {
          return {
            width: window.innerWidth,
            leftWidth: window.innerWidth,
            showChat: true,
            showList: true
          };
        } else if (isTablet) {
          return {
            width: Math.min(720, window.innerWidth * 0.8),
            leftWidth: Math.min(300, window.innerWidth * 0.35),
            showChat: true,
            showList: true
          };
        } else if (isUltraWide) {
          return {
            width: Math.min(1200, window.innerWidth * 0.6),
            leftWidth: Math.min(400, window.innerWidth * 0.2),
            showChat: true,
            showList: true
          };
        } else {
          return {
            width: Math.min(900, window.innerWidth * 0.7),
            leftWidth: Math.min(350, window.innerWidth * 0.25),
            showChat: true,
            showList: true
          };
        }
      default:
        return {
          width: 0,
          leftWidth: 0,
          showChat: false,
          showList: false
        };
    }
  };

  const stateConfig = getStateConfig();

  return (
    <>
      {/* 右下角浮动按钮已移除 - 用户不希望从此处打开消息系统 */}

      {/* 消息系统侧边栏 */}
      <AnimatePresence>
        {systemState !== 'collapsed' && (
          <>
            {/* 遮罩层（移动端） */}
            {isMobile && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-[100]"
                onClick={() => setOpen(false)}
              />
            )}

            {/* 桌面端背景遮罩 - 防止内容穿模 */}
            {!isMobile && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-[90]"
                style={{
                  background: 'rgba(0, 0, 0, 0.1)',
                  backdropFilter: 'blur(2px)',
                  WebkitBackdropFilter: 'blur(2px)'
                }}
                onClick={() => setOpen(false)}
              />
            )}

            {/* 侧边栏 */}
            <motion.div
              initial={{
                x: isMobile ? 0 : '100%',
                y: isMobile ? '100%' : 0,
                width: isMobile ? '100%' : stateConfig.width
              }}
              animate={{
                x: 0,
                y: 0,
                width: isMobile ? '100%' : stateConfig.width
              }}
              exit={{
                x: isMobile ? 0 : '100%',
                y: isMobile ? '100%' : 0
              }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className={cn(
                "fixed z-[9999] flex message-system-container",
                isMobile
                  ? "inset-x-0 bottom-0 h-[90vh] rounded-t-3xl flex-col"
                  : "right-6 top-6 bottom-6 rounded-3xl",
                systemState === 'fully-expanded' ? "flex-row" : "flex-col"
              )}
              style={{
                background: 'rgba(0, 0, 0, 0.25)',
                backdropFilter: 'blur(20px) saturate(180%)',
                WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                width: isMobile ? '100%' : `${stateConfig.width}px`
              }}
            >


              {/* 对话列表区域 */}
              <div
                className="flex flex-col relative z-[10000] overflow-hidden message-system-left-panel"
                style={{
                  width: systemState === 'fully-expanded' ? `${stateConfig.leftWidth}px` : '100%',
                  minWidth: systemState === 'fully-expanded' ? `${stateConfig.leftWidth}px` : 'auto',
                  maxWidth: systemState === 'fully-expanded' ? `${stateConfig.leftWidth}px` : 'none',
                  background: systemState === 'fully-expanded'
                    ? 'rgba(255, 255, 255, 0.08)'
                    : 'rgba(255, 255, 255, 0.03)',
                  borderRight: systemState === 'fully-expanded'
                    ? '1px solid rgba(255, 255, 255, 0.15)'
                    : 'none',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)'
                }}
              >
                {/* 头部 */}
                <div
                  className="flex items-center justify-between p-4 border-b border-white/15 relative z-[10001] overflow-hidden"
                  style={{
                    width: '100%',
                    maxWidth: '100%'
                  }}
                >
                  <div className="flex items-center gap-3 min-w-0 flex-1 overflow-hidden">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary/15 to-primary/5 flex items-center justify-center flex-shrink-0">
                      <MessageSquare className="w-5 h-5 text-primary" />
                    </div>
                    <div className="min-w-0 flex-1 overflow-hidden">
                      <h2 className="font-bold text-lg text-foreground truncate">对话</h2>
                      {totalUnread > 0 && (
                        <span
                          className="text-xs text-muted-foreground truncate block"
                          key={`unread-${totalUnread}-${refreshTrigger}`}
                        >
                          {totalUnread} 条未读消息
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleNewConversation}
                      className="h-10 w-10 rounded-xl hover:bg-muted/50 transition-colors"
                      title="新建对话"
                    >
                      <UserPlus className="w-5 h-5" />
                    </Button>

                    {systemState === 'semi-expanded' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={expandToFull}
                        className="h-10 w-10 rounded-xl hover:bg-muted/50 transition-colors"
                        title="展开聊天界面"
                      >
                        <Expand className="w-5 h-5" />
                      </Button>
                    )}
                    {systemState === 'fully-expanded' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={collapseToSemi}
                        className="h-10 w-10 rounded-xl hover:bg-muted/50 transition-colors"
                        title="收起聊天界面"
                      >
                        <Shrink className="w-5 h-5" />
                      </Button>
                    )}

                    <Dialog open={showSettings} onOpenChange={setShowSettings}>
                      <DialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-10 w-10 rounded-xl hover:bg-muted/50 transition-colors"
                          title="设置"
                        >
                          <Settings className="w-5 h-5" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <MessageSettings />
                      </DialogContent>
                    </Dialog>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={collapseCompletely}
                      className="h-10 w-10 rounded-xl hover:bg-muted/50 transition-colors"
                      title="关闭"
                    >
                      <X className="w-5 h-5" />
                    </Button>
                  </div>
                </div>

              {/* 搜索栏 - 已隐藏 */}
              {false && (
                <div className="p-6 border-b border-border/20">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                    <Input
                      placeholder="搜索对话和消息..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-12 h-12 text-sm rounded-xl transition-all border-0"
                      style={{
                        background: 'rgba(255, 255, 255, 0.05)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        color: 'rgba(255, 255, 255, 0.9)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)'
                      }}
                    />
                  </div>
                </div>
              )}

              {/* 对话列表 */}
              <div className="flex-1 overflow-hidden">
                <ConversationList
                  searchQuery=""
                  selectedConversationId={selectedConversationId}
                  onConversationSelect={(id, unreadCount) => {
                    handleConversationSelect(id, unreadCount);
                    if (systemState === 'semi-expanded') {
                      expandToFull();
                    }
                  }}

                />
              </div>
              </div>

              {/* 实时聊天界面区域 - 仅在完全展开时显示 */}
              {systemState === 'fully-expanded' && stateConfig.showChat && (
                <div
                  className="relative overflow-hidden message-system-right-panel"
                  style={{
                    width: `${stateConfig.width - stateConfig.leftWidth}px`,
                    minWidth: `${stateConfig.width - stateConfig.leftWidth}px`,
                    maxWidth: `${stateConfig.width - stateConfig.leftWidth}px`,
                    background: 'rgba(255, 255, 255, 0.03)',
                    borderLeft: '1px solid rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)'
                  }}
                >
                  <RealTimeChatInterface
                    conversationId={selectedConversationId}
                    onBack={() => {
                      if (systemState === 'fully-expanded') {
                        collapseToSemi();
                      }
                    }}
                    onClose={() => {
                      // 关闭整个消息系统
                      setOpen(false);
                      setSystemState('collapsed');
                    }}
                    onClearUnread={clearConversationUnread}
                  />
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* 新建对话对话框 */}
      <NewConversationDialog
        open={showNewConversation}
        onOpenChange={setShowNewConversation}
        onStartConversation={handleStartConversation}
      />

    </>
  );
};
