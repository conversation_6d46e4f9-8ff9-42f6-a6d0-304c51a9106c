package com.kitolus.community.service;

import com.kitolus.community.entity.ProductStatus;
import com.kitolus.community.entity.ProductStatusHistory;
import com.kitolus.community.mapper.ProductStatusHistoryMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ProductStatusHistoryServiceImpl implements ProductStatusHistoryService {
    
    private final ProductStatusHistoryMapper historyMapper;
    
    @Override
    @Transactional
    public void recordStatusChange(Long productId, ProductStatus oldStatus, ProductStatus newStatus, 
                                 String changedBy, String reason, String notes) {
        // 只有状态真正发生变化时才记录
        if (oldStatus != newStatus) {
            ProductStatusHistory history = new ProductStatusHistory();
            history.setProductId(productId);
            history.setOldStatus(oldStatus);
            history.setNewStatus(newStatus);
            history.setChangedBy(changedBy);
            history.setChangedAt(new Timestamp(System.currentTimeMillis()));
            history.setReason(reason);
            history.setNotes(notes);
            
            historyMapper.insert(history);
        }
    }
    
    @Override
    public List<ProductStatusHistory> getProductStatusHistory(Long productId) {
        return historyMapper.findByProductId(productId);
    }
}
