'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { 
    Users, 
    Package, 
    DollarSign, 
    TrendingUp, 
    ShoppingCart, 
    UserCheck, 
    PackageCheck,
    AlertCircle,
    BarChart3,
    Activity
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getAdminDashboardStats, getAdminEarningsSummary } from '@/services/api';
import { AdminDashboardStats } from '@/types/AdminDashboardStats';
import { EarningsSummaryDTO } from '@/types/EarningsSummary';
import { Badge } from '@/components/ui/badge';

const AdminDataCenterDashboard = () => {
    const { user: authUser } = useAuth();
    const [stats, setStats] = useState<AdminDashboardStats | null>(null);
    const [earningsSummary, setEarningsSummary] = useState<EarningsSummaryDTO | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchAdminData = async () => {
            if (!authUser || authUser.role !== 'KitolusAdmin') return;
            
            try {
                setLoading(true);
                const [statsData, earningsData] = await Promise.all([
                    getAdminDashboardStats(),
                    getAdminEarningsSummary(),
                ]);
                setStats(statsData);
                setEarningsSummary(earningsData);
            } catch (err) {
                setError('无法加载管理员数据，请稍后重试。');
                console.error('Failed to fetch admin data:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchAdminData();
    }, [authUser]);

    const formatCurrency = (value: number | undefined) => `¥${(value || 0).toFixed(2)}`;

    if (loading) {
        return (
            <div className="space-y-6">
                <Skeleton className="h-8 w-1/3" />
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                        <Skeleton key={i} className="h-32" />
                    ))}
                </div>
                <div className="grid gap-6 md:grid-cols-2">
                    {Array.from({ length: 2 }).map((_, i) => (
                        <Skeleton key={i} className="h-64" />
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>加载错误</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-3xl font-bold tracking-tight text-white">数据中心</h2>
                <Badge variant="secondary" className="text-sm">
                    <Activity className="mr-1 h-3 w-3" />
                    管理员面板
                </Badge>
            </div>

            {/* 关键指标卡片 */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-blue-100">今日新用户</CardTitle>
                        <Users className="h-4 w-4 text-blue-400" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.newUsersToday || 0}</div>
                        <p className="text-xs text-blue-200 mt-1">
                            新注册用户数量
                        </p>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 border-orange-500/20">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-orange-100">待审核申请</CardTitle>
                        <UserCheck className="h-4 w-4 text-orange-400" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.pendingDeveloperApplications || 0}</div>
                        <p className="text-xs text-orange-200 mt-1">
                            开发者申请待处理
                        </p>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 border-purple-500/20">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-purple-100">待审核产品</CardTitle>
                        <PackageCheck className="h-4 w-4 text-purple-400" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-white">{stats?.pendingProducts || 0}</div>
                        <p className="text-xs text-purple-200 mt-1">
                            产品审核待处理
                        </p>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-green-100">平台总收入</CardTitle>
                        <DollarSign className="h-4 w-4 text-green-400" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-white">
                            {formatCurrency(earningsSummary?.totalRevenue)}
                        </div>
                        <p className="text-xs text-green-200 mt-1">
                            累计平台收入
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* 详细统计信息 */}
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <BarChart3 className="h-5 w-5" />
                            平台概览
                        </CardTitle>
                        <CardDescription>平台整体运营数据</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">总销售量</span>
                            <span className="font-semibold">{earningsSummary?.totalSales || 0} 笔</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">平均订单价值</span>
                            <span className="font-semibold">{formatCurrency(earningsSummary?.averageOrderValue)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">开发者数量</span>
                            <span className="font-semibold">{earningsSummary?.developerCount || 0} 人</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">产品总数</span>
                            <span className="font-semibold">{earningsSummary?.productCount || 0} 个</span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="h-5 w-5" />
                            热门产品
                        </CardTitle>
                        <CardDescription>最受欢迎的产品</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <div className="space-y-3">
                                <div className="flex items-center gap-3">
                                    <Skeleton className="h-8 w-8 rounded" />
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-32" />
                                        <Skeleton className="h-3 w-20" />
                                    </div>
                                </div>
                                <Skeleton className="h-3 w-full" />
                                <Skeleton className="h-3 w-3/4" />
                            </div>
                        ) : earningsSummary?.topSellingProducts && earningsSummary.topSellingProducts.length > 0 ? (
                            <div className="space-y-4">
                                {/* 显示前3个热门产品 */}
                                {earningsSummary.topSellingProducts.slice(0, 3).map((product, index) => (
                                    <div key={product.id} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                                                <span className="text-sm font-bold text-primary">#{index + 1}</span>
                                            </div>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="font-semibold truncate">{product.name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatCurrency(product.price)}
                                            </p>
                                        </div>
                                        <Package className="h-5 w-5 text-muted-foreground" />
                                    </div>
                                ))}

                                {/* 总销量统计 */}
                                <div className="pt-2 border-t">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">总销量</span>
                                        <span className="font-medium">{earningsSummary.totalSales || 0} 笔</span>
                                    </div>
                                </div>
                            </div>
                        ) : earningsSummary?.topSellingProduct ? (
                            <div className="space-y-3">
                                <div className="flex items-center gap-3">
                                    <Package className="h-8 w-8 text-primary" />
                                    <div>
                                        <p className="font-semibold">{earningsSummary.topSellingProduct.name}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {formatCurrency(earningsSummary.topSellingProduct.price)}
                                        </p>
                                    </div>
                                </div>
                                <div className="text-sm text-muted-foreground">
                                    {earningsSummary.topSellingProduct.description}
                                </div>
                                {/* 显示销售统计信息 */}
                                <div className="pt-2 border-t">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">总销量</span>
                                        <span className="font-medium">{earningsSummary.totalSales || 0} 笔</span>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="text-center text-muted-foreground py-8">
                                <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                                <p>暂无销售数据</p>
                                <p className="text-xs mt-1">当前没有已完成的订单</p>
                                {/* 调试信息 */}
                                {process.env.NODE_ENV === 'development' && (
                                    <div className="mt-4 text-xs text-left bg-gray-800 p-2 rounded">
                                        <p>调试信息:</p>
                                        <p>earningsSummary: {earningsSummary ? 'exists' : 'null'}</p>
                                        <p>topSellingProduct: {earningsSummary?.topSellingProduct ? 'exists' : 'null'}</p>
                                        <p>totalSales: {earningsSummary?.totalSales || 0}</p>
                                    </div>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* 待处理事项提醒 */}
            {(stats?.pendingDeveloperApplications || 0) > 0 || (stats?.pendingProducts || 0) > 0 ? (
                <Card className="border-yellow-500/20 bg-yellow-500/5">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-yellow-400">
                            <AlertCircle className="h-5 w-5" />
                            待处理事项
                        </CardTitle>
                        <CardDescription>需要您关注的管理任务</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        {(stats?.pendingDeveloperApplications || 0) > 0 && (
                            <div className="flex justify-between items-center p-3 bg-orange-500/10 rounded-lg">
                                <span className="text-sm">开发者申请审核</span>
                                <Badge variant="secondary">{stats?.pendingDeveloperApplications} 个待处理</Badge>
                            </div>
                        )}
                        {(stats?.pendingProducts || 0) > 0 && (
                            <div className="flex justify-between items-center p-3 bg-purple-500/10 rounded-lg">
                                <span className="text-sm">产品审核</span>
                                <Badge variant="secondary">{stats?.pendingProducts} 个待处理</Badge>
                            </div>
                        )}
                    </CardContent>
                </Card>
            ) : null}
        </div>
    );
};

export default AdminDataCenterDashboard;
