'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Heart, Clock, FileText } from 'lucide-react';
import { getUserRecentActivity } from '@/services/api';
import { UserRecentActivity, RecentPost, RecentComment } from '@/types/UserActivity';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface UserRecentActivityProps {
  username: string;
}

export default function UserRecentActivityComponent({ username }: UserRecentActivityProps) {
  const [activity, setActivity] = useState<UserRecentActivity | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchActivity = async () => {
      try {
        const data = await getUserRecentActivity(username);
        setActivity(data);
      } catch (error) {
        console.error('获取用户活动失败:', error);
        // 设置模拟数据
        setActivity({
          recentPosts: [
            {
              id: 1,
              title: 'GTNH 2.6.0 更新内容详解',
              content: '这次更新带来了很多新的内容和优化...',
              createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              likesCount: 25,
              commentsCount: 8,
              category: '攻略'
            },
            {
              id: 2,
              title: '高效的AE2自动化设计',
              content: '分享一个高效的AE2自动化系统设计方案...',
              createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
              likesCount: 42,
              commentsCount: 15,
              category: '教程'
            }
          ],
          recentComments: [
            {
              id: 1,
              content: '这个方法确实很有效，我试过了！',
              createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
              post: {
                id: 3,
                title: '新手必看：GTNH入门指南'
              },
              likesCount: 5
            },
            {
              id: 2,
              content: '感谢分享，学到了很多新知识。',
              createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
              post: {
                id: 4,
                title: '高级电路板制作技巧'
              },
              likesCount: 3
            }
          ]
        });
      } finally {
        setLoading(false);
      }
    };

    fetchActivity();
  }, [username]);

  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
          <CardHeader>
            <CardTitle className="text-zinc-200">最近活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-zinc-700 rounded w-3/4"></div>
              <div className="h-4 bg-zinc-700 rounded w-1/2"></div>
              <div className="h-4 bg-zinc-700 rounded w-2/3"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!activity) {
    return null;
  }

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: zhCN 
      });
    } catch {
      return '未知时间';
    }
  };

  return (
    <div className="space-y-6">
      {/* 最近发布的帖子 */}
      {activity.recentPosts.length > 0 && (
        <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
          <CardHeader>
            <CardTitle className="text-zinc-200 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              最近发布的帖子
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {activity.recentPosts.map((post) => (
              <div key={post.id} className="p-4 bg-zinc-900/30 rounded-lg hover:bg-zinc-800/50 transition-colors">
                <Link href={`/community/post/${post.id}`} className="block">
                  <h4 className="font-semibold text-zinc-100 hover:text-blue-400 transition-colors mb-2">
                    {post.title}
                  </h4>
                  <p className="text-zinc-400 text-sm line-clamp-2 mb-3">
                    {post.content}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-zinc-500">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimeAgo(post.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        <span>{post.likesCount}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        <span>{post.commentsCount}</span>
                      </div>
                    </div>
                    {post.category && (
                      <Badge variant="secondary" className="bg-zinc-800/60 text-zinc-300 border-zinc-700/40">
                        {post.category}
                      </Badge>
                    )}
                  </div>
                </Link>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 最近的评论 */}
      {activity.recentComments.length > 0 && (
        <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
          <CardHeader>
            <CardTitle className="text-zinc-200 flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              最近的评论
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {activity.recentComments.map((comment) => (
              <div key={comment.id} className="p-4 bg-zinc-900/30 rounded-lg hover:bg-zinc-800/50 transition-colors">
                <Link href={`/community/post/${comment.post.id}`} className="block">
                  <p className="text-zinc-300 mb-2">
                    {comment.content}
                  </p>
                  <div className="text-sm text-zinc-500 mb-2">
                    回复了帖子: <span className="text-blue-400 hover:text-blue-300">{comment.post.title}</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-zinc-500">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{formatTimeAgo(comment.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="h-3 w-3" />
                      <span>{comment.likesCount}</span>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 如果没有活动 */}
      {activity.recentPosts.length === 0 && activity.recentComments.length === 0 && (
        <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
          <CardContent className="text-center py-8">
            <p className="text-zinc-400">该用户最近没有活动</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
