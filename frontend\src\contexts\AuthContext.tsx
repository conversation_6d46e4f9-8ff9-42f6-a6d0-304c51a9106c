'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { jwtDecode } from 'jwt-decode';
import apiService from '@/services/api';
import { User } from '@/types/User';
import { sessionMonitor } from '@/lib/sessionMonitor';
import { sessionManager } from '@/lib/sessionManager';

interface DecodedToken {
    sub: string; // "sub" is the standard claim for subject (username)
    exp: number;
    iat: number;
}

export interface AuthContextType {
    token: string | null;
    user: User | null;
    setUser: (user: User | null) => void; // Add this
    login: (newToken: string) => void;
    logout: (skipBroadcast?: boolean) => void;
    updateUserAvatar: (newAvatarUrl: string, newAvatarVersion: number) => void;
    updateUserBanner: (newBannerUrl: string, newBannerVersion: number) => void;
    refreshToken: () => Promise<void>;
    isAuthenticated: boolean;
    loading: boolean;
    hasRole: (role: string) => boolean;
    isAdmin: boolean;
    isDeveloper: boolean;
    justLoggedIn: boolean;
    resetJustLoggedIn: () => void;
    isInitializing: boolean; // 添加初始化状态
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [token, setToken] = useState<string | null>(null);
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    const [isAdmin, setIsAdmin] = useState(false);
    const [isDeveloper, setIsDeveloper] = useState(false);
    const [justLoggedIn, setJustLoggedIn] = useState(false);
    const [isInitializing, setIsInitializing] = useState(true); // 添加初始化状态

    const fetchUser = async (): Promise<User | null> => {
        // 检查localStorage中的token（因为API拦截器从那里读取）
        const token = localStorage.getItem('jwt_token');
        if (token) {
            try {
                console.log('🔍 发送用户信息请求...');
                const response = await apiService.get('/api/user/profile');
                const userData: User = response.data;
                
                // Determine roles
                const roles = userData.role ? userData.role.split(',') : [];
                // Hardcode 'Kitolus' as admin for failsafe
                const isAdminUser = roles.includes('ROLE_ADMIN') || roles.includes('ROLE_KITOLUS_ADMIN') || userData.username === 'Kitolus';
                // An admin is always a developer
                const isDeveloperUser = roles.includes('ROLE_DEVELOPER') || isAdminUser;

                setIsAdmin(isAdminUser);
                setIsDeveloper(isDeveloperUser);

                // Add flags to user object before setting state
                userData.isAdmin = isAdminUser;
                userData.isDeveloper = isDeveloperUser;

                setUser(userData);
                return userData;
            } catch (error: any) {
                console.error('❌ fetchUser错误:', error);
                console.error('❌ 错误详情:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    url: error.config?.url,
                    hasToken: !!localStorage.getItem('jwt_token')
                });

                // 只有在非初始化阶段才执行登出
                if (!isInitializing) {
                    console.log('🔔 fetchUser失败，执行登出');
                    logout();
                } else {
                    console.log('ℹ️ 初始化阶段fetchUser失败，不执行登出');
                }
                return null;
            }
        }
        return null;
    };

    useEffect(() => {
        const initializeAuth = async () => {
            console.log('🔧 开始初始化认证...');

            // 尝试恢复会话
            const session = sessionManager.restoreSession();

            if (session) {
                console.log('🔑 恢复会话，开始验证用户...');

                // 确保localStorage中有token（fetchUser需要从这里读取）
                localStorage.setItem('jwt_token', session.token);
                console.log('✅ 会话token已同步到localStorage');

                setToken(session.token);
                apiService.defaults.headers.common['Authorization'] = `Bearer ${session.token}`;

                try {
                    const userData = await fetchUser();
                    if (userData) {
                        console.log('✅ 用户验证成功:', userData.username);
                        // 设置初始化完成标记
                        localStorage.setItem('auth_initialized', 'true');
                        // WebSocket连接由GlobalWebSocketContext管理
                        // 会话恢复成功，不需要额外的SSO操作
                    } else {
                        throw new Error('获取用户信息失败');
                    }
                } catch (error) {
                    console.error('❌ 用户验证失败:', error);

                    // 检查错误类型，只有在token真正无效时才清除
                    if (error.response?.status === 401 || error.response?.status === 403) {
                        console.log('🔄 Token无效，清除会话');
                        sessionManager.clearSession();
                        setToken(null);
                        setUser(null);
                        apiService.defaults.headers.common['Authorization'] = null;
                    } else {
                        console.log('⚠️ 网络错误或其他问题，保留会话等待重试');
                    }
                }
            } else {
                console.log('ℹ️ 没有找到会话数据');
            }

            setLoading(false);
            setIsInitializing(false); // 标记初始化完成

            // 设置初始化完成标记，供API拦截器使用
            localStorage.setItem('auth_initialized', 'true');
            console.log('✅ 认证初始化完成');
        };

        initializeAuth();
    }, []);

    useEffect(() => {
        console.log('🔧 AuthContext初始化会话管理器...');

        // 设置会话失效回调
        sessionManager.onSessionInvalidated(() => {
            console.log('🔔 会话失效，执行登出');
            logout(true);
        });

        const handleAuthError = () => {
            // 只有在初始化完成后才处理认证错误
            if (!isInitializing) {
                console.log('🔔 收到认证错误事件，执行登出');
                logout();
            } else {
                console.log('ℹ️ 初始化阶段收到认证错误，忽略');
            }
        };

        window.addEventListener('auth-error', handleAuthError);

        console.log('✅ 会话管理器已设置');

        return () => {
            console.log('🧹 清理会话管理器');
            window.removeEventListener('auth-error', handleAuthError);
        };
    }, [token]);

    const login = async (newToken: string) => {
        try {
            setLoading(true);
            setJustLoggedIn(true);

            console.log('🔧 开始登录流程，token:', newToken.substring(0, 20) + '...');

            // 先保存token到localStorage，因为API拦截器需要从那里读取
            localStorage.setItem('jwt_token', newToken);
            console.log('✅ Token已保存到localStorage');

            setToken(newToken);
            apiService.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
            console.log('✅ API请求头已设置');

            const userData = await fetchUser();
            if (!userData) {
                throw new Error('获取用户信息失败');
            }

            console.log('🔄 登录成功，创建新会话:', { userId: userData.id, token: newToken.substring(0, 20) + '...' });

            // 创建新会话（这会自动强制登出其他标签页）
            sessionManager.takeOverSession(userData.id, newToken);

            // 设置初始化完成标记
            localStorage.setItem('auth_initialized', 'true');

            // WebSocket连接由GlobalWebSocketContext管理

            console.log('✅ 新会话已创建，其他标签页将自动登出');

            // 启动会话监控
            sessionMonitor.startMonitoring();
        } catch (error) {
            console.error('❌ 登录过程中出错:', error);
            logout();
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const refreshToken = async () => {
        try {
            const newToken = localStorage.getItem('jwt_token');
            if (newToken) {
                setToken(newToken);
                apiService.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
                const userData = await fetchUser();
                if (!userData) {
                    throw new Error('获取用户信息失败');
                }
            } else {
                throw new Error('Token 不存在');
            }
        } catch (error) {
            logout();
        }
    };

    const logout = (skipBroadcast = false) => {
        console.log('🔄 执行登出，skipBroadcast:', skipBroadcast);

        // 停止会话监控
        sessionMonitor.stopMonitoring();

        // 清理会话
        sessionManager.clearSession();

        // WebSocket连接由GlobalWebSocketContext管理和清理

        // 清理所有状态
        setToken(null);
        setUser(null);
        setLoading(false);
        setIsAdmin(false);
        setIsDeveloper(false);
        setJustLoggedIn(false);

        // 清理API请求头
        apiService.defaults.headers.common['Authorization'] = null;

        console.log('✅ 登出完成');
    };

    const resetJustLoggedIn = () => {
        setJustLoggedIn(false);
    };

    const updateUserAvatar = async (newAvatarUrl: string, newAvatarVersion: number) => {
        setUser(prevUser => {
            if (prevUser) {
                return { 
                    ...prevUser, 
                    avatarUrl: newAvatarUrl,
                    avatarVersion: newAvatarVersion 
                };
            }
            return null;
        });
        await refreshToken();
    };

    const updateUserBanner = async (newBannerUrl: string, newBannerVersion: number) => {
        setUser(prevUser => {
            if (prevUser) {
                return { 
                    ...prevUser, 
                    bannerUrl: newBannerUrl,
                    bannerVersion: newBannerVersion
                };
            }
            return null;
        });
        await refreshToken();
    };

    const hasRole = (role: string): boolean => {
        return user?.role === role;
    };

    return (
        <AuthContext.Provider value={{ token, user, setUser, login, logout, updateUserAvatar, updateUserBanner, refreshToken, isAuthenticated: !!token, loading, hasRole, isAdmin, isDeveloper, justLoggedIn, resetJustLoggedIn, isInitializing }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === null) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    const { user, ...rest } = context;

    const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';

    // A helper function to correctly join URL parts.
    // It handles leading/trailing slashes to prevent duplication.
    const constructUrl = (base: string, path: string | null): string | null => {
        if (!path) return null;
        // Ensure base url does not end with a slash
        const cleanBase = base.endsWith('/') ? base.slice(0, -1) : base;
        // Ensure path starts with a slash
        const cleanPath = path.startsWith('/') ? path : `/${path}`;
        return `${cleanBase}${cleanPath}`;
    };

    const enhancedUser = useMemo(() => {
        if (!user) return null;
        return {
            ...user,
            fullAvatarUrl: constructUrl(apiUrl, user.avatarUrl),
            fullBannerUrl: constructUrl(apiUrl, user.bannerUrl),
        };
    }, [user, apiUrl]);

    return { ...rest, user: enhancedUser };
}; 