'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getPublicUserProfile } from '@/services/api';
import { ProfileDTO } from '@/types/ProfileDTO';
import { Skeleton } from '@/components/ui/skeleton';
import ProfileOverview from '../components/ProfileOverview';
import UserRecentActivity from '../components/UserRecentActivity';
import PageWrapper from '@/components/PageWrapper';
import { format } from 'date-fns';
import { Calendar, MessageSquare, Edit, Settings, Shield, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import AccountSettings from '../components/AccountSettings';
import SecuritySettings from '../components/SecuritySettings';
import DangerZone from '../components/DangerZone';

const constructUrl = (base: string, path: string | null): string | null => {
    if (!path) return null;
    const cleanBase = base.endsWith('/') ? base.slice(0, -1) : base;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${cleanBase}${cleanPath}`;
};

const UserProfilePage = () => {
    const params = useParams();
    const username = params.username as string;
    const [profile, setProfile] = useState<ProfileDTO | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showSettings, setShowSettings] = useState(false);
    const [backgroundImage, setBackgroundImage] = useState<string>('');
    const [currentBannerUrl, setCurrentBannerUrl] = useState<string>('');
    const [currentBannerVersion, setCurrentBannerVersion] = useState<number>(0);

    // 获取当前登录用户信息
    const { user: currentUser } = useAuth();

    // 判断是否是用户自己的主页
    const isOwnProfile = currentUser && profile && currentUser.username === profile.username;

    useEffect(() => {
        if (username) {
            const fetchProfile = async () => {
                try {
                    setLoading(true);
                    const data = await getPublicUserProfile(username);
                    setProfile(data);
                } catch (err: any) {
                    setError('无法加载此用户的个人资料。用户可能不存在或发生了网络错误。');
                    console.error(err);
                } finally {
                    setLoading(false);
                }
            };
            fetchProfile();
        }
    }, [username]);

    // 更新背景图片和Banner信息
    useEffect(() => {
        if (profile) {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
            const bannerUrl = constructUrl(apiUrl, profile.bannerUrl);
            const bannerImageUrl = bannerUrl
                ? `${bannerUrl}?v=${profile.bannerVersion}`
                : '/images/background/background.webp';
            setBackgroundImage(bannerImageUrl);

            // 初始化Banner状态
            if (bannerUrl) {
                setCurrentBannerUrl(bannerUrl);
                setCurrentBannerVersion(profile.bannerVersion);
            }
        }
    }, [profile]);

    // 监听当前用户的banner变化（用于实时更新）
    useEffect(() => {
        if (isOwnProfile && currentUser) {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
            const bannerUrl = constructUrl(apiUrl, currentUser.bannerUrl);
            const bannerImageUrl = bannerUrl
                ? `${bannerUrl}?v=${currentUser.bannerVersion}`
                : '/images/background/background.webp';
            setBackgroundImage(bannerImageUrl);

            // 更新Banner状态
            if (bannerUrl) {
                setCurrentBannerUrl(bannerUrl);
                setCurrentBannerVersion(currentUser.bannerVersion || 0);
            }
        }
    }, [currentUser?.bannerUrl, currentUser?.bannerVersion, isOwnProfile]);

    // 键盘快捷键支持
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && showSettings) {
                setShowSettings(false);
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [showSettings]);

    if (loading) {
        return <UserProfileSkeleton />;
    }

    if (error) {
        return <div className="text-center text-red-500 py-20">{error}</div>;
    }

    if (!profile) {
        return <div className="text-center py-20">未找到用户。</div>;
    }

    return (
        <main className="min-h-screen relative">
            {/* The main page background should also use the user's banner */}
            <div
                className="fixed inset-0 bg-cover bg-bottom opacity-50 z-[-1] transition-all duration-500"
                style={{ backgroundImage: `url(${backgroundImage})` }}
            ></div>
            <div className="fixed inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60 z-[-1]"></div>
            <PageWrapper>
                <div className="container mx-auto px-4 pt-24 space-y-12">
                    {/* 设置按钮或取消/完成按钮 - 右上角固定位置 */}
                    {isOwnProfile && (
                        <div className="fixed top-20 right-4 z-50">
                            <AnimatePresence mode="wait">
                                {!showSettings ? (
                                    <motion.div
                                        key="settings-button"
                                        initial={{ opacity: 0, x: 20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        exit={{ opacity: 0, x: 20 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <Button
                                            onClick={() => setShowSettings(true)}
                                            variant="outline"
                                            className="flex items-center gap-2 bg-black/50 backdrop-blur-md border-white/20 text-white hover:bg-black/60 transition-all duration-300"
                                        >
                                            <Settings className="w-4 h-4" />
                                            设置
                                        </Button>
                                    </motion.div>
                                ) : (
                                    <motion.div
                                        key="action-buttons"
                                        initial={{ opacity: 0, x: 20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        exit={{ opacity: 0, x: 20 }}
                                        transition={{ duration: 0.3 }}
                                        className="flex gap-2"
                                    >
                                        <Button
                                            onClick={() => setShowSettings(false)}
                                            variant="outline"
                                            className="bg-black/50 backdrop-blur-md border-white/20 text-white hover:bg-black/60 transition-all duration-300"
                                        >
                                            取消
                                        </Button>
                                        <Button
                                            onClick={() => setShowSettings(false)}
                                            variant="outline"
                                            className="bg-black/50 backdrop-blur-md border-white/20 text-white hover:bg-black/60 transition-all duration-300"
                                        >
                                            完成
                                        </Button>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    )}

                    <ProfileOverview
                        profile={profile}
                        isPublic={!isOwnProfile}
                        onBannerUpdate={(newBannerUrl: string, bannerUrl?: string, bannerVersion?: number) => {
                            setBackgroundImage(newBannerUrl);
                            if (bannerUrl && bannerVersion !== undefined) {
                                setCurrentBannerUrl(bannerUrl);
                                setCurrentBannerVersion(bannerVersion);
                            }
                        }}
                        currentBannerUrl={currentBannerUrl}
                        currentBannerVersion={currentBannerVersion}
                    />

                    {/* 设置区域和个人动态的切换 */}
                    <AnimatePresence mode="wait">
                        {isOwnProfile && showSettings ? (
                            <motion.section
                                key="settings"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.4 }}
                                className="max-w-4xl mx-auto relative"
                            >
                                {/* 设置页面背景覆盖层 */}
                                <div className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-3xl -z-10" />
                            {/* 设置页面标题 */}
                            <div className="mb-6">
                                <h2 className="text-2xl font-bold text-white">个人设置</h2>
                                <p className="text-zinc-400 text-sm mt-1">管理您的账户设置和偏好</p>
                            </div>

                            <Accordion type="multiple" className="w-full space-y-8">
                                <AccordionItem value="item-1" className="bg-black/30 backdrop-blur-md border border-white/10 rounded-2xl">
                                    <AccordionTrigger className="hover:no-underline px-6">
                                        <div className="flex items-center text-lg font-semibold text-white">
                                            <Settings className="mr-3 h-5 w-5 text-primary" />
                                            账户设置
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="pt-4 px-6">
                                        <AccountSettings />
                                    </AccordionContent>
                                </AccordionItem>

                                <AccordionItem value="item-2" className="bg-black/30 backdrop-blur-md border border-white/10 rounded-2xl">
                                    <AccordionTrigger className="hover:no-underline px-6">
                                        <div className="flex items-center text-lg font-semibold text-white">
                                            <Shield className="mr-3 h-5 w-5 text-primary" />
                                            安全设置
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="pt-4 px-6">
                                        <SecuritySettings />
                                    </AccordionContent>
                                </AccordionItem>

                                <AccordionItem value="item-3" className="bg-black/30 backdrop-blur-md border border-red-500/20 rounded-2xl">
                                    <AccordionTrigger className="hover:no-underline px-6">
                                        <div className="flex items-center text-lg font-semibold text-red-400">
                                            <AlertTriangle className="mr-3 h-5 w-5 text-red-400" />
                                            危险区域
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="pt-4 px-6">
                                        <DangerZone />
                                    </AccordionContent>
                                </AccordionItem>
                            </Accordion>
                            </motion.section>
                        ) : (
                            /* 个人动态 - 只有在非设置模式下才显示 */
                            <motion.div
                                key="activity"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.4 }}
                                className="mt-8"
                            >
                                <UserRecentActivity username={profile.username} />
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </PageWrapper>
        </main>
    );
};

const UserProfileSkeleton = () => (
    <div className="w-full">
        <Skeleton className="h-48 md:h-64 w-full" />
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col sm:flex-row items-center sm:items-end -mt-16 sm:-mt-20">
                <Skeleton className="w-32 h-32 sm:w-40 sm:h-40 rounded-full border-4 border-gray-800" />
                <div className="mt-4 sm:ml-6">
                    <Skeleton className="h-9 w-48" />
                    <Skeleton className="h-5 w-64 mt-2" />
                </div>
            </div>
            <div className="mt-8">
                <Skeleton className="h-32 w-full rounded-lg" />
            </div>
        </div>
    </div>
);


export default UserProfilePage; 