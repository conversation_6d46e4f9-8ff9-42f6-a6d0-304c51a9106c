'use client';

import { useEffect, useState } from 'react';
import { getAllWithdrawalRequests, reviewWithdrawalRequest } from '@/services/api';
import { WithdrawalRequest, WithdrawalStatus } from '@/types/WithdrawalRequest';
import { But<PERSON> } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {Badge} from '@/components/ui/badge';
import {Textarea} from '@/components/ui/textarea';
import {toast} from 'sonner';
import {Loader2} from 'lucide-react';

// Helper function to safely format dates
const formatDate = (dateString: string | Date | undefined | null) => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '无效日期';
    }
};

const statusTextMap: { [key in WithdrawalStatus]: string } = {
    [WithdrawalStatus.PENDING]: '审核中',
    [WithdrawalStatus.APPROVED]: '已批准',
    [WithdrawalStatus.REJECTED]: '已拒绝',
};

export const WithdrawalManagementDashboard = () => {
    const [requests, setRequests] = useState<WithdrawalRequest[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedRequest, setSelectedRequest] = useState<WithdrawalRequest | null>(null);
    const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
    const [notes, setNotes] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        fetchRequests();
    }, []);

    const fetchRequests = async () => {
        setLoading(true);
        try {
            const data = await getAllWithdrawalRequests();
            setRequests(data);
        } catch (error) {
            toast.error('无法加载提现申请列表。');
        } finally {
            setLoading(false);
        }
    };

    const handleOpenReviewModal = (request: WithdrawalRequest) => {
        setSelectedRequest(request);
        setNotes(request.notes || '');
        setIsReviewModalOpen(true);
    };

    const handleReview = async (status: WithdrawalStatus.APPROVED | WithdrawalStatus.REJECTED) => {
        if (!selectedRequest) return;

        if (status === WithdrawalStatus.REJECTED && !notes.trim()) {
            toast.error('拒绝申请必须填写理由。');
            return;
        }

        setIsSubmitting(true);
        try {
            const reviewData = {
                status,
                notes: status === WithdrawalStatus.REJECTED ? notes : undefined,
            };
            
            const updatedRequest = await reviewWithdrawalRequest(selectedRequest.id, reviewData);

            setRequests(requests.map(r => r.id === updatedRequest.id ? {...r, ...updatedRequest} : r));
            toast.success(`申请已${status === WithdrawalStatus.APPROVED ? '批准' : '拒绝'}。`);
            setIsReviewModalOpen(false);
        } catch (error) {
            toast.error('处理申请时出错。');
        } finally {
            setIsSubmitting(false);
        }
    };

    const formatCurrency = (value: number) => `¥${value.toFixed(2)}`;

    return (
        <div className="p-1">
            <h2 className="text-2xl font-bold mb-4">提现申请管理</h2>
            <div className="rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>申请人</TableHead>
                            <TableHead>金额</TableHead>
                            <TableHead>渠道</TableHead>
                            <TableHead>收款账号</TableHead>
                            <TableHead>申请时间</TableHead>
                            <TableHead>状态</TableHead>
                            <TableHead>操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center">
                                    <Loader2 className="mx-auto h-8 w-8 animate-spin text-muted-foreground" />
                                </TableCell>
                            </TableRow>
                        ) : requests.map((request) => (
                            <TableRow key={request.id}>
                                <TableCell>{request.username}</TableCell>
                                <TableCell>{formatCurrency(request.amount)}</TableCell>
                                <TableCell>{request.channel}</TableCell>
                                <TableCell>{request.accountInfo}</TableCell>
                                <TableCell>{formatDate(request.createdAt)}</TableCell>
                                <TableCell>
                                    <Badge variant={
                                        request.status === WithdrawalStatus.APPROVED ? 'success'
                                        : request.status === WithdrawalStatus.REJECTED ? 'destructive'
                                        : 'secondary'
                                    }>{statusTextMap[request.status]}</Badge>
                                </TableCell>
                                <TableCell>
                                    {request.status === WithdrawalStatus.PENDING && (
                                        <Button size="sm" onClick={() => handleOpenReviewModal(request)}>审核</Button>
                                    )}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {selectedRequest && (
                <Dialog open={isReviewModalOpen} onOpenChange={setIsReviewModalOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>审核提现申请 #{selectedRequest.id}</DialogTitle>
                            <DialogDescription>
                                申请人: {selectedRequest.username} | 金额: {formatCurrency(selectedRequest.amount)}
                            </DialogDescription>
                        </DialogHeader>
                        <div className="py-4 space-y-4">
                           <p>请仔细核对信息后进行操作。此操作不可逆。</p>
                            <div>
                                <label htmlFor="notes" className="block text-sm font-medium mb-1">
                                    备注/拒绝理由 (如果拒绝)
                                </label>
                                <Textarea
                                    id="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="如果拒绝申请，请在此说明原因..."
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsReviewModalOpen(false)} disabled={isSubmitting}>取消</Button>
                            <Button 
                                variant="destructive" 
                                onClick={() => handleReview(WithdrawalStatus.REJECTED)}
                                disabled={isSubmitting}
                            >
                                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                                拒绝
                            </Button>
                            <Button 
                                variant="default"
                                className="bg-green-600 hover:bg-green-700 text-white"
                                onClick={() => handleReview(WithdrawalStatus.APPROVED)}
                                disabled={isSubmitting}
                            >
                                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                                批准
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            )}
        </div>
    );
}; 