package com.kitolus.community.service;

import com.kitolus.community.dto.CreateProductRequestDTO;
import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.dto.UpdateProductRequestDTO;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.security.access.AccessDeniedException;

public interface ProductService {
    List<ProductDTO> getApprovedProductsWithAuthor();

    Product createProduct(CreateProductRequestDTO requestDTO, User currentUser);

    Product createProduct(String name, BigDecimal price, String description, String downloadUrl, MultipartFile imageFile, User author) throws IOException;

    List<Product> findPurchasedByUserId(Long userId);

    List<ProductDTO> findByUserId(Long userId);

    Product updateProduct(Long id, UpdateProductRequestDTO requestDTO, User currentUser);

    void deleteRejectedProduct(Long productId, User currentUser);

    void deleteProductAsDeveloper(Long productId, User currentUser);

    Product getProductById(Long id);

    List<ProductDTO> getMyProducts(Long userId);

    List<Product> getPurchasedProducts(Long userId);
} 