'use client';

import { useState, useEffect } from 'react';
import apiService from '@/services/api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ShieldCheck, UserX, UserCheck } from 'lucide-react';
import AvatarDisplay from '@/components/AvatarDisplay';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

type DeveloperApplication = {
    id: number;
    userId: number;
    username: string;
    userAvatarUrl: string;
    userSerialNumber: string;
    status: 'PENDING' | 'APPROVED' | 'REJECTED';
    message: string;
    createdAt: string;
};

const DeveloperApplicationDashboard = () => {
    const [applications, setApplications] = useState<DeveloperApplication[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [rejectionTarget, setRejectionTarget] = useState<DeveloperApplication | null>(null);
    const [rejectionReason, setRejectionReason] = useState('');

    const fetchApplications = async () => {
        setIsLoading(true);
        try {
            const response = await apiService.get('/api/admin/developer-applications?status=PENDING');
            setApplications(response.data);
        } catch (err) {
            setError('无法加载开发者申请列表。');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchApplications();
    }, []);

    const handleApprove = async (applicationId: number) => {
        try {
            await apiService.post(`/api/admin/developer-applications/${applicationId}/approve`);
            setApplications(prev => prev.filter(app => app.id !== applicationId));
        } catch (err) {
            console.error(`Failed to approve application`, err);
            alert(`操作失败，请稍后重试。`);
        }
    };

    const handleConfirmReject = async () => {
        if (!rejectionTarget) return;
        try {
            await apiService.post(`/api/admin/developer-applications/${rejectionTarget.id}/reject`, { reason: rejectionReason });
            setApplications(prev => prev.filter(app => app.id !== rejectionTarget.id));
        } catch (err) {
            console.error(`Failed to reject application`, err);
            alert(`操作失败，请稍后重试。`);
        } finally {
            setRejectionTarget(null);
            setRejectionReason('');
        }
    };

    if (isLoading) return <div className="p-4">正在加载申请...</div>;
    if (error) return <div className="p-4 text-red-500">{error}</div>;

    return (
        <>
            <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-lg">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <ShieldCheck />
                        开发者申请审核
                    </CardTitle>
                    <CardDescription>以下是等待您审核的开发者资格申请。</CardDescription>
                </CardHeader>
                <CardContent>
                    {applications.length === 0 ? (
                        <p>目前没有待处理的申请。</p>
                    ) : (
                        <div className="space-y-6">
                            {applications.map(app => (
                                <div key={app.id} className="p-4 border rounded-lg bg-secondary/50 shadow-md">
                                    <div className="flex items-start gap-4">
                                        <AvatarDisplay
                                            avatarUrl={app.userAvatarUrl}
                                            serialNumber={app.userSerialNumber}
                                            size={40}
                                        />
                                        <div className="flex-1">
                                            <div className="flex justify-between items-center">
                                                <span className="font-bold">{app.username}</span>
                                                <span className="text-xs text-muted-foreground">
                                                    {formatDistanceToNow(new Date(app.createdAt), { addSuffix: true, locale: zhCN })}
                                                </span>
                                            </div>
                                            <p className="mt-2 text-sm p-3 bg-background/70 rounded-md">{app.message || "用户未填写申请理由。"}</p>
                                        </div>
                                    </div>
                                    <div className="flex justify-end gap-2 mt-4">
                                        <Button size="sm" variant="outline" onClick={() => setRejectionTarget(app)}>
                                            <UserX className="mr-2 h-4 w-4" />
                                            拒绝
                                        </Button>
                                        <Button size="sm" onClick={() => handleApprove(app.id)}>
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            批准
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {rejectionTarget && (
                <AlertDialog open={!!rejectionTarget} onOpenChange={(open) => !open && setRejectionTarget(null)}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>确认拒绝申请</AlertDialogTitle>
                            <AlertDialogDescription>
                                您确定要拒绝 {rejectionTarget.username} 的开发者申请吗？请填写拒绝理由，该理由将通知给用户。
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <textarea
                            className="w-full p-2 border rounded bg-background"
                            placeholder="请填写拒绝理由..."
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                        />
                        <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction onClick={handleConfirmReject} disabled={!rejectionReason.trim()}>确认拒绝</AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            )}
        </>
    );
};

export default DeveloperApplicationDashboard; 