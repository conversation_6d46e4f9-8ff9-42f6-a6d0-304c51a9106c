package com.kitolus.community.dto;

import com.kitolus.community.entity.WithdrawalChannel;
import com.kitolus.community.entity.WithdrawalStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
public class WithdrawalRequestDetailsDTO {
    private Long id;
    private Long userId;
    private String username;
    private BigDecimal amount;
    private WithdrawalChannel channel;
    private String accountInfo;
    private WithdrawalStatus status;
    private String notes;
    private Timestamp createdAt;
    private Timestamp reviewedAt;
    private Long reviewerId;
    private String reviewerName;
} 