'use client';

// 确保framer-motion在所有组件加载前可用
import '@/lib/framer-motion-polyfill';

import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from '@/contexts/AuthContext';
import { QueryProvider } from '@/providers/QueryProvider';
import { GlobalWebSocketProvider } from '@/contexts/GlobalWebSocketContext';
import ErrorBoundary from '@/components/ErrorBoundary';

export function AppProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ErrorBoundary>
      <QueryProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <GlobalWebSocketProvider>
              {children}
            </GlobalWebSocketProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryProvider>
    </ErrorBoundary>
  );
}
