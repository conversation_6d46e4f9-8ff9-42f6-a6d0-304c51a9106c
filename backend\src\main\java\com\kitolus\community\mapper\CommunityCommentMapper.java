package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.CommunityComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

import java.util.List;

@Mapper
public interface CommunityCommentMapper extends BaseMapper<CommunityComment> {

    @Select("SELECT " +
            "c.id, c.post_id, c.author_id, c.parent_id, c.content, c.created_at, " +
            "u.id AS `author.id`, u.username AS `author.username`, u.avatar_url AS `author.avatar_url`, u.serial_number AS `author.serialNumber`, " +
            "parent_user.username AS parentAuthorUsername " +
            "FROM community_comment c " +
            "LEFT JOIN `user` u ON c.author_id = u.id " +
            "LEFT JOIN community_comment parent_comment ON c.parent_id = parent_comment.id " +
            "LEFT JOIN `user` parent_user ON parent_comment.author_id = parent_user.id " +
            "WHERE c.post_id = #{postId} " +
            "ORDER BY c.created_at ASC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "author.id", column = "author.id"),
        @Result(property = "author.username", column = "author.username"),
        @Result(property = "author.avatarUrl", column = "author.avatar_url"),
        @Result(property = "author.serialNumber", column = "author.serialNumber"),
        @Result(property = "parentAuthorUsername", column = "parentAuthorUsername")
    })
    List<CommunityComment> selectCommentsByPostId(@Param("postId") Long postId);

    @Select("SELECT " +
            "c.id, c.post_id, c.author_id, c.parent_id, c.content, c.created_at, " +
            "u.id AS `author.id`, u.username AS `author.username`, u.avatar_url AS `author.avatar_url`, u.serial_number AS `author.serialNumber`, " +
            "parent_user.username AS parentAuthorUsername " +
            "FROM community_comment c " +
            "LEFT JOIN `user` u ON c.author_id = u.id " +
            "LEFT JOIN community_comment parent_comment ON c.parent_id = parent_comment.id " +
            "LEFT JOIN `user` parent_user ON parent_comment.author_id = parent_user.id " +
            "WHERE c.id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "author.id", column = "author.id"),
        @Result(property = "author.username", column = "author.username"),
        @Result(property = "author.avatarUrl", column = "author.avatar_url"),
        @Result(property = "author.serialNumber", column = "author.serialNumber"),
        @Result(property = "parentAuthorUsername", column = "parentAuthorUsername")
    })
    CommunityComment selectCommentById(@Param("id") Long id);
} 