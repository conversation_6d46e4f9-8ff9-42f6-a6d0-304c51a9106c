@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.highlight-comment {
  animation: highlight-animation 2s ease-out;
}

@keyframes highlight-animation {
  0% {
    background-color: hsl(var(--primary) / 0.2);
    border-radius: 0.5rem;
    box-shadow: 0 0 15px hsl(var(--primary) / 0.5);
  }
  100% {
    background-color: transparent;
    box-shadow: 0 0 0px transparent;
  }
}

/* Enhanced ProductDetailModal Animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out 0.2s forwards;
  opacity: 0;
}

.animate-fade-in-right {
  animation: fade-in-right 1s ease-out 0.4s forwards;
  opacity: 0;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}