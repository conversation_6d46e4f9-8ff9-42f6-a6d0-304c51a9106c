package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.CommunityPost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Many;
import org.apache.ibatis.annotations.ResultMap;
import java.util.List;

@Mapper
public interface CommunityPostMapper extends BaseMapper<CommunityPost> {

    /**
     * Fetches all posts and joins them with author and topic information.
     * It also checks if the current user has liked each post.
     */
    @Select("SELECT " +
            "p.id, p.title, p.content, p.author_id, p.topic_id, p.created_at, " +
            "p.is_pinned, p.pinned_at, p.pinned_by, " +
            "u.id AS `author.id`, u.username AS `author.username`, u.avatar_url AS `author.avatar_url`, u.serial_number AS `author.serialNumber`, " +
            "t.name AS `topic.name`, t.tier as `topic.tier`, t.era as `topic.era`, " +
            "CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END AS `likedByCurrentUser`, " +
            "(SELECT COUNT(*) FROM community_post_likes WHERE post_id = p.id) AS `_count.likes`, " +
            "(SELECT COUNT(*) FROM community_comment WHERE post_id = p.id) AS `_count.comments` " +
            "FROM community_post p " +
            "LEFT JOIN `user` u ON p.author_id = u.id " +
            "LEFT JOIN community_topic t ON p.topic_id = t.id " +
            "LEFT JOIN community_post_likes l ON p.id = l.post_id AND l.user_id = #{currentUserId} " +
            "ORDER BY p.created_at DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "author.id", column = "author.id"),
        @Result(property = "author.username", column = "author.username"),
        @Result(property = "author.avatarUrl", column = "author.avatar_url"),
        @Result(property = "author.serialNumber", column = "author.serialNumber"),
        @Result(property = "topic.name", column = "topic.name"),
        @Result(property = "topic.tier", column = "topic.tier"),
        @Result(property = "topic.era", column = "topic.era"),
        @Result(property = "_count.likes", column = "_count.likes"),
        @Result(property = "_count.comments", column = "_count.comments")
    })
    List<CommunityPost> selectAllPostsWithDetails(@Param("currentUserId") Long currentUserId);

    /**
     * Fetches a single post by its ID and joins it with author and topic information.
     * It also checks if the current user has liked the post.
     * It uses a nested select to fetch all comments for the post.
     */
    @Select("SELECT " +
            "p.id, p.title, p.content, p.author_id, p.topic_id, p.created_at, p.updated_at, " +
            "p.is_pinned, p.pinned_at, p.pinned_by, " +
            "u.id AS `author.id`, u.username AS `author.username`, u.avatar_url AS `author.avatar_url`, u.serial_number AS `author.serialNumber`, u.created_at AS `author.createdAt`, " +
            "CASE WHEN u.role = 'KitolusAdmin' THEN 1 ELSE 0 END AS `author.isAdmin`, " +
            "CASE WHEN u.role = 'DEVELOPER' THEN 1 ELSE 0 END AS `author.isDeveloper`, " +
            "t.id AS `topic.id`, t.name AS `topic.name`, t.tier as `topic.tier`, t.era as `topic.era`, " +
            "CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END AS `likedByCurrentUser`, " +
            "(SELECT COUNT(*) FROM community_post_likes WHERE post_id = p.id) AS `_count.likes`, " +
            "(SELECT COUNT(*) FROM community_comment WHERE post_id = p.id) AS `_count.comments` " +
            "FROM community_post p " +
            "LEFT JOIN `user` u ON p.author_id = u.id " +
            "LEFT JOIN community_topic t ON p.topic_id = t.id " +
            "LEFT JOIN community_post_likes l ON p.id = l.post_id AND l.user_id = #{currentUserId} " +
            "WHERE p.id = #{postId}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "author.id", column = "author.id"),
        @Result(property = "author.username", column = "author.username"),
        @Result(property = "author.avatarUrl", column = "author.avatar_url"),
        @Result(property = "author.serialNumber", column = "author.serialNumber"),
        @Result(property = "author.createdAt", column = "author.createdAt"),
        @Result(property = "author.isAdmin", column = "author.isAdmin"),
        @Result(property = "author.isDeveloper", column = "author.isDeveloper"),
        @Result(property = "topic.id", column = "topic.id"),
        @Result(property = "topic.name", column = "topic.name"),
        @Result(property = "topic.tier", column = "topic.tier"),
        @Result(property = "topic.era", column = "topic.era"),
        @Result(property = "_count.likes", column = "_count.likes"),
        @Result(property = "_count.comments", column = "_count.comments"),
        @Result(property = "comments", column = "id",
            many = @Many(select = "com.kitolus.community.mapper.CommunityCommentMapper.selectCommentsByPostId"))
    })
    CommunityPost selectPostWithDetailsById(@Param("postId") Long postId, @Param("currentUserId") Long currentUserId);
}
