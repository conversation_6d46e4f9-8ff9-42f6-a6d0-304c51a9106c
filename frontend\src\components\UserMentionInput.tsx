'use client';

import React, { useState, useRef, useEffect, useCallback, forwardRef } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { searchUsers } from '@/services/api';
import { AtSign } from 'lucide-react';

interface UserMentionInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    maxLength?: number;
    rows?: number;
    disabled?: boolean;
}



interface UserSuggestion {
    username: string;
}

const UserMentionInput = forwardRef<HTMLTextAreaElement, UserMentionInputProps>(({
    value,
    onChange,
    placeholder = "添加评论...",
    className = "",
    maxLength = 500,
    rows = 4,
    disabled = false
}, ref) => {
    const [suggestions, setSuggestions] = useState<UserSuggestion[]>([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [mentionStart, setMentionStart] = useState(-1);
    const [mentionQuery, setMentionQuery] = useState('');
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const suggestionsRef = useRef<HTMLDivElement>(null);

    // 合并外部ref和内部ref
    const mergedRef = useCallback((node: HTMLTextAreaElement | null) => {
        // 设置内部ref
        if (textareaRef.current !== node) {
            (textareaRef as React.MutableRefObject<HTMLTextAreaElement | null>).current = node;
        }

        // 设置外部ref
        if (typeof ref === 'function') {
            ref(node);
        } else if (ref) {
            (ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = node;
        }
    }, [ref]);



    // 搜索用户
    const searchUsersDebounced = async (query: string) => {
        if (query.length < 1) {
            setSuggestions([]);
            return;
        }
        
        try {
            const users = await searchUsers(query);
            setSuggestions(users);
        } catch (error) {
            console.error('搜索用户失败:', error);
            setSuggestions([]);
        }
    };

    // 处理文本变化
    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        const cursorPos = e.target.selectionStart;
        
        onChange(newValue);
        
        // 检查是否在输入@
        const textBeforeCursor = newValue.substring(0, cursorPos);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');
        
        if (lastAtIndex !== -1) {
            const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
            
            // 检查@后面是否只有用户名字符（字母、数字、下划线、连字符）
            if (/^[a-zA-Z0-9_-]*$/.test(textAfterAt)) {
                setMentionStart(lastAtIndex);
                setMentionQuery(textAfterAt);
                setShowSuggestions(true);
                setSelectedIndex(0);
                searchUsersDebounced(textAfterAt);
            } else {
                setShowSuggestions(false);
            }
        } else {
            setShowSuggestions(false);
        }
    };

    // 选择用户
    const selectUser = (username: string) => {
        if (mentionStart === -1) return;
        
        const beforeMention = value.substring(0, mentionStart);
        const afterMention = value.substring(mentionStart + 1 + mentionQuery.length);
        const newValue = beforeMention + '@' + username + ' ' + afterMention;
        
        onChange(newValue);
        setShowSuggestions(false);
        
        // 设置光标位置
        setTimeout(() => {
            if (textareaRef.current) {
                const newCursorPos = mentionStart + username.length + 2; // +2 for @ and space
                textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
                textareaRef.current.focus();
            }
        }, 0);
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (!showSuggestions || suggestions.length === 0) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev => (prev + 1) % suggestions.length);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
                break;
            case 'Enter':
            case 'Tab':
                e.preventDefault();
                selectUser(suggestions[selectedIndex].username);
                break;
            case 'Escape':
                setShowSuggestions(false);
                break;
        }
    };

    // 点击外部关闭建议
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
                textareaRef.current && !textareaRef.current.contains(event.target as Node)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <div className="relative">
            <Textarea
                ref={mergedRef}
                value={value}
                onChange={handleTextChange}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                className={`${className} resize-none`}
                maxLength={maxLength}
                rows={rows}
                disabled={disabled}
            />
            
            {/* 字数统计 */}
            <div className="absolute bottom-2 right-2 text-xs text-zinc-500">
                {value.length} / {maxLength}
            </div>
            
            {/* 用户建议列表 */}
            {showSuggestions && suggestions.length > 0 && (
                <div
                    ref={suggestionsRef}
                    className="absolute z-50 w-full mt-1 bg-zinc-800 border border-zinc-700 rounded-md shadow-lg max-h-40 overflow-y-auto"
                >
                    {suggestions.map((user, index) => (
                        <div
                            key={user.username}
                            className={`px-3 py-2 cursor-pointer flex items-center gap-2 ${
                                index === selectedIndex ? 'bg-zinc-700' : 'hover:bg-zinc-700'
                            }`}
                            onClick={() => selectUser(user.username)}
                        >
                            <AtSign size={14} className="text-zinc-400" />
                            <span className="text-white">{user.username}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
});

UserMentionInput.displayName = 'UserMentionInput';

export default UserMentionInput;
