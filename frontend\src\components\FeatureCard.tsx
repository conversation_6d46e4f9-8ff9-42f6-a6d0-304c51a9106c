import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, children }) => {
  return (
    <Card className="bg-background/50 border-border text-center hover:border-primary/50 transition-all duration-300">
      <CardHeader>
        <div className="mx-auto bg-secondary p-3 rounded-full w-fit">
          {icon}
        </div>
        <CardTitle className="text-foreground mt-4">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          {children}
        </p>
      </CardContent>
    </Card>
  );
};

export default FeatureCard; 