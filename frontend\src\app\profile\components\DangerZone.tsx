'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import apiService from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const DangerZone = () => {
    const { logout } = useAuth();
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDeleteAccount = async () => {
        setIsDeleting(true);
        try {
            await apiService.delete('/api/user/me');
            toast.success("您的账户已成功删除。");
            // Perform logout and redirect
            logout(); 
        } catch (err: any) {
            toast.error(err.response?.data?.message || '账户删除失败，请稍后再试。');
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <div className="p-4 border border-red-500/30 rounded-lg bg-red-900/10">
            <h3 className="text-lg font-semibold text-red-400">危险区域</h3>
            <p className="text-sm text-gray-400 mt-1 mb-4">
                请注意，删除您的账户是一个不可逆转的操作。所有与您账户相关的数据，包括个人资料、上传内容和购买历史，都将被永久移除。
            </p>
            <AlertDialog>
                <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={isDeleting}>
                        {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                        删除我的账户
                    </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>您确定要删除您的账户吗？</AlertDialogTitle>
                        <AlertDialogDescription>
                            此操作无法撤销。您的账户和所有相关数据将被永久删除。
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteAccount} className="bg-red-600 hover:bg-red-700">
                            确认删除
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default DangerZone; 