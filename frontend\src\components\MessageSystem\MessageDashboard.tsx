'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Users, 
  Bell, 
  Mail,
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useMessageStore, useUnreadCount, useConnectionStatus } from '@/stores/messageStore';
import { useMessageStats } from '@/hooks/useMessageQueries';
import { MessageStats } from '@/types/Message';
import { cn } from '@/lib/utils';

interface MessageDashboardProps {
  className?: string;
}

export const MessageDashboard: React.FC<MessageDashboardProps> = ({ className }) => {
  const { data: messageStats, isLoading } = useMessageStats();
  const connectionStatus = useConnectionStatus();

  // 提供默认值以避免类型错误
  const defaultStats: MessageStats = {
    totalUnread: 0,
    privateUnread: 0,
    systemUnread: 0,
    communityUnread: 0,
    announcementUnread: 0,
  };

  const stats: MessageStats = (messageStats as MessageStats) || defaultStats;

  if (isLoading) {
    return (
      <div className={cn("p-4", className)}>
        <div className="grid grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const dashboardStats = [
    {
      title: '总消息',
      value: stats.totalUnread,
      icon: MessageSquare,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: '私信',
      value: stats.privateUnread,
      icon: Mail,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      change: '+5%',
      changeType: 'positive' as const
    },
    {
      title: '系统通知',
      value: stats.systemUnread,
      icon: Bell,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10',
      change: '-2%',
      changeType: 'negative' as const
    },
    {
      title: '社区互动',
      value: stats.communityUnread,
      icon: Users,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      change: '+8%',
      changeType: 'positive' as const
    }
  ];

  const getConnectionStatusInfo = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: CheckCircle2,
          text: '已连接',
          color: 'text-green-500',
          bgColor: 'bg-green-500/10'
        };
      case 'connecting':
        return {
          icon: Clock,
          text: '连接中',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10'
        };
      case 'disconnected':
      case 'error':
        return {
          icon: AlertCircle,
          text: '已断开',
          color: 'text-red-500',
          bgColor: 'bg-red-500/10'
        };
      default:
        return {
          icon: AlertCircle,
          text: '未知',
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10'
        };
    }
  };

  const connectionInfo = getConnectionStatusInfo();

  return (
    <div className={cn("p-4 space-y-4", className)}>
      {/* 连接状态 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={cn("p-2 rounded-full", connectionInfo.bgColor)}>
                <connectionInfo.icon className={cn("w-4 h-4", connectionInfo.color)} />
              </div>
              <div>
                <p className="font-medium">实时连接</p>
                <p className={cn("text-sm", connectionInfo.color)}>
                  {connectionInfo.text}
                </p>
              </div>
            </div>
            <Badge 
              variant={connectionStatus === 'connected' ? 'default' : 'secondary'}
              className={connectionStatus === 'connected' ? 'bg-green-500' : ''}
            >
              {connectionInfo.text}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* 消息统计 */}
      <div className="grid grid-cols-2 gap-4">
        {dashboardStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center justify-between">
                  <span>{stat.title}</span>
                  <div className={cn("p-1.5 rounded-full", stat.bgColor)}>
                    <stat.icon className={cn("w-3 h-3", stat.color)} />
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{stat.value}</span>
                  <div className="flex items-center gap-1">
                    <TrendingUp className={cn(
                      "w-3 h-3",
                      stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'
                    )} />
                    <span className={cn(
                      "text-xs font-medium",
                      stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'
                    )}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 活动概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">今日活动</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>消息发送</span>
            <span className="font-medium">24</span>
          </div>
          <Progress value={75} className="h-2" />
          
          <div className="flex items-center justify-between text-sm">
            <span>消息接收</span>
            <span className="font-medium">18</span>
          </div>
          <Progress value={60} className="h-2" />
          
          <div className="flex items-center justify-between text-sm">
            <span>活跃会话</span>
            <span className="font-medium">6</span>
          </div>
          <Progress value={40} className="h-2" />
        </CardContent>
      </Card>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">快速操作</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <button className="w-full text-left p-2 text-sm rounded hover:bg-muted transition-colors">
            📝 标记所有为已读
          </button>
          <button className="w-full text-left p-2 text-sm rounded hover:bg-muted transition-colors">
            🔍 搜索消息
          </button>
          <button className="w-full text-left p-2 text-sm rounded hover:bg-muted transition-colors">
            ⚙️ 消息设置
          </button>
          <button className="w-full text-left p-2 text-sm rounded hover:bg-muted transition-colors">
            📊 详细统计
          </button>
        </CardContent>
      </Card>
    </div>
  );
};
