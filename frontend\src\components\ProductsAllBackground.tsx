'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

const ProductsAllBackground = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const { clientX, clientY } = event;
      const x = (clientX / window.innerWidth - 0.5) * 2;
      const y = (clientY / window.innerHeight - 0.5) * 2;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  const layers = [
    { strength: 0.003, delay: '0s' },
    { strength: 0.008, delay: '3s' },
    { strength: 0.015, delay: '6s' },
    { strength: 0.025, delay: '2s' },
    { strength: 0.04, delay: '4s' },
  ];

  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden">
      {layers.map((layer, i) => (
        <div
          key={i}
          className="absolute inset-0"
          style={{
            transform: `translate(${mousePosition.x * 100 * layer.strength}px, ${mousePosition.y * 100 * layer.strength}px)`,
            transition: 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          }}
        >
          <div
            className={cn(
              'absolute inset-0 w-full h-full bg-cover bg-center animate-subtle-zoom'
            )}
            style={{
              backgroundImage: `url('/images/background/productsAllBackground.webp')`,
              backgroundSize: `${115 + i * 4}%`,
              backgroundPosition: `calc(50% + ${-i * 3}px) calc(50% + ${-i * 3}px)`,
              animationDelay: layer.delay,
            }}
          />
        </div>
      ))}
      
      {/* Enhanced gradient overlay for better text readability */}
      <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_center,transparent_50%,rgba(0,0,0,0.6))]"></div>
      
      {/* Additional subtle vignette effect */}
      <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/40"></div>
    </div>
  );
};

export default ProductsAllBackground;
