'use client';

import { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { updateProduct } from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, UploadCloud, X } from 'lucide-react';
import { Product } from '@/types/Product';
import { compressImageWithPreset } from '@/lib/imageCompression';

// For the edit form, the image is optional.
const productSchema = z.object({
    name: z.string().min(3, '名称至少需要3个字符'),
    price: z.coerce.number().min(0, '价格不能为负数'),
    description: z.string().min(10, '描述至少需要10个字符').max(500, '描述不能超过500个字符'),
    downloadUrl: z.string().url('请输入有效的下载链接'),
    image: z.instanceof(FileList).optional(),
});

type ProductFormValues = z.infer<typeof productSchema>;

interface EditProductFormProps {
    product: Product;
    onProductUpdated: () => void;
    isViewOnly?: boolean;
}

const EditProductForm = ({ product, onProductUpdated, isViewOnly = false }: EditProductFormProps) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { register, handleSubmit, formState: { errors }, watch, setValue, reset } = useForm<ProductFormValues>({
        resolver: zodResolver(productSchema),
        defaultValues: {
            name: product.name,
            price: product.price,
            description: product.description,
            downloadUrl: product.downloadUrl,
        }
    });
    
    // Set initial image preview from existing product
    useEffect(() => {
        if(product.imageUrl) {
            // The imageUrl from the product should now be a direct public path
            setImagePreview(product.imageUrl);
        }
    }, [product.imageUrl]);


    const imageFile = watch('image');

    // Update preview if a new file is selected
    useEffect(() => {
        let fileReader: FileReader;
        let isCancelled = false;

        if (imageFile && imageFile.length > 0) {
            fileReader = new FileReader();
            fileReader.onload = (e) => {
                const result = e.target?.result;
                if (result && !isCancelled) {
                    setImagePreview(result as string);
                }
            };
            fileReader.readAsDataURL(imageFile[0]);
        }

        return () => {
            isCancelled = true;
            if (fileReader && fileReader.readyState === 1) {
                fileReader.abort();
            }
        };
    }, [imageFile]);


    const onSubmit: SubmitHandler<ProductFormValues> = async (data) => {
        setIsSubmitting(true);
        try {
            let imageUrl: string | undefined = undefined;

            // Step 1: If a new image is provided, upload it and get the path.
            if (data.image && data.image.length > 0) {
                // 压缩产品图片
                const originalFile = data.image[0];
                const compressedBlob = await compressImageWithPreset(originalFile, 'product');
                const compressedFile = new File([compressedBlob], `product.jpg`, { type: "image/jpeg" });

                const imageFormData = new FormData();
                imageFormData.append('file', compressedFile);

                const uploadResponse = await fetch('/api/uploadProduct', {
                    method: 'POST',
                    body: imageFormData,
                });

                const uploadResult = await uploadResponse.json();

                if (!uploadResult.success) {
                    throw new Error(uploadResult.message || '图片上传失败');
                }
                imageUrl = uploadResult.path;
            }

            // Step 2: Submit product data to the backend.
            const productData = {
                name: data.name,
                price: data.price,
                description: data.description,
                downloadUrl: data.downloadUrl,
                imageUrl: imageUrl, // Will be undefined if no new image was uploaded
            };

            await updateProduct(product.id, productData);
            toast.success('产品已成功更新！');
            onProductUpdated();
        } catch (error) {
            console.error('Failed to update product', error);
            const errorMessage = error instanceof Error ? error.message : '产品更新失败，请重试。';
            toast.error(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };
    
    return (
        <>
            <CardHeader>
                <CardTitle>{isViewOnly ? '查看产品' : '编辑产品'}</CardTitle>
                <CardDescription>
                    {isViewOnly ? '您正在查看已下架产品的详细信息。' : '更新您的产品信息。只有在产品被驳回或处于待审核状态时才能进行编辑。'}
                </CardDescription>
            </CardHeader>
            <CardContent className="overflow-y-auto max-h-[calc(90vh-200px)] pr-4">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <Label htmlFor="name-edit">产品名称</Label>
                            <Input id="name-edit" {...register('name')} placeholder="例如：便携式扫描仪" className="bg-input/50" disabled={isViewOnly} />
                            {errors.name && <p className="text-red-400 text-sm mt-1">{errors.name.message}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="price-edit">价格 (元)</Label>
                            <Input id="price-edit" type="number" step="0.01" {...register('price')} placeholder="例如：15.00" className="bg-input/50" disabled={isViewOnly} />
                            {errors.price && <p className="text-red-400 text-sm mt-1">{errors.price.message}</p>}
                        </div>
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="description-edit">产品描述</Label>
                        <Textarea id="description-edit" {...register('description')} rows={5} placeholder="详细介绍您的产品，包括功能、用途等..." className="bg-input/50" disabled={isViewOnly} />
                        {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description.message}</p>}
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="downloadUrl-edit">下载链接</Label>
                        <Input id="downloadUrl-edit" {...register('downloadUrl')} placeholder="https://example.com/download/product.zip" className="bg-input/50" disabled={isViewOnly} />
                        {errors.downloadUrl && <p className="text-red-400 text-sm mt-1">{errors.downloadUrl.message}</p>}
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="image-edit">{isViewOnly ? '产品图片' : '替换产品图片 (可选)'}</Label>
                        <div className="flex items-center gap-4">
                            {imagePreview && (
                                <div className="relative w-32 h-32 flex-shrink-0">
                                    <img src={imagePreview} alt="产品图片预览" className="w-full h-full object-cover rounded-md border border-border/50" />
                                </div>
                            )}
                            <div className="w-full">
                                {!isViewOnly && (
                                    <>
                                        <Input id="image-edit" type="file" accept="image/*" {...register('image')} className="bg-input/50 file:text-foreground" />
                                        <p className="text-xs text-muted-foreground mt-2">上传新图片将会替换旧图片。推荐尺寸：800x600px，最大 2MB。</p>
                                    </>
                                )}
                                {errors.image && <p className="text-red-400 text-sm mt-1">{errors.image.message as string}</p>}
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end gap-4 pt-4 border-t border-border/50">
                        {isViewOnly ? (
                            <Button type="button" onClick={onProductUpdated}>
                                关闭
                            </Button>
                        ) : (
                            <>
                                <Button type="button" variant="ghost" onClick={onProductUpdated} disabled={isSubmitting}>
                                    取消
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            {product.status === 'REJECTED' ? '重新提交中...' : '保存中...'}
                                        </>
                                    ) : (
                                        product.status === 'REJECTED' ? '重新提交审核' : '保存更改'
                                    )}
                                </Button>
                            </>
                        )}
                    </div>
                </form>
            </CardContent>
        </>
    );
};

export default EditProductForm; 