'use client';

import { useState } from 'react';
import apiService from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Eye, EyeOff } from 'lucide-react';

const SecuritySettings = () => {
    const [passwordData, setPasswordData] = useState({ currentPassword: '', newPassword: '', confirmPassword: '' });
    const [isPasswordChanging, setIsPasswordChanging] = useState(false);
    const [showPasswords, setShowPasswords] = useState(false);

    const handlePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setPasswordData({ ...passwordData, [e.target.name]: e.target.value });
    };

    const handleChangePassword = async (e: React.FormEvent) => {
        e.preventDefault();
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            toast.error("新密码两次输入不一致。");
            return;
        }
        if (passwordData.newPassword.length < 8) {
            toast.error("新密码长度不能少于8位。");
            return;
        }
        setIsPasswordChanging(true);
        try {
            const response = await apiService.post('/api/user/change-password', {
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword,
            });
            toast.success(response.data.message || '密码更新成功！');
            setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
        } catch (err: any) {
            toast.error(err.response?.data?.message || '密码更新失败，请检查当前密码是否正确。');
        } finally {
            setIsPasswordChanging(false);
        }
    };

    return (
        <div className="text-white pt-2 pb-6">
            <form onSubmit={handleChangePassword} className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="currentPassword">当前密码</Label>
                    <Input id="currentPassword" name="currentPassword" type={showPasswords ? 'text' : 'password'} value={passwordData.currentPassword} onChange={handlePasswordInputChange} required className="bg-white/5 border-white/20 focus:ring-primary focus:border-primary" />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="newPassword">新密码</Label>
                    <Input id="newPassword" name="newPassword" type={showPasswords ? 'text' : 'password'} value={passwordData.newPassword} onChange={handlePasswordInputChange} required className="bg-white/5 border-white/20 focus:ring-primary focus:border-primary" />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="confirmPassword">确认新密码</Label>
                    <Input id="confirmPassword" name="confirmPassword" type={showPasswords ? 'text' : 'password'} value={passwordData.confirmPassword} onChange={handlePasswordInputChange} required className="bg-white/5 border-white/20 focus:ring-primary focus:border-primary" />
                </div>
                <div className="flex items-center justify-between pt-4">
                    <Button 
                        type="submit" 
                        disabled={isPasswordChanging} 
                        className="bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600 font-bold"
                    >
                        {isPasswordChanging && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        确认修改
                    </Button>
                    <div className="flex items-center gap-2">
                        <Button type="button" variant="ghost" onClick={() => setShowPasswords(!showPasswords)} className="text-gray-400 hover:text-white p-2">
                            {showPasswords ? <EyeOff size={18} /> : <Eye size={18} />}
                            <span className="sr-only">{showPasswords ? '隐藏密码' : '显示密码'}</span>
                        </Button>
                        <span className="text-sm text-gray-400 select-none">
                            {showPasswords ? '隐藏密码' : '显示密码'}
                        </span>
                    </div>
                </div>
            </form>
        </div>
    );
};

export default SecuritySettings; 