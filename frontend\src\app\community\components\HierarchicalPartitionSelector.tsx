'use client';

import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Zap, 
  Settings, 
  FolderOpen, 
  BookOpen, 
  Trophy, 
  Handshake,
  Pickaxe,
  Cpu,
  Atom,
  Crown,
  Bot,
  Cog,
  Sparkles,
  FileText,
  Download,
  Wrench,
  GraduationCap,
  Target,
  HelpCircle,
  Factory,
  Award,
  Palette,
  Package,
  Users,
  Server,
  Check
} from 'lucide-react';
import { communitySections, CommunitySection } from '@/lib/community-sections-data';
import { gtnhProgression } from '@/lib/gtnh-progression-data';
import { cn } from '@/lib/utils';

// 图标映射
const iconMap = {
  Z<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>hak<PERSON>,
  <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Atom, Crown, Bot, Cog, Sparkles, FileText,
  Download, Wrench, GraduationCap, Target, HelpCircle,
  Factory, Award, Palette, Package, Users, Server, Check
};

interface HierarchicalPartitionSelectorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const HierarchicalPartitionSelector: React.FC<HierarchicalPartitionSelectorProps> = ({
  value,
  onChange,
  placeholder = "选择分区"
}) => {
  const [activeSection, setActiveSection] = useState<string>('progression');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  
  // 解析当前选中的值
  const parseValue = (val: string) => {
    if (!val) return null;
    
    // 检查是否为传统格式 (era|tier|topic)
    const parts = val.split('|');
    if (parts.length === 3) {
      return {
        type: 'legacy',
        era: parts[0],
        tier: parts[1],
        topic: parts[2]
      };
    }
    
    // 检查是否为新格式 (section|category|subcategory)
    if (parts.length === 3) {
      return {
        type: 'new',
        section: parts[0],
        category: parts[1],
        subcategory: parts[2]
      };
    }
    
    return null;
  };

  const currentValue = parseValue(value);
  
  // 生成传统阶段分区选项
  const legacyOptions = useMemo(() => {
    return gtnhProgression.flatMap(era => 
      era.tiers.flatMap(tier => 
        tier.topics.map(topic => ({
          value: `${era.name}|${tier.name}|${topic.subStage}`,
          label: `${topic.subStage}`,
          description: topic.milestone,
          era: era.name,
          tier: tier.name,
          circuit: topic.circuit
        }))
      )
    );
  }, []);

  const handleLegacySelection = (optionValue: string) => {
    onChange(optionValue);
  };

  const handleNewSelection = (sectionId: string, categoryId: string, subcategoryId: string) => {
    const newValue = `${sectionId}|${categoryId}|${subcategoryId}`;
    onChange(newValue);
  };

  const currentSection = communitySections.find(s => s.id === activeSection);

  return (
    <div className="w-full">
      <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
        {/* Section Tabs */}
        <div className="bg-zinc-900/50 backdrop-blur-sm rounded-xl p-2 mb-6 border border-zinc-800">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 bg-transparent gap-1">
            {communitySections.map((section) => {
              const IconComponent = iconMap[section.icon as keyof typeof iconMap];
              return (
                <TabsTrigger
                  key={section.id}
                  value={section.id}
                  className="flex items-center gap-2 text-xs py-3 px-4 rounded-lg bg-zinc-800/50 data-[state=active]:bg-zinc-700 data-[state=active]:text-zinc-200 border border-zinc-700 data-[state=active]:border-zinc-600 transition-all duration-200"
                >
                  {IconComponent && <IconComponent size={14} />}
                  <span className="hidden sm:inline text-xs font-medium">{section.name}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>

        {/* Section Content */}
        {communitySections.map((section) => (
          <TabsContent key={section.id} value={section.id} className="mt-0">
            <div className="bg-zinc-900/50 backdrop-blur-md rounded-xl border border-zinc-800">
              <div className="p-6 border-b border-zinc-800">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-zinc-800 rounded-lg">
                    {iconMap[section.icon as keyof typeof iconMap] &&
                      React.createElement(iconMap[section.icon as keyof typeof iconMap], { size: 20, className: "text-zinc-400" })
                    }
                  </div>
                  <h3 className="text-lg font-semibold text-zinc-100">{section.name}</h3>
                </div>
                <p className="text-sm text-zinc-400">{section.description}</p>
              </div>
              <div className="p-6">
                {section.id === 'progression' ? (
                  // 特殊处理阶段分区，显示传统的层级结构
                  <div className="space-y-6">
                    {gtnhProgression.map((era) => (
                      <div key={era.name} className="space-y-3">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-zinc-600 rounded-full"></div>
                          <h4 className="font-semibold text-base text-zinc-200">{era.name}</h4>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                          {era.tiers.flatMap(tier =>
                            tier.topics.map(topic => {
                              const optionValue = `${era.name}|${tier.name}|${topic.subStage}`;
                              const isSelected = value === optionValue;

                              return (
                                <button
                                  key={optionValue}
                                  onClick={() => handleLegacySelection(optionValue)}
                                  className={cn(
                                    "text-left p-4 rounded-xl border transition-all duration-200 group",
                                    isSelected
                                      ? "bg-zinc-700 border-zinc-600"
                                      : "bg-zinc-800/50 border-zinc-700 hover:bg-zinc-800 hover:border-zinc-600"
                                  )}
                                >
                                  <div className="flex flex-col gap-2">
                                    <div className="flex items-center justify-between">
                                      <span className={cn(
                                        "font-medium",
                                        isSelected ? "text-zinc-200" : "text-zinc-300 group-hover:text-zinc-200"
                                      )}>
                                        {topic.subStage}
                                      </span>
                                      {isSelected && (
                                        <div className="p-1 bg-zinc-600 rounded-full">
                                          <Check size={12} className="text-zinc-300" />
                                        </div>
                                      )}
                                    </div>
                                    <span className="text-xs text-zinc-500">[{tier.name}] {topic.milestone}</span>
                                    {topic.circuit && (
                                      <Badge className="text-xs bg-zinc-700 text-zinc-300 border-zinc-600 w-fit">
                                        {topic.circuit}
                                      </Badge>
                                    )}
                                  </div>
                                </button>
                              );
                            })
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // 新分区的层级选择
                  <div className="space-y-6">
                    {section.categories.map((category) => (
                      <div key={category.id} className="space-y-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-zinc-800 rounded-lg">
                            {iconMap[category.icon as keyof typeof iconMap] &&
                              React.createElement(iconMap[category.icon as keyof typeof iconMap], { size: 16, className: "text-zinc-400" })
                            }
                          </div>
                          <h4 className="font-semibold text-base text-zinc-200">{category.name}</h4>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          {category.subcategories.map((subcategory) => {
                            const optionValue = `${section.id}|${category.id}|${subcategory.id}`;
                            const isSelected = value === optionValue;

                            return (
                              <button
                                key={subcategory.id}
                                onClick={() => handleNewSelection(section.id, category.id, subcategory.id)}
                                className={cn(
                                  "text-left p-4 rounded-xl border transition-all duration-200 group",
                                  isSelected
                                    ? "bg-zinc-700 border-zinc-600"
                                    : "bg-zinc-800/50 border-zinc-700 hover:bg-zinc-800 hover:border-zinc-600"
                                )}
                              >
                                <div className="flex flex-col gap-2">
                                  <div className="flex items-center justify-between">
                                    <span className={cn(
                                      "font-medium text-sm",
                                      isSelected ? "text-zinc-200" : "text-zinc-300 group-hover:text-zinc-200"
                                    )}>
                                      {subcategory.name}
                                    </span>
                                    {isSelected && (
                                      <div className="p-1 bg-zinc-600 rounded-full">
                                        <Check size={12} className="text-zinc-300" />
                                      </div>
                                    )}
                                  </div>
                                  <span className="text-xs text-zinc-500">{subcategory.description}</span>
                                  {subcategory.tags && subcategory.tags.length > 0 && (
                                    <div className="flex gap-1 mt-2 flex-wrap">
                                      {subcategory.tags.map((tag) => (
                                        <Badge key={tag} className="text-xs bg-zinc-700 text-zinc-300 border-zinc-600">
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
