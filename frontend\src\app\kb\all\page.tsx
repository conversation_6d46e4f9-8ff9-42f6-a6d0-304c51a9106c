import KnowledgeBaseAllClient from './KnowledgeBaseAllClient.tsx';
import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';

function LoadingFallback() {
  return (
    <main className="flex-grow container mx-auto px-4 py-32 md:py-40">
      <div className="max-w-3xl mx-auto text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">知识库列表</h1>
        <p className="text-muted-foreground">浏览或搜索社区分享的所有指南、教程和技术蓝图。</p>
      </div>
      <div className="text-center p-8">
        <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
        <p className="mt-4 text-muted-foreground">正在加载组件...</p>
      </div>
    </main>
  );
}

export default function KnowledgeBaseAllPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <KnowledgeBaseAllClient />
    </Suspense>
  );
}