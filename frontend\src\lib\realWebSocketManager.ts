/**
 * 真实的WebSocket连接管理器
 * 用于生产环境的实时消息推送
 */

import { EventEmitter } from 'events';

interface WebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectInterval: number;
  heartbeatInterval: number;
}

class RealWebSocketManager extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private token: string | null = null;

  constructor(config: Partial<WebSocketConfig> = {}) {
    super();
    this.config = {
      url: process.env.NEXT_PUBLIC_WS_URL || (typeof window !== 'undefined' ?
        `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/messages` :
        'ws://localhost:8080/ws/messages'),
      reconnectAttempts: 5,
      reconnectInterval: 1000,
      heartbeatInterval: 30000,
      ...config,
    };
  }

  // 连接WebSocket
  async connect(token?: string) {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.token = token || this.token;
    this.isConnecting = true;
    this.emit('connecting');

    try {
      // 构建WebSocket URL
      const wsUrl = this.token 
        ? `${this.config.url}?token=${this.token}`
        : this.config.url;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.emit('connected');
        this.startHeartbeat();
        // WebSocket连接成功
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          // 静默处理消息解析错误
        }
      };

      this.ws.onclose = (event) => {
        this.isConnecting = false;
        this.ws = null;
        this.stopHeartbeat();
        // WebSocket连接断开
        this.emit('disconnected', event);
        // WebSocket连接断开

        // 自动重连（除非是正常关闭）
        if (event.code !== 1000) {
          this.handleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        this.isConnecting = false;
        // 静默处理WebSocket错误
      };

    } catch (error) {
      this.isConnecting = false;
      // 静默处理连接初始化失败
    }
  }

  // 断开连接
  disconnect() {
    this.reconnectAttempts = this.config.reconnectAttempts; // 阻止重连
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  // 发送消息
  send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        ...data,
        timestamp: Date.now()
      };
      this.ws.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  // 处理接收到的消息
  private handleMessage(data: any) {
    switch (data.type) {
      case 'NEW_MESSAGE':
        this.emit('NEW_MESSAGE', data.payload);
        break;
      case 'MESSAGE_STATUS_CHANGED':
        this.emit('MESSAGE_STATUS_CHANGED', data.payload);
        break;
      case 'USER_ONLINE_STATUS':
        this.emit('USER_ONLINE_STATUS', data.payload);
        break;
      case 'TYPING_STATUS':
        this.emit('TYPING_STATUS', data.payload);
        break;
      case 'HEARTBEAT':
        this.emit('heartbeat');
        break;
      default:
        this.emit('message', data);
    }
  }

  // 心跳机制
  private startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'HEARTBEAT' });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 重连机制
  private handleReconnect() {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000
    );

    // 尝试重连
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  // 获取连接状态
  get isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  get connectionState() {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // 更新认证token
  updateToken(token: string | null) {
    this.token = token;
    if (token && !this.isConnected) {
      this.connect();
    } else if (!token && this.isConnected) {
      this.disconnect();
    }
  }
}

// 创建全局实例
export const realWebSocketManager = new RealWebSocketManager();

// 自动连接管理
if (typeof window !== 'undefined') {
  // 监听token变化 - 统一使用jwt_token
  window.addEventListener('storage', (e) => {
    if (e.key === 'jwt_token') {
      realWebSocketManager.updateToken(e.newValue);
    }
  });

  // 监听页面可见性变化 - 统一使用jwt_token
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && !realWebSocketManager.isConnected) {
      const token = localStorage.getItem('jwt_token');
      if (token) {
        realWebSocketManager.connect(token);
      }
    }
  });

  // 页面加载时自动连接 - 统一使用jwt_token
  if (document.readyState === 'complete') {
    const token = localStorage.getItem('jwt_token');
    if (token) {
      realWebSocketManager.connect(token);
    }
  } else {
    window.addEventListener('load', () => {
      const token = localStorage.getItem('jwt_token');
      if (token) {
        realWebSocketManager.connect(token);
      }
    });
  }
}

export default RealWebSocketManager;
