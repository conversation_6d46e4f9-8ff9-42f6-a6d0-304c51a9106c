package com.kitolus.community.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kitolus.community.util.JwtTokenUtil;
import com.kitolus.community.service.UserService;
import com.kitolus.community.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.net.URI;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket消息处理器
 * 实现完美的实时消息推送
 */
@Component
public class MessageWebSocketHandler extends TextWebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(MessageWebSocketHandler.class);
    
    // 存储用户ID到WebSocket会话的映射
    private static final Map<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();

    // 存储会话ID到用户ID的映射（用于快速查找）
    private static final Map<String, Long> sessionToUser = new ConcurrentHashMap<>();

    // 存储在线用户ID集合
    private static final Set<Long> onlineUsers = new ConcurrentSkipListSet<>();

    // 存储正在输入的用户 (conversationId -> Set<userId>)
    private static final Map<String, Set<Long>> typingUsers = new ConcurrentHashMap<>();

    // 存储输入状态的定时器 (conversationId_userId -> ScheduledFuture)
    private static final Map<String, java.util.concurrent.ScheduledFuture<?>> typingTimers = new ConcurrentHashMap<>();

    // 定时任务执行器，用于清理过期的typing状态
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    private final JwtTokenUtil jwtTokenUtil;
    private final UserService userService;
    private final ObjectMapper objectMapper;

    public MessageWebSocketHandler(JwtTokenUtil jwtTokenUtil, UserService userService) {
        this.jwtTokenUtil = jwtTokenUtil;
        this.userService = userService;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("🚀 WebSocket连接尝试建立 - 会话ID: {}, URI: {}", session.getId(), session.getUri());

        try {
            // 从查询参数中获取token
            String token = getTokenFromSession(session);
            logger.info("🔑 提取到的token: {}", token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null");

            if (token == null) {
                logger.warn("❌ WebSocket连接缺少token参数");
                session.close(CloseStatus.BAD_DATA.withReason("Missing token"));
                return;
            }

            // 验证token并获取用户信息
            String username = null;
            try {
                username = jwtTokenUtil.getUsernameFromToken(token);
                logger.info("✅ Token解析成功，用户名: {}", username);
            } catch (Exception e) {
                logger.warn("❌ WebSocket连接token解析失败: {}", e.getMessage());
                session.close(CloseStatus.BAD_DATA.withReason("Invalid token"));
                return;
            }

            if (username == null) {
                logger.warn("WebSocket连接token无效: {}", token.substring(0, Math.min(20, token.length())));
                session.close(CloseStatus.BAD_DATA.withReason("Invalid token"));
                return;
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                logger.warn("WebSocket连接用户不存在: {}", username);
                session.close(CloseStatus.BAD_DATA.withReason("User not found"));
                return;
            }

            // 存储用户会话映射
            Long userId = user.getId();
            
            // 如果用户已有连接，关闭旧连接
            WebSocketSession oldSession = userSessions.get(userId);
            if (oldSession != null && oldSession.isOpen()) {
                try {
                    oldSession.close(CloseStatus.NORMAL.withReason("New connection established"));
                } catch (Exception e) {
                    logger.warn("关闭旧WebSocket连接失败", e);
                }
                sessionToUser.remove(oldSession.getId());
            }

            // 建立新的映射关系
            userSessions.put(userId, session);
            sessionToUser.put(session.getId(), userId);

            // 检查用户是否已经在线，避免重复广播
            boolean wasOnline = onlineUsers.contains(userId);
            onlineUsers.add(userId);

            logger.info("✅ WebSocket连接建立成功 - 用户: {} (ID: {}), 会话: {}",
                       username, userId, session.getId());

            // 发送连接成功消息
            sendToUser(userId, Map.of(
                "type", "CONNECTION_ESTABLISHED",
                "message", "WebSocket连接成功",
                "userId", userId,
                "timestamp", System.currentTimeMillis()
            ));

            // 发送当前在线用户列表给新连接的用户
            sendToUser(userId, Map.of(
                "type", "ONLINE_STATUS",
                "onlineUsers", onlineUsers,
                "timestamp", System.currentTimeMillis()
            ));

            // 只有当用户之前不在线时才广播上线状态
            if (!wasOnline) {
                broadcastUserStatusChange(userId, true);
            }

        } catch (Exception e) {
            logger.error("WebSocket连接建立失败", e);
            session.close(CloseStatus.SERVER_ERROR.withReason("Connection setup failed"));
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        Long userId = sessionToUser.remove(sessionId);

        if (userId != null) {
            userSessions.remove(userId);
            onlineUsers.remove(userId);

            // 清理该用户的所有typing状态
            clearUserTypingStatus(userId);

            logger.info("❌ WebSocket连接关闭 - 用户ID: {}, 会话: {}, 状态: {}",
                       userId, sessionId, status);

            // 广播用户下线状态
            broadcastUserStatusChange(userId, false);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket传输错误 - 会话: {}", session.getId(), exception);
        
        String sessionId = session.getId();
        Long userId = sessionToUser.remove(sessionId);
        if (userId != null) {
            userSessions.remove(userId);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            String payload = message.getPayload();
            Map<String, Object> data = objectMapper.readValue(payload, Map.class);

            String type = (String) data.get("type");
            Long userId = sessionToUser.get(session.getId());

            if (userId == null) {
                logger.warn("收到未认证会话的消息: {}", session.getId());
                return;
            }

            switch (type) {
                case "PING":
                    // 响应心跳包
                    sendToUser(userId, Map.of(
                        "type", "PONG",
                        "timestamp", System.currentTimeMillis()
                    ));
                    break;

                case "TYPING_START":
                    handleTypingStart(userId, data);
                    break;

                case "TYPING_STOP":
                    handleTypingStop(userId, data);
                    break;

                case "GET_ONLINE_STATUS":
                    handleGetOnlineStatus(userId, data);
                    break;

                default:
                    logger.debug("未知的WebSocket消息类型: {}", type);
            }

        } catch (Exception e) {
            logger.error("处理WebSocket消息失败", e);
        }
    }

    /**
     * 向指定用户发送消息
     */
    public boolean sendToUser(Long userId, Object message) {
        WebSocketSession session = userSessions.get(userId);
        if (session == null || !session.isOpen()) {
            logger.debug("用户 {} 的WebSocket连接不存在或已关闭", userId);
            return false;
        }

        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.sendMessage(new TextMessage(jsonMessage));
            logger.info("📤 向用户 {} 发送WebSocket消息: {}", userId, jsonMessage);
            return true;
        } catch (IOException e) {
            logger.error("向用户 {} 发送WebSocket消息失败", userId, e);
            // 连接异常，清理映射关系
            userSessions.remove(userId);
            sessionToUser.remove(session.getId());
            return false;
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        WebSocketSession session = userSessions.get(userId);
        return session != null && session.isOpen();
    }

    /**
     * 处理用户开始输入
     */
    private void handleTypingStart(Long userId, Map<String, Object> data) {
        try {
            Object conversationIdObj = data.get("conversationId");
            if (conversationIdObj == null) {
                logger.warn("TYPING_START消息缺少conversationId");
                return;
            }

            String conversationId = conversationIdObj.toString();
            String timerKey = conversationId + "_" + userId;

            // 取消之前的定时器
            java.util.concurrent.ScheduledFuture<?> existingTimer = typingTimers.get(timerKey);
            if (existingTimer != null) {
                existingTimer.cancel(false);
            }

            // 检查用户是否已经在输入状态
            Set<Long> currentTypingUsers = typingUsers.get(conversationId);
            boolean wasTyping = currentTypingUsers != null && currentTypingUsers.contains(userId);

            // 添加到正在输入的用户集合
            typingUsers.computeIfAbsent(conversationId, k -> new ConcurrentSkipListSet<>()).add(userId);

            // 只有当用户之前不在输入状态时才通知
            if (!wasTyping) {
                notifyTypingStatus(conversationId, userId, true);
                logger.debug("用户 {} 开始在对话 {} 中输入", userId, conversationId);
            }

            // 设置新的自动清理任务（3秒后自动停止typing状态）
            java.util.concurrent.ScheduledFuture<?> newTimer = scheduler.schedule(() -> {
                handleTypingStop(userId, Map.of("conversationId", conversationId));
                typingTimers.remove(timerKey);
            }, 3, TimeUnit.SECONDS);

            typingTimers.put(timerKey, newTimer);

        } catch (Exception e) {
            logger.error("处理TYPING_START失败", e);
        }
    }

    /**
     * 处理用户停止输入
     */
    private void handleTypingStop(Long userId, Map<String, Object> data) {
        try {
            Object conversationIdObj = data.get("conversationId");
            if (conversationIdObj == null) {
                return;
            }

            String conversationId = conversationIdObj.toString();
            String timerKey = conversationId + "_" + userId;

            // 取消定时器
            java.util.concurrent.ScheduledFuture<?> timer = typingTimers.remove(timerKey);
            if (timer != null) {
                timer.cancel(false);
            }

            // 从正在输入的用户集合中移除
            Set<Long> typingSet = typingUsers.get(conversationId);
            if (typingSet != null) {
                boolean wasTyping = typingSet.remove(userId);
                if (typingSet.isEmpty()) {
                    typingUsers.remove(conversationId);
                }

                // 只有当用户确实在输入状态时才通知
                if (wasTyping) {
                    notifyTypingStatus(conversationId, userId, false);
                    logger.debug("用户 {} 停止在对话 {} 中输入", userId, conversationId);
                }
            }

        } catch (Exception e) {
            logger.error("处理TYPING_STOP失败", e);
        }
    }

    /**
     * 处理获取在线状态请求
     */
    private void handleGetOnlineStatus(Long userId, Map<String, Object> data) {
        try {
            // 发送当前在线用户列表（可以根据需要过滤，比如只发送好友的状态）
            sendToUser(userId, Map.of(
                "type", "ONLINE_STATUS",
                "onlineUsers", onlineUsers,
                "timestamp", System.currentTimeMillis()
            ));

        } catch (Exception e) {
            logger.error("处理GET_ONLINE_STATUS失败", e);
        }
    }

    /**
     * 通知对话中的用户typing状态变化
     */
    private void notifyTypingStatus(String conversationId, Long typingUserId, boolean isTyping) {
        try {
            // 这里需要根据conversationId获取对话参与者
            // 简化实现：假设conversationId格式为 "userId1_userId2"
            String[] userIds = conversationId.split("_");
            if (userIds.length == 2) {
                Long otherUserId = null;
                try {
                    Long user1 = Long.parseLong(userIds[0]);
                    Long user2 = Long.parseLong(userIds[1]);
                    otherUserId = user1.equals(typingUserId) ? user2 : user1;
                } catch (NumberFormatException e) {
                    logger.warn("无法解析conversationId: {}", conversationId);
                    return;
                }

                // 只通知对话中的另一个用户
                if (otherUserId != null && onlineUsers.contains(otherUserId)) {
                    sendToUser(otherUserId, Map.of(
                        "type", isTyping ? "USER_TYPING_START" : "USER_TYPING_STOP",
                        "userId", typingUserId,
                        "conversationId", conversationId,
                        "timestamp", System.currentTimeMillis()
                    ));
                }
            }

        } catch (Exception e) {
            logger.error("通知typing状态失败", e);
        }
    }

    /**
     * 广播用户状态变化
     */
    private void broadcastUserStatusChange(Long userId, boolean isOnline) {
        try {
            // 简化实现：广播给所有在线用户
            // 在实际应用中，可能只需要广播给好友或相关用户
            Map<String, Object> statusMessage = Map.of(
                "type", "USER_STATUS_CHANGE",
                "userId", userId,
                "isOnline", isOnline,
                "timestamp", System.currentTimeMillis()
            );

            // 广播给所有其他在线用户
            for (Long onlineUserId : onlineUsers) {
                if (!onlineUserId.equals(userId)) {
                    sendToUser(onlineUserId, statusMessage);
                }
            }

            logger.debug("广播用户 {} 状态变化: {}", userId, isOnline ? "上线" : "下线");

        } catch (Exception e) {
            logger.error("广播用户状态变化失败", e);
        }
    }

    /**
     * 清理用户的所有typing状态
     */
    private void clearUserTypingStatus(Long userId) {
        try {
            typingUsers.entrySet().removeIf(entry -> {
                Set<Long> typingSet = entry.getValue();
                boolean removed = typingSet.remove(userId);
                if (removed) {
                    // 通知该对话中的其他用户
                    notifyTypingStatus(entry.getKey(), userId, false);
                }
                return typingSet.isEmpty();
            });
        } catch (Exception e) {
            logger.error("清理用户typing状态失败", e);
        }
    }

    /**
     * 从WebSocket会话中提取token
     */
    private String getTokenFromSession(WebSocketSession session) {
        try {
            URI uri = session.getUri();
            if (uri != null) {
                String query = uri.getQuery();
                if (query != null) {
                    String[] params = query.split("&");
                    for (String param : params) {
                        String[] keyValue = param.split("=");
                        if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                            return keyValue[1];
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("提取WebSocket token失败", e);
        }
        return null;
    }
}
