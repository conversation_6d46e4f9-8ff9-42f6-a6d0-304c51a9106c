package com.kitolus.community.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
public class CreatePostRequestDTO {

    @NotBlank(message = "Title cannot be blank")
    @Size(max = 100, message = "Title cannot exceed 100 characters")
    private String title;

    @NotBlank(message = "Content cannot be blank")
    private String content;
    
    @NotNull(message = "Partition cannot be null")
    @NotBlank(message = "Partition cannot be blank")
    private String partition; // The format "Era|Tier|Topic" from frontend
} 