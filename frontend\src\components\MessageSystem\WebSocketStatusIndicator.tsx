/**
 * WebSocket状态指示器组件
 * 显示WebSocket连接状态和提供调试信息
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  AlertCircle, 
  RefreshCw, 
  Info,
  X,
  CheckCircle2,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useWebSocketStatus } from '@/hooks/useWebSocketStatus';
import { cn } from '@/lib/utils';

interface WebSocketStatusIndicatorProps {
  showDetails?: boolean;
  className?: string;
}

export const WebSocketStatusIndicator: React.FC<WebSocketStatusIndicatorProps> = ({
  showDetails = false,
  className
}) => {
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const webSocketStatus = useWebSocketStatus();

  // 获取状态图标和颜色
  const getStatusInfo = () => {
    switch (webSocketStatus.connectionState) {
      case 'connected':
        return {
          icon: <Wifi className="w-4 h-4" />,
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/20',
          label: '已连接',
          description: 'WebSocket连接正常'
        };
      case 'connecting':
        return {
          icon: <RefreshCw className="w-4 h-4 animate-spin" />,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500/20',
          label: '连接中',
          description: '正在建立WebSocket连接'
        };
      case 'error':
        return {
          icon: <AlertCircle className="w-4 h-4" />,
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/20',
          label: '连接错误',
          description: webSocketStatus.lastError || 'WebSocket连接出现错误'
        };
      case 'disconnected':
      default:
        return {
          icon: <WifiOff className="w-4 h-4" />,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10',
          borderColor: 'border-gray-500/20',
          label: '未连接',
          description: webSocketStatus.isAvailable 
            ? 'WebSocket服务可用但未连接' 
            : 'WebSocket服务不可用，使用模拟模式'
        };
    }
  };

  const statusInfo = getStatusInfo();

  // 简单状态指示器
  if (!showDetails) {
    return (
      <div 
        className={cn("flex items-center gap-2", className)}
        title={statusInfo.description}
      >
        <div className={cn("p-1 rounded-full", statusInfo.bgColor)}>
          <div className={statusInfo.color}>
            {statusInfo.icon}
          </div>
        </div>
        {showDetails && (
          <span className={cn("text-sm", statusInfo.color)}>
            {statusInfo.label}
          </span>
        )}
      </div>
    );
  }

  // 详细状态面板
  return (
    <div className={cn("relative", className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowDebugPanel(!showDebugPanel)}
        className={cn(
          "flex items-center gap-2 h-8 px-2",
          statusInfo.bgColor,
          statusInfo.borderColor,
          "border"
        )}
      >
        <div className={statusInfo.color}>
          {statusInfo.icon}
        </div>
        <span className={cn("text-xs", statusInfo.color)}>
          {statusInfo.label}
        </span>
        <Info className="w-3 h-3 text-muted-foreground" />
      </Button>

      <AnimatePresence>
        {showDebugPanel && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 mt-2 z-[10300]"
          >
            <Card className="w-80 shadow-lg border">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <div className={statusInfo.color}>
                      {statusInfo.icon}
                    </div>
                    WebSocket状态
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowDebugPanel(false)}
                    className="w-6 h-6"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {/* 连接状态 */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">连接状态:</span>
                  <Badge 
                    variant="outline" 
                    className={cn(statusInfo.color, statusInfo.bgColor)}
                  >
                    {statusInfo.label}
                  </Badge>
                </div>

                {/* 服务可用性 */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">服务状态:</span>
                  <div className="flex items-center gap-1">
                    {webSocketStatus.isAvailable ? (
                      <CheckCircle2 className="w-3 h-3 text-green-500" />
                    ) : (
                      <AlertCircle className="w-3 h-3 text-red-500" />
                    )}
                    <span className="text-xs">
                      {webSocketStatus.isAvailable ? '可用' : '不可用'}
                    </span>
                  </div>
                </div>

                {/* 重连次数 */}
                {webSocketStatus.reconnectAttempts > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">重连次数:</span>
                    <span className="text-xs">{webSocketStatus.reconnectAttempts}</span>
                  </div>
                )}

                {/* 错误信息 */}
                {webSocketStatus.lastError && (
                  <div className="space-y-1">
                    <span className="text-sm text-muted-foreground">错误信息:</span>
                    <div className="text-xs text-red-500 bg-red-500/10 p-2 rounded border border-red-500/20">
                      {webSocketStatus.lastError}
                    </div>
                  </div>
                )}

                {/* 状态描述 */}
                <div className="space-y-1">
                  <span className="text-sm text-muted-foreground">说明:</span>
                  <p className="text-xs text-muted-foreground">
                    {statusInfo.description}
                  </p>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={webSocketStatus.reconnect}
                    disabled={webSocketStatus.connectionState === 'connecting'}
                    className="flex-1"
                  >
                    <RefreshCw className={cn(
                      "w-3 h-3 mr-1",
                      webSocketStatus.connectionState === 'connecting' && "animate-spin"
                    )} />
                    重连
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={webSocketStatus.checkAvailability}
                    className="flex-1"
                  >
                    <Clock className="w-3 h-3 mr-1" />
                    检测
                  </Button>
                </div>

                {/* 开发环境信息 */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="pt-2 border-t border-border">
                    <details className="text-xs">
                      <summary className="cursor-pointer text-muted-foreground mb-1">
                        调试信息
                      </summary>
                      <pre className="bg-muted p-2 rounded text-xs overflow-auto">
                        {JSON.stringify(webSocketStatus, null, 2)}
                      </pre>
                    </details>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
