'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

interface WebSocketStatusProps {
  wsManager: typeof unifiedWebSocketManager | null;
}

/**
 * WebSocket连接状态指示器
 * 显示实时连接状态和在线用户数
 */
export const WebSocketStatus: React.FC<WebSocketStatusProps> = ({ wsManager }) => {
  const [status, setStatus] = useState({
    connected: false,
    connecting: false,
    reconnectAttempts: 0
  });

  useEffect(() => {
    if (!wsManager) return;

    const updateStatus = () => {
      setStatus({
        connected: wsManager.isConnected,
        connecting: wsManager.connectionState === 'connecting',
        reconnectAttempts: 0 // 统一管理器暂时不暴露这个信息
      });
    };

    // 监听连接状态变化
    wsManager.on('connected', updateStatus);
    wsManager.on('disconnected', updateStatus);
    wsManager.on('error', updateStatus);

    // 定期更新状态
    const interval = setInterval(updateStatus, 1000);

    // 初始状态
    updateStatus();

    return () => {
      clearInterval(interval);
      wsManager.off('connected', updateStatus);
      wsManager.off('disconnected', updateStatus);
      wsManager.off('error', updateStatus);
    };
  }, [wsManager]);

  if (!wsManager) return null;

  const getStatusColor = () => {
    if (status.connected) return 'text-green-500';
    if (status.connecting) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getStatusText = () => {
    if (status.connected) return '实时连接';
    if (status.connecting) return '连接中...';
    if (status.reconnectAttempts > 0) return `重连中(${status.reconnectAttempts})`;
    return '连接断开';
  };

  const getStatusIcon = () => {
    if (status.connected) return <Wifi className="w-3 h-3" />;
    if (status.connecting) return <RefreshCw className="w-3 h-3 animate-spin" />;
    return <WifiOff className="w-3 h-3" />;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className="flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium"
        style={{
          background: status.connected 
            ? 'rgba(34, 197, 94, 0.1)' 
            : status.connecting 
            ? 'rgba(234, 179, 8, 0.1)'
            : 'rgba(239, 68, 68, 0.1)',
          border: `1px solid ${
            status.connected 
              ? 'rgba(34, 197, 94, 0.2)' 
              : status.connecting 
              ? 'rgba(234, 179, 8, 0.2)'
              : 'rgba(239, 68, 68, 0.2)'
          }`
        }}
      >
        <span className={getStatusColor()}>
          {getStatusIcon()}
        </span>
        <span className={`${getStatusColor()} whitespace-nowrap`}>
          {getStatusText()}
        </span>
      </motion.div>
    </AnimatePresence>
  );
};
