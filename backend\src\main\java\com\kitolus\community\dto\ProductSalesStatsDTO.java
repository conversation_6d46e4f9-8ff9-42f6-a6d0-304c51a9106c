package com.kitolus.community.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSalesStatsDTO {
    private Long productId;
    private String productName;
    private String productImageUrl;
    private BigDecimal productPrice;
    private Long salesCount;
    private BigDecimal totalRevenue;
    private BigDecimal developerRevenue;
    private String createdAt;
    
    // Constructor for query results
    public ProductSalesStatsDTO(Long productId, String productName, String productImageUrl, 
                               BigDecimal productPrice, Long salesCount, BigDecimal totalRevenue, 
                               BigDecimal developerRevenue) {
        this.productId = productId;
        this.productName = productName;
        this.productImageUrl = productImageUrl;
        this.productPrice = productPrice;
        this.salesCount = salesCount;
        this.totalRevenue = totalRevenue;
        this.developerRevenue = developerRevenue;
    }
}
