'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import {
  MoreVertical,
  Trash2,
  Pin,
  Circle,
  CheckCircle2,
  MessageSquare,
  AlertCircle,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useAuth } from '@/contexts/AuthContext';
import { Conversation } from '@/types/Message';
import { getConversations, deleteConversation } from '@/services/messageApi';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useMessageStore } from '@/stores/messageStore';
import { useOnlineStatus } from '@/contexts/GlobalWebSocketContext';
import { OnlineStatusIndicator } from '@/components/ui/OnlineStatusIndicator';

interface ConversationListProps {
  searchQuery: string;
  onConversationSelect: (conversationId: string, unreadCount?: number) => void;
  selectedConversationId: string | null;
}

export const ConversationList: React.FC<ConversationListProps> = ({
  searchQuery,
  onConversationSelect,
  selectedConversationId
}) => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [deletingConversations, setDeletingConversations] = useState<Set<string>>(new Set());

  // 使用全局在线状态
  const { isUserOnline } = useOnlineStatus();

  // 获取会话列表
  const fetchConversations = useCallback(async (pageNum = 1, reset = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const response = await getConversations();
      const conversationsData = response.conversations || response || [];

      if (reset || pageNum === 1) {
        setConversations(conversationsData);
      } else {
        setConversations(prev => [...prev, ...conversationsData]);
      }

      setHasMore(false); // 新API暂时不支持分页
      setPage(pageNum);
      setError(null);
    } catch (error) {
      // 静默处理获取对话列表失败
      const errorMessage = error instanceof Error ? error.message : '获取对话列表失败';
      setError(errorMessage);
      setConversations([]);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, []);

  useEffect(() => {
    fetchConversations(1, true);
  }, [fetchConversations]);

  // 智能更新对话列表，避免闪烁
  const updateConversationInPlace = useCallback((otherUserId: number, lastMessage: string, timestamp?: number, isCurrentlyViewing?: boolean) => {

    setConversations(prevConversations => {
      const updatedConversations = [...prevConversations];

      // 查找需要更新的对话 - 使用后端返回的字段名
      const conversationIndex = updatedConversations.findIndex((conv: any) => {
        return conv.other_user_id === otherUserId;
      });

      if (conversationIndex !== -1) {
        // 更新现有对话 - 使用后端字段名
        const updatedConversation = { ...updatedConversations[conversationIndex] } as any;

        // 更新最后一条消息内容和时间
        updatedConversation.last_message_content = lastMessage;
        updatedConversation.last_message_time = timestamp ? new Date(timestamp).toISOString() : new Date().toISOString();

        // 检查是否正在查看该对话
        const currentConversationId = `conv_${otherUserId}`;
        const isViewingFromParam = isCurrentlyViewing; // 来自RealTimeChatInterface的参数
        const isViewingFromState = selectedConversationId === currentConversationId; // 来自本地状态
        const actuallyViewing = isViewingFromParam || isViewingFromState;

        // 如果没有在查看该对话，则增加未读数量
        if (!actuallyViewing) {
          const oldUnreadCount = updatedConversation.unread_count || 0;
          updatedConversation.unread_count = oldUnreadCount + 1;

          // 同时更新全局未读统计
          try {
            const { updateUnreadStats, unreadStats } = useMessageStore.getState();
            if (unreadStats) {
              const newStats = {
                ...unreadStats,
                totalUnread: unreadStats.totalUnread + 1,
                privateUnread: unreadStats.privateUnread + 1
              };
              updateUnreadStats(newStats);
            }
          } catch (error) {
            console.error('📋 [ConversationList] 更新全局未读统计失败:', error);
          }
        }

        // 移动到列表顶部
        updatedConversations.splice(conversationIndex, 1);
        updatedConversations.unshift(updatedConversation);

      } else {
        // 如果对话不存在，则重新加载列表
        fetchConversations(1, true);
        return prevConversations;
      }

      return updatedConversations;
    });
  }, [fetchConversations, selectedConversationId]);

  // 监听对话更新事件
  useEffect(() => {
    const handleConversationUpdate = (event: CustomEvent) => {
      const { otherUserId, lastMessage, timestamp, isCurrentlyViewing } = event.detail || {};

      if (otherUserId && lastMessage) {
        // 智能更新，避免重新加载
        updateConversationInPlace(otherUserId, lastMessage, timestamp, isCurrentlyViewing);
      } else {
        // 如果没有足够的信息，则重新加载
        fetchConversations(1, true);
      }
    };

    // 监听对话已读请求事件
    const handleConversationReadRequest = (event: CustomEvent) => {
      const { conversationId, source } = event.detail;

      let clearedUnreadCount = 0;

      // 先获取当前的未读数量
      const currentConversation = conversations.find(conv => conv.id === conversationId);
      if (currentConversation) {
        const convAny = currentConversation as any;
        clearedUnreadCount = convAny.unread_count || 0;
      }

      // 立即更新对话列表状态
      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === conversationId) {

            return {
              ...conv,
              unread_count: 0 // 清除未读数量
            } as any;
          }
          return conv;
        });

        // 立即触发响应事件，不使用setTimeout
        if (clearedUnreadCount > 0) {
          // 使用微任务确保状态更新完成后再触发事件
          Promise.resolve().then(() => {

            window.dispatchEvent(new CustomEvent('conversationReadResponse', {
              detail: { conversationId, clearedUnreadCount, source }
            }));
          });
        }

        // 强制触发重新渲染
        setRefreshTrigger(prev => prev + 1);


        return updated;
      });
    };

    // 监听直接清除未读消息事件
    const handleDirectClearUnread = (event: CustomEvent) => {
      const { conversationId } = event.detail;



      setConversations(prev => {
        const updated = prev.map(conv => {
          // 🔥 关键修复：使用正确的ID匹配逻辑
          const convAny = conv as any;
          const currentConversationId = `conv_${convAny.other_user_id}` || convAny.id;

          if (currentConversationId === conversationId) {
            const clearedCount = convAny.unread_count || 0;



            // 如果有未读消息，更新全局统计
            if (clearedCount > 0) {
              try {
                const { updateUnreadStats, unreadStats } = useMessageStore.getState();
                if (unreadStats) {
                  const newStats = {
                    ...unreadStats,
                    totalUnread: Math.max(0, unreadStats.totalUnread - clearedCount),
                    privateUnread: Math.max(0, unreadStats.privateUnread - clearedCount)
                  };
                  updateUnreadStats(newStats);

                }
              } catch (error) {
                console.error('📋 [ConversationList] 更新全局统计失败:', error);
              }
            }

            return {
              ...conv,
              unread_count: 0
            } as any;
          }
          return conv;
        });

        // 只有在真正有变化时才刷新
        const hasChanges = updated.some((conv, index) =>
          (conv as any).unread_count !== (prev[index] as any).unread_count
        );

        if (hasChanges) {
          setRefreshTrigger(prev => prev + 1);

        }

        return updated;
      });
    };

    // 监听清除对话未读消息事件
    const handleClearConversationUnread = (event: CustomEvent) => {
      const { conversationId } = event.detail;



      setConversations(prev => {
        let hasChanges = false;
        const updated = prev.map(conv => {
          // 🔥 关键修复：使用正确的ID匹配逻辑
          const convAny = conv as any;
          const currentConversationId = `conv_${convAny.other_user_id}` || convAny.id;

          if (currentConversationId === conversationId && convAny.unread_count > 0) {

            hasChanges = true;
            return {
              ...conv,
              unread_count: 0
            } as any;
          }
          return conv;
        });

        // 只有在真正有变化时才刷新
        if (hasChanges) {
          setRefreshTrigger(prev => prev + 1);

        }

        return updated;
      });
    };



    window.addEventListener('conversationUpdated', handleConversationUpdate as EventListener);
    window.addEventListener('conversationReadRequest', handleConversationReadRequest as EventListener);
    window.addEventListener('directClearUnread', handleDirectClearUnread as EventListener);
    window.addEventListener('clearConversationUnread', handleClearConversationUnread as EventListener);

    return () => {
      window.removeEventListener('conversationUpdated', handleConversationUpdate as EventListener);
      window.removeEventListener('conversationReadRequest', handleConversationReadRequest as EventListener);
      window.removeEventListener('directClearUnread', handleDirectClearUnread as EventListener);
      window.removeEventListener('clearConversationUnread', handleClearConversationUnread as EventListener);
    };
  }, [updateConversationInPlace, fetchConversations]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchConversations(page + 1);
    }
  }, [fetchConversations, page, loadingMore, hasMore]);

  // 过滤会话 - 适配后端数据格式
  const filteredConversations = conversations.filter((conversation: any) => {
    if (!searchQuery) return true;

    // 获取参与者信息
    const otherParticipant = getOtherParticipant(conversation);
    const participantName = otherParticipant?.username || '';

    // 获取最后一条消息内容
    const lastMessageContent = conversation.last_message_content || conversation.lastMessage?.content || '';

    return participantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
           lastMessageContent.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // 获取对话对象 - 适配后端返回的数据格式
  const getOtherParticipant = (conversation: any) => {
    // 后端返回的格式是 Map<String, Object>，包含 other_user_* 字段
    if (conversation.other_user_id && conversation.other_user_name) {
      // 构建完整的头像URL - 使用正确的格式
      let fullAvatarUrl = null;
      if (conversation.other_user_avatar) {
        // 如果已经是完整URL，直接使用
        if (conversation.other_user_avatar.startsWith('http')) {
          fullAvatarUrl = conversation.other_user_avatar;
        } else {
          // 使用相对路径，让前端处理
          fullAvatarUrl = conversation.other_user_avatar.startsWith('/')
            ? conversation.other_user_avatar
            : `/${conversation.other_user_avatar}`;
        }
      }

      return {
        id: conversation.other_user_id,
        username: conversation.other_user_name,
        fullAvatarUrl: fullAvatarUrl
      };
    }

    // 兼容旧格式 - 如果有 participants 数组
    if (conversation.participants && Array.isArray(conversation.participants)) {
      return conversation.participants.find((p: any) => p.id !== user?.id);
    }

    return null;
  };

  // 删除单个会话
  const handleDeleteConversation = async (conversationId: string) => {
    try {
      // 添加到删除中的会话集合
      setDeletingConversations(prev => new Set(prev).add(conversationId));

      // 显示初始加载状态
      toast.loading('正在准备删除会话...', { id: `delete-${conversationId}` });

      const otherUserId = parseInt(conversationId.replace('conv_', ''));
      if (isNaN(otherUserId)) {
        throw new Error('无效的用户ID');
      }

      // 更新进度提示
      toast.loading('正在获取会话消息...', { id: `delete-${conversationId}` });

      const result = await deleteConversation(otherUserId);

      // 移除加载状态
      toast.dismiss(`delete-${conversationId}`);

      if (result.success) {
        console.log(`删除会话成功: ${conversationId}`);

        // 从列表中移除会话
        setConversations(prev => {
          const updated = prev.filter(conv => conv.id !== conversationId);
          console.log(`会话列表更新: ${prev.length} -> ${updated.length}`);
          return updated;
        });

        // 如果当前选中的会话被删除，清除选中状态
        if (selectedConversationId === conversationId) {
          onConversationSelect(''); // 传递空字符串而不是null
        }

        // 触发全局会话更新事件
        window.dispatchEvent(new CustomEvent('conversationDeleted', {
          detail: { conversationId, otherUserId: parseInt(conversationId.replace('conv_', '')) }
        }));

        // 强制刷新对话列表以确保同步
        setTimeout(() => {
          fetchConversations(1, true);
        }, 100);

        toast.success(result.message || '会话已删除');
      } else {
        console.error(`删除会话失败: ${conversationId}`, result);
        toast.error(result.message || '删除会话失败');
      }
    } catch (error: any) {
      console.error('删除会话失败:', error);
      toast.dismiss(`delete-${conversationId}`);

      // 提供更友好的错误信息
      if (error.message?.includes('500')) {
        toast.error('服务器暂时不可用，请稍后重试');
      } else if (error.message?.includes('网络')) {
        toast.error('网络连接失败，请检查网络后重试');
      } else {
        toast.error(error.message || '删除会话失败，请重试');
      }
    } finally {
      // 从删除中的会话集合中移除
      setDeletingConversations(prev => {
        const updated = new Set(prev);
        updated.delete(conversationId);
        return updated;
      });
    }
  };



  // 处理会话操作
  const handleConversationAction = (action: string, conversationId: string) => {
    switch (action) {
      case 'delete':
        handleDeleteConversation(conversationId);
        break;
      case 'archive':
        // TODO: 实现归档会话
        break;
      case 'pin':
        // TODO: 实现置顶会话
        break;
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="w-14 h-14 rounded-2xl" />
            <div className="flex-1 space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-center">
        <div
          className="w-20 h-20 rounded-3xl flex items-center justify-center mb-6"
          style={{
            background: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.2)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)'
          }}
        >
          <AlertCircle className="w-10 h-10 text-destructive" />
        </div>
        <h3 className="font-semibold text-lg mb-3">加载失败</h3>
        <p className="text-sm text-muted-foreground mb-6 max-w-[280px] leading-relaxed">{error}</p>
        <Button
          variant="outline"
          onClick={() => fetchConversations(1, true)}
          className="gap-2 rounded-xl border-0"
          style={{
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)'
          }}
        >
          <RefreshCw className="w-4 h-4" />
          重新加载
        </Button>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full w-full">
      <div className="p-2 w-full min-w-0 overflow-hidden">
        {/* 对话列表头部 - 显示对话数量 */}
        {conversations.length > 0 && (
          <div className="flex items-center justify-between p-4 mb-2 border-b border-white/5 w-full min-w-0">
            <span className="text-sm text-muted-foreground font-medium truncate">
              {conversations.length} 个对话
            </span>
          </div>
        )}

        {filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div
              className="w-24 h-24 rounded-3xl flex items-center justify-center mb-6"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)'
              }}
            >
              <MessageSquare className="w-12 h-12 text-muted-foreground/40" />
            </div>
            <h3 className="font-semibold text-lg mb-3">
              {searchQuery ? '没有找到匹配的对话' : '还没有对话'}
            </h3>
            <p className="text-sm text-muted-foreground max-w-[280px] leading-relaxed">
              {searchQuery
                ? '尝试使用不同的关键词搜索，或者检查拼写是否正确'
                : '开始与其他用户交流，创建你的第一个对话'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3 p-6" data-conversation-list>
            {filteredConversations.map((conversation: any, index: number) => {
              const otherParticipant = getOtherParticipant(conversation);
              // 使用 conv_ 前缀格式，与RealTimeChatInterface保持一致
              const conversationId = `conv_${conversation.other_user_id}` || conversation.id;
              const isSelected = selectedConversationId === conversationId;
              const isDeleting = deletingConversations.has(conversationId);



              return (
                <motion.div
                  key={`${conversationId}-${refreshTrigger}`}
                  layout
                  initial={{ opacity: 0, y: 8 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -8 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.02,
                    layout: { duration: 0.3, ease: "easeInOut" }
                  }}
                  className={cn(
                    "group relative flex items-center p-4 rounded-xl cursor-pointer transition-all duration-300 overflow-hidden conversation-item-container",
                    isSelected
                      ? "border border-white/20 shadow-md scale-[1.02]"
                      : "hover:shadow-sm hover:scale-[1.01] border border-transparent",
                    isDeleting && "opacity-50 pointer-events-none"
                  )}
                  style={{
                    background: isSelected
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'transparent',
                    backdropFilter: isSelected ? 'blur(10px)' : 'none',
                    WebkitBackdropFilter: isSelected ? 'blur(10px)' : 'none'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSelected) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                      e.currentTarget.style.backdropFilter = 'blur(5px)';
                      (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(5px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSelected) {
                      e.currentTarget.style.background = 'transparent';
                      e.currentTarget.style.backdropFilter = 'none';
                      (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                    }
                  }}
                  onClick={() => {
                    // 获取当前对话的未读数量
                    const convAny = conversation as any;
                    const currentUnreadCount = convAny.unread_count || 0;



                    // 只调用回调，不在这里清除状态
                    // 让MessageSystem统一处理状态清除
                    onConversationSelect(conversationId, currentUnreadCount);
                  }}
                  data-conversation-id={conversationId}
                >
                  {/* 头像 */}
                  <div className="relative flex-shrink-0">
                    <Avatar className="w-12 h-12 ring-2 ring-background shadow-sm">
                      <AvatarImage src={otherParticipant?.fullAvatarUrl || undefined} />
                      <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary text-base font-semibold">
                        {otherParticipant?.username?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    {/* 在线状态指示器 */}
                    {otherParticipant && (
                      <div className="absolute -bottom-0.5 -right-0.5">
                        <OnlineStatusIndicator
                          isOnline={isUserOnline(otherParticipant.id)}
                          size="sm"
                        />
                      </div>
                    )}
                  </div>

                  {/* 会话信息 */}
                  <div className="flex-1 ml-4 min-w-0 overflow-hidden">
                    <div className="flex items-start justify-between mb-1.5 gap-2">
                      <h4 className="font-semibold text-sm text-foreground truncate flex-1 min-w-0">
                        {otherParticipant?.username || '未知用户'}
                      </h4>
                      <span className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">
                        {conversation.last_message_time ? formatDistanceToNow(new Date(conversation.last_message_time), { addSuffix: true, locale: zhCN }) : ''}
                      </span>
                    </div>

                    <div className="flex items-start justify-between gap-2 min-w-0">
                      <div className="flex-1 min-w-0 overflow-hidden">
                        <p className={cn(
                          "text-sm leading-relaxed break-words",
                          conversation.unread_count > 0
                            ? "text-foreground font-medium"
                            : "text-muted-foreground",
                          // 限制最多显示2行，超出部分用省略号
                          "line-clamp-2 overflow-hidden"
                        )}
                        style={{
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          hyphens: 'auto'
                        }}>
                          {conversation.last_message_content || '暂无消息'}
                        </p>
                      </div>

                      {/* 未读消息数量 */}
                      {conversation.unread_count > 0 && (
                        <span
                          className="flex-shrink-0 bg-primary text-primary-foreground text-xs rounded-full px-2.5 py-1 min-w-[24px] text-center font-medium shadow-sm"
                          data-unread-count={conversation.unread_count}
                        >
                          {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 操作菜单 */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-8 h-8 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/50 rounded-lg"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleConversationAction('pin', conversation.id)}>
                        <Pin className="w-4 h-4 mr-2" />
                        置顶会话
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            onSelect={(e) => e.preventDefault()}
                            className="text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            删除会话
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2">
                              <AlertTriangle className="w-5 h-5 text-destructive" />
                              确认删除会话
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              确定要删除与 "{otherParticipant?.username || '未知用户'}" 的对话吗？
                              <br />
                              <span className="text-destructive font-medium">此操作将永久删除所有消息记录，无法撤销。</span>
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteConversation(conversationId)}
                              className="bg-destructive hover:bg-destructive/90"
                            >
                              确认删除
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* 已读/未读状态指示器 */}
                  <div className="absolute left-2 top-1/2 -translate-y-1/2">
                    {conversation.unreadCount > 0 ? (
                      <Circle className="w-2.5 h-2.5 fill-primary text-primary" />
                    ) : (
                      <CheckCircle2 className="w-2.5 h-2.5 text-muted-foreground/30" />
                    )}
                  </div>
                </motion.div>
              );
            })}

            {/* 加载更多 */}
            {hasMore && (
              <div className="p-4 text-center">
                <Button
                  variant="ghost"
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="text-sm"
                >
                  {loadingMore ? '加载中...' : '加载更多'}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </ScrollArea>
  );
};
