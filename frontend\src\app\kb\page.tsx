import KnowledgeBaseClient from './KnowledgeBaseClient.tsx';
import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';

function LoadingFallback() {
  return (
    <div className="container mx-auto px-4 md:px-6 py-32 md:py-40">
      <div className="max-w-3xl mx-auto text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">知识库</h1>
        <p className="text-muted-foreground">搜索社区分享的所有指南、教程和技术蓝图。</p>
      </div>
      <div className="text-center">
        <Loader2 className="mx-auto h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">正在加载组件...</p>
      </div>
    </div>
  );
}

export default function KnowledgeBasePage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <KnowledgeBaseClient />
    </Suspense>
  );
} 