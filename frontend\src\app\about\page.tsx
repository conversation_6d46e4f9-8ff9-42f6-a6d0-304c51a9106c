'use client';

import Header from '@/components/Header';
import { motion } from 'framer-motion';
import { Github, Home, Video, Heart } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const AboutPage = () => {
    const iconProps = {
        size: 24,
        className: 'group-hover:text-primary transition-colors'
    };
    
    const socialLinks = [
        { name: '哔哩哔哩', href: 'https://space.bilibili.com/1788510520', icon: <Video {...iconProps} /> },
        { name: '官网', href: 'https://kitolus.top', icon: <Home {...iconProps} /> },
        { name: '爱发电', href: 'https://afdian.com/a/Kitolus', icon: <Heart {...iconProps} /> },
        { name: 'GitHub', href: 'https://github.com/Kitolus99', icon: <Github {...iconProps} /> },
    ];

    return (
        <div className="relative min-h-screen text-white flex flex-col">
            <div className="fixed inset-0 -z-10">
                <div
                    className="absolute inset-0 w-full h-full bg-cover bg-center"
                    style={{ backgroundImage: "url('/images/background/About.webp')" }}
                />
                <div className="absolute inset-0 bg-black/60" />
            </div>

            <Header />
            <div className="flex-grow flex items-center justify-center">
                <motion.div
                    className="text-center p-8 max-w-4xl mx-auto flex flex-col md:flex-row items-center gap-12"
                    initial="hidden"
                    animate="visible"
                    variants={{ visible: { transition: { staggerChildren: 0.3 } } }}
                >
                    {/* Profile Image */}
                    <motion.div
                        className="w-48 h-48 md:w-64 md:h-64 flex-shrink-0"
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                    >
                        <Image
                            src="/Kitolus.jpg"
                            alt="Kitolus"
                            width={256}
                            height={256}
                            className="rounded-full object-cover w-full h-full shadow-lg"
                        />
                    </motion.div>

                    {/* Content */}
                    <motion.div
                        className="text-left"
                        variants={{ hidden: { opacity: 0, x: 50 }, visible: { opacity: 1, x: 0, transition: { duration: 0.8, ease: 'easeOut' } } }}
                    >
                        <h1 className="text-5xl font-bold text-primary">Kitolus</h1>
                        <p className="mt-4 text-lg text-zinc-300">
                            一个充满热情的开发者和创造者，专注于在《我的世界》GTNH（格雷科技：新视野）的宇宙中构建独特的体验。本网站是我的项目中心，也是社区交流的地方。
                        </p>
                        <div className="mt-8 flex flex-wrap justify-center md:justify-start gap-4">
                            {socialLinks.map(link => (
                                <a
                                    key={link.name}
                                    href={link.href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="group flex items-center gap-3 px-4 py-2 bg-zinc-800/50 border border-zinc-700 rounded-lg hover:bg-zinc-700/70 hover:border-primary transition-all duration-300"
                                >
                                    {link.icon}
                                    <span className="text-zinc-300 group-hover:text-white transition-colors">{link.name}</span>
                                </a>
                            ))}
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </div>
    );
};

export default AboutPage; 