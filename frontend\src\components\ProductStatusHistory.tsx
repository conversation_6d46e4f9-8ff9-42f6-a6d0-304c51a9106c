'use client';

import { useState, useEffect } from 'react';
import { getProductStatusHistory } from '@/services/api';
import { ProductStatusHistory, ProductStatus } from '@/types/Product';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, User, FileText, ArrowRight } from 'lucide-react';

interface ProductStatusHistoryProps {
    productId: number;
}

const ProductStatusHistoryComponent = ({ productId }: ProductStatusHistoryProps) => {
    const [history, setHistory] = useState<ProductStatusHistory[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const statusTextMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: '待审核',
        [ProductStatus.APPROVED]: '已批准',
        [ProductStatus.REJECTED]: '已驳回',
        [ProductStatus.DELISTED]: '已下架',
    };

    const statusColorMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: 'bg-yellow-500',
        [ProductStatus.APPROVED]: 'bg-green-500',
        [ProductStatus.REJECTED]: 'bg-red-500',
        [ProductStatus.DELISTED]: 'bg-gray-500',
    };

    useEffect(() => {
        const fetchHistory = async () => {
            try {
                const data = await getProductStatusHistory(productId);
                setHistory(data || []);
            } catch (error) {
                console.error('Failed to fetch product status history:', error);
                setHistory([]); // 设置为空数组以防止渲染错误
            } finally {
                setIsLoading(false);
            }
        };

        if (productId) {
            fetchHistory();
        }
    }, [productId]);

    if (isLoading) {
        return (
            <div className="space-y-4">
                <h3 className="font-semibold text-lg mb-4">状态变更历史</h3>
                {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-start space-x-4 p-4 border rounded-lg">
                        <Skeleton className="h-6 w-6 rounded-full" />
                        <div className="flex-1 space-y-2">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-3 w-48" />
                            <Skeleton className="h-3 w-24" />
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    if (history.length === 0) {
        return (
            <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无状态变更历史</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <h3 className="font-semibold text-lg mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                状态变更历史
            </h3>
            <div className="space-y-3">
                {history.map((item, index) => (
                    <div key={item.id} className="relative">
                        {/* Timeline line */}
                        {index < history.length - 1 && (
                            <div className="absolute left-6 top-12 w-0.5 h-16 bg-border" />
                        )}
                        
                        <div className="flex items-start space-x-4 p-4 bg-card/30 border border-border/50 rounded-lg">
                            <div className="flex-shrink-0 mt-1">
                                <div className={`h-3 w-3 rounded-full ${statusColorMap[item.newStatus]}`} />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-2">
                                    {item.oldStatus && (
                                        <>
                                            <Badge variant="outline" className="text-xs">
                                                {statusTextMap[item.oldStatus]}
                                            </Badge>
                                            <ArrowRight className="h-3 w-3 text-muted-foreground" />
                                        </>
                                    )}
                                    <Badge className={`${statusColorMap[item.newStatus]} text-white text-xs`}>
                                        {statusTextMap[item.newStatus]}
                                    </Badge>
                                </div>
                                
                                <div className="text-sm text-muted-foreground mb-2">
                                    <div className="flex items-center space-x-4">
                                        <span className="flex items-center">
                                            <User className="h-3 w-3 mr-1" />
                                            {item.changedBy}
                                        </span>
                                        <span className="flex items-center">
                                            <Clock className="h-3 w-3 mr-1" />
                                            {new Date(item.changedAt).toLocaleString()}
                                        </span>
                                    </div>
                                </div>
                                
                                {item.reason && (
                                    <div className="text-sm mb-1">
                                        <span className="font-medium text-muted-foreground">原因: </span>
                                        <span>{item.reason}</span>
                                    </div>
                                )}
                                
                                {item.notes && (
                                    <div className="text-sm">
                                        <span className="font-medium text-muted-foreground">备注: </span>
                                        <span className="text-muted-foreground">{item.notes}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ProductStatusHistoryComponent;
