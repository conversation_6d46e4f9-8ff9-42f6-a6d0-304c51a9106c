package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user")
public class User {
    private Long id;
    private String username;
    private String password;
    private String email;
    private String role;
    @TableField("serial_number")
    private String serialNumber;
    @TableField("avatar_url")
    private String avatarUrl;
    @TableField("banner_url")
    private String bannerUrl;
    @TableField("avatar_version")
    private Integer avatarVersion;
    @TableField("banner_version")
    private Integer bannerVersion;
    @TableField("created_at")
    private Timestamp createdAt;
    @TableField("enabled")
    private boolean enabled;
    @TableField(exist = false)
    @JsonProperty("isAdmin")
    private boolean isAdmin;
    @TableField(exist = false)
    @JsonProperty("isDeveloper")
    private boolean isDeveloper;
    @TableField("alipay_account")
    private String alipayAccount;
    @TableField("wechat_account")
    private String wechatAccount;
} 