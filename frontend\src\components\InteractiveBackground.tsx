'use client';

import React, { useRef, Suspense, useMemo, useEffect } from 'react';
import { Canvas, extend, use<PERSON>rame, MaterialNode } from '@react-three/fiber';
import { Stars, shaderMaterial } from '@react-three/drei';
// import { EffectComposer, Bloom } from '@react-three/postprocessing';
import * as THREE from 'three';

// By declaring our custom materials in the ThreeElements interface, we make them
// known to TypeScript and can use them in JSX without @ts-ignore.
declare module '@react-three/fiber' {
  interface ThreeElements {
    abyssMaterial: MaterialNode<THREE.ShaderMaterial, typeof AbyssMaterial>;
    circuitLineMaterial: MaterialNode<THREE.ShaderMaterial, typeof CircuitLineMaterial>;
  }
}

// "Abyss Eye" Shader
const AbyssMaterial = shaderMaterial(
  { time: 0 },
  `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  `
    uniform float time;
    varying vec2 vUv;
    
    vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
    vec2 mod289(vec2 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
    vec3 permute(vec3 x) { return mod289(((x*34.0)+1.0)*x); }

    float snoise(vec2 v) {
        const vec4 C = vec4(0.211324865405187, 0.366025403784439, -0.577350269189626, 0.024390243902439);
        vec2 i  = floor(v + dot(v, C.yy) );
        vec2 x0 = v -   i + dot(i, C.xx);
        vec2 i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
        vec4 x12 = x0.xyxy + C.xxzz;
        x12.xy -= i1;
        i = mod289(i);
        vec3 p = permute( permute( i.y + vec3(0.0, i1.y, 1.0 )) + i.x + vec3(0.0, i1.x, 1.0 ));
        vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
        m = m*m;
        m = m*m;
        vec3 x = 2.0 * fract(p * C.www) - 1.0;
        vec3 h = abs(x) - 0.5;
        vec3 ox = floor(x + 0.5);
        vec3 a0 = x - ox;
        m *= 1.79284291400159 - 0.85373472095314 * ( a0*a0 + h*h );
        vec3 g;
        g.x  = a0.x  * x0.x  + h.x  * x0.y;
        g.yz = a0.yz * x12.xz + h.yz * x12.yw;
        return 130.0 * dot(m, g);
            }

    void main() {
        vec2 uv = vUv - 0.5;
        float t = time * 0.1;
        float noise = snoise(uv * 2.0 + t);
        noise += snoise(uv * 4.0 - t * 1.5) * 0.5;
        float d = distance(vUv, vec2(0.5));
        float iris = smoothstep(0.3, 0.2, d) * (1.0 - smoothstep(0.2, 0.0, d));
        iris *= 0.7 + 0.3 * sin(time * 0.3 - 1.5);
        vec3 dark_purple = vec3(0.1, 0.0, 0.2);
        vec3 glowing_purple = vec3(0.6, 0.2, 0.8);
        vec3 color = mix(dark_purple, glowing_purple, noise * 0.2);
        color = mix(color, glowing_purple, iris);
        gl_FragColor = vec4(color, 1.0);
    }
  `
);
extend({ AbyssMaterial });

const CircuitLineMaterial = shaderMaterial(
  { time: 0 },
  `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  `
    uniform float time;
    varying vec2 vUv;

    // Pseudo-random generator
    vec2 hash( vec2 p ) {
        p = vec2( dot(p,vec2(127.1,311.7)), dot(p,vec2(269.5,183.3)) );
        return -1.0 + 2.0 * fract(sin(p)*43758.5453123);
            }
            
    // Voronoi noise returning F1 and F2 distances
    vec2 voronoi( in vec2 x ) {
        vec2 p = floor( x );
        vec2 f = fract( x );

        vec2 res = vec2( 8.0 );
        for( int j=-1; j<=1; j++ ) {
            for( int i=-1; i<=1; i++ ) {
                vec2 b = vec2( float(i), float(j) );
                vec2 r = vec2( b ) - f + hash( p + b );
                float d = dot( r, r );
                
                if( d < res.x ) {
                    res.y = res.x;
                    res.x = d;
                } else if( d < res.y ) {
                    res.y = d;
                }
            }
        }
        return sqrt( res );
            }

    void main() {
        // Use a different pattern for each cycle to make it look like "new" lines are generated
        float cycle_duration = 6.0;
        float time_in_cycle = mod(time, cycle_duration);
        float cycle_index = floor(time / cycle_duration);

        vec2 uv = vUv * 6.0; // Control density - reduced from 8.0
        uv.x += cycle_index * 5.37; // Offset pattern for each new cycle

        vec2 v = voronoi(uv);
        
        // Create sharp lines from the difference between the two closest points
        float lines = 1.0 - smoothstep(0.0, 0.05, v.y - v.x);
        
        if (lines < 0.1) {
            discard;
        }
        
        // Animation: fade in fast, stay, fade out slow
        float intensity = 0.0;
        if (time_in_cycle < 1.0) {
            intensity = time_in_cycle; // Fade in
        } else if (time_in_cycle < 4.0) { // Stay longer
            intensity = 1.0; // Stay
        } else if (time_in_cycle < 6.0) {
            intensity = 1.0 - (time_in_cycle - 4.0) / 2.0; // Fade out
        }

        vec3 color = vec3(0.0, 0.8, 0.8); // Slightly brighter Cyan color

        gl_FragColor = vec4(color, lines * intensity);
    }
  `
);

extend({ CircuitLineMaterial });

const SceneConstants = {
  FOG: ['#000000', 15, 30] as [string, number, number],
  POINT_LIGHT_COLOR: '#6a0dad',
  POINT_LIGHT_INTENSITY: 2.5,
  ABYSS_SPHERE_ARGS: [3.5, 32, 32] as [number, number, number],
  CIRCUIT_SPHERE_ARGS: [3.51, 32, 32] as [number, number, number],
  RING_1_GEOMETRY: [5.0, 0.12, 16, 100] as [number, number, number, number],
  RING_2_GEOMETRY: [5.8, 0.12, 16, 100] as [number, number, number, number],
  RING_MATERIAL_PROPS: {
    color: '#666666',
    metalness: 0.6,
    roughness: 0.4,
    emissive: '#666666',
    emissiveIntensity: 0.5,
  },
  ROTATION_SPEED: {
    CENTRAL_SYSTEM_Y: 0.05,
    CENTRAL_SYSTEM_X: 0.02,
    STARS_Y: 0.01,
    RING_1_ORBIT: 0.12,
    RING_1_SPIN: 0.05,
    RING_2_ORBIT: 0.08,
    RING_2_SPIN: 0.05,
  },
  STARS_PROPS: {
    radius: 200,
    depth: 50,
    count: 8000,
    factor: 4,
    saturation: 0,
    fade: true,
    speed: 1,
  },
  BLOOM_PROPS: {
    luminanceThreshold: 0.05,
    luminanceSmoothing: 0.0,
    height: 400,
    intensity: 0.5,
  },
};

function ShootingStars({ count = 200 }) {
    const pointsRef = useRef<THREE.Points>(null!);
    const lastBurstTime = useRef(-10); // Start with a burst on load
  
    const particles = useMemo(() => {
        const arr = new Array(count).fill(0).map(() => ({
            life: 0,
            velocity: new THREE.Vector3(),
        }));
        return arr;
    }, [count]);
  
    const positions = useMemo(() => {
        const arr = new Float32Array(count * 3);
        // Start all stars off-screen
        arr.fill(10000); 
        return arr;
    }, [count]);

    const resetParticle = (i: number) => {
        const p = particles[i];
        
        p.life = THREE.MathUtils.randFloat(3, 6); // Lives for 3-6 seconds
        
        const startPos = new THREE.Vector3().randomDirection().multiplyScalar(THREE.MathUtils.randFloat(60, 100));
        positions[i * 3] = startPos.x;
        positions[i * 3 + 1] = startPos.y;
        positions[i * 3 + 2] = startPos.z;

        // Aim for a point near the origin for a "fly-by" effect, not directly at it
        const target = new THREE.Vector3().randomDirection().multiplyScalar(THREE.MathUtils.randFloat(10, 20));

        const speed = THREE.MathUtils.randFloat(20, 30);
        p.velocity.subVectors(target, startPos).normalize().multiplyScalar(speed);
    };

    useFrame((state, delta) => {
        if (!pointsRef.current) return;
        const time = state.clock.getElapsedTime();
        const geom = pointsRef.current.geometry;
        const posAttr = geom.attributes.position as THREE.BufferAttribute;
    
        // Update living particles
        for (let i = 0; i < count; i++) {
            const p = particles[i];
    
            if (p.life > 0) {
                p.life -= delta;
        
                positions[i * 3] += p.velocity.x * delta;
                positions[i * 3 + 1] += p.velocity.y * delta;
                positions[i * 3 + 2] += p.velocity.z * delta;

            } else {
                // Hide dead particles until they are re-spawned
                positions[i * 3] = 10000;
            }
        }
        
        // Trigger a new burst every 10 seconds
        if (time - lastBurstTime.current > 10) {
            lastBurstTime.current = time;
            const burstSize = THREE.MathUtils.randInt(40, 80);
            let activated = 0;
            for (let i = 0; i < count && activated < burstSize; i++) {
                // Find a dead particle to activate
                if (particles[i].life <= 0) {
                    resetParticle(i);
                    activated++;
                }
            }
        }

        posAttr.array.set(positions);
        posAttr.needsUpdate = true;
    });
  
    return (
        <points ref={pointsRef}>
            <bufferGeometry>
                <bufferAttribute
                    attach="attributes-position"
                    count={count}
                    array={positions}
                    itemSize={3}
                    usage={THREE.DynamicDrawUsage}
                />
            </bufferGeometry>
            <pointsMaterial size={0.2} color="#ffffff" transparent opacity={0.8} fog={false} />
        </points>
    );
}

function Scene() {
  const centralSystemRef = useRef<THREE.Group>(null!);
  const starsRef = useRef<THREE.Group>(null!);
  const ring1GroupRef = useRef<THREE.Group>(null!);
  const ring2GroupRef = useRef<THREE.Group>(null!);
  const abyssMatRef = useRef<THREE.ShaderMaterial>(null);
  const ringMaterialRef = useRef<THREE.Shader>(null!);
  const circuitMatRef = useRef<THREE.ShaderMaterial>(null);

  const ringMaterial = useMemo(() => {
    const material = new THREE.MeshStandardMaterial(SceneConstants.RING_MATERIAL_PROPS);

    material.onBeforeCompile = (shader) => {
        shader.uniforms.time = { value: 0 };

        shader.vertexShader = 'varying vec2 vUv;\n' + shader.vertexShader;
        shader.vertexShader = shader.vertexShader.replace(
            '#include <begin_vertex>',
            '#include <begin_vertex>\nvUv = uv;'
        );

        shader.fragmentShader = `
            uniform float time;
            varying vec2 vUv;
            // A simple but effective noise function
            float snoise(vec3 co){
                return fract(sin(dot(co, vec3(12.9898, 78.233, 151.7182))) * 43758.5453);
            }
        ` + shader.fragmentShader;

        shader.fragmentShader = shader.fragmentShader.replace(
            '#include <roughnessmap_fragment>',
            `
            float roughnessFactor = roughness;
            #ifdef USE_ROUGHNESSMAP
                vec4 texelRoughness = texture2D( roughnessMap, vUv );
                roughnessFactor *= texelRoughness.g;
            #endif
            
            float noise = snoise(vec3(vUv * 50.0, time * 0.2));
            // Modulate roughness to create a weathered, non-uniform surface
            roughnessFactor = mix(roughnessFactor * 0.5, roughnessFactor * 1.5, noise);
            roughnessFactor = clamp(roughnessFactor, 0.0, 1.0);
            `
        );
        
        shader.fragmentShader = shader.fragmentShader.replace(
            '#include <emissivemap_fragment>',
            `
            #include <emissivemap_fragment>
            float emissiveNoise = snoise(vec3(vUv * 30.0, time * -0.1));
            // Make the emissive light flicker and flow like captured energy
            totalEmissiveRadiance *= mix(0.3, 1.0, emissiveNoise);
            `
        );

        ringMaterialRef.current = shader;
    };

    return material;
    }, []);

  useFrame((state, delta) => {
    const t = state.clock.getElapsedTime();
    if (abyssMatRef.current) {
      abyssMatRef.current.uniforms.time.value = t;
    }
    if (ringMaterialRef.current) {
      ringMaterialRef.current.uniforms.time.value = t;
    }
    if (circuitMatRef.current) {
      circuitMatRef.current.uniforms.time.value = t;
    }
    const speeds = SceneConstants.ROTATION_SPEED;
    if (centralSystemRef.current) {
      centralSystemRef.current.rotation.y += delta * speeds.CENTRAL_SYSTEM_Y;
      centralSystemRef.current.rotation.x += delta * speeds.CENTRAL_SYSTEM_X;
    }
    if (starsRef.current) {
      starsRef.current.rotation.y -= delta * speeds.STARS_Y;
    }
    if (ring1GroupRef.current) {
      ring1GroupRef.current.rotation.y += delta * speeds.RING_1_ORBIT;
      ring1GroupRef.current.children[0].rotation.x += delta * speeds.RING_1_SPIN;
    }
    if (ring2GroupRef.current) {
      ring2GroupRef.current.rotation.y -= delta * speeds.RING_2_ORBIT;
      ring2GroupRef.current.children[0].rotation.x -= delta * speeds.RING_2_SPIN;
    }
  });

  return (
    <>
      <color attach="background" args={['#000000']} />
      <fog attach="fog" args={SceneConstants.FOG} />
      <pointLight position={[0, 0, 0]} intensity={SceneConstants.POINT_LIGHT_INTENSITY} color={SceneConstants.POINT_LIGHT_COLOR} />
      
      <group 
        ref={centralSystemRef}
      >
        <mesh>
            <sphereGeometry args={SceneConstants.ABYSS_SPHERE_ARGS} />
            <abyssMaterial ref={abyssMatRef} key={AbyssMaterial.key} />
        </mesh>
        <mesh>
            <sphereGeometry args={SceneConstants.CIRCUIT_SPHERE_ARGS} />
            <circuitLineMaterial ref={circuitMatRef} key={CircuitLineMaterial.key} transparent={true} />
        </mesh>
      </group>

      <group ref={ring1GroupRef}>
        <mesh rotation-x={Math.PI / 2} material={ringMaterial}>
            <torusGeometry args={SceneConstants.RING_1_GEOMETRY} />
        </mesh>
      </group>
      <group ref={ring2GroupRef}>
        <mesh rotation-x={Math.PI / 2} material={ringMaterial}>
            <torusGeometry args={SceneConstants.RING_2_GEOMETRY} />
        </mesh>
      </group>
      
      <group ref={starsRef}>
        <Stars {...SceneConstants.STARS_PROPS} />
      </group>
      
      <ShootingStars count={50} />
    </>
  );
}

const InteractiveBackground: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  if (!isVisible) {
    return null;
  }
  
  return (
    <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', zIndex: -1 }}>
      <Suspense fallback={null}>
        <Canvas camera={{ position: [0, 0, 15], fov: 75 }}>
          <Scene />
        </Canvas>
      </Suspense>
    </div>
  );
};

export default InteractiveBackground;
