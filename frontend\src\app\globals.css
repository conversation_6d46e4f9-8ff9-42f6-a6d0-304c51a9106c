@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Custom prose styles to fix image link behavior */
  .prose a:has(> img) {
    @apply inline-block leading-none;
  }

  /* 强制防穿模样式 - 用于评论内容 */
  .comment-content-container {
    min-width: 0;
    width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: anywhere;
    word-break: break-all;
  }

  .comment-content-container * {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: anywhere !important;
    word-break: break-all !important;
  }

  .comment-content-container pre {
    white-space: pre-wrap !important;
    overflow-x: auto !important;
    max-width: 100% !important;
  }

  .comment-content-container code {
    word-break: break-all !important;
    overflow-wrap: anywhere !important;
  }

  .comment-content-container a {
    word-break: break-all !important;
    overflow-wrap: anywhere !important;
    display: inline-block !important;
    max-width: 100% !important;
  }

  .comment-content-container table {
    table-layout: fixed !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 文本行数限制样式 */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 对话列表防穿模样式 */
  .conversation-item-container {
    min-width: 0;
    width: 100%;
    overflow: hidden;
  }

  .conversation-item-container * {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  .comment-content-container td,
  .comment-content-container th {
    word-break: break-all !important;
    overflow-wrap: anywhere !important;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 消息系统布局优化 */
  .message-system-container {
    contain: layout style;
  }

  .message-system-left-panel {
    contain: layout style;
    overflow: hidden;
  }

  .message-system-right-panel {
    contain: layout style;
    overflow: hidden;
  }

  /* 防止文本溢出 */
  .text-truncate-safe {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    word-wrap: break-word;
  }
}

@layer base {
  :root {
    --header-height: 64px; /* Or whatever your header height is */

    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
 
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
 
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
 
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 224 71% 4%;
    --foreground: 0 0% 98%;
 
    --card: 220 10% 15%;
    --card-foreground: 0 0% 98%;
 
    --popover: 220 10% 10%;
    --popover-foreground: 0 0% 98%;
 
    --primary: 0 0% 98%;
    --primary-foreground: 220 8% 20%;
 
    --secondary: 220 10% 25%;
    --secondary-foreground: 0 0% 98%;
 
    --muted: 220 10% 25%;
    --muted-foreground: 220 5% 65%;
 
    --accent: 220 10% 25%;
    --accent-foreground: 0 0% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }

  body {
    @apply bg-background text-foreground;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .text-shadow-glow {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 
                 0 0 20px rgba(255, 255, 255, 0.4);
  }

  @keyframes subtle-glow {
    0%, 100% {
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.1),
                   0 0 20px rgba(255, 255, 255, 0.1);
    }
    50% {
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
                   0 0 20px rgba(255, 255, 255, 0.2);
    }
  }

  .animate-subtle-glow {
    animation: subtle-glow 6s ease-in-out infinite;
  }

  .text-gradient-shine {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-slate-200 via-slate-50 to-slate-200;
    animation: shine-effect 5s linear infinite;
    background-size: 200% auto;
  }

  @keyframes shine-effect {
    to {
      background-position: -200% center;
    }
  }

  .text-glow {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
  }

  .animate-shine {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent) no-repeat;
    background-size: 200% 100%;
    background-position: -200% 0;
    -webkit-background-clip: text;
    background-clip: text;
    animation: shine 5s infinite linear;
  }

  @keyframes shine {
    to {
      background-position: 200% 0;
    }
  }

  .title-with-shine {
    /* This uses two background layers:
       1. The top layer is the moving shine effect.
       2. The bottom layer is the static gradient for the text color. */
    background-image: 
      linear-gradient(
        100deg,
        rgba(255, 255, 255, 0) 10%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 90%
      ),
      linear-gradient(to bottom right, #fafafa, #a1a1aa); /* from-zinc-50 to to-zinc-400 */

    background-size: 200% 100%, 100% 100%;
    background-repeat: no-repeat;
    
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    
    animation: title-shine-bg 4s ease-in-out infinite;
  }

  @keyframes title-shine-bg {
    0% {
      background-position: -200% 0, 0 0;
    }
    60%, 100% {
      background-position: 200% 0, 0 0;
    }
  }

  .bg-circuit-board {
    background-color: #000000;
    background-image:
      linear-gradient(30deg, rgba(255,255,255,0.02) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.02) 87.5%, transparent),
      linear-gradient(-30deg, rgba(255,255,255,0.02) 12%, transparent 12.5%, transparent 87%, rgba(255,255,255,0.02) 87.5%, transparent),
      linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px),
      radial-gradient(circle at center, rgba(255,255,255,0.05) 1px, transparent 1.5px);
    background-size: 40px 70px, 40px 70px, 40px 70px, 10px 10px;
  }

  @keyframes subtle-zoom {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }

  .animate-subtle-zoom {
    animation: subtle-zoom 25s ease-in-out infinite;
  }

  .bg-tavern-scene {
    background-image: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('/images/background/Pubs.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .bg-parchment {
    background-color: #fdf6e3; /* A creamy parchment color */
    background-image:
        linear-gradient(rgba(226, 212, 186, 0.3) .1em, transparent .1em),
        linear-gradient(.1em, rgba(226, 212, 186, 0.3) .1em, transparent .1em);
    background-size: 3em 3em;
    color: #4f422e; /* A dark brown for text, like ink */
  }

  .journal-scrollbar::-webkit-scrollbar {
    width: 10px;
  }
  .journal-scrollbar::-webkit-scrollbar-track {
    background: #e2d4ba; /* A slightly darker parchment for the track */
  }
  .journal-scrollbar::-webkit-scrollbar-thumb {
    background-color: #8a6c4a; /* A woody, leather-like color for the thumb */
    border-radius: 4px;
    border: 2px solid #e2d4ba;
  }
  .journal-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #6b533a;
  }
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-oxanium), sans-serif;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-ExtraLight.woff') format('woff');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-SemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Oxanium';
  src: url('/fonts/Oxanium-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

.title-frame {
  position: relative;
  padding: 1.5rem 2.5rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 1400px; /* Constrain width for better look */
  border: 1px solid rgba(200, 225, 255, 0.1);
  background: rgba(10, 20, 42, 0.25);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: inset 0 0 25px rgba(0, 5, 10, 0.6);
}

.glass-pane {
  border: 1px solid rgba(200, 225, 255, 0.08);
  background: rgba(10, 20, 42, 0.2);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  transition: all 0.3s ease;
}

.glass-pane:hover {
  background: rgba(20, 30, 52, 0.4);
  border-color: rgba(200, 225, 255, 0.15);
}

/* This empty span is a helper to create the other two corners */
.title-frame-corners {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Base styles for all four corner pieces */
.title-frame::before,
.title-frame::after,
.title-frame-corners::before,
.title-frame-corners::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 25px;
  border-color: rgba(200, 225, 255, 0.7);
  border-style: solid;
  transition: all 0.3s ease-in-out;
}

/* Top-left corner */
.title-frame::before {
  top: 10px;
  left: 10px;
  border-width: 2px 0 0 2px;
}

/* Bottom-right corner */
.title-frame::after {
  bottom: 10px;
  right: 10px;
  border-width: 0 2px 2px 0;
}

/* Top-right corner */
.title-frame-corners::before {
  top: 10px;
  right: 10px;
  border-width: 2px 2px 0 0;
}

/* Bottom-left corner */
.title-frame-corners::after {
  bottom: 10px;
  left: 10px;
  border-width: 0 0 2px 2px;
}

@keyframes title-shine-bg {
  0% {
    background-position: -200% 0, 0 0;
  }
  60%, 100% {
    background-position: 200% 0, 0 0;
  }
}
