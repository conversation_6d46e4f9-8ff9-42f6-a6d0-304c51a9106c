'use client';

import React, { useState, useEffect } from "react";
import Header from "@/components/Header";
import apiService from "@/services/api";
import { Product } from "@/types/Product";
import ProductDetailModal from "@/components/ProductDetailModal";
import { useAuth } from "@/contexts/AuthContext";
import { getPurchasedProducts } from "@/services/api";
import StackedProductCard from "@/components/StackedProductCard";
import ProductsAllBackground from "@/components/ProductsAllBackground";
import { motion } from "framer-motion";
import { toast } from "sonner";

const AllProductsPage = () => {
    const { isAuthenticated } = useAuth();
    const [products, setProducts] = useState<Product[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [isDetailModalOpen, setDetailModalOpen] = useState(false);
    const [purchasedProductIds, setPurchasedProductIds] = useState<Set<number>>(new Set());
    const [activeIndex, setActiveIndex] = useState<number | null>(null);

    useEffect(() => {
        const fetchProducts = async () => {
            try {
                const response = await apiService.get('/api/products', { authRequired: false });
                const formattedProducts = response.data.map((p: any) => ({
                    ...p,
                    imageUrl: p.imageUrl && p.imageUrl.startsWith('http')
                        ? p.imageUrl
                        : p.imageUrl
                            ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}${p.imageUrl}`
                            : '/images/placeholder.webp',
                    downloadUrl: p.downloadUrl && !p.downloadUrl.startsWith('http')
                        ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}${p.downloadUrl}`
                        : p.downloadUrl
                }));
                setProducts(formattedProducts);
            } catch (err: any) {
                setError(err.message || 'Failed to fetch products.');
            } finally {
                setLoading(false);
            }
        };

        const fetchPurchased = async () => {
            if (isAuthenticated) {
                try {
                    const purchased = await getPurchasedProducts();
                    setPurchasedProductIds(new Set(purchased.map(p => p.id)));
                } catch (err) {
                    console.error("Failed to fetch purchased products:", err);
                }
            }
        };

        fetchProducts();
        fetchPurchased();
    }, [isAuthenticated]);

    useEffect(() => {
        // Set initial active card to the middle one for a symmetrical look
        if (products.length > 0 && activeIndex === null) {
            setActiveIndex(Math.floor(products.length / 2));
        }
    }, [products, activeIndex]);

    const handlePurchaseSuccess = async () => {
        if (isAuthenticated) {
            try {
                const purchased = await getPurchasedProducts();
                setPurchasedProductIds(new Set(purchased.map(p => p.id)));
                // toast.success("您的购买记录已刷新！"); // Removed this line for silent refresh
            } catch (err) {
                console.error("Failed to re-fetch purchased products:", err);
                toast.error("刷新购买记录失败，请手动刷新页面。");
            }
        }
    };

    const handlePurchaseClick = (product: Product) => {
        setSelectedProduct(product);
        setDetailModalOpen(true);
    };

    const handleCloseDetailModal = () => {
        setDetailModalOpen(false);
        setSelectedProduct(null);
    };

    return (
        <div className="min-h-screen text-gray-300 font-sans overflow-hidden relative">
            {/* Enhanced Background with Parallax and Blur */}
            <div className="fixed inset-0 -z-10">
                <ProductsAllBackground />
            </div>

            {/* Semi-transparent blur overlay */}
            <div className="fixed inset-0 bg-black/30 backdrop-blur-sm -z-5"></div>

            <Header />
            <main className="container mx-auto px-4 py-8 flex flex-col items-center justify-center relative z-10">
                <h1 className="text-4xl font-bold text-center my-12 text-gray-100">所有宝藏</h1>
                {loading ? (
                    <div className="text-center">正在加载...</div>
                ) : error ? (
                    <div className="text-center text-red-500">加载失败: {error}</div>
                ) : (
                    <div className="relative w-full h-[600px] flex justify-center items-center">
                        {products.map((product, index) => {
                            const isActive = activeIndex === index;
                            return (
                            <motion.div
                                key={product.id}
                                    className="absolute cursor-pointer"
                                    onMouseEnter={() => setActiveIndex(index)}
                                    style={{ transform: 'translateZ(0)' }}
                                animate={{
                                        x: (index - (products.length - 1) / 2) * 60,
                                        y: isActive ? -20 : 0,
                                        scale: isActive ? 1.05 : 1,
                                        zIndex: isActive ? products.length + 1 : products.length - index,
                                }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <StackedProductCard
                                    product={product}
                                    onPurchaseClick={handlePurchaseClick}
                                    isOwned={purchasedProductIds.has(product.id)}
                                />
                            </motion.div>
                            );
                        })}
                    </div>
                )}
            </main>
            {isDetailModalOpen && (
                <ProductDetailModal
                    isOpen={isDetailModalOpen}
                    onClose={handleCloseDetailModal}
                    product={selectedProduct}
                    onPurchaseSuccess={handlePurchaseSuccess}
                />
            )}
        </div>
    );
};

export default AllProductsPage; 