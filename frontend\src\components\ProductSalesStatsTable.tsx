'use client';

import React from 'react';
import { ProductSalesStats } from '@/types/ProductSalesStats';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { Package, TrendingUp, DollarSign } from 'lucide-react';

interface ProductSalesStatsTableProps {
    stats: ProductSalesStats[];
    loading?: boolean;
}

const ProductSalesStatsTable: React.FC<ProductSalesStatsTableProps> = ({ stats, loading }) => {
    const formatCurrency = (amount: number | undefined | null) => {
        if (amount === undefined || amount === null) return '¥0.00';
        return `¥${amount.toFixed(2)}`;
    };

    if (loading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        产品销售统计
                    </CardTitle>
                    <CardDescription>查看每个产品的销售数量和收益详情</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center h-32">
                        <div className="text-muted-foreground">加载中...</div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (!stats || stats.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        产品销售统计
                    </CardTitle>
                    <CardDescription>查看每个产品的销售数量和收益详情</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center h-32">
                        <div className="text-muted-foreground">暂无销售数据</div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    产品销售统计
                </CardTitle>
                <CardDescription>查看每个产品的销售数量和收益详情</CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>产品</TableHead>
                            <TableHead>价格</TableHead>
                            <TableHead className="text-center">销售数量</TableHead>
                            <TableHead className="text-right">总收入</TableHead>
                            <TableHead className="text-right">我的收益</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {stats.map((stat) => (
                            <TableRow key={stat.productId}>
                                <TableCell>
                                    <div className="flex items-center gap-3">
                                        <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                                            {stat.productImageUrl ? (
                                                <Image
                                                    src={stat.productImageUrl}
                                                    alt={stat.productName}
                                                    fill
                                                    className="object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full flex items-center justify-center">
                                                    <Package className="w-6 h-6 text-gray-400" />
                                                </div>
                                            )}
                                        </div>
                                        <div>
                                            <div className="font-medium text-sm">{stat.productName}</div>
                                            <div className="text-xs text-muted-foreground">ID: {stat.productId}</div>
                                        </div>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="font-medium">{formatCurrency(stat.productPrice)}</div>
                                </TableCell>
                                <TableCell className="text-center">
                                    <Badge variant={stat.salesCount > 0 ? "default" : "secondary"} className="flex items-center gap-1 w-fit mx-auto">
                                        <TrendingUp className="w-3 h-3" />
                                        {stat.salesCount}
                                    </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                    <div className="font-medium text-green-600">
                                        {formatCurrency(stat.totalRevenue)}
                                    </div>
                                </TableCell>
                                <TableCell className="text-right">
                                    <div className="font-medium text-blue-600 flex items-center justify-end gap-1">
                                        <DollarSign className="w-4 h-4" />
                                        {formatCurrency(stat.developerRevenue)}
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                
                {/* Summary Row */}
                <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between items-center text-sm">
                        <div className="font-medium">
                            总计: {stats.length} 个产品
                        </div>
                        <div className="flex gap-6">
                            <div className="text-muted-foreground">
                                总销量: <span className="font-medium text-foreground">
                                    {stats.reduce((sum, stat) => sum + stat.salesCount, 0)}
                                </span>
                            </div>
                            <div className="text-muted-foreground">
                                总收益: <span className="font-medium text-blue-600">
                                    {formatCurrency(stats.reduce((sum, stat) => sum + stat.developerRevenue, 0))}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ProductSalesStatsTable;
