package com.kitolus.community.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kitolus.community.entity.User;
import com.kitolus.community.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.security.authentication.DisabledException;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserMapper userMapper;

    @Autowired
    public UserDetailsServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user;
        if (username.contains("@")) {
            user = userMapper.findByEmail(username);
        } else {
            user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        }
        
        if (user == null) {
            throw new UsernameNotFoundException("User not found with identifier: " + username);
        }

        if (!user.isEnabled()) {
            throw new DisabledException("User account is disabled");
        }

        // Grant authorities based on user role
        // This allows for more complex role strings if needed in the future e.g., "ADMIN,USER"
        String role = user.getRole();
        List<SimpleGrantedAuthority> authorities;
        if (role != null && !role.isEmpty()) {
            authorities = Stream.of(role.split(","))
                    .map(r -> new SimpleGrantedAuthority(r.trim()))
                    .collect(Collectors.toList());
        } else {
            // Failsafe: if role is null or empty, grant default USER role to prevent crashes.
            authorities = Collections.singletonList(new SimpleGrantedAuthority("USER"));
        }

        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(),
                user.isEnabled(),
                true,
                true,
                true,
                authorities
        );
    }
} 