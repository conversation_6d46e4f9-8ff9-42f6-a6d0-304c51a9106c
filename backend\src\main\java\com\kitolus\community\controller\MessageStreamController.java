package com.kitolus.community.controller;

import com.kitolus.community.util.JwtTokenUtil;
import com.kitolus.community.service.UserService;

import com.kitolus.community.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 消息流控制器 - 提供Server-Sent Events支持
 * 用于实时消息推送，作为WebSocket的替代方案
 */
@RestController
@RequestMapping("/api/message-stream")
@CrossOrigin(origins = {"https://kitolus.top", "http://localhost:3000"})
public class MessageStreamController {

    private static final Logger logger = LoggerFactory.getLogger(MessageStreamController.class);
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Autowired
    private UserService userService;

    // 存储活跃的SSE连接
    private final Map<Long, SseEmitter> activeConnections = new ConcurrentHashMap<>();
    
    // 心跳调度器
    private final ScheduledExecutorService heartbeatScheduler = Executors.newScheduledThreadPool(1);
    
    public MessageStreamController() {
        // 启动心跳机制
        startHeartbeat();
    }

    /**
     * 建立SSE连接
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamMessages(
            @RequestParam(required = false) String token,
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            // 验证token - 支持查询参数或Authorization header
            String actualToken = token;
            if (actualToken == null && authHeader != null) {
                actualToken = authHeader.replace("Bearer ", "");
            }

            if (actualToken == null) {
                logger.warn("No token provided for SSE connection");
                return null;
            }

            String username;
            try {
                username = jwtTokenUtil.getUsernameFromToken(actualToken);
                if (username == null) {
                    logger.warn("Invalid token for SSE connection: {}", actualToken);
                    return null;
                }
            } catch (Exception e) {
                logger.warn("Invalid token for SSE connection: {}", actualToken, e);
                return null;
            }
            User user = userService.findByUsername(username);
            
            if (user == null) {
                logger.warn("User not found for SSE connection: {}", username);
                return null;
            }
            
            // 创建SSE连接
            SseEmitter emitter = new SseEmitter(Long.MAX_VALUE); // 无超时
            Long userId = user.getId();
            
            // 存储连接
            activeConnections.put(userId, emitter);

            // 设置连接完成和超时处理
            emitter.onCompletion(() -> {
                activeConnections.remove(userId);
                logger.info("SSE connection completed for user: {}", userId);
            });

            emitter.onTimeout(() -> {
                activeConnections.remove(userId);
                logger.info("SSE connection timeout for user: {}", userId);
            });

            emitter.onError((ex) -> {
                activeConnections.remove(userId);
                logger.error("SSE connection error for user: {}", userId, ex);
            });
            
            // 发送连接确认消息
            try {
                emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("{\"type\":\"SYSTEM_MESSAGE\",\"data\":{\"content\":\"消息系统已连接 (SSE)\",\"timestamp\":\"" + 
                          java.time.Instant.now().toString() + "\"},\"timestamp\":" + System.currentTimeMillis() + "}"));
                
                logger.info("SSE connection established for user: {}", userId);
            } catch (IOException e) {
                logger.error("Failed to send connection confirmation", e);
                activeConnections.remove(userId);
                emitter.completeWithError(e);
            }
            
            return emitter;
            
        } catch (Exception e) {
            logger.error("Error establishing SSE connection", e);
            return null;
        }
    }

    // 轮询端点已移动到MessageController中，避免重复映射

    /**
     * 发送消息端点
     */
    @PostMapping("/send")
    public ResponseEntity<?> sendMessage(@RequestBody Map<String, Object> message,
                                       @RequestHeader("Authorization") String authHeader) {
        try {
            // 提取token
            String token = authHeader.replace("Bearer ", "");
            
            // 验证token
            String username;
            try {
                username = jwtTokenUtil.getUsernameFromToken(token);
                if (username == null) {
                    return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
                }
            } catch (Exception e) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }
            
            // 处理消息发送逻辑
            logger.info("Message sent by user {}: {}", user.getId(), message);

            // TODO: 广播消息（暂时禁用）
            logger.info("Message would be broadcasted: {}", message);
            
            return ResponseEntity.ok(Map.of(
                "status", "sent",
                "messageId", System.currentTimeMillis(),
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            logger.error("Error sending message", e);
            return ResponseEntity.status(500).body(Map.of("error", "Failed to send message"));
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<?> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "ok",
            "service", "message-stream",
            "activeConnections", activeConnections.size(),
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * 启动心跳机制
     */
    private void startHeartbeat() {
        heartbeatScheduler.scheduleAtFixedRate(() -> {
            if (!activeConnections.isEmpty()) {
                // TODO: 发送心跳消息（暂时禁用）
                logger.debug("Heartbeat for {} connections", activeConnections.size());
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
}
