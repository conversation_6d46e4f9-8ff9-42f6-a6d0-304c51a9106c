'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { sessionManager } from '@/lib/sessionManager';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

export default function TestSSOPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const [ssoStatus, setSSOStatus] = useState({
    isMainTab: false,
    tabId: '',
    allTabs: {} as any,
    lastEvent: null as any,
  });
  const [sessionStatus, setSessionStatus] = useState({
    isMonitoring: false,
    consecutiveFailures: 0,
    lastCheckTime: 0,
  });
  const [wsStatus, setWSStatus] = useState({
    isConnected: false,
    connectionState: 'disconnected',
  });

  useEffect(() => {
    // 监听简单SSO事件
    const handleSSOEvent = (event: any) => {
      setSSOStatus(prev => ({
        ...prev,
        lastEvent: event,
      }));
    };

    simpleSSO.on('logout', handleSSOEvent);
    simpleSSO.on('forceLogout', handleSSOEvent);
    simpleSSO.on('activeStatusChanged', handleSSOEvent);

    // 定期更新状态
    const interval = setInterval(() => {
      const ssoStatusData = simpleSSO.getStatus();
      setSSOStatus(prev => ({
        ...prev,
        isMainTab: ssoStatusData.isActive,
        tabId: ssoStatusData.tabId,
        allTabs: ssoStatusData.allTabs,
      }));

      setSessionStatus(sessionMonitor.getStatus());

      setWSStatus({
        isConnected: unifiedWebSocketManager.isConnected,
        connectionState: unifiedWebSocketManager.connectionState,
      });
    }, 1000);

    return () => {
      clearInterval(interval);
      simpleSSO.off('logout', handleSSOEvent);
      simpleSSO.off('forceLogout', handleSSOEvent);
      simpleSSO.off('activeStatusChanged', handleSSOEvent);
    };
  }, []);

  const handleTestLogout = () => {
    console.log('🧪 测试：主动登出');
    logout();
  };

  const handleTestSessionConflict = () => {
    console.log('🧪 测试：模拟会话冲突');
    ssoManager.broadcastSessionConflict();
  };

  const handleTestForceLogin = () => {
    console.log('🧪 测试：模拟新登录流程');

    // 模拟新登录：先强制登出其他标签页，然后触发登录流程
    console.log('🔄 步骤1：强制登出其他标签页');
    simpleSSO.forceLogoutOthers();

    // 模拟登录成功后的逻辑
    setTimeout(() => {
      console.log('🔄 步骤2：模拟登录成功，调用login函数');
      const currentToken = localStorage.getItem('jwt_token');
      if (currentToken && user) {
        // 直接调用login函数来模拟新登录
        login(currentToken);
      }
    }, 500);
  };

  const handleTestBroadcastLogout = () => {
    console.log('🧪 测试：广播登出事件');
    simpleSSO.broadcastLogout();
  };

  const handleTestDirectForceLogout = () => {
    console.log('🧪 测试：直接强制登出其他标签页');
    const ssoStatus = simpleSSO.getStatus();
    console.log('📋 当前SSO状态:', ssoStatus);

    // 如果用户信息为空，先设置用户信息
    if (user && token) {
      console.log('🔧 设置用户信息到SSO系统');
      simpleSSO.setUserInfo(user.id, token);
    }

    // 直接调用强制登出
    simpleSSO.forceLogoutOthers();
  };

  const handleForceReconnect = () => {
    const token = localStorage.getItem('jwt_token');
    if (token) {
      unifiedWebSocketManager.updateToken(token);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">SSO 系统测试页面</h1>
        <p className="text-muted-foreground mt-2">
          测试多标签页登录状态同步和WebSocket连接管理
        </p>
      </div>

      {/* 用户状态 */}
      <Card>
        <CardHeader>
          <CardTitle>用户认证状态</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <span>认证状态:</span>
            <Badge variant={isAuthenticated ? "default" : "destructive"}>
              {isAuthenticated ? "已登录" : "未登录"}
            </Badge>
          </div>
          {user && (
            <div className="space-y-2">
              <div>用户名: {user.username}</div>
              <div>用户ID: {user.id}</div>
              <div>角色: {user.role}</div>
            </div>
          )}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={handleTestLogout} variant="destructive" size="sm">
              测试登出
            </Button>
            <Button onClick={handleTestSessionConflict} variant="outline" size="sm">
              模拟会话冲突
            </Button>
            <Button onClick={handleTestForceLogin} variant="secondary" size="sm">
              模拟新登录
            </Button>
            <Button onClick={handleTestBroadcastLogout} variant="outline" size="sm">
              广播登出
            </Button>
            <Button onClick={handleTestDirectForceLogout} variant="destructive" size="sm">
              直接强制登出
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* SSO状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Badge variant={ssoStatus.isMainTab ? "default" : "secondary"}>
              {ssoStatus.isMainTab ? "主标签页" : "从标签页"}
            </Badge>
            简单SSO 状态
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between">
            <span>标签页ID:</span>
            <span className="text-sm font-mono">{ssoStatus.tabId}</span>
          </div>
          <div className="flex justify-between">
            <span>主标签页:</span>
            <Badge variant={ssoStatus.isMainTab ? "default" : "secondary"}>
              {ssoStatus.isMainTab ? "是" : "否"}
            </Badge>
          </div>
          <div className="flex justify-between">
            <span>活跃标签页数:</span>
            <span>{Object.keys(ssoStatus.allTabs).length}</span>
          </div>
          <div className="space-y-1">
            <span className="text-sm font-medium">所有标签页:</span>
            {Object.values(ssoStatus.allTabs).map((tab: any) => (
              <div key={tab.id} className="text-xs p-2 bg-muted rounded">
                <div>ID: {tab.id.substring(0, 20)}...</div>
                <div>用户: {tab.userId || '未知'}</div>
                <div>Token: {tab.userToken || '未知'}</div>
                <div>状态: {tab.isActive ? '活跃' : '非活跃'}</div>
              </div>
            ))}
          </div>
          {ssoStatus.lastEvent && (
            <div className="space-y-2">
              <div>最后事件类型: {ssoStatus.lastEvent.type}</div>
              <div>事件时间: {new Date(ssoStatus.lastEvent.timestamp).toLocaleString()}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 会话监控状态 */}
      <Card>
        <CardHeader>
          <CardTitle>会话监控状态</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <span>监控状态:</span>
            <Badge variant={sessionStatus.isMonitoring ? "default" : "secondary"}>
              {sessionStatus.isMonitoring ? "监控中" : "未监控"}
            </Badge>
          </div>
          <div>连续失败次数: {sessionStatus.consecutiveFailures}</div>
          <div>
            最后检查时间: {
              sessionStatus.lastCheckTime 
                ? new Date(sessionStatus.lastCheckTime).toLocaleString()
                : "从未检查"
            }
          </div>
        </CardContent>
      </Card>

      {/* WebSocket状态 */}
      <Card>
        <CardHeader>
          <CardTitle>WebSocket 连接状态</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <span>连接状态:</span>
            <Badge variant={wsStatus.isConnected ? "default" : "destructive"}>
              {wsStatus.connectionState}
            </Badge>
          </div>
          <Button onClick={handleForceReconnect} variant="outline" size="sm">
            强制重连
          </Button>
        </CardContent>
      </Card>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p><strong>SSO测试步骤：</strong></p>
          <p>1. 在多个标签页中打开此页面</p>
          <p>2. 在一个标签页中点击"模拟新登录"，观察其他标签页是否自动登出</p>
          <p>3. 在一个标签页中点击"测试登出"，观察其他标签页是否同步登出</p>
          <p>4. 观察WebSocket连接在SSO场景下的行为</p>
          <p>5. 检查浏览器控制台的SSO事件日志</p>
          <p><strong>预期结果：</strong></p>
          <p>• 新登录应该踢出其他标签页的登录状态</p>
          <p>• WebSocket连接应该正确断开和重连</p>
          <p>• 所有标签页的状态应该保持同步</p>
        </CardContent>
      </Card>
    </div>
  );
}
