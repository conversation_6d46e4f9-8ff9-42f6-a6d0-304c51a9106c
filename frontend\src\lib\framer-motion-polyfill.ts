/**
 * Framer Motion Polyfill
 * 确保AnimatePresence在所有环境中都可用
 */

import { AnimatePresence, motion } from 'framer-motion';
import { ClientOnlyAnimatePresence } from '@/components/ClientOnlyAnimatePresence';

// 创建一个安全的AnimatePresence包装器
const SafeAnimatePresence = typeof window !== 'undefined' ? AnimatePresence : ClientOnlyAnimatePresence;

// 确保在全局作用域中可用
if (typeof window !== 'undefined') {
  // 将AnimatePresence添加到全局对象
  (window as any).AnimatePresence = AnimatePresence;
  (window as any).motion = motion;

  // 也添加到globalThis以确保兼容性
  (globalThis as any).AnimatePresence = AnimatePresence;
  (globalThis as any).motion = motion;

  // 添加安全版本
  (window as any).SafeAnimatePresence = SafeAnimatePresence;
  (globalThis as any).SafeAnimatePresence = SafeAnimatePresence;
} else {
  // 服务端环境下的fallback
  (globalThis as any).AnimatePresence = ClientOnlyAnimatePresence;
  (globalThis as any).motion = motion;
  (globalThis as any).SafeAnimatePresence = ClientOnlyAnimatePresence;
}

// 导出以供其他模块使用
export { AnimatePresence, motion, SafeAnimatePresence };
export default SafeAnimatePresence;
