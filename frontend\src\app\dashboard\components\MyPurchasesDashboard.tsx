'use client';

import { useState, useEffect } from 'react';
import { getPurchasedProducts } from '@/services/api';
import { Product } from '@/types/Product';
import StackedProductCard from '@/components/StackedProductCard';
import ProductDetailModal from '@/components/ProductDetailModal';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ServerCrash } from 'lucide-react';

const MyPurchasesDashboard = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

    useEffect(() => {
        const fetchPurchasedProducts = async () => {
            try {
                setIsLoading(true);
                const data = await getPurchasedProducts();
                const formattedProducts = data.map((p: Product) => ({
                    ...p,
                    imageUrl: p.imageUrl && !p.imageUrl.startsWith('http')
                        ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}${p.imageUrl}`
                        : p.imageUrl,
                    downloadUrl: p.downloadUrl && !p.downloadUrl.startsWith('http')
                        ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}${p.downloadUrl}`
                        : p.downloadUrl,
                }));
                setProducts(formattedProducts);
                setError(null);
            } catch (err) {
                setError('无法加载已购买的商品列表。请稍后再试。');
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        };

        fetchPurchasedProducts();
    }, []);

    const handleProductClick = (product: Product) => {
        setSelectedProduct(product);
    };

    const handleCloseModal = () => {
        setSelectedProduct(null);
    };
    
    // This function will be triggered from the modal's primary action button.
    const handleDownload = (product: Product) => {
        if (product.downloadUrl) {
            window.open(product.downloadUrl, '_blank');
        }
        handleCloseModal(); // Close modal after initiating download.
    };

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                {Array.from({ length: 10 }).map((_, index) => (
                     <Skeleton key={index} className="w-[275px] h-[550px] rounded-2xl" />
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="bg-gray-900/80 border-red-500/50 text-red-400">
                <ServerCrash className="h-4 w-4" />
                <AlertTitle>加载错误</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }
    
    if (products.length === 0) {
        return (
             <Alert className="bg-gray-900/80 border-gray-700">
                <ServerCrash className="h-4 w-4" />
                <AlertTitle>空空如也</AlertTitle>
                <AlertDescription>您还没有购买任何商品。快去商店看看吧！</AlertDescription>
            </Alert>
        );
    }

    return (
        <>
            <div className="flex flex-wrap justify-center sm:justify-start gap-6">
                {products.map((product) => (
                    <StackedProductCard 
                        key={product.id} 
                        product={product} 
                        onPurchaseClick={() => handleProductClick(product)} 
                        isOwned={true} 
                    />
                ))}
            </div>
            {selectedProduct && (
                <ProductDetailModal
                    product={selectedProduct}
                    isOpen={!!selectedProduct}
                    onClose={handleCloseModal}
                    onPurchaseSuccess={() => {
                        // Optional: can be used to refresh the product list if needed
                        console.log("Modal closed or action completed.");
                    }}
                />
            )}
        </>
    );
};

export default MyPurchasesDashboard; 