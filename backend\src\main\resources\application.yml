server:
  port: 8080

spring:
  application:
    name: KitolusCommunity
  datasource:
    url: *******************************************************************************************************************************************************************
    username: Kitolus
    password: dsbz233S # Make sure to change this in a production environment
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  redis:
    host: localhost
    port: 6379
    password: "" # Empty if no password
    database: 0
  mail:
    host: smtpdm.aliyun.com # Changed to <PERSON><PERSON> SMTP server
    port: 465 # Changed to Aliyun SSL port
    username: "<EMAIL>" # IMPORTANT: 请在这里填入您在阿里云设置的发信地址
    password: "KitolusMeditate69" # IMPORTANT: 请在这里填入您在阿里云为该地址设置的SMTP密码
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
        debug: true # 开启debug以方便排错
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

logging:
  level:
    root: INFO
    com.kitolus.community: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.springframework.mail: DEBUG
    org.springframework.data.redis: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 10MB
    max-history: 30

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted  # 全局逻辑删除字段名
      logic-delete-value: true     # 逻辑已删除值(使用boolean true)
      logic-not-delete-value: false # 逻辑未删除值(使用boolean false)
  type-enums-package: com.kitolus.community.entity

# LantuPay Configuration
lantu:
  payment:
    mch_id: "1719309092"              # LantuPay Merchant ID
    secret_key: "bf5135a2ed7c84a385bc15056b5c73d1" # LantuPay Secret Key
    notify_url: "https://kitolus.top/api/payment/notify" # LantuPay Notify URL
    api_url: "https://api.ltzf.cn/api/wxpay/native"

# JWT Configuration
jwt:
  secret: "change-this-secret-key-in-production-environment-for-kitolus-community" # Change this!
  expiration: 86400000 # 24 hours in milliseconds 

# Knowledge Base Configuration
kb:
  upload-path: "kb_articles" # Use a relative path for portability. This will resolve next to the application JAR.

# File Storage Configuration
file-storage:
  # 生产环境文件存储的根目录
  storage-base-path: "/home/<USER>/app/backend/storage"

# Application-wide properties
app:
  # 这里的 base-url 必须是您最终的、对外提供服务的域名
  base-url: "https://kitolus.top"
  frontend-url: "https://kitolus.top" # Add frontend URL for password reset links

# CORS allowed origins
cors:
  allowed-origins: https://kitolus.top,http://localhost:3000