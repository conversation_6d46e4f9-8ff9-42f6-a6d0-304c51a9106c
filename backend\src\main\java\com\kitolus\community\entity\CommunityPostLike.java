package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.sql.Timestamp;

@Data
@TableName("community_post_likes")
@NoArgsConstructor
@AllArgsConstructor
public class CommunityPostLike {
    private Long id;
    private Long userId;
    private Long postId;
    private Timestamp createdAt;

    public CommunityPostLike(Long userId, Long postId) {
        this.userId = userId;
        this.postId = postId;
    }
} 