'use client';

import { motion } from 'framer-motion';
import { Product } from '@/types/Product';
import Image from 'next/image';
import { ImageOff, ShoppingCart, Download } from 'lucide-react';

interface StackedProductCardProps {
    product: Product;
    isOwned: boolean;
    onPurchaseClick: (product: Product) => void;
}

const StackedProductCard = ({ product, isOwned, onPurchaseClick }: StackedProductCardProps) => {
    return (
        <motion.div
            onClick={() => onPurchaseClick(product)}
            className="group relative w-[275px] h-[550px] rounded-2xl overflow-hidden shadow-2xl bg-gray-950/80 backdrop-blur-sm ring-1 ring-inset ring-gray-700 flex-shrink-0 font-sans cursor-pointer"
        >
            {/* Background Image */}
            <div className="absolute inset-0">
                {product.imageUrl ? (
                    <Image
                        src={product.imageUrl}
                        alt={product.name}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                ) : (
                    <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                        <ImageOff className="w-16 h-16 text-gray-700" />
                    </div>
                )}
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/70 to-transparent" />
            </div>

            {/* Content */}
            <div className="relative flex flex-col justify-end h-full p-6 text-gray-300 z-10">
                <h3 className="text-2xl font-bold text-gray-200 transition-colors duration-300 group-hover:text-white">
                    {product.name}
                </h3>
                {/* Details that appear on hover */}
                <div className="transition-all duration-300 opacity-0 group-hover:opacity-100 max-h-0 group-hover:max-h-40">
                    <p className="text-sm text-gray-400 line-clamp-3 mt-2 mb-4">
                        {product.description}
                    </p>

                    <div className={`flex items-center mt-4 ${isOwned ? 'justify-center' : 'justify-between'}`}>
                        {!isOwned && (
                            <p className="text-2xl font-bold text-gray-200">
                                {`¥${product.price.toFixed(2)}`}
                            </p>
                        )}
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                onPurchaseClick(product);
                            }}
                            className="flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-gray-800 text-gray-200 hover:bg-gray-700 transition-colors"
                        >
                            {isOwned ? <Download size={20} /> : <ShoppingCart size={20} />}
                            <span className="font-semibold whitespace-nowrap">{isOwned ? '立即下载' : '立即购买'}</span>
                        </button>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default StackedProductCard; 