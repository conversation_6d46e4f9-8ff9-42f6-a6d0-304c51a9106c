'use client';

import { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import apiService, { createProduct } from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, UploadCloud, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { compressImageWithPreset, validateImageSize, validateImageType } from '@/lib/imageCompression';

const productSchema = z.object({
    name: z.string().min(3, '名称至少需要3个字符'),
    price: z.coerce.number().min(0, '价格不能为负数'),
    description: z.string().min(10, '描述至少需要10个字符').max(500, '描述不能超过500个字符'),
    downloadUrl: z.string().url('请输入有效的下载链接'),
    image: z.any()
        .refine((files) => files?.length === 1, '请上传一张图片')
        .refine((files) => files?.[0]?.size <= 2 * 1024 * 1024, `图片大小不能超过 2MB.`),
});

type ProductFormValues = z.infer<typeof productSchema>;

interface CreateProductFormProps {
    onProductCreated: () => void;
}

const CreateProductForm = ({ onProductCreated }: CreateProductFormProps) => {
    const { user } = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { register, handleSubmit, formState: { errors }, watch, setValue, reset } = useForm<ProductFormValues>({
        resolver: zodResolver(productSchema),
    });

    const imageFile = watch('image');

    useEffect(() => {
        let fileReader: FileReader;
        let isCancelled = false;

        if (imageFile && imageFile.length > 0) {
            fileReader = new FileReader();
            fileReader.onload = (e) => {
                const result = e.target?.result;
                if (result && !isCancelled) {
                    setImagePreview(result as string);
                }
            };
            fileReader.readAsDataURL(imageFile[0]);
        } else {
            setImagePreview(null);
        }

        return () => {
            isCancelled = true;
            if (fileReader && fileReader.readyState === 1) {
                fileReader.abort();
            }
        };
    }, [imageFile]);

    const handleRemoveImage = () => {
        setValue('image', new DataTransfer().files, { shouldValidate: true });
        setImagePreview(null);
    }

    const onSubmit: SubmitHandler<ProductFormValues> = async (data) => {
        if (!data.image || data.image.length === 0) {
            toast.error('请上传产品图片');
            return;
        }
        setIsSubmitting(true);

        try {
            // 压缩产品图片
            const originalFile = data.image[0];
            const compressedBlob = await compressImageWithPreset(originalFile, 'product');
            const compressedFile = new File([compressedBlob], `product.jpg`, { type: "image/jpeg" });

            const formData = new FormData();
            formData.append('name', data.name);
            formData.append('price', data.price.toString());
            formData.append('description', data.description);
            formData.append('downloadUrl', data.downloadUrl);
            formData.append('image', compressedFile);

            await createProduct(formData);
            
            toast.success(user?.role === 'KitolusAdmin' ? '产品已成功发布！' : '产品已成功提交审核！');
            reset();
            setImagePreview(null);
            onProductCreated();
        } catch (error) {
            console.error('Failed to create product', error);
            const errorMessage = error instanceof Error ? error.message : '产品提交失败，请重试。';
            toast.error(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };
    
    return (
        <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-lg">
            <CardHeader>
                <CardTitle>上架新产品</CardTitle>
                <CardDescription>
                    {user?.role === 'KitolusAdmin'
                        ? '填写以下信息以直接发布您的产品。'
                        : '填写以下信息以提交您的产品进行审核。'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                <Label htmlFor="name">产品名称</Label>
                            <Input id="name" {...register('name')} placeholder="例如：便携式扫描仪" className="bg-input/50"/>
                            {errors.name && <p className="text-red-400 text-sm mt-1">{errors.name.message}</p>}
            </div>
                        <div className="space-y-2">
                <Label htmlFor="price">价格 (元)</Label>
                            <Input id="price" type="number" step="0.01" {...register('price')} placeholder="例如：15.00" className="bg-input/50"/>
                            {errors.price && <p className="text-red-400 text-sm mt-1">{errors.price.message}</p>}
                        </div>
            </div>
                    
                    <div className="space-y-2">
                <Label htmlFor="description">产品描述</Label>
                        <Textarea id="description" {...register('description')} rows={5} placeholder="详细介绍您的产品，包括功能、用途等..." className="bg-input/50"/>
                        {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description.message}</p>}
            </div>
                    
                    <div className="space-y-2">
                <Label htmlFor="downloadUrl">下载链接</Label>
                        <Input id="downloadUrl" {...register('downloadUrl')} placeholder="https://example.com/download/product.zip" className="bg-input/50"/>
                        {errors.downloadUrl && <p className="text-red-400 text-sm mt-1">{errors.downloadUrl.message}</p>}
            </div>
                    
                    <div className="space-y-2">
                <Label htmlFor="image">产品图片</Label>
                        <div className="flex items-center gap-4">
                            {imagePreview ? (
                                <div className="relative w-32 h-32 flex-shrink-0">
                                    <img src={imagePreview} alt="产品图片预览" className="w-full h-full object-cover rounded-md border border-border/50" />
                                    <Button type="button" variant="destructive" size="icon" className="absolute -top-2 -right-2 h-6 w-6 rounded-full" onClick={handleRemoveImage}>
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            ) : (
                                <div className="w-32 h-32 flex-shrink-0 flex items-center justify-center bg-muted/50 rounded-md border-2 border-dashed border-border/50">
                                    <UploadCloud className="h-8 w-8 text-muted-foreground" />
                                </div>
                            )}
                            <div className="w-full">
                                <Input id="image" type="file" accept="image/*" {...register('image')} className="bg-input/50 file:text-foreground" />
                                <p className="text-xs text-muted-foreground mt-2">推荐尺寸：800x600px, 格式为 JPG, PNG 或 WEBP。最大 2MB。</p>
                                {errors.image && <p className="text-red-400 text-sm mt-1">{errors.image.message as string}</p>}
                            </div>
                        </div>
            </div>

                    <Button type="submit" disabled={isSubmitting} className="w-full text-lg py-6">
                        {isSubmitting ? (
                            <>
                                <Loader2 className="mr-2 h-5 w-5 animate-spin" /> 正在提交...
                            </>
                        ) : (user?.role === 'KitolusAdmin' ? '发布产品' : '提交审核')}
            </Button>
        </form>
            </CardContent>
        </Card>
    );
};

export default CreateProductForm; 