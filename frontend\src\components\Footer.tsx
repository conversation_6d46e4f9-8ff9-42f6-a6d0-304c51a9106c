'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';

export default function Footer() {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div
      className="fixed bottom-0 left-0 right-0 h-16 z-50"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
    <footer
      className={cn(
          'absolute bottom-0 w-full py-3 px-6 text-center text-xs text-muted-foreground',
          'bg-background/50 backdrop-blur-lg border-t border-border/30',
        'transition-all duration-500 ease-in-out',
          isVisible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-10 pointer-events-none'
      )}
    >
      <div className="container mx-auto flex items-center justify-center space-x-4">
        <p>
          解释权由 Kitolus Community 所有 | ©2025 - 2026
        </p>
        <span className="hidden md:inline">|</span>
        <p className="hidden md:flex items-center space-x-4">
            <a
              href="https://beian.miit.gov.cn/"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary"
            >
            辽ICP备2025057228号
          </a>
          <span>|</span>
            <a
              href="https://www.12377.cn/"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary"
            >
            违法和不良信息举报(中国)
          </a>
        </p>
      </div>
    </footer>
    </div>
  );
} 