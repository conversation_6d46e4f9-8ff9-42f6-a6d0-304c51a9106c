package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.ProductStatusHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface ProductStatusHistoryMapper extends BaseMapper<ProductStatusHistory> {
    
    @Select("SELECT * FROM product_status_history WHERE product_id = #{productId} ORDER BY changed_at DESC")
    List<ProductStatusHistory> findByProductId(@Param("productId") Long productId);
}
