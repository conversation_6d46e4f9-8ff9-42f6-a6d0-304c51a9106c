package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@TableName("products")
public class Product {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("user_id")
    private Long userId;
    private String name;
    private BigDecimal price;
    private String description;
    @TableField("image_url")
    private String imageUrl;
    @TableField("download_url")
    private String downloadUrl;
    @TableField("author_name")
    private String authorName;
    @TableField("created_at")
    private Timestamp createdAt;
    @TableField("updated_at")
    private Timestamp updatedAt;
    @TableField("reviewed_at")
    private Timestamp reviewedAt;
    @TableField("status")
    private ProductStatus status;
    @TableField("rejection_reason")
    private String rejectionReason;
    @TableField("approval_notes")
    private String approvalNotes;
    @TableField("reviewed_by")
    private String reviewedBy;

    @TableLogic
    private boolean deleted;
} 