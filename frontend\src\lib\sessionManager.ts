/**
 * 简化的SSO会话管理器
 * 核心思路：使用唯一会话ID确保同一账号只有一个活跃会话
 */

export interface SessionInfo {
  sessionId: string;
  userId: number;
  token: string;
  loginTime: number;
  lastActive: number;
}

export class SessionManager {
  private static instance: SessionManager;
  private currentSession: SessionInfo | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 3000; // 3秒检查一次
  private readonly GRACE_PERIOD = 5000; // 5秒宽限期
  private onSessionInvalid?: () => void;

  private constructor() {}

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * 创建新会话（登录时调用）
   */
  createSession(userId: number, token: string): SessionInfo {
    const sessionId = this.generateSessionId();
    const now = Date.now();
    
    const session: SessionInfo = {
      sessionId,
      userId,
      token,
      loginTime: now,
      lastActive: now
    };

    // 保存到localStorage
    localStorage.setItem('current_session', JSON.stringify(session));
    localStorage.setItem('session_lock', sessionId); // 会话锁
    localStorage.setItem('jwt_token', token); // 保持兼容性
    
    this.currentSession = session;
    
    console.log('🔐 创建新会话:', {
      sessionId: sessionId.substring(0, 8) + '...',
      userId,
      tokenPrefix: token.substring(0, 20) + '...'
    });

    // 开始监控会话
    this.startSessionMonitoring();
    
    return session;
  }

  /**
   * 恢复会话（页面刷新时调用）
   */
  restoreSession(): SessionInfo | null {
    try {
      const sessionData = localStorage.getItem('current_session');
      if (!sessionData) {
        console.log('📭 没有找到会话数据');
        return null;
      }

      const session: SessionInfo = JSON.parse(sessionData);
      const currentLock = localStorage.getItem('session_lock');
      
      // 检查会话是否还有效
      if (currentLock !== session.sessionId) {
        console.log('🔒 会话已被新登录替换:', {
          currentSessionId: session.sessionId.substring(0, 8) + '...',
          activeLock: currentLock?.substring(0, 8) + '...'
        });
        this.clearSession();
        return null;
      }

      // 检查会话是否过期（24小时）
      const now = Date.now();
      if (now - session.loginTime > 24 * 60 * 60 * 1000) {
        console.log('⏰ 会话已过期');
        this.clearSession();
        return null;
      }

      // 更新最后活跃时间
      session.lastActive = now;
      localStorage.setItem('current_session', JSON.stringify(session));

      // 确保jwt_token也在localStorage中（API拦截器需要）
      localStorage.setItem('jwt_token', session.token);

      this.currentSession = session;
      
      console.log('🔄 恢复会话:', {
        sessionId: session.sessionId.substring(0, 8) + '...',
        userId: session.userId,
        age: Math.round((now - session.loginTime) / 1000) + 's'
      });

      // 开始监控会话
      this.startSessionMonitoring();
      
      return session;
    } catch (error) {
      console.error('❌ 恢复会话失败:', error);
      this.clearSession();
      return null;
    }
  }

  /**
   * 检查当前会话是否有效
   */
  isSessionValid(): boolean {
    if (!this.currentSession) {
      return false;
    }

    const currentLock = localStorage.getItem('session_lock');
    return currentLock === this.currentSession.sessionId;
  }

  /**
   * 更新会话活跃时间
   */
  updateActivity(): void {
    if (this.currentSession) {
      this.currentSession.lastActive = Date.now();
      localStorage.setItem('current_session', JSON.stringify(this.currentSession));
    }
  }

  /**
   * 清除会话
   */
  clearSession(): void {
    console.log('🧹 清除会话');
    
    if (this.currentSession) {
      // 只清除自己的锁
      const currentLock = localStorage.getItem('session_lock');
      if (currentLock === this.currentSession.sessionId) {
        localStorage.removeItem('session_lock');
      }
    }
    
    localStorage.removeItem('current_session');
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('auth_initialized');
    this.currentSession = null;
    this.stopSessionMonitoring();
  }

  /**
   * 设置会话失效回调
   */
  onSessionInvalidated(callback: () => void): void {
    this.onSessionInvalid = callback;
  }

  /**
   * 获取当前会话信息
   */
  getCurrentSession(): SessionInfo | null {
    return this.currentSession;
  }

  /**
   * 开始会话监控
   */
  private startSessionMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    console.log('👁️ 开始会话监控');
    
    this.checkInterval = setInterval(() => {
      if (!this.isSessionValid()) {
        console.log('⚠️ 检测到会话失效，触发登出');
        this.stopSessionMonitoring();
        
        // 延迟执行，给用户一个宽限期
        setTimeout(() => {
          if (!this.isSessionValid() && this.onSessionInvalid) {
            this.onSessionInvalid();
          }
        }, this.GRACE_PERIOD);
      } else {
        // 更新活跃时间
        this.updateActivity();
      }
    }, this.CHECK_INTERVAL);
  }

  /**
   * 停止会话监控
   */
  private stopSessionMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🛑 停止会话监控');
    }
  }

  /**
   * 生成唯一会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * 检查是否有其他活跃会话
   */
  hasActiveSession(): boolean {
    const currentLock = localStorage.getItem('session_lock');
    return !!currentLock;
  }

  /**
   * 强制接管会话（新登录时）
   */
  takeOverSession(userId: number, token: string): SessionInfo {
    console.log('🔄 强制接管会话，登出其他标签页');

    // 防止重复执行
    if (this.currentSession && this.currentSession.userId === userId) {
      console.log('ℹ️ 相同用户会话已存在，跳过强制接管');
      return this.currentSession;
    }

    // 清除旧会话
    this.clearSession();

    // 添加延迟，确保清理完成
    setTimeout(() => {
      // 创建新会话
      return this.createSession(userId, token);
    }, 100);

    // 立即返回临时会话信息
    return this.createSession(userId, token);
  }
}

// 导出单例实例
export const sessionManager = SessionManager.getInstance();
