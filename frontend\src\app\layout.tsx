import './globals.css';
import localFont from 'next/font/local';
import { cn } from '@/lib/utils';
import { Metadata } from 'next';
import { AppProviders } from '@/components/AppProviders';
import MainLayout from '@/components/MainLayout';
import RouteGuard from '@/components/RouteGuard';
import SessionGuard from '@/components/SessionGuard';
import { ErrorRecovery } from '@/components/ErrorRecovery';

// 导入framer-motion polyfill确保全局可用
import '@/lib/framer-motion-polyfill';

const oxanium = localFont({
  src: [
    { path: '../fonts/Oxanium-ExtraLight.ttf', weight: '200' },
    { path: '../fonts/Oxanium-Light.ttf', weight: '300' },
    { path: '../fonts/Oxanium-Regular.ttf', weight: '400' },
    { path: '../fonts/Oxanium-Medium.ttf', weight: '500' },
    { path: '../fonts/Oxanium-SemiBold.ttf', weight: '600' },
    { path: '../fonts/Oxanium-Bold.ttf', weight: '700' },
  ],
  display: 'swap',
  variable: '--font-oxanium',
});

export const metadata: Metadata = {
  title: 'Kitolus Community',
  description: 'A community for Kitolus',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body
        className={cn(
          'bg-background font-sans antialiased',
          oxanium.variable
        )}
      >
        <AppProviders>
          <ErrorRecovery>
            <SessionGuard>
              <RouteGuard>
                <MainLayout>
                  {children}
                </MainLayout>
              </RouteGuard>
            </SessionGuard>
          </ErrorRecovery>
        </AppProviders>
      </body>
    </html>
  );
}
