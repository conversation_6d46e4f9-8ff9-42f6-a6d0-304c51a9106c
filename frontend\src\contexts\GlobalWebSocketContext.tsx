/**
 * 全局WebSocket上下文
 * 管理全局的WebSocket连接和在线状态
 */

'use client';

import React, { createContext, useContext, useEffect, useState, useRef, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

interface GlobalWebSocketContextType {
  wsManager: typeof unifiedWebSocketManager | null;
  isConnected: boolean;
  onlineUsers: Set<number>;
  isUserOnline: (userId: number) => boolean;
  typingUsers: { [conversationId: string]: { [userId: string]: boolean } };
  isUserTyping: (conversationId: string, userId: number) => boolean;
  startTyping: (conversationId: string) => void;
  stopTyping: () => void;
}

const GlobalWebSocketContext = createContext<GlobalWebSocketContextType | null>(null);

export const useGlobalWebSocket = () => {
  const context = useContext(GlobalWebSocketContext);
  if (!context) {
    throw new Error('useGlobalWebSocket must be used within a GlobalWebSocketProvider');
  }
  return context;
};

interface GlobalWebSocketProviderProps {
  children: React.ReactNode;
}

export const GlobalWebSocketProvider: React.FC<GlobalWebSocketProviderProps> = ({ children }) => {
  const { user, token, isAuthenticated } = useAuth();
  const [wsManager, setWsManager] = useState<typeof unifiedWebSocketManager | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<Set<number>>(new Set());
  const [typingUsers, setTypingUsers] = useState<{ [conversationId: string]: { [userId: string]: boolean } }>({});

  const wsManagerRef = useRef<typeof unifiedWebSocketManager | null>(null);

  // 初始化WebSocket连接
  useEffect(() => {
    if (isAuthenticated && user && token) {
      console.log('🌐 设置统一WebSocket管理器...');

      // 设置管理器引用
      wsManagerRef.current = unifiedWebSocketManager;
      setWsManager(unifiedWebSocketManager);

      // 总是重新初始化连接以确保使用最新token
      console.log('🔄 重新初始化WebSocket连接以确保最新状态');
      unifiedWebSocketManager.initialize(token);

      // 监听连接状态
      const handleConnect = () => {
        console.log('🌐 统一WebSocket连接成功');
        setIsConnected(true);
        // 连接成功后立即请求在线状态
        setTimeout(() => {
          unifiedWebSocketManager.requestOnlineStatus();
        }, 100);
      };

      const handleDisconnect = () => {
        console.log('🌐 统一WebSocket连接断开');
        setIsConnected(false);
        setOnlineUsers(new Set());
        setTypingUsers({});
      };

      const handleError = (error: any) => {
        console.error('🌐 统一WebSocket连接错误:', error);
      };

      // 处理在线状态更新
      const handleOnlineStatus = (data: any) => {
        console.log('🔍 处理在线状态更新:', data);
        if (data.onlineUsers && Array.isArray(data.onlineUsers)) {
          console.log('🟢 设置在线用户:', data.onlineUsers);
          setOnlineUsers(new Set(data.onlineUsers));
        }
      };

      // 处理用户状态变化
      const handleUserStatusChange = (data: any) => {
        console.log('🔄 处理用户状态变化:', data);
        const { userId, isOnline } = data;
        if (typeof userId === 'number' && typeof isOnline === 'boolean') {
          setOnlineUsers(prev => {
            const newSet = new Set(prev);
            if (isOnline) {
              newSet.add(userId);
            } else {
              newSet.delete(userId);
            }
            return newSet;
          });
        }
      };

      unifiedWebSocketManager.on('connected', handleConnect);
      unifiedWebSocketManager.on('disconnected', handleDisconnect);
      unifiedWebSocketManager.on('error', handleError);
      unifiedWebSocketManager.on('ONLINE_STATUS', handleOnlineStatus);
      unifiedWebSocketManager.on('USER_STATUS_CHANGE', handleUserStatusChange);

      // 如果已经连接，立即更新状态
      if (unifiedWebSocketManager.isConnected) {
        handleConnect();
      }

      return () => {
        console.log('🌐 清理统一WebSocket连接');
        // 移除事件监听器
        unifiedWebSocketManager.off('connected', handleConnect);
        unifiedWebSocketManager.off('disconnected', handleDisconnect);
        unifiedWebSocketManager.off('error', handleError);
        unifiedWebSocketManager.off('ONLINE_STATUS', handleOnlineStatus);
        unifiedWebSocketManager.off('USER_STATUS_CHANGE', handleUserStatusChange);

        // 注意：不要销毁单例，只是清理引用
        wsManagerRef.current = null;
        setWsManager(null);
      };
    }
  }, [isAuthenticated, user, token]);

  // 用户登出时清理连接
  useEffect(() => {
    if (!isAuthenticated && wsManagerRef.current) {
      console.log('🌐 用户登出，断开全局WebSocket连接');
      wsManagerRef.current.disconnect();
      wsManagerRef.current = null;
      setWsManager(null);
    }
  }, [isAuthenticated]);

  // 辅助函数
  const isUserOnline = useCallback((userId: number): boolean => {
    return onlineUsers.has(userId);
  }, [onlineUsers]);

  const isUserTyping = useCallback((conversationId: string, userId: number): boolean => {
    return typingUsers[conversationId]?.[userId] === true;
  }, [typingUsers]);

  const startTyping = useCallback((conversationId: string) => {
    if (wsManager) {
      wsManager.startTyping(conversationId);
    }
  }, [wsManager]);

  const stopTyping = useCallback(() => {
    if (wsManager) {
      wsManager.stopTyping();
    }
  }, [wsManager]);

  const contextValue: GlobalWebSocketContextType = {
    wsManager,
    isConnected,
    onlineUsers,
    isUserOnline,
    typingUsers,
    isUserTyping,
    startTyping,
    stopTyping,
  };

  return (
    <GlobalWebSocketContext.Provider value={contextValue}>
      {children}
    </GlobalWebSocketContext.Provider>
  );
};

/**
 * Hook for components that only need online status
 */
export const useOnlineStatus = () => {
  const { onlineUsers, isUserOnline } = useGlobalWebSocket();
  return { onlineUsers, isUserOnline };
};

/**
 * Hook for components that need typing status
 */
export const useTypingStatus = () => {
  const { typingUsers, isUserTyping, startTyping, stopTyping } = useGlobalWebSocket();
  return { typingUsers, isUserTyping, startTyping, stopTyping };
};
