/**
 * SSO管理器 - 处理多标签页登录状态同步
 */

type SSOEventType = 'login' | 'logout' | 'session_conflict';

interface SSOEvent {
  type: SSOEventType;
  timestamp: number;
  token?: string;
  userId?: number;
}

class SSOManager {
  private channel: BroadcastChannel | null = null;
  private listeners: Map<SSOEventType, ((event: SSOEvent) => void)[]> = new Map();
  private currentToken: string | null = null;
  private currentUserId: number | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      console.log('🔧 初始化SSO管理器...');

      // 检查BroadcastChannel支持
      if ('BroadcastChannel' in window) {
        this.channel = new BroadcastChannel('sso-channel');
        this.channel.onmessage = this.handleMessage.bind(this);
        console.log('✅ BroadcastChannel已初始化');
      } else {
        console.warn('⚠️ BroadcastChannel不支持，SSO功能可能受限');
      }

      this.currentToken = localStorage.getItem('jwt_token');

      // 监听localStorage变化（跨标签页）
      window.addEventListener('storage', this.handleStorageChange.bind(this));
      console.log('✅ localStorage事件监听器已设置');
    }
  }

  private handleMessage(event: MessageEvent<SSOEvent>) {
    const { type, timestamp, token, userId } = event.data;

    console.log('📨 收到SSO广播消息:', event.data);

    // 忽略太旧的消息（超过5秒）
    if (Math.abs(Date.now() - timestamp) > 5000) {
      console.log('⏰ 忽略过期的SSO消息');
      return;
    }

    console.log('🔔 触发SSO事件:', type);
    this.emit(type, event.data);
  }

  private handleStorageChange(event: StorageEvent) {
    console.log('📦 localStorage变化事件:', event.key, event.newValue);

    // 处理SSO事件
    if (event.key === 'sso-event' && event.newValue) {
      try {
        const ssoEvent: SSOEvent = JSON.parse(event.newValue);
        console.log('📨 收到localStorage SSO事件:', ssoEvent);
        this.handleMessage({ data: ssoEvent } as MessageEvent<SSOEvent>);
      } catch (error) {
        console.error('❌ 解析localStorage SSO事件失败:', error);
      }
    }

    // 处理token变化
    if (event.key === 'jwt_token') {
      const newToken = event.newValue;
      const oldToken = this.currentToken;

      console.log('🔑 Token变化:', { oldToken: oldToken?.substring(0, 20), newToken: newToken?.substring(0, 20) });

      if (newToken !== oldToken) {
        this.currentToken = newToken;

        if (newToken && !oldToken) {
          // 新登录
          console.log('🔔 检测到新登录');
          this.emit('login', { type: 'login', timestamp: Date.now(), token: newToken });
        } else if (!newToken && oldToken) {
          // 登出
          console.log('🔔 检测到登出');
          this.emit('logout', { type: 'logout', timestamp: Date.now() });
        } else if (newToken && oldToken && newToken !== oldToken) {
          // Token变化，可能是会话冲突
          console.log('🔔 检测到会话冲突');
          this.emit('session_conflict', {
            type: 'session_conflict',
            timestamp: Date.now(),
            token: newToken
          });
        }
      }
    }
  }

  // 广播登录事件
  broadcastLogin(token: string, userId: number) {
    console.log('🔄 广播登录事件:', { token: token.substring(0, 20) + '...', userId });

    // 先广播登出事件，让其他标签页知道要登出
    this.broadcastLogout();

    // 等待一小段时间确保登出事件被处理
    setTimeout(() => {
      this.currentToken = token;
      this.currentUserId = userId;

      const event: SSOEvent = {
        type: 'login',
        timestamp: Date.now(),
        token,
        userId
      };

      console.log('📤 发送登录广播:', event);

      // 方法1: 使用BroadcastChannel
      if (this.channel) {
        try {
          this.channel.postMessage(event);
          console.log('✅ BroadcastChannel登录广播发送成功');
        } catch (error) {
          console.error('❌ BroadcastChannel登录广播发送失败:', error);
        }
      }

      // 方法2: 使用localStorage作为备用方案
      try {
        localStorage.setItem('sso-event', JSON.stringify(event));
        // 立即删除，触发storage事件
        localStorage.removeItem('sso-event');
        console.log('✅ localStorage登录广播发送成功');
      } catch (error) {
        console.error('❌ localStorage登录广播发送失败:', error);
      }
    }, 100);
  }

  // 广播登出事件
  broadcastLogout() {
    console.log('🔄 广播登出事件');

    const event: SSOEvent = {
      type: 'logout',
      timestamp: Date.now()
    };

    console.log('📤 发送登出广播:', event);

    // 方法1: 使用BroadcastChannel
    if (this.channel) {
      try {
        this.channel.postMessage(event);
        console.log('✅ BroadcastChannel登出广播发送成功');
      } catch (error) {
        console.error('❌ BroadcastChannel登出广播发送失败:', error);
      }
    }

    // 方法2: 使用localStorage作为备用方案
    try {
      localStorage.setItem('sso-event', JSON.stringify(event));
      // 立即删除，触发storage事件
      localStorage.removeItem('sso-event');
      console.log('✅ localStorage登出广播发送成功');
    } catch (error) {
      console.error('❌ localStorage登出广播发送失败:', error);
    }

    this.currentToken = null;
    this.currentUserId = null;
  }

  // 广播会话冲突事件
  broadcastSessionConflict() {
    const event: SSOEvent = {
      type: 'session_conflict',
      timestamp: Date.now()
    };

    this.channel?.postMessage(event);
  }

  // 添加事件监听器
  on(type: SSOEventType, listener: (event: SSOEvent) => void) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(listener);
  }

  // 移除事件监听器
  off(type: SSOEventType, listener: (event: SSOEvent) => void) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(type: SSOEventType, event: SSOEvent) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }
  }

  // 检查当前是否为活跃标签页
  isActiveTab(): boolean {
    return document.visibilityState === 'visible';
  }

  // 销毁管理器
  destroy() {
    this.channel?.close();
    this.listeners.clear();
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('storage', this.handleStorageChange.bind(this));
    }
  }
}

// 创建全局实例
export const ssoManager = new SSOManager();

// 导出类型
export type { SSOEvent, SSOEventType };
