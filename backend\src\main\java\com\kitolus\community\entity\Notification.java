package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("notification")
public class Notification {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("recipient_id")
    private Long recipientId; // The user who receives the notification

    @TableField("sender_id")
    private Long senderId; // The user who triggered the notification

    @TableField("type")
    private NotificationType type;

    @TableField(value = "post_id", insertStrategy = FieldStrategy.ALWAYS)
    private Long postId;

    @TableField(value = "comment_id", insertStrategy = FieldStrategy.ALWAYS)
    private Long commentId;

    @TableField("is_read")
    private boolean isRead = false;

    @TableField("created_at")
    private Timestamp createdAt;

    // Not a DB field, used for DTO mapping
    @TableField(exist = false)
    private User sender;
    
    @TableField(exist = false)
    private CommunityPost post;

    @TableField("content_preview")
    private String contentPreview;
} 