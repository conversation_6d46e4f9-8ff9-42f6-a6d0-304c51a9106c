# GTNH 全栈项目知识体系

## 核心技术栈与架构

### 前端 (Frontend) - Next.js 14
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **UI & 样式**:
  - **核心**: Tailwind CSS, Shadcn/UI
  - **工具**: `clsx`, `tailwind-merge` for class management
  - **动画**: Framer Motion
- **3D 渲染**: React Three Fiber (`@react-three/fiber`), <PERSON><PERSON> (`@react-three/drei`)
- **状态管理 & 数据获取**:
  - **API 请求**: `axios`
  - **全局状态**: React Context (e.g., `AuthContext`)
  - **通知/Toast**: `Sonner`
- **认证**: NextAuth.js with Prisma Adapter
- **Markdown**: `react-markdown` with `remark-gfm` and `rehype-slug`
- **代码高亮**: `react-syntax-highlighter`
- **ORM**: Prisma
- **组件库**: `Lucide Icons` for icons, `Swiper` for carousels
- **图像处理**: `react-image-crop`

### 后端 (Backend) - Spring Boot 3
- **框架**: Spring Boot 3.3.0
- **语言**: Java 17
- **数据访问**:
  - **ORM**: MyBatis-Plus
  - **数据库**: MySQL
- **安全**: Spring Security, JWT (via `jjwt` library)
- **缓存**: Spring Data Redis
- **搜索**: Apache Lucene (Core, QueryParser, SmartCN for Chinese analysis)
- **API**: Spring Web (RESTful APIs), Spring Validation
- **邮件服务**: Spring Boot Starter Mail
- **工具**: Lombok

## 项目关键实现模式
- **全栈认证流程**:
  1. 前端使用 `NextAuth.js` 管理会话和凭证。
  2. 后端使用 `Spring Security` 和 `JWT` 进行 token 的生成和验证。
  3. 前后端通过 `Authorization` header 中的 Bearer Token 进行认证通信。
- **知识库 (KB)**:
  - 后端使用 `Apache Lucene` 建立索引和提供搜索功能。
  - 前端通过 `/kb` 路径展示文章和搜索结果。
- **用户配置**:
  - 头像上传存储在 `backend/storage/avatars` 目录。
  - 用户信息、安全设置在 `/profile` 页面管理。
- **前后端通信**:
  - 主要通过 `axios` 实例 (`/frontend/src/services/api.ts`) 发送 RESTful API 请求到后端。
  - 后端通过 Controller (`/backend/src/main/java/com/kitolus/community/controller/`) 暴露接口。
- **代码组织**:
  - 前端遵循 Next.js App Router 的目录结构 (`app/page.tsx`, `layout.tsx`, etc.)。
  - 后端遵循标准的 `Controller-Service-Mapper` 分层架构。