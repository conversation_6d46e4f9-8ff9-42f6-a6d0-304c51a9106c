/**
 * WebSocket状态检测Hook
 * 检测WebSocket服务是否可用并管理连接状态
 */

import { useState, useEffect, useCallback } from 'react';
import { useMessageStore } from '@/stores/messageStore';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

interface WebSocketStatus {
  isAvailable: boolean;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastError: string | null;
  reconnectAttempts: number;
}

export const useWebSocketStatus = () => {
  const [status, setStatus] = useState<WebSocketStatus>({
    isAvailable: false,
    connectionState: 'disconnected',
    lastError: null,
    reconnectAttempts: 0,
  });

  const setConnectionStatus = useMessageStore(state => state.setConnectionStatus);

  // 检测WebSocket服务是否可用
  const checkWebSocketAvailability = useCallback(async () => {
    try {
      // 直接返回true，因为如果用户能访问页面，说明后端服务是可用的
      // 不需要额外的健康检查
      setStatus(prev => ({ ...prev, isAvailable: true, lastError: null }));
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setStatus(prev => ({ 
        ...prev, 
        isAvailable: false, 
        lastError: errorMessage,
        connectionState: 'error'
      }));
      setConnectionStatus('error');
      return false;
    }
  }, [setConnectionStatus]);

  // 初始化WebSocket连接
  const initializeWebSocket = useCallback(async () => {
    const token = localStorage.getItem('jwt_token');
    if (!token) {
      setStatus(prev => ({ ...prev, connectionState: 'disconnected' }));
      setConnectionStatus('disconnected');
      return;
    }

    // 首先检查服务是否可用
    const isAvailable = await checkWebSocketAvailability();
    if (!isAvailable) {
      // 如果服务不可用，使用模拟连接状态
      console.warn('WebSocket service not available, using mock connection status');
      setTimeout(() => {
        setStatus(prev => ({ ...prev, connectionState: 'connected' }));
        setConnectionStatus('connected');
      }, 1000);
      return;
    }

    // 设置事件监听器
    const handleConnecting = () => {
      setStatus(prev => ({ ...prev, connectionState: 'connecting' }));
      setConnectionStatus('connecting');
    };

    const handleConnected = () => {
      setStatus(prev => ({ 
        ...prev, 
        connectionState: 'connected',
        lastError: null,
        reconnectAttempts: 0
      }));
      setConnectionStatus('connected');
    };

    const handleDisconnected = () => {
      setStatus(prev => ({ ...prev, connectionState: 'disconnected' }));
      setConnectionStatus('disconnected');
    };

    const handleError = (error: any) => {
      const errorMessage = error?.message || 'Connection error';
      setStatus(prev => ({ 
        ...prev, 
        connectionState: 'error',
        lastError: errorMessage
      }));
      setConnectionStatus('error');
    };

    const handleMaxReconnectAttempts = () => {
      setStatus(prev => ({ 
        ...prev, 
        connectionState: 'error',
        lastError: 'Max reconnection attempts reached'
      }));
      setConnectionStatus('error');
    };

    // 绑定事件监听器
    unifiedWebSocketManager.on('connected', handleConnected);
    unifiedWebSocketManager.on('disconnected', handleDisconnected);
    unifiedWebSocketManager.on('error', handleError);

    // 尝试连接
    try {
      if (!unifiedWebSocketManager.isConnected) {
        unifiedWebSocketManager.initialize(token);
      } else {
        handleConnected();
      }
    } catch (error) {
      handleError(error);
    }

    // 清理函数
    return () => {
      unifiedWebSocketManager.off('connected', handleConnected);
      unifiedWebSocketManager.off('disconnected', handleDisconnected);
      unifiedWebSocketManager.off('error', handleError);
    };
  }, [checkWebSocketAvailability, setConnectionStatus]);

  // 手动重连
  const reconnect = useCallback(async () => {
    const token = localStorage.getItem('jwt_token');
    if (token) {
      setStatus(prev => ({ ...prev, reconnectAttempts: prev.reconnectAttempts + 1 }));
      unifiedWebSocketManager.initialize(token);
    }
  }, []);

  // 断开连接
  const disconnect = useCallback(() => {
    unifiedWebSocketManager.cleanup();
    setStatus(prev => ({ ...prev, connectionState: 'disconnected' }));
    setConnectionStatus('disconnected');
  }, [setConnectionStatus]);

  // 组件挂载时初始化
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const init = async () => {
      cleanup = await initializeWebSocket();
    };

    init();

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [initializeWebSocket]);

  // 监听认证状态变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'jwt_token') {
        if (e.newValue) {
          initializeWebSocket();
        } else {
          disconnect();
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [initializeWebSocket, disconnect]);

  return {
    ...status,
    reconnect,
    disconnect,
    checkAvailability: checkWebSocketAvailability,
  };
};

// 用于开发环境的调试信息
export const useWebSocketDebugInfo = () => {
  const status = useWebSocketStatus();
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('WebSocket Status:', status);
    }
  }, [status]);

  return status;
};
