package com.kitolus.community.controller;

import com.kitolus.community.dto.JwtResponseDTO;
import com.kitolus.community.dto.LoginRequestDTO;
import com.kitolus.community.dto.ProfileDTO;
import com.kitolus.community.dto.RegisterRequestDTO;
import com.kitolus.community.dto.VerificationRequestDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.service.LoginHistoryService;
import com.kitolus.community.service.UserService;
import com.kitolus.community.util.JwtTokenUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private UserService userService;

    @Autowired
    private LoginHistoryService loginHistoryService;

    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequestDTO loginRequest, HttpServletRequest request) {
        try {
            authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

            final UserDetails userDetails = userDetailsService.loadUserByUsername(loginRequest.getUsername());
            final String jwt = jwtTokenUtil.generateToken(userDetails);

            // --- 记录登录历史 ---
            User user = userService.findByUsername(loginRequest.getUsername());
            if (user != null) {
                String ipAddress = getClientIp(request);
                String deviceFingerprint = request.getHeader("X-Device-Fingerprint");
                loginHistoryService.addLoginHistory(user.getId(), ipAddress, deviceFingerprint);
            }
            
            ProfileDTO profileDTO = userService.getUserProfile(loginRequest.getUsername());
            return ResponseEntity.ok(new JwtResponseDTO(jwt, profileDTO));
        } catch (BadCredentialsException e) {
            return ResponseEntity.status(401).body(Map.of("message", "用户名或密码错误"));
        }
    }

    @PostMapping("/register")
    public ResponseEntity<?> requestRegistration(@Valid @RequestBody RegisterRequestDTO registerRequest) {
        Map<String, Object> result = userService.requestRegistration(registerRequest);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/verify")
    public ResponseEntity<?> verifyAndRegister(@Valid @RequestBody VerificationRequestDTO verificationRequest) {
        User newUser = userService.completeRegistration(verificationRequest.getEmail(), verificationRequest.getCode());

        final UserDetails userDetails = userDetailsService.loadUserByUsername(newUser.getUsername());
        final String jwt = jwtTokenUtil.generateToken(userDetails);

        ProfileDTO profileDTO = userService.getUserProfile(newUser.getUsername());
        return ResponseEntity.ok(new JwtResponseDTO(jwt, profileDTO));
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            if (ip.contains(",")) {
                return ip.split(",")[0];
            }
            return ip;
        }
        ip = request.getHeader("X-Real-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        String remoteAddr = request.getRemoteAddr();
        if ("0:0:0:0:0:0:0:1".equals(remoteAddr)) {
            return "127.0.0.1";
        }
        return remoteAddr;
    }
} 