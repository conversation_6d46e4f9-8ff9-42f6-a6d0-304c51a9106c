package com.kitolus.community.controller;

import com.kitolus.community.dto.ProfileDTO;
import com.kitolus.community.dto.UpdateProfileRequestDTO;
import com.kitolus.community.dto.ChangePasswordRequestDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.service.UserService;
import com.kitolus.community.util.JwtTokenUtil;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.security.Principal;
import java.util.Map;
import com.kitolus.community.entity.DeveloperApplication;
import org.springframework.http.HttpStatus;
import com.kitolus.community.service.EmailService;
import com.kitolus.community.dto.ResetPasswordRequestDTO;
import com.kitolus.community.entity.Product;
import java.util.List;
import com.kitolus.community.dto.CreateProductRequestDTO;
import com.kitolus.community.dto.UpdateProductRequestDTO;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import com.kitolus.community.service.ProductService;
import com.kitolus.community.mapper.OrderMapper;
import com.kitolus.community.dto.UpdateWithdrawalSettingsDTO;
import com.kitolus.community.entity.CommunityPost;
import com.kitolus.community.entity.CommunityComment;
import com.kitolus.community.service.CommunityService;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private ProductService productService;

    @Autowired
    private CommunityService communityService;

    // @Autowired
    // private OrderMapper orderMapper; // Temporarily removed to fix dependency issue.

    @GetMapping("/profile/{username}")
    public ResponseEntity<ProfileDTO> getPublicUserProfile(@PathVariable String username) {
        ProfileDTO profile = userService.getUserPublicProfile(username);
        return ResponseEntity.ok(profile);
    }

    @GetMapping("/{userId}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long userId) {
        try {
            User user = userService.findById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            // 返回基本的公开用户信息
            Map<String, Object> userInfo = Map.of(
                "id", user.getId(),
                "username", user.getUsername(),
                "avatarUrl", user.getAvatarUrl(),
                "fullAvatarUrl", user.getAvatarUrl() != null ? user.getAvatarUrl() : null
            );

            return ResponseEntity.ok(userInfo);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get user info"));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<List<Map<String, String>>> searchUsers(@RequestParam String query) {
        if (query == null || query.trim().length() < 2) {
            return ResponseEntity.ok(List.of());
        }

        List<Map<String, String>> users = userService.searchUsersByUsername(query.trim());
        return ResponseEntity.ok(users);
    }

    @GetMapping("/profile")
    public ResponseEntity<ProfileDTO> getUserProfile(Principal principal) {
        String username = principal.getName();
        ProfileDTO profile = userService.getUserProfile(username);
        return ResponseEntity.ok(profile);
    }

    @PutMapping("/profile")
    public ResponseEntity<?> updateUserProfile(Principal principal, @Valid @RequestBody UpdateProfileRequestDTO profileData) {
        String username = principal.getName();
        User updatedUser = userService.updateUserProfile(username, profileData);

        if (updatedUser == null) {
            return ResponseEntity.ok(Map.of("message", "个人资料无任何变更。"));
        }

        // 检查用户名是否发生了变化
        boolean usernameChanged = !username.equals(updatedUser.getUsername());

        if (usernameChanged) {
            // 只有用户名改变时才生成新token
            final UserDetails userDetails = userDetailsService.loadUserByUsername(updatedUser.getUsername());
            final String token = jwtTokenUtil.generateToken(userDetails);
            return ResponseEntity.ok(Map.of("message", "个人资料更新成功！", "token", token));
        } else {
            // 用户名没有改变，不需要新token
            return ResponseEntity.ok(Map.of("message", "个人资料更新成功！"));
        }
    }

    @PostMapping("/change-password")
    public ResponseEntity<?> changePassword(Principal principal, @Valid @RequestBody ChangePasswordRequestDTO changePasswordRequest) {
        userService.changePassword(principal.getName(), changePasswordRequest);
        return ResponseEntity.ok(Map.of("message", "密码更新成功！"));
    }

    @PostMapping("/avatar")
    public ResponseEntity<?> updateUserAvatar(Principal principal, @RequestParam("avatar") MultipartFile file) {
        String username = principal.getName();
        userService.updateUserAvatar(username, file);
        
        // Refetch user to get the updated version number
        User updatedUser = userService.findByUsername(username);

        return ResponseEntity.ok(Map.of(
            "avatarUrl", updatedUser.getAvatarUrl(),
            "avatarVersion", updatedUser.getAvatarVersion()
        ));
    }

    @PostMapping("/banner")
    public ResponseEntity<?> updateUserBanner(Principal principal, @RequestParam("banner") MultipartFile file) {
        String username = principal.getName();
        userService.updateUserBanner(username, file);

        // Refetch user to get the updated version number
        User updatedUser = userService.findByUsername(username);

        return ResponseEntity.ok(Map.of(
            "bannerUrl", updatedUser.getBannerUrl(),
            "bannerVersion", updatedUser.getBannerVersion()
        ));
    }

    @DeleteMapping("/me")
    public ResponseEntity<?> deleteUser(Principal principal) {
        String username = principal.getName();
        userService.deleteUser(username);
        return ResponseEntity.ok(Map.of("message", "账户删除成功。"));
    }

    @PostMapping("/apply-developer")
    public ResponseEntity<?> applyForDeveloper(Principal principal, @RequestBody Map<String, String> payload) {
        userService.applyForDeveloperRole(principal.getName(), payload.get("message"));
        return ResponseEntity.ok(Map.of("message", "申请已成功提交"));
    }

    @GetMapping("/developer-application")
    public ResponseEntity<DeveloperApplication> getMyDeveloperApplication(Principal principal) {
        User user = userService.findByUsername(principal.getName());
        if (user == null) {
            // This should technically not happen for an authenticated user, but as a safeguard:
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        DeveloperApplication application = userService.findLatestDeveloperApplicationByUserId(user.getId());
        if (application != null) {
            return ResponseEntity.ok(application);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@RequestBody Map<String, String> payload) {
        String email = payload.get("email");
        userService.handleForgotPasswordRequest(email);
        // For security reasons, always return a generic success message
        // to prevent user enumeration attacks.
        return ResponseEntity.ok(Map.of("message", "如果该邮箱地址已注册，您将会收到一封包含重置链接的邮件。"));
    }

    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@Valid @RequestBody ResetPasswordRequestDTO requestDTO) {
        userService.resetPassword(requestDTO.getToken(), requestDTO.getNewPassword());
        return ResponseEntity.ok(Map.of("message", "您的密码已成功重置，现在可以使用新密码登录。"));
    }

    @PutMapping("/withdrawal-settings")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<?> updateWithdrawalSettings(Principal principal, @Valid @RequestBody UpdateWithdrawalSettingsDTO settingsDTO) {
        userService.updateWithdrawalSettings(principal.getName(), settingsDTO);
        return ResponseEntity.ok(Map.of("message", "提现设置更新成功！"));
    }

    @GetMapping("/purchases")
    public ResponseEntity<List<Product>> getPurchasedProducts(Principal principal) {
        User user = userService.findByUsername(principal.getName());
        List<Product> purchasedProducts = userService.getPurchasedProducts(user.getId());
        return ResponseEntity.ok(purchasedProducts);
    }

    @PostMapping("/products")
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<Product> createProduct(@RequestBody CreateProductRequestDTO requestDTO) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.findByUsername(authentication.getName());

        Product newProduct = productService.createProduct(requestDTO, currentUser);
        return ResponseEntity.status(HttpStatus.CREATED).body(newProduct);
    }
    
    @PutMapping("/products/{id}")
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<Product> updateProduct(@PathVariable Long id, @RequestBody UpdateProductRequestDTO requestDTO) throws Exception {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.findByUsername(authentication.getName());
        Product updatedProduct = productService.updateProduct(id, requestDTO, currentUser);
        return ResponseEntity.ok(updatedProduct);
    }

    @GetMapping("/{username}/recent-activity")
    public ResponseEntity<Map<String, Object>> getUserRecentActivity(@PathVariable String username) {
        try {
            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            // 获取用户最近的帖子（最多5个）
            List<CommunityPost> recentPosts = communityService.getUserRecentPosts(user.getId(), 5);

            // 获取用户最近的评论（最多5个）
            List<CommunityComment> recentComments = communityService.getUserRecentComments(user.getId(), 5);

            // 为评论添加帖子信息
            List<Map<String, Object>> commentsWithPosts = recentComments.stream()
                .map(comment -> {
                    Map<String, Object> commentData = new java.util.HashMap<>();
                    commentData.put("id", comment.getId());
                    commentData.put("content", comment.getContent());
                    commentData.put("createdAt", comment.getCreatedAt());
                    commentData.put("authorId", comment.getAuthorId());
                    commentData.put("author", comment.getAuthor());

                    // 获取关联的帖子信息
                    try {
                        CommunityPost post = communityService.getPostById(comment.getPostId());
                        if (post != null) {
                            Map<String, Object> postInfo = new java.util.HashMap<>();
                            postInfo.put("id", post.getId());
                            postInfo.put("title", post.getTitle());
                            commentData.put("post", postInfo);
                        }
                    } catch (Exception e) {
                        // 如果获取帖子失败，跳过这个评论
                        return null;
                    }

                    return commentData;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            Map<String, Object> activity = Map.of(
                "recentPosts", recentPosts,
                "recentComments", commentsWithPosts
            );

            return ResponseEntity.ok(activity);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get user activity"));
        }
    }
}
