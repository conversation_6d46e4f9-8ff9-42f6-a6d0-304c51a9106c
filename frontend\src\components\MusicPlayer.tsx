'use client';

import { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, SkipBack, SkipForward, Music, Repeat, Repeat1, Shuffle, Loader2, Volume2, Volume1, VolumeX, ChevronUp } from 'lucide-react';
import { toast } from 'sonner';

type Song = {
    title: string;
    artist: string;
    url: string;
    cover?: string | null;
};

type PlaybackMode = 'playlist-loop' | 'single-loop' | 'shuffle';

type MusicPlayerProps = {
    startPaused?: boolean;
};

export type MusicPlayerRef = {
    play: () => void;
    pause: () => void;
};

const MusicPlayer = forwardRef<MusicPlayerRef, MusicPlayerProps>((_, ref) => {
    const [songs, setSongs] = useState<Song[]>([]);
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentSongIndex, setCurrentSongIndex] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);
    const [isMuted, setIsMuted] = useState(false);
    const [playbackMode, setPlaybackMode] = useState<PlaybackMode>('playlist-loop');
    const [shuffledIndices, setShuffledIndices] = useState<number[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isExpanded, setIsExpanded] = useState(false);
    const audioRef = useRef<HTMLAudioElement>(null);
    const playerFooterRef = useRef<HTMLDivElement>(null);

    const fetchSongs = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await fetch('/internal-api/music');
            if (!response.ok) throw new Error('无法加载本地音乐');
            
            const data = await response.json();
            const newSongs: Song[] = Array.isArray(data) ? data : [];
            
            if (newSongs.length > 0) {
              setSongs(newSongs);
              setShuffledIndices(newSongs.map((_, i) => i).sort(() => Math.random() - 0.5));
              setCurrentSongIndex(0);
              setCurrentTime(0);
              setDuration(0);
            } else {
              setSongs([]);
              toast.info('本地音乐库为空。');
            }
        } catch (e: any) {
            setError(e.message);
            toast.error(`加载音乐时出错: ${e.message}`);
        } finally {
            setIsLoading(false);
        }
    }, []);
    
    useEffect(() => {
        fetchSongs();
    }, [fetchSongs]);

    useImperativeHandle(ref, () => ({
        play: () => {
            if (songs.length > 0 && audioRef.current) {
                audioRef.current.play().then(() => {
                    setIsPlaying(true);
                }).catch(e => {
                    // Autoplay was prevented, user interaction needed.
                    // Handle silently
                });
            }
        },
        pause: () => {
            if (audioRef.current) {
                audioRef.current.pause();
                setIsPlaying(false);
            }
        },
    }));
    
    const handlePlayPause = () => {
        if (songs.length > 0) {
            setIsPlaying(!isPlaying);
        }
    };

    const handleNext = useCallback(() => {
        if (songs.length === 0) return;

        if (playbackMode === 'shuffle') {
            const currentIndexInShuffled = shuffledIndices.indexOf(currentSongIndex);
            const nextIndexInShuffled = (currentIndexInShuffled + 1) % shuffledIndices.length;
            setCurrentSongIndex(shuffledIndices[nextIndexInShuffled]);
        } else {
            setCurrentSongIndex((prevIndex) => (prevIndex + 1) % songs.length);
        }
    }, [songs.length, playbackMode, currentSongIndex, shuffledIndices]);

    const handlePrev = () => {
        if (songs.length === 0) return;

        if (playbackMode === 'shuffle') {
            const currentIndexInShuffled = shuffledIndices.indexOf(currentSongIndex);
            const prevIndexInShuffled = (currentIndexInShuffled - 1 + shuffledIndices.length) % shuffledIndices.length;
            setCurrentSongIndex(shuffledIndices[prevIndexInShuffled]);
        } else {
            setCurrentSongIndex((prevIndex) => (prevIndex - 1 + songs.length) % songs.length);
        }
    };

    const handleSongSelect = (index: number) => {
        setCurrentSongIndex(index);
        setIsPlaying(true);
    };

    const handleTimeUpdate = () => {
        if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
        }
    };
    
    const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (audioRef.current) {
            const newTime = Number(e.target.value);
            audioRef.current.currentTime = newTime;
            setCurrentTime(newTime);
        }
    };

    const handleLoadedMetadata = () => {
        if (audioRef.current) {
            setDuration(audioRef.current.duration);
        }
    };

    const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newVolume = Number(e.target.value);
        setVolume(newVolume);
        if (isMuted && newVolume > 0) {
            setIsMuted(false);
        }
    };
    
    const toggleMute = () => {
        setIsMuted(!isMuted);
    };

    const togglePlaybackMode = () => {
        const modes: PlaybackMode[] = ['playlist-loop', 'single-loop', 'shuffle'];
        const currentModeIndex = modes.indexOf(playbackMode);
        const nextMode = modes[(currentModeIndex + 1) % modes.length];
        setPlaybackMode(nextMode);
        toast.info(nextMode === 'playlist-loop' ? '列表循环' : nextMode === 'single-loop' ? '单曲循环' : '随机播放');
    };

    const getVolumeIcon = () => {
        if (isMuted || volume === 0) return <VolumeX size={16} />;
        if (volume < 0.5) return <Volume1 size={16} />;
        return <Volume2 size={16} />;
    };
    
    const currentSong = songs[currentSongIndex];
    
    useEffect(() => {
      const audio = audioRef.current;
      if (!audio) return;
      if (isPlaying) {
        audio.play().catch(e => {
          // Handle audio play error silently
        });
      } else {
        audio.pause();
      }
    }, [isPlaying, currentSongIndex, songs]);

    useEffect(() => {
      const audio = audioRef.current;
      if (audio) {
          audio.volume = volume;
          audio.muted = isMuted;
      }
    }, [volume, isMuted]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (playerFooterRef.current && !playerFooterRef.current.contains(event.target as Node)) {
                setIsExpanded(false);
            }
        };
        if (isExpanded) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isExpanded]);


    const formatTime = (seconds: number) => {
        if (isNaN(seconds) || seconds < 0) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
    };

    return (
        <motion.footer 
            ref={playerFooterRef}
            className="fixed bottom-0 left-0 right-0 bg-black/60 backdrop-blur-xl text-white shadow-2xl border-t border-white/10 rounded-t-lg z-50"
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            transition={{ type: 'spring', stiffness: 200, damping: 30 }}
        >
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: 10, opacity: 0 }}
                transition={{ duration: 0.2, ease: 'easeInOut' }}
                className="absolute bottom-full w-full bg-black/60 backdrop-blur-xl border-t border-white/10 rounded-t-lg"
              >
                <div className="p-2">
                    <h3 className="text-center font-semibold mb-2 text-sm">播放列表</h3>
                     <div className="overflow-y-auto max-h-40 space-y-1">
                        {songs.length > 0 ? songs.map((song, index) => (
                            <div key={index} className={`flex items-center justify-between p-1.5 rounded-md cursor-pointer transition-colors ${currentSongIndex === index ? 'bg-primary/30' : 'hover:bg-white/10'}`} onClick={() => handleSongSelect(index)}>
                                 <div className="flex items-center space-x-2">
                                     <img src={song.cover || '/images/default-album-art.png'} alt={song.title} className="w-8 h-8 rounded-md object-cover" />
                                     <div>
                                         <p className={`text-sm font-semibold ${currentSongIndex === index ? 'text-primary' : ''}`}>{song.title}</p>
                                         <p className="text-xs text-gray-400">{song.artist}</p>
                                     </div>
                                 </div>
                                 {currentSongIndex === index && <Music size={16} className="text-primary" />}
                             </div>
                        )) : <p className="text-center text-gray-400 py-4">播放列表为空</p>}
                    </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <audio 
            ref={audioRef} 
            onTimeUpdate={handleTimeUpdate} 
            onLoadedMetadata={handleLoadedMetadata} 
            onEnded={handleNext} 
            src={currentSong?.url}
            loop={playbackMode === 'single-loop'}
          />
          
          <div className="flex items-center justify-between w-full p-2 h-14">
              <div className="flex items-center w-1/4 min-w-0 ml-4">
                  {isLoading ? (
                      <div className="flex items-center space-x-3">
                          <Loader2 size={40} className="animate-spin text-gray-400" />
                          <div>
                              <p className="text-sm font-semibold">加载中...</p>
                          </div>
                      </div>
                  ) : currentSong ? (
                      <div className="flex items-center space-x-3 truncate">
                          <img 
                              src={currentSong.cover || '/images/default-album-art.png'} 
                              alt={currentSong.title}
                              className="w-8 h-8 rounded-md object-cover flex-shrink-0"
                          />
                          <div className="truncate">
                              <p className="font-semibold text-white truncate">{currentSong.title}</p>
                              <p className="text-sm text-gray-400 truncate">{currentSong.artist}</p>
                          </div>
                      </div>
                  ) : (
                    <div className="flex items-center space-x-3">
                       <Music size={40} className="text-gray-500" />
                        <div>
                            <p className="text-sm font-semibold">暂无音乐</p>
                            <p className="text-xs text-gray-500">请检查播放列表</p>
                        </div>
                    </div>
                  )}
              </div>

              <div className="flex flex-col items-center justify-center w-1/2">
                  <div className="flex items-center gap-2">
                      <button onClick={togglePlaybackMode} className="p-2 transition-colors duration-200 hover:text-primary" aria-label="切换播放模式">
                          {playbackMode === 'single-loop' && <Repeat1 size={16} className="text-primary"/>}
                          {playbackMode === 'playlist-loop' && <Repeat size={16} />}
                          {playbackMode === 'shuffle' && <Shuffle size={16}  />}
                      </button>
                      <button onClick={handlePrev} className="p-2 transition-colors duration-200 hover:text-primary disabled:text-gray-600" disabled={songs.length < 2} aria-label="上一首">
                          <SkipBack size={20} />
                      </button>
                      <button 
                          onClick={handlePlayPause}
                          className="p-1.5 bg-primary text-black rounded-full shadow-lg transform transition-transform hover:scale-110 disabled:bg-gray-700 disabled:cursor-not-allowed"
                          disabled={!currentSong}
                          aria-label={isPlaying ? "暂停" : "播放"}
                      >
                          {isPlaying ? <Pause size={22} /> : <Play size={22} />}
                      </button>
                      <button onClick={handleNext} className="p-2 transition-colors duration-200 hover:text-primary disabled:text-gray-600" disabled={songs.length < 2} aria-label="下一首">
                          <SkipForward size={20} />
                      </button>
                      <button onClick={() => setIsExpanded(!isExpanded)} className="p-2 transition-colors duration-200 hover:text-primary" aria-label={isExpanded ? "收起播放列表" : "展开播放列表"}>
                          <ChevronUp size={20} className={`transition-transform ${isExpanded ? 'rotate-180' : ''}`}/>
                      </button>
                  </div>
                  <div className="w-full max-w-md flex items-center gap-2 mt-1">
                        <span className="text-xs font-mono w-10 text-center">{formatTime(currentTime)}</span>
                        <input
                          type="range"
                          min="0"
                          max={duration || 0}
                          value={currentTime}
                          onChange={handleProgressChange}
                          aria-label="播放进度"
                          className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer range-sm"
                          style={{'--progress': `${(currentTime / duration) * 100}%`} as React.CSSProperties}
                          disabled={!currentSong}
                        />
                        <span className="text-xs font-mono w-10 text-center">{formatTime(duration)}</span>
                  </div>
              </div>

              <div className="flex items-center justify-end w-1/4 mr-4">
                  <button onClick={toggleMute} className="p-2 transition-colors duration-200 hover:text-primary" aria-label={isMuted ? "取消静音" : "静音"}>
                      {getVolumeIcon()}
                  </button>
                   <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        value={isMuted ? 0 : volume}
                        onChange={handleVolumeChange}
                        aria-label="音量控制"
                        className="w-24 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer range-sm"
                        style={{'--progress': `${(isMuted ? 0 : volume) * 100}%`} as React.CSSProperties}
                   />
              </div>
          </div>
        </motion.footer>
    );
});

MusicPlayer.displayName = 'MusicPlayer';

export default MusicPlayer; 