'use client';

import React, { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

interface ClientOnlyAnimatePresenceProps {
  children: React.ReactNode;
  mode?: 'wait' | 'sync' | 'popLayout';
  initial?: boolean;
  onExitComplete?: () => void;
  exitBeforeEnter?: boolean;
}

/**
 * 客户端专用的AnimatePresence包装器
 * 确保只在客户端渲染时使用AnimatePresence
 */
export const ClientOnlyAnimatePresence: React.FC<ClientOnlyAnimatePresenceProps> = ({
  children,
  mode,
  initial = true,
  onExitComplete,
  exitBeforeEnter,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 在服务端或未挂载时，直接渲染子组件（无动画）
  if (!isMounted) {
    return <>{children}</>;
  }

  // 客户端渲染时使用AnimatePresence
  return (
    <AnimatePresence
      mode={mode}
      initial={initial}
      onExitComplete={onExitComplete}
      {...(exitBeforeEnter && { exitBeforeEnter })}
    >
      {children}
    </AnimatePresence>
  );
};

/**
 * 客户端专用的motion包装器
 */
interface ClientOnlyMotionProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
  style?: React.CSSProperties;
  initial?: any;
  animate?: any;
  exit?: any;
  transition?: any;
  variants?: any;
  layout?: boolean | string;
  layoutId?: string;
  onClick?: () => void;
  onHoverStart?: () => void;
  onHoverEnd?: () => void;
}

export const ClientOnlyMotion: React.FC<ClientOnlyMotionProps> = ({
  children,
  as = 'div',
  className,
  style,
  initial,
  animate,
  exit,
  transition,
  variants,
  layout,
  layoutId,
  onClick,
  onHoverStart,
  onHoverEnd,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 在服务端或未挂载时，渲染普通元素
  if (!isMounted) {
    return React.createElement(
      as,
      {
        className,
        style,
        onClick,
        onMouseEnter: onHoverStart,
        onMouseLeave: onHoverEnd,
      },
      children
    );
  }

  // 客户端渲染时使用motion
  const MotionComponent = motion[as as keyof typeof motion] as any;
  
  return (
    <MotionComponent
      className={className}
      style={style}
      initial={initial}
      animate={animate}
      exit={exit}
      transition={transition}
      variants={variants}
      layout={layout}
      layoutId={layoutId}
      onClick={onClick}
      onHoverStart={onHoverStart}
      onHoverEnd={onHoverEnd}
    >
      {children}
    </MotionComponent>
  );
};

// 导出默认的AnimatePresence以保持兼容性
export { AnimatePresence, motion } from 'framer-motion';
export default ClientOnlyAnimatePresence;
