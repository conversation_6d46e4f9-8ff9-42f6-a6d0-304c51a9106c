package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@TableName("login_history")
public class LoginHistory {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String ipAddress;

    private String deviceFingerprint;

    private Timestamp loginTimestamp;

    public LoginHistory(Long userId, String ipAddress, String deviceFingerprint) {
        this.userId = userId;
        this.ipAddress = ipAddress;
        this.deviceFingerprint = deviceFingerprint;
        this.loginTimestamp = new Timestamp(System.currentTimeMillis());
    }
} 