import { getArticleById, getAllArticles } from '@/services/api';
import { KnowledgeBaseArticle } from '@/types/KnowledgeBaseArticle';
import { Suspense } from 'react';
import ArticleClient from './ArticleClient';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// This function is called at build time
export async function generateStaticParams() {
  // Return an empty array to disable static generation of these pages.
  // They will be rendered dynamically at request time.
  return [];
}

// This function generates the page's metadata (e.g., title)
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const { id } = params;
  try {
    const article = await getArticleById(id);
    if (!article) {
      return { title: '文章未找到' };
    }
    return {
      title: `${article.title} - 知识库`,
      description: article.summary || '知识库文章',
    };
  } catch (error) {
    console.error(`Failed to fetch metadata for article ${id}:`, error);
    return {
      title: '错误',
      description: '加载文章时出错。',
    };
  }
}

export default async function ArticlePage({ params }: { params: { id: string } }) {
  const { id } = params;
  const article = await getArticleById(id);

  if (!article) {
    notFound();
  }
  
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-32 text-center">
        <p>加载文章中...</p>
      </div>
    }>
      <div className="py-32 md:py-40">
        <ArticleClient article={article} />
      </div>
    </Suspense>
  );
} 