'use client';

import { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import apiService, { updateUserProfile } from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import AvatarDisplay from '@/components/AvatarDisplay';
import { AvatarCropModal } from './AvatarCropModal';
import { compressImageWithPreset, validateImageSize, validateImageType } from '@/lib/imageCompression';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input";

const AccountSettings = () => {
    const { user, setUser, updateUserAvatar, logout } = useAuth();
    const router = useRouter();
    const [isUploading, setIsUploading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [newUsername, setNewUsername] = useState(user?.username || '');
    const [isSavingUsername, setIsSavingUsername] = useState(false);
    const [paymentAccounts, setPaymentAccounts] = useState({
        alipayAccount: user?.alipayAccount || '',
        wechatAccount: user?.wechatAccount || ''
    });
    const [isSavingPayment, setIsSavingPayment] = useState(false);
    
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];

            // 使用新的验证函数
            if (!validateImageSize(file, 5)) {
                toast.error("文件大小不能超过 5MB。");
                return;
            }
            if (!validateImageType(file, ['image/png', 'image/jpeg', 'image/webp'])) {
                toast.error("只支持上传 JPG, PNG 或 WebP 格式的图片。");
                return;
            }

            const reader = new FileReader();
            reader.onloadend = () => {
                setSelectedImage(reader.result as string);
                setIsModalOpen(true);
            };
            reader.readAsDataURL(file);
        }
        if(fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const handleAvatarUpload = async (imageBlob: Blob) => {
        setIsUploading(true);
        try {
            // 压缩图片
            const compressedBlob = await compressImageWithPreset(imageBlob, 'avatar');
            const file = new File([compressedBlob], "avatar.jpg", { type: "image/jpeg" });

            const formData = new FormData();
            formData.append('avatar', file);

            const response = await apiService.post('/api/user/avatar', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            const { avatarUrl, avatarVersion } = response.data;

            if (avatarUrl && avatarVersion !== undefined) {
                updateUserAvatar(avatarUrl, avatarVersion);
            } else {
                throw new Error("从服务器返回的头像数据格式无效。");
            }

            toast.success("头像更新成功！", {
                duration: 2000,
            });
        } catch (err: any) {
            toast.error(err.response?.data?.message || '头像上传失败。');
        } finally {
            setIsUploading(false);
            setIsModalOpen(false);
            setSelectedImage(null);
        }
    };

    const handleUsernameChange = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!newUsername.trim() || newUsername.trim() === user?.username) {
            toast.info("请输入一个新的、与当前不同的用户名。");
            return;
        }
        setIsSavingUsername(true);
        try {
            const result = await updateUserProfile({
                username: newUsername.trim(),
                alipayAccount: user?.alipayAccount,
                wechatAccount: user?.wechatAccount
            });

            const newUsernameValue = newUsername.trim();

            // 如果返回了新token，说明用户名发生了变化，需要更新token
            if (result.token) {
                localStorage.setItem('token', result.token);
                // 更新API请求头
                apiService.defaults.headers.common['Authorization'] = `Bearer ${result.token}`;
            }

            if(user) {
                // 只更新需要的字段，保留其他字段（如fullAvatarUrl等）
                setUser({
                    ...user,
                    username: newUsernameValue,
                    alipayAccount: user.alipayAccount,
                    wechatAccount: user.wechatAccount
                });
            }

            toast.success("用户名更新成功！正在跳转到新的个人资料页面...");

            // 延迟一下让用户看到成功提示，然后重定向到新的用户名URL
            setTimeout(() => {
                router.push(`/profile/${newUsernameValue}`);
            }, 1500);
        } catch (err: any) {
             toast.error(err.response?.data?.message || '用户名更新失败。');
        } finally {
            setIsSavingUsername(false);
        }
    };

    const handlePaymentAccountsChange = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSavingPayment(true);
        try {
            const result = await updateUserProfile({
                username: user?.username || '',
                alipayAccount: paymentAccounts.alipayAccount,
                wechatAccount: paymentAccounts.wechatAccount,
            });

            // 支付账户更新不会改变用户名，所以不应该有新token，但为了安全起见还是检查一下
            if (result.token) {
                localStorage.setItem('token', result.token);
                apiService.defaults.headers.common['Authorization'] = `Bearer ${result.token}`;
            }

            if(user) {
                // 只更新需要的字段，保留其他字段（如fullAvatarUrl等）
                setUser({
                    ...user,
                    alipayAccount: paymentAccounts.alipayAccount,
                    wechatAccount: paymentAccounts.wechatAccount
                });
            }
            toast.success("提现账户更新成功！");
        } catch (err: any) {
            toast.error(err.response?.data?.message || '提现账户更新失败。');
        } finally {
            setIsSavingPayment(false);
        }
    };

    const handleDeleteAccount = async () => {
        setIsDeleting(true);
        try {
            await apiService.delete('/api/user/me');
            toast.success("您的账户已成功删除。");
            logout(); 
        } catch (err: any) {
            toast.error(err.response?.data?.message || '账户删除失败，请稍后再试。');
        } finally {
            setIsDeleting(false);
        }
    };

  return (
    <div className="text-white pt-2 pb-6 px-1">
        <div>
            <h3 className="text-lg font-semibold mb-4 text-gray-300">个人资料图片</h3>
            <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
                <AvatarDisplay
                    avatarUrl={user?.avatarUrl}
                    serialNumber={user?.serialNumber}
                    avatarVersion={user?.avatarVersion}
                    size={80}
                    className="h-20 w-20 rounded-full"
                    isLoading={isUploading}
                />
                <div className="flex-grow text-center sm:text-left">
                    <Button
                        variant="outline"
                        className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20 text-white"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                    >
                        {isUploading ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : null}
                        上传新头像
                    </Button>
                    <p className="text-xs text-gray-500 mt-2">支持 PNG, JPG, WebP。最大 5MB。</p>
                </div>
            </div>
        </div>

        <div className="my-8 border-b border-gray-700/50"></div>

        <div>
            <h3 className="text-lg font-semibold mb-4 text-gray-300">修改用户名</h3>
            <form onSubmit={handleUsernameChange} className="flex items-start space-x-2">
                <div className="flex-grow">
                    <Input
                        type="text"
                        value={newUsername}
                        onChange={(e) => setNewUsername(e.target.value)}
                        placeholder="输入新的用户名"
                        className="bg-white/5 border-white/20"
                        />
                    <p className="text-xs text-gray-500 mt-2">用户名是您在社区中的唯一标识。</p>
                </div>
                <Button type="submit" variant="outline" className="bg-white/10 hover:bg-white/20 border-white/20 text-white" disabled={isSavingUsername || newUsername === user?.username}>
                    {isSavingUsername ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : null}
                    保存
                </Button>
            </form>
            <p className="text-xs text-gray-500 mt-4 text-center sm:text-left">
                提示：想更换您的个人主页横幅吗？只需在您的公开个人资料页上点击它即可。
            </p>
        </div>

        <input 
            type="file" 
            ref={fileInputRef}
            onChange={handleFileSelect}
            className="hidden"
            accept="image/png, image/jpeg, image/webp"
            aria-label="Avatar Upload"
        />

        {selectedImage && (
            <AvatarCropModal
                isOpen={isModalOpen}
                onClose={() => {
                    setIsModalOpen(false);
                    setSelectedImage(null);
                }}
                image={selectedImage}
                onSave={handleAvatarUpload}
            />
        )}
    </div>
  );
};

export default AccountSettings; 