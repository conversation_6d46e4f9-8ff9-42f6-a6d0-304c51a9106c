'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { SteppedCreatePostForm } from './components/SteppedCreatePostForm';
import { CommunityPost } from '@/types/CommunityPost';
import { motion } from 'framer-motion';
import { gtnhProgression } from '@/lib/gtnh-progression-data';
import { CommunitySections } from './components/CommunitySections';
import { getPartitionDisplayInfoFromTopic } from '@/lib/partition-mapping';
import { isPostInSection, isPostInCategory, isPostInSubcategory } from '@/lib/smart-section-mapping';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare, ThumbsUp, Dot, Pin, Shield, Globe, BookCopy, BookOpen, GitPullRequest, MessageCircle as IconMessageCircle, Mail } from 'lucide-react';
import { CommunityGuidelinesModal } from './components/CommunityGuidelinesModal';
import { getCommunityPosts, createCommunityPost, togglePostLike, getPinnedPosts, getNonPinnedPosts } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { resolveAvatarUrl } from '@/lib/utils';
import AvatarDisplay from '@/components/AvatarDisplay';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useMessageStore } from '@/stores/messageStore';


const CommunityPage = () => {
    const [posts, setPosts] = useState<CommunityPost[]>([]);
    const [pinnedPosts, setPinnedPosts] = useState<CommunityPost[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // 获取消息系统的方法
    const { startNewConversation } = useMessageStore();
    const [error, setError] = useState<string | null>(null);
    const { token, user } = useAuth();

    const [isCreatePostModalOpen, setCreatePostModalOpen] = useState(false);
    const [activeFilter, setActiveFilter] = useState<{ type: 'era' | 'tier' | 'topic' | 'section' | 'category' | 'subcategory', value: string } | null>(null);
    const [isGuidelinesModalOpen, setGuidelinesModalOpen] = useState(false);
    const [isPartitionCollapsed, setIsPartitionCollapsed] = useState(false);



    useEffect(() => {
        const fetchData = async () => {
            try {
                setIsLoading(true);

                const [fetchedPosts, fetchedPinnedPosts] = await Promise.all([
                    getNonPinnedPosts(),
                    getPinnedPosts()
                ]);

                setPosts(fetchedPosts);
                setPinnedPosts(fetchedPinnedPosts);
                setError(null);
            } catch (err) {
                setError('无法加载社区数据，请稍后重试。');
                toast.error('数据加载失败');
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    const handlePostSubmit = async (newPostData: { title: string; content: string; partition: string }) => {
        if (!token || !user) {
            toast.error('请先登录再发帖');
            return;
        }
        try {
            const newPost = await createCommunityPost(newPostData, token);
            // Prepend the new post to the list for immediate UI update
            setPosts(prevPosts => [newPost, ...prevPosts]);
            setCreatePostModalOpen(false);
            toast.success('帖子发布成功！');
        } catch (error) {
            toast.error('帖子发布失败，请稍后重试。');
        }
    };

    const handleToggleLike = async (postId: number, e: React.MouseEvent) => {
        e.stopPropagation();
        if (!token) {
            toast.error("请先登录再点赞");
            return;
        }

        try {
            const isNowLiked: boolean = await togglePostLike(postId);

            setPosts(prevPosts =>
                prevPosts.map(p => {
                    if (p.id === postId) {
                        const currentLikes = p._count?.likes ?? 0;
                        const newLikes = isNowLiked ? currentLikes + 1 : Math.max(0, currentLikes - 1);
                        
                        return {
                            ...p,
                            likedByCurrentUser: isNowLiked,
                            _count: {
                                ...p._count,
                                comments: p._count?.comments ?? 0,
                                likes: newLikes,
                            },
                        };
                    }
                    return p;
                })
            );
        } catch (error) {
            toast.error("操作失败，请稍后重试");
        }
    };

    // 智能过滤函数，能够正确处理不同类型的过滤器
    const filteredPosts = useMemo(() => {
        if (!activeFilter) {
            return posts;
        }

        const filtered = posts.filter(post => {
            // 检查帖子是否有基本信息
            if (!post.title || !post.content) {
                return false;
            }

            switch (activeFilter.type) {
                case 'era':
                    // 传统的era过滤
                    return post.topic?.era === activeFilter.value;

                case 'tier':
                    // 传统的tier过滤
                    return post.topic?.tier === activeFilter.value;

                case 'topic':
                    // 传统的topic过滤
                    return post.topic?.name === activeFilter.value;

                case 'section':
                    // 使用智能分区映射判断帖子是否属于该分区
                    return isPostInSection(post, activeFilter.value);

                case 'category':
                    // 如果是阶段分区的类别，使用传统匹配
                    if (post.topic?.era && activeFilter.value === post.topic.era) {
                        return true;
                    }

                    // 对于其他分区，使用智能类别映射
                    // 从activeFilter.value中提取分区ID和类别ID
                    const categoryParts = activeFilter.value.split('|');
                    if (categoryParts.length === 2) {
                        const [sectionId, categoryId] = categoryParts;
                        return isPostInCategory(post, sectionId, categoryId);
                    }

                    return false;

                case 'subcategory':
                    // 对于传统的阶段分区格式
                    if (activeFilter.value.includes('|')) {
                        const parts = activeFilter.value.split('|');
                        if (parts.length === 3) {
                            // 如果是传统的era|tier|topic格式
                            if (!parts[0].includes('|')) {
                                const [era, tier, topic] = parts;
                                return post.topic?.era === era &&
                                       post.topic?.tier === tier &&
                                       post.topic?.name === topic;
                            }

                            // 如果是新的section|category|subcategory格式
                            const [sectionId, categoryId, subcategoryId] = parts;
                            return isPostInSubcategory(post, sectionId, categoryId, subcategoryId);
                        }
                    }

                    // 直接的topic名称匹配
                    return post.topic?.name === activeFilter.value;

                default:
                    return false;
            }
        });

        return filtered;
    }, [posts, activeFilter]);

    const trendingPosts = useMemo(() => {
        return [...posts]
            .sort((a, b) => {
                const scoreA = (a._count?.likes ?? 0) + (a._count?.comments ?? 0) * 2;
                const scoreB = (b._count?.likes ?? 0) + (b._count?.comments ?? 0) * 2;
                return scoreB - scoreA;
            })
            .slice(0, 5);
    }, [posts]);

    const handleCreatePostClick = () => {
        if (user) {
            setCreatePostModalOpen(true);
        } else {
            toast.error('请先登录再发帖', {
                action: {
                    label: '前往登录',
                    onClick: () => window.location.href = '/login',
                },
            });
        }
    };

    // 处理发私信 - 打开消息中心
    const handleSendPrivateMessage = (userId: number, username: string) => {
        if (!user) {
            toast.error('请先登录');
            return;
        }

        if (userId === user.id) {
            toast.error('不能给自己发私信');
            return;
        }

        // 使用messageStore打开消息中心并开始对话
        startNewConversation(userId);
        toast.success(`正在与 ${username} 开始对话`);
    };



    const onlineTools = [
        { name: "Greginator", url: "https://divran.github.io/greginator/", description: "多功能计算器，含超频、矿脉查找等" },
        { name: "GTNH-Flow", url: "https://github.com/OrderedSet86/gtnh-flow", description: "生产线流程图设计与自动化计算工具" },
        { name: "神秘时代研究助手", url: "https://www.mcmod.cn/tools/tctool/", description: "快速查找神秘时代研究笔记连线" },
        { name: "GTNH 任务书在线查阅", url: "https://gtnhquestsbook.top/", description: "在线查阅 GTNH 任务书，方便玩家跟踪任务进度" }
    ];

    return (
        <div className="relative min-h-screen text-zinc-300">
            <div className="fixed inset-0 -z-10">
                <div 
                    className="absolute inset-0 w-full h-full bg-cover bg-center"
                    style={{ backgroundImage: "url('/images/background/Community.webp')" }}
                />
                <div className="absolute inset-0 bg-black/60" />
            </div>

            {/* Hero Section */}
            <div className="relative title-frame mt-20">
                 <span className="title-frame-corners" aria-hidden="true"></span>
                <div className="relative h-[30vh] flex items-center justify-center text-center overflow-hidden">
                    <div className="absolute inset-0">
                        <div className="absolute inset-0 bg-gradient-to-t from-zinc-950 to-transparent"></div>
                    </div>
                    <div className="relative z-10 px-4">
                        <motion.div
                            initial={{ opacity: 0, y: 0 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.7 }}
                        >
                            <h1 className="text-5xl md:text-7xl font-bold text-white mb-4 title-with-shine">
                                社区中心
                            </h1>
                            <p className="text-lg md:text-xl text-zinc-300 max-w-2xl mx-auto mb-8" style={{ textShadow: '0 0 15px rgba(0, 0, 0, 0.5)' }}>
                                分享你的知识、经验和史诗级基地
                            </p>
                            <div className="flex justify-center gap-4">
                                <Button size="lg" variant="outline" onClick={() => setGuidelinesModalOpen(true)} className="text-white border-white/50 hover:bg-white/10">
                                    <Shield size={20} className="mr-2"/> 社区公约
                                </Button>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </div>
            
            <div className="container mx-auto px-4 pb-24 mt-6">
                <SteppedCreatePostForm
                    isOpen={isCreatePostModalOpen}
                    onClose={() => setCreatePostModalOpen(false)}
                    onSubmit={handlePostSubmit}
                    partitions={gtnhProgression}
                />




                <CommunityGuidelinesModal isOpen={isGuidelinesModalOpen} onClose={() => setGuidelinesModalOpen(false)} />

                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">

                    {/* Left Sidebar: Enhanced Community Sections */}
                    <aside className="md:col-span-1">
                        <div className="sticky top-24 space-y-4">
                            <CommunitySections
                                activeFilter={activeFilter}
                                onFilterChange={setActiveFilter}
                            />
                        </div>
                    </aside>

                    {/* Main Content: Post Feed */}
                    <div className="md:col-span-2 space-y-6">
                        {/* Create Post Card */}
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.1 }}
                            className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-4 flex items-center gap-4"
                            >
                            {user ? (
                                <>
                                    <AvatarDisplay 
                                        avatarUrl={user.avatarUrl}
                                        serialNumber={user.serialNumber}
                                        avatarVersion={user.avatarVersion}
                                        size={40}
                                    />
                                <button
                                        onClick={handleCreatePostClick}
                                        className="flex-grow text-left text-zinc-400 bg-zinc-900/50 border border-zinc-700 rounded-md px-4 py-2 hover:bg-zinc-800/50 hover:text-white transition-colors"
                                >
                                    创建新帖子...
                                </button>
                                </>
                            ) : (
                                <div className="w-full text-center text-zinc-400">
                                    <Link href="/login" className="text-sky-400 hover:underline">登录</Link> 后发表帖子
                                </div>
                            )}
                            </motion.div>
                        
                        {/* Post List */}
                        {isLoading ? (
                            <PostFeedSkeleton />
                        ) : error ? (
                            <div className="text-center text-red-400 bg-black/20 backdrop-blur-md rounded-xl p-8 shadow-lg">{error}</div>
                        ) : (
                            filteredPosts.map(post => (
                                <PostCard
                                    key={post.id}
                                    post={post}
                                    onToggleLike={handleToggleLike}
                                    user={user}
                                    onSendPrivateMessage={handleSendPrivateMessage}
                                />
                            ))
                        )}
                    </div>

                    {/* Right Sidebar: Trending & Resources */}
                    <aside className="md:col-span-1">
                        <div className="sticky top-24 space-y-4">
                            
                            <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
                                <CardHeader>
                                    <CardTitle className="text-lg font-semibold text-zinc-200 flex items-center gap-2">
                                        <Pin className="text-blue-400"/>置顶帖子
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="px-6 pb-6">
                                    {isLoading ? <TrendingSkeleton /> : <PinnedPosts posts={pinnedPosts} />}
                                </CardContent>
                            </Card>

                            {/* Online Tools Section */}
                            <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
                                <CardHeader>
                                    <CardTitle className="text-lg font-semibold text-zinc-200 flex items-center gap-2">
                                        <Globe size={18} />
                                        在线工具
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-3">
                                        {onlineTools.map((tool, index) => (
                                            <li key={index}>
                                                <a href={tool.url} target="_blank" rel="noopener noreferrer" className="group text-blue-400 hover:text-blue-300 transition-colors">
                                                    <h5 className="font-semibold">{tool.name}</h5>
                                                    <p className="text-xs text-zinc-400 group-hover:text-zinc-300 transition-colors">{tool.description}</p>
                                                </a>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Community Resources Section */}
                            <Card className="bg-black/20 backdrop-blur-md border-zinc-800 shadow-lg">
                                <CardHeader>
                                    <CardTitle className="text-lg font-semibold text-zinc-200 flex items-center gap-2">
                                        <BookCopy size={18} />
                                        社区资源
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-3">
                                        <li><a href="https://gtnh.miraheze.org/wiki/Main_Page" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors flex items-center gap-2"><BookOpen size={16} /> 官方英文 Wiki</a></li>
                                        <li><a href="https://gtnh.huijiwiki.com/wiki/%E9%A6%96%E9%A1%B5" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors flex items-center gap-2"><BookOpen size={16} /> GTNH 中文维基 (灰机wiki)</a></li>
                                        <li><a href="https://www.mcmod.cn/modpack/1.html" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors flex items-center gap-2"><BookOpen size={16} /> GTNH MC百科页面</a></li>
                                        <li><a href="https://github.com/GTNewHorizons/GT-New-Horizons-Modpack" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors flex items-center gap-2"><GitPullRequest size={16} /> GitHub Repo</a></li>
                                        <li><a href="https://discord.gg/gtnh" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors flex items-center gap-2"><IconMessageCircle size={16} /> Discord</a></li>
                                    </ul>
                                </CardContent>
                            </Card>
                        </div>
                    </aside>
                </div>
            </div>


        </div>
    );
};

const PostFeedSkeleton = () => (
    <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-black/20 backdrop-blur-md border border-zinc-800 rounded-xl shadow-lg p-6">
                <div className="flex items-center mb-4">
                    <Skeleton className="h-10 w-10 rounded-full mr-4" />
                    <div className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-16" />
                    </div>
                </div>
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-5/6 mb-4" />
                <div className="flex justify-between items-center">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                </div>
            </div>
        ))}
    </div>
);

const PinnedPosts: React.FC<{ posts: CommunityPost[] }> = ({ posts }) => (
    <div className="space-y-2">
        {posts.length === 0 ? (
            <div className="text-center text-zinc-500 py-4">
                <Pin size={24} className="mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无置顶帖子</p>
            </div>
        ) : (
            <ul className="space-y-2">
                {posts.map(post => (
                    <li key={post.id} className="text-sm">
                        <Link href={`/community/post/${post.id}`} className="block hover:bg-zinc-800/50 p-2 rounded-md transition-colors">
                            <div className="flex items-start gap-2">
                                <Pin size={14} className="text-blue-400 mt-0.5 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                    <span className="font-semibold block truncate text-zinc-200">{post.title}</span>
                                    <span className="text-xs text-zinc-400">{post._count?.likes} 点赞 • {post._count?.comments} 评论</span>
                                </div>
                            </div>
                        </Link>
                    </li>
                ))}
            </ul>
        )}
    </div>
);

const TrendingPosts: React.FC<{ posts: CommunityPost[] }> = ({ posts }) => (
     <ul className="space-y-2">
         {posts.map(post => (
             <li key={post.id} className="text-sm">
                 <Link href={`/community/post/${post.id}`} className="block hover:bg-zinc-800/50 p-2 rounded-md transition-colors">
                     <span className="font-semibold block truncate text-zinc-200">{post.title}</span>
                     <span className="text-xs text-zinc-400">{post._count?.likes} 点赞 • {post._count?.comments} 评论</span>
                 </Link>
             </li>
         ))}
     </ul>
);

const TrendingSkeleton = () => (
    <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 w-4/5" />
            </div>
        ))}
    </div>
);

const PostCard: React.FC<{
    post: CommunityPost;
    onToggleLike: (postId: number, e: React.MouseEvent) => void;
    user: any;
    onSendPrivateMessage: (userId: number, username: string) => void;
}> = ({ post, onToggleLike, user, onSendPrivateMessage }) => {
    const createPreview = (markdown: string, maxLength: number) => {
        const plainText = markdown
            .replace(/!\[.*?\]\(.*?\)/g, '')    // 移除图片
            .replace(/\[(.*?)\]\(.*?\)/g, '$1')  // 将链接替换为链接文本
            .replace(/<[^>]+>/g, '')              // 移除 HTML 标签
            .replace(/#{1,6}\s/g, '')             // 移除标题符号
            .replace(/(\*|_|`)+/g, '')            // 移除加粗、斜体、代码等标记
            .replace(/\s\s+/g, ' ')               // 合并多余的空格
            .trim();

        if (plainText.length <= maxLength) {
            return plainText;
        }
        return plainText.substring(0, maxLength).trim() + '...';
    };

    return (
        <motion.div
            layout
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg overflow-hidden"
        >
            <CardHeader className="p-4">
                <CardTitle className="text-xl font-bold text-white break-words">
                    <Link href={`/community/post/${post.id}`} className="hover:text-sky-400 transition-colors">
                        {post.title}
                    </Link>
                </CardTitle>
                <div className="flex items-center text-xs text-zinc-400 gap-2 flex-wrap pt-2">
                    <AvatarDisplay
                        avatarUrl={post.author.avatarUrl}
                        serialNumber={post.author.serialNumber}
                        avatarVersion={post.author.avatarVersion}
                        size={24}
                        className="h-6 w-6"
                    />
                    <span>{post.author.username}</span>

                    {/* 私信按钮 */}
                    {user && user.id !== post.author.id && (
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs text-zinc-400 hover:text-zinc-200 hover:bg-zinc-800/50"
                            onClick={() => onSendPrivateMessage(post.author.id, post.author.username)}
                        >
                            <Mail className="w-3 h-3 mr-1" />
                            私信
                        </Button>
                    )}

                    <Dot />
                    <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                    <Dot />
                    <span className="bg-zinc-800/50 px-2 py-0.5 rounded">
                        {(() => {
                            const partitionInfo = getPartitionDisplayInfoFromTopic(post.topic);
                            return partitionInfo ? partitionInfo.shortDisplayName : `${post.topic.era} - ${post.topic.name}`;
                        })()}
                    </span>
                </div>
            </CardHeader>
            <CardContent className="p-4 pt-0 text-zinc-300">
                <p className="text-sm text-zinc-400 h-10 overflow-hidden">
                    {createPreview(post.content, 70)}
                </p>
            </CardContent>
            <CardFooter className="bg-zinc-900/30 p-2 flex justify-end gap-4">
                <button
                    onClick={(e) => onToggleLike(post.id, e)}
                    className={`flex items-center gap-1.5 text-sm text-zinc-400 hover:text-white transition-colors ${post.likedByCurrentUser ? 'text-zinc-200' : ''}`}
                >
                    <ThumbsUp size={16} />
                    {post._count?.likes ?? 0}
                </button>
                <Link href={`/community/post/${post.id}#comments`} className="flex items-center gap-1.5 text-sm text-zinc-400 hover:text-white transition-colors">
                    <MessageSquare size={16} />
                    {post._count?.comments ?? 0}
                </Link>
            </CardFooter>
        </motion.div>
    );
};

export default CommunityPage; 