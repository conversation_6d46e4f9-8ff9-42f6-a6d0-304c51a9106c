/**
 * 消息系统相关的类型定义
 */

import { User } from './User';

// 消息类型枚举
export enum MessageType {
  PRIVATE = 'PRIVATE',           // 私信
  SYSTEM = 'SYSTEM',             // 系统通知
  COMMUNITY_REPLY = 'COMMUNITY_REPLY',     // 帖子回复通知
  COMMUNITY_LIKE = 'COMMUNITY_LIKE',       // 点赞通知
  COMMUNITY_FOLLOW = 'COMMUNITY_FOLLOW',   // 关注通知
  ANNOUNCEMENT = 'ANNOUNCEMENT'   // 系统公告
}

// 消息状态枚举
export enum MessageStatus {
  UNREAD = 'UNREAD',
  READ = 'READ',
  DELETED = 'DELETED'
}

// 消息优先级枚举
export enum MessagePriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 基础消息接口
export interface Message {
  id: number;
  type: MessageType;
  status: MessageStatus;
  priority: MessagePriority;
  title: string;
  content: string;
  senderId?: number;
  sender?: User;
  receiverId: number;
  receiver?: User;
  relatedPostId?: number;
  relatedCommentId?: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  readAt?: string;
}

// 私信消息
export interface PrivateMessage extends Message {
  type: MessageType.PRIVATE;
  senderId: number;
  sender: User;
  conversationId: string;
}

// 系统通知
export interface SystemMessage extends Message {
  type: MessageType.SYSTEM | MessageType.ANNOUNCEMENT;
  senderId?: never;
  sender?: never;
  systemType: 'UPDATE' | 'MAINTENANCE' | 'ANNOUNCEMENT' | 'WARNING';
}

// 社区互动通知
export interface CommunityMessage extends Message {
  type: MessageType.COMMUNITY_REPLY | MessageType.COMMUNITY_LIKE | MessageType.COMMUNITY_FOLLOW;
  senderId: number;
  sender: User;
  relatedPostId?: number;
  relatedCommentId?: number;
  actionType: string;
}

// 消息会话
export interface Conversation {
  id: string;
  participants: User[];
  lastMessage?: Message;
  lastMessageAt: string;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

// 消息统计
export interface MessageStats {
  totalUnread: number;
  privateUnread: number;
  systemUnread: number;
  communityUnread: number;
  announcementUnread: number;
}

// 消息查询参数
export interface MessageQueryParams {
  type?: MessageType;
  status?: MessageStatus;
  senderId?: number;
  page?: number;
  limit?: number;
  search?: string;
  startDate?: string;
  endDate?: string;
}

// 发送消息请求
export interface SendMessageRequest {
  type: MessageType;
  receiverId?: number;
  title: string;
  content: string;
  priority?: MessagePriority;
  relatedPostId?: number;
  relatedCommentId?: number;
  metadata?: Record<string, any>;
}

// 消息操作响应
export interface MessageResponse {
  success: boolean;
  message: string;
  data?: Message | Message[];
}

// 消息分页响应
export interface MessagePageResponse {
  messages: Message[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 会话分页响应
export interface ConversationPageResponse {
  conversations: Conversation[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// WebSocket消息事件
export interface WebSocketMessageEvent {
  type: 'NEW_MESSAGE' | 'MESSAGE_READ' | 'MESSAGE_DELETED' | 'TYPING' | 'ONLINE_STATUS';
  data: any;
  timestamp: string;
}

// 在线状态
export interface OnlineStatus {
  userId: number;
  isOnline: boolean;
  lastSeen: string;
}

// 消息系统配置
export interface MessageSystemConfig {
  enableRealtime: boolean;
  maxMessageLength: number;
  maxConversationParticipants: number;
  messageRetentionDays: number;
  enableMessageSearch: boolean;
  enableFileAttachments: boolean;
  maxAttachmentSize: number;
}
