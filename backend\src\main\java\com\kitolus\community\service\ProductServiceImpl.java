package com.kitolus.community.service;

import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.mapper.ProductMapper;
import com.kitolus.community.entity.ProductStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import com.kitolus.community.exception.InvalidDataException;
import org.springframework.web.multipart.MultipartFile;
import java.util.UUID;
import com.kitolus.community.dto.CreateProductRequestDTO;
import com.kitolus.community.dto.UpdateProductRequestDTO;

@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final ProductMapper productMapper;
    private final FileStorageService fileStorageService;

    @Override
    public List<ProductDTO> getApprovedProductsWithAuthor() {
        return productMapper.selectApprovedProductsWithAuthor();
    }

    @Override
    @Transactional
    public Product createProduct(CreateProductRequestDTO requestDTO, User currentUser) {
        // This method is now intended for cases without file uploads, e.g., using a URL.
        Product newProduct = new Product();
        newProduct.setName(requestDTO.getName());
        newProduct.setPrice(requestDTO.getPrice());
        newProduct.setDescription(requestDTO.getDescription());
        newProduct.setDownloadUrl(requestDTO.getDownloadUrl());
        newProduct.setImageUrl(requestDTO.getImageUrl()); // Directly use the provided image URL
        newProduct.setAuthorName(currentUser.getUsername());
        newProduct.setUserId(currentUser.getId());
        newProduct.setCreatedAt(new java.sql.Timestamp(System.currentTimeMillis()));

        if ("KitolusAdmin".equals(currentUser.getRole())) {
            newProduct.setStatus(ProductStatus.APPROVED);
        } else {
            newProduct.setStatus(ProductStatus.PENDING_APPROVAL);
        }

        productMapper.insert(newProduct);
        return newProduct;
    }

    @Override
    @Transactional
    public Product createProduct(String name, BigDecimal price, String description, String downloadUrl, MultipartFile imageFile, User author) throws IOException {
        Product newProduct = new Product();
        newProduct.setName(name);
        newProduct.setPrice(price);
        newProduct.setDescription(description);
        newProduct.setDownloadUrl(downloadUrl);
        newProduct.setAuthorName(author.getUsername());
        newProduct.setUserId(author.getId());
        newProduct.setCreatedAt(new java.sql.Timestamp(System.currentTimeMillis()));

        // Handle image storage
        if (imageFile != null && !imageFile.isEmpty()) {
            // The storage service will handle file naming to avoid conflicts.
            String storedFileName = fileStorageService.storeProductImage(imageFile);
            // The service returns the actual stored filename.
            newProduct.setImageUrl("/storage/products/" + storedFileName);
        } else {
            newProduct.setImageUrl(null); // Explicitly set to null if no image is provided
        }

        // Set status based on user role
        if ("KitolusAdmin".equals(author.getRole())) {
            newProduct.setStatus(ProductStatus.APPROVED);
        } else {
            newProduct.setStatus(ProductStatus.PENDING_APPROVAL);
        }

        // Directly insert the new product within the same transaction
        productMapper.insert(newProduct);
        return newProduct;
    }

    @Override
    public List<Product> findPurchasedByUserId(Long userId) {
        return productMapper.findPurchasedByUserId(userId);
    }

    @Override
    public List<ProductDTO> findByUserId(Long userId) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).orderByDesc("created_at");
        List<Product> products = productMapper.selectList(queryWrapper);

        return products.stream().map(product -> {
            ProductDTO dto = new ProductDTO();
            dto.setId(product.getId());
            dto.setName(product.getName());
            dto.setPrice(product.getPrice());
            dto.setDescription(product.getDescription());
            dto.setImageUrl(product.getImageUrl());
            dto.setDownloadUrl(product.getDownloadUrl());
            dto.setAuthorName(product.getAuthorName());
            dto.setCreatedAt(product.getCreatedAt());
            dto.setStatus(product.getStatus().name());
            dto.setRejectionReason(product.getRejectionReason());
            dto.setApprovalNotes(product.getApprovalNotes());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Product updateProduct(Long id, UpdateProductRequestDTO requestDTO, User currentUser) {
        Product product = productMapper.selectById(id);

        if (product == null) {
            throw new ResourceNotFoundException("Product not found");
        }

        if (!product.getUserId().equals(currentUser.getId())) {
            throw new AccessDeniedException("You are not the owner of this product");
        }

        if (product.getStatus() == ProductStatus.APPROVED) {
            throw new InvalidDataException("已上架的商品不能被编辑。");
        }

        product.setName(requestDTO.getName());
        product.setPrice(requestDTO.getPrice());
        product.setDescription(requestDTO.getDescription());
        product.setDownloadUrl(requestDTO.getDownloadUrl());

        if (requestDTO.getImageUrl() != null && !requestDTO.getImageUrl().isEmpty()) {
            product.setImageUrl(requestDTO.getImageUrl());
        }

        // If a developer edits a REJECTED product, it goes back to PENDING.
        if(product.getStatus() == ProductStatus.REJECTED) {
            product.setStatus(ProductStatus.PENDING_APPROVAL);
        }

        productMapper.updateById(product);
        return product;
    }

    @Override
    @Transactional
    public void deleteRejectedProduct(Long productId, User currentUser) {
        Product product = productMapper.selectById(productId);

        if (product == null) {
            throw new ResourceNotFoundException("未找到ID为 " + productId + " 的产品");
        }

        if (!Objects.equals(product.getUserId(), currentUser.getId())) {
            throw new AccessDeniedException("您没有权限删除此产品，因为它不属于您。");
        }

        if (product.getStatus() != ProductStatus.REJECTED) {
            throw new IllegalStateException("只有被驳回的商品才能被删除。");
        }

        productMapper.deleteById(productId);
    }

    @Override
    @Transactional
    public void deleteProductAsDeveloper(Long productId, User currentUser) {
        Product product = productMapper.selectById(productId);

        if (product == null) {
            throw new ResourceNotFoundException("未找到ID为 " + productId + " 的产品");
        }

        if (!Objects.equals(product.getUserId(), currentUser.getId())) {
            throw new AccessDeniedException("您没有权限删除此产品，因为它不属于您。");
        }

        if (product.getStatus() == ProductStatus.APPROVED) {
            throw new IllegalStateException("已上架的商品不能被删除。");
        }

        productMapper.deleteById(productId);
    }

    @Override
    public Product getProductById(Long id) {
        return productMapper.selectById(id);
    }

    @Override
    public List<ProductDTO> getMyProducts(Long userId) {
        return findByUserId(userId);
    }

    @Override
    public List<Product> getPurchasedProducts(Long userId) {
        return findPurchasedByUserId(userId);
    }
} 