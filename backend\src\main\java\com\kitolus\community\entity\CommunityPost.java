package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@Data
@TableName("community_post")
public class CommunityPost {
    private Long id;
    private String title;
    private String content;

    @TableField("author_id")
    private Long authorId;

    @TableField("topic_id")
    private Long topicId;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_at")
    private Timestamp updatedAt;

    @TableField("is_pinned")
    private Boolean isPinned;

    @TableField("pinned_at")
    private Timestamp pinnedAt;

    @TableField("pinned_by")
    private Long pinnedBy;

    // These fields are not part of the database table, they will be populated by a JOIN query
    @TableField(exist = false)
    private User author;

    @TableField(exist = false)
    private CommunityTopic topic;

    @TableField(exist = false)
    private boolean likedByCurrentUser;

    // This field will be populated by a separate query in the mapper
    @TableField(exist = false)
    private List<CommunityComment> comments;

    // This field will be populated by a separate query or service logic
    @TableField(exist = false)
    private Set<String> mentionedUsers;

    // This field will be populated by subqueries in the mapper
    @TableField(exist = false)
    private Count _count;

    // Inner class for MyBatis to map the count results
    @Data
    public static class Count {
        private int likes;
        private int comments;
    }
}