/**
 * 用户最近活动相关的类型定义
 */

export interface RecentPost {
  id: number;
  title: string;
  content: string;
  createdAt: string;
  likesCount: number;
  commentsCount: number;
  category?: string;
}

export interface RecentComment {
  id: number;
  content: string;
  createdAt: string;
  post: {
    id: number;
    title: string;
  };
  likesCount: number;
}

export interface UserRecentActivity {
  recentPosts: RecentPost[];
  recentComments: RecentComment[];
}
