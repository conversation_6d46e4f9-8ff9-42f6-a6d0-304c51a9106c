/**
 * 现代化数据获取 - React Query集成
 * 优势：自动缓存、后台更新、乐观更新、错误重试
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useMessageStore } from '@/stores/messageStore';
import { useWebSocketStatus } from './useWebSocketStatus';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';
import {
  getMessages,
  getConversations,
  sendMessage,
  markMessageAsRead,
  markMessagesAsRead,
  getMessageStats,
  searchMessages,
  getConversationMessages
} from '@/services/messageApi';
import { MessageQueryParams, SendMessageRequest, Message } from '@/types/Message';
import { toast } from 'sonner';

// 查询键工厂 - 统一管理查询键
export const messageKeys = {
  all: ['messages'] as const,
  stats: () => [...messageKeys.all, 'stats'] as const,
  conversations: () => [...messageKeys.all, 'conversations'] as const,
  conversation: (id: string) => [...messageKeys.conversations(), id] as const,
  messages: (params: MessageQueryParams) => [...messageKeys.all, 'list', params] as const,
  search: (query: string) => [...messageKeys.all, 'search', query] as const,
};

// 消息统计查询
export const useMessageStats = () => {
  const updateUnreadStats = useMessageStore(state => state.updateUnreadStats);

  const query = useQuery({
    queryKey: messageKeys.stats(),
    queryFn: getMessageStats,
    staleTime: 30 * 1000, // 30秒内不重新获取
    refetchInterval: 60 * 1000, // 每分钟后台刷新
  });

  // 使用 useEffect 替代 onSuccess
  useEffect(() => {
    if (query.data) {
      updateUnreadStats(query.data);
    }
  }, [query.data, updateUnreadStats]);

  return query;
};

// 无限滚动会话列表
export const useConversations = () => {
  const updateConversation = useMessageStore(state => state.updateConversation);

  const query = useInfiniteQuery({
    queryKey: messageKeys.conversations(),
    queryFn: ({ pageParam = 1 }) => getConversations(),
    getNextPageParam: (lastPage) => lastPage.hasMore ? lastPage.page + 1 : undefined,
    staleTime: 5 * 60 * 1000, // 5分钟
    initialPageParam: 1,
  });

  // 使用 useEffect 替代 onSuccess
  useEffect(() => {
    if (query.data) {
      query.data.pages.forEach(page => {
        page.conversations.forEach(updateConversation);
      });
    }
  }, [query.data, updateConversation]);

  return query;
};

// 会话消息查询（支持虚拟滚动）
export const useConversationMessages = (conversationId: string | null) => {
  const getCachedMessages = useMessageStore(state => state.getCachedMessages);
  const setCachedMessages = useMessageStore(state => state.setCachedMessages);

  const query = useInfiniteQuery({
    queryKey: messageKeys.conversation(conversationId || ''),
    queryFn: ({ pageParam = 1 }) => {
      if (!conversationId) throw new Error('No conversation ID');
      const otherUserId = parseInt(conversationId.replace('conv_', ''));
      return getConversationMessages(otherUserId, pageParam - 1, 50);
    },
    getNextPageParam: (lastPage) => lastPage.hasMore ? lastPage.page + 1 : undefined,
    enabled: !!conversationId,
    staleTime: 2 * 60 * 1000, // 2分钟
    initialPageParam: 1,
    // 使用本地缓存作为初始数据
    initialData: () => {
      if (!conversationId) return undefined;
      const cached = getCachedMessages(conversationId);
      if (cached && cached.length > 0) {
        return {
          pages: [{ messages: cached, hasMore: true, page: 1, total: cached.length }],
          pageParams: [1],
        };
      }
      return undefined;
    },
  });

  // 使用 useEffect 替代 onSuccess
  useEffect(() => {
    if (query.data && conversationId) {
      const allMessages = query.data.pages.flatMap(page => page.messages);
      setCachedMessages(conversationId, allMessages);
    }
  }, [query.data, conversationId, setCachedMessages]);

  return query;
};

// 智能消息搜索（防抖）
export const useMessageSearch = (query: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: messageKeys.search(query),
    queryFn: () => searchMessages(query, 1, 50),
    enabled: enabled && query.length > 2,
    staleTime: 5 * 60 * 1000,
    // 防抖延迟
    refetchOnWindowFocus: false,
  });
};

// 发送消息 - 乐观更新
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  const addMessage = useMessageStore(state => state.addMessage);
  
  return useMutation({
    mutationFn: (data: SendMessageRequest) => sendMessage(data),
    // 乐观更新
    onMutate: async (newMessage) => {
      // 创建临时消息对象
      const tempMessage = {
        id: Date.now(), // 临时ID
        ...newMessage,
        status: 'sending' as const,
        createdAt: new Date().toISOString(),
        sender: queryClient.getQueryData(['user']) as any,
      };
      
      // 立即添加到UI
      addMessage(tempMessage as any);
      
      return { tempMessage };
    },
    onSuccess: (response, variables, context) => {
      // 替换临时消息为真实消息
      if (context?.tempMessage && response?.data) {
        const updateMessage = useMessageStore.getState().updateMessage;
        const messageData = Array.isArray(response.data) ? response.data[0] : response.data;
        if (messageData) {
          updateMessage(context.tempMessage.id, {
            id: messageData.id,
            status: 'sent' as any,
          });
        }
      }

      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: messageKeys.stats() });
      queryClient.invalidateQueries({ queryKey: messageKeys.conversations() });

      toast.success('消息发送成功');
    },
    onError: (error, variables, context) => {
      // 移除失败的临时消息
      if (context?.tempMessage) {
        const removeMessage = useMessageStore.getState().removeMessage;
        removeMessage(context.tempMessage.id);
      }
      
      toast.error('消息发送失败，请重试');
    },
  });
};

// 标记消息已读 - 批量优化
export const useMarkAsRead = () => {
  const queryClient = useQueryClient();
  const markConversationAsRead = useMessageStore(state => state.markConversationAsRead);

  return useMutation({
    mutationFn: async ({ messageIds, conversationId }: { messageIds: number[], conversationId?: string }) => {
      if (messageIds.length === 1) {
        await markMessageAsRead(messageIds[0]);
      } else {
        await markMessagesAsRead(messageIds);
      }
    },
    onMutate: async ({ conversationId }) => {
      // 乐观更新
      if (conversationId) {
        markConversationAsRead(conversationId);
      }
    },
    onSuccess: () => {
      // 更新统计
      queryClient.invalidateQueries({ queryKey: messageKeys.stats() });
    },
  });
};

// 使用统一WebSocket管理器
const getWebSocketManager = () => {
  return unifiedWebSocketManager;
};

// 实时数据同步Hook
export const useRealtimeSync = () => {
  const queryClient = useQueryClient();
  const addMessage = useMessageStore(state => state.addMessage);
  const updateMessage = useMessageStore(state => state.updateMessage);

  // 使用新的WebSocket状态管理
  const webSocketStatus = useWebSocketStatus();

  useEffect(() => {
    const wsManager = getWebSocketManager();
    
    // 连接状态监听（WebSocket状态管理已移至useWebSocketStatus）
    // wsManager.on('connected', () => setConnectionStatus('connected'));
    // wsManager.on('disconnected', () => setConnectionStatus('disconnected'));
    // wsManager.on('error', () => setConnectionStatus('error'));
    
    // 新消息监听
    wsManager.on('NEW_MESSAGE', (message: Message & { conversationId?: string }) => {
      addMessage(message);

      // 如果是当前会话，标记为已读
      const selectedConversationId = useMessageStore.getState().selectedConversationId;
      if (message.conversationId === selectedConversationId) {
        markMessageAsRead(message.id);
      }

      // 更新相关查询缓存
      if (message.conversationId) {
        queryClient.setQueryData(
          messageKeys.conversation(message.conversationId),
          (old: any) => {
            if (!old) return old;
            return {
              ...old,
              pages: old.pages.map((page: any, index: number) =>
                index === 0
                  ? { ...page, messages: [message, ...page.messages] }
                  : page
              ),
            };
          }
        );
      }
    });
    
    // 消息状态更新
    wsManager.on('MESSAGE_STATUS_CHANGED', ({ messageId, status }: { messageId: number, status: any }) => {
      updateMessage(messageId, { status });
    });

    // 用户在线状态
    wsManager.on('USER_ONLINE_STATUS', ({ userId, isOnline, lastSeen }: { userId: number, isOnline: boolean, lastSeen: string }) => {
      const updateUserLastSeen = useMessageStore.getState().updateUserLastSeen;
      updateUserLastSeen(userId, lastSeen);
    });

    // 正在输入状态
    wsManager.on('TYPING_STATUS', ({ conversationId, userId, isTyping }: { conversationId: string, userId: number, isTyping: boolean }) => {
      const setTypingUsers = useMessageStore.getState().setTypingUsers;
      const currentTyping = useMessageStore.getState().typingUsers[conversationId] || [];

      if (isTyping) {
        if (!currentTyping.includes(userId)) {
          setTypingUsers(conversationId, [...currentTyping, userId]);
        }
      } else {
        setTypingUsers(conversationId, currentTyping.filter(id => id !== userId));
      }
    });
    
    return () => {
      wsManager.off('connected');
      wsManager.off('disconnected');
      wsManager.off('error');
      wsManager.off('NEW_MESSAGE');
      wsManager.off('MESSAGE_STATUS_CHANGED');
      wsManager.off('USER_ONLINE_STATUS');
      wsManager.off('TYPING_STATUS');
    };
  }, []);
};
