package com.kitolus.community.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.CacheControl;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.file.Paths;
import java.util.Arrays;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    // 统一使用 storage-base-path
    @Value("${file-storage.storage-base-path}")
    private String storageBasePath;

    @Value("${frontend.public-path:}")
    private String frontendPublicPath;

    private final Environment environment;

    public WebConfig(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // --- 1. 用户上传内容（头像、横幅等）的处理器，使用 /storage/** 路径 ---
        // 这个处理器现在是唯一的，并且通过 application-dev.yml 的配置，
        // 在开发环境下指向本地的 backend/storage 目录，在生产环境指向服务器目录。
        String storageLocation = "file:" + Paths.get(storageBasePath).toAbsolutePath().normalize().toString() + "/";
        registry.addResourceHandler("/storage/**")
                .addResourceLocations(storageLocation)
                .setCacheControl(CacheControl.noCache());

    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                response.setHeader("Pragma", "no-cache");
                response.setHeader("Expires", "0");
                return true;
            }
        }).addPathPatterns("/api/**");
    }
} 