package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.dto.WithdrawalRequestDetailsDTO;
import com.kitolus.community.entity.WithdrawalRequest;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface WithdrawalRequestMapper extends BaseMapper<WithdrawalRequest> {

    @Select("SELECT wr.id, wr.user_id, wr.amount, wr.channel, wr.status, wr.account_info, " +
            "wr.notes, wr.created_at, wr.reviewed_by as reviewerId, wr.reviewed_at, " +
            "u.username as username, r.username as reviewerName " +
            "FROM withdrawal_requests wr " +
            "JOIN user u ON wr.user_id = u.id " +
            "LEFT JOIN user r ON wr.reviewed_by = r.id " +
            "ORDER BY wr.created_at DESC")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "amount", column = "amount"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "accountInfo", column = "account_info"),
            @Result(property = "status", column = "status"),
            @Result(property = "notes", column = "notes"),
            @Result(property = "createdAt", column = "created_at"),
            @Result(property = "reviewerId", column = "reviewerId"),
            @Result(property = "reviewedAt", column = "reviewed_at"),
            @Result(property = "username", column = "username"),
            @Result(property = "reviewerName", column = "reviewerName")
    })
    List<WithdrawalRequestDetailsDTO> findAllWithUserDetails();

    @Select("SELECT wr.id, wr.user_id, wr.amount, wr.channel, wr.status, wr.account_info, " +
            "wr.notes, wr.created_at, wr.reviewed_by as reviewerId, wr.reviewed_at, " +
            "u.username as username, r.username as reviewerName " +
            "FROM withdrawal_requests wr " +
            "JOIN user u ON wr.user_id = u.id " +
            "LEFT JOIN user r ON wr.reviewed_by = r.id " +
            "WHERE wr.user_id = #{userId} " +
            "ORDER BY wr.created_at DESC")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "amount", column = "amount"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "accountInfo", column = "account_info"),
            @Result(property = "status", column = "status"),
            @Result(property = "notes", column = "notes"),
            @Result(property = "createdAt", column = "created_at"),
            @Result(property = "reviewerId", column = "reviewerId"),
            @Result(property = "reviewedAt", column = "reviewed_at"),
            @Result(property = "username", column = "username"),
            @Result(property = "reviewerName", column = "reviewerName")
    })
    List<WithdrawalRequestDetailsDTO> findByUserIdWithUserDetails(@Param("userId") Long userId);

    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdrawal_requests WHERE user_id = #{userId} AND status = 'APPROVED'")
    BigDecimal sumApprovedWithdrawalsByUserId(@Param("userId") Long userId);
} 