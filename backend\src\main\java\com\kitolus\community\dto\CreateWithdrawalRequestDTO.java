package com.kitolus.community.dto;

import com.kitolus.community.entity.WithdrawalChannel;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class CreateWithdrawalRequestDTO {

    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0.01", message = "提现金额必须大于0")
    private BigDecimal amount;

    @NotNull(message = "提现渠道不能为空")
    private WithdrawalChannel channel;

    private String notes;
} 