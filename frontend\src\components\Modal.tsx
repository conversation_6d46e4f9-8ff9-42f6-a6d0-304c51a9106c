'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { X } from 'lucide-react';

type ModalProps = {
    isOpen: boolean;
    onClose: () => void;
    qrCodeUrl?: string; // Make it optional
};

const Modal = ({ isOpen, onClose, qrCodeUrl }: ModalProps) => {
    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-background/50 backdrop-blur-sm flex justify-center items-center z-50"
                    onClick={onClose}
                >
                    <motion.div
                        initial={{ scale: 0.9, opacity: 0, y: 50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.9, opacity: 0, y: 50 }}
                        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                        className="relative bg-card/80 backdrop-blur-lg border border-border rounded-2xl shadow-2xl shadow-primary/10 w-full max-w-sm p-8"
                        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
                    >
                        <button
                            type="button"
                            aria-label="关闭窗口"
                            onClick={onClose}
                            className="absolute top-3 right-3 text-muted-foreground hover:text-foreground transition-colors"
                        >
                            <X size={24} />
                        </button>

                        <div className="flex flex-col items-center justify-center">
                            <h2 className="text-2xl font-bold text-primary mb-4">扫码支付</h2>
                            {qrCodeUrl ? (
                                <div className="p-2 bg-white rounded-lg">
                                    <Image src={qrCodeUrl} alt="支付二维码" width={256} height={256} />
                                </div>
                            ) : (
                                <div className="w-64 h-64 flex items-center justify-center bg-secondary rounded-lg">
                                    <p className="text-muted-foreground">加载二维码中...</p>
                                </div>
                            )}
                            <p className="mt-4 text-sm text-muted-foreground">
                                请使用您的支付应用完成购买。
                            </p>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default Modal; 