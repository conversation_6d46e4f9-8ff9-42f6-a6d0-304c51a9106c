'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Search, Sparkles, Palette, Zap, Eye, Smartphone } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

/**
 * 搜索页面设计演示
 */
export default function SearchDemoPage() {
  const features = [
    {
      icon: <Palette className="h-6 w-6" />,
      title: "现代化配色",
      description: "黑灰主题配色，多种渐变色彩区分内容类型",
      color: "from-blue-500 to-purple-500"
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "流畅动画",
      description: "Framer Motion驱动的专业级动画效果",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: <Eye className="h-6 w-6" />,
      title: "毛玻璃效果",
      description: "现代化的毛玻璃背景和深度层次感",
      color: "from-orange-500 to-red-500"
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: "响应式设计",
      description: "完美适配桌面端、平板和移动设备",
      color: "from-purple-500 to-pink-500"
    }
  ];

  const colorSchemes = [
    { name: "用户", color: "bg-gradient-to-r from-green-500 to-emerald-500", description: "绿色系，代表用户和社交" },
    { name: "帖子", color: "bg-gradient-to-r from-orange-500 to-red-500", description: "橙红色系，代表内容和讨论" },
    { name: "文章", color: "bg-gradient-to-r from-purple-500 to-pink-500", description: "紫粉色系，代表知识和文档" },
    { name: "通用", color: "bg-gradient-to-r from-blue-500 to-purple-500", description: "蓝紫色系，代表搜索和导航" }
  ];

  return (
    <div className="min-h-screen relative">
      {/* 背景图片 */}
      <div 
        className="fixed inset-0 z-0"
        style={{
          backgroundImage: 'url(/images/background/Search.webp)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-4 py-12 max-w-6xl">
        {/* 标题区域 */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="inline-flex items-center gap-3 mb-6"
          >
            <div className="p-4 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10">
              <Search className="h-10 w-10 text-blue-400" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              现代化搜索页面
            </h1>
          </motion.div>
          <p className="text-gray-400 text-xl mb-8">全新设计的沉浸式搜索体验</p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex justify-center gap-4"
          >
            <Link href="/search">
              <Button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-8 py-3 text-lg">
                <Sparkles className="h-5 w-5 mr-2" />
                体验搜索
              </Button>
            </Link>
            <Link href="/test-search">
              <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800/50 px-8 py-3 text-lg">
                功能测试
              </Button>
            </Link>
          </motion.div>
        </motion.div>

        {/* 特性展示 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-white text-center mb-12">核心特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
              >
                <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 h-full">
                  <CardContent className="p-6 text-center">
                    <div className={`inline-flex p-3 rounded-lg bg-gradient-to-r ${feature.color} bg-opacity-20 mb-4`}>
                      <div className="text-white">
                        {feature.icon}
                      </div>
                    </div>
                    <h3 className="text-white font-semibold mb-2">{feature.title}</h3>
                    <p className="text-gray-400 text-sm">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* 配色方案 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-white text-center mb-12">配色方案</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {colorSchemes.map((scheme, index) => (
              <motion.div
                key={scheme.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
              >
                <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-700/50 overflow-hidden">
                  <div className={`h-24 ${scheme.color}`} />
                  <CardContent className="p-4">
                    <h3 className="text-white font-semibold mb-2">{scheme.name}</h3>
                    <p className="text-gray-400 text-sm">{scheme.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* 技术栈 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="mb-16"
        >
          <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-700/50">
            <CardHeader>
              <CardTitle className="text-white text-2xl text-center">技术栈</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                {[
                  { name: "React 18", desc: "最新React特性" },
                  { name: "Framer Motion", desc: "专业动画库" },
                  { name: "Tailwind CSS", desc: "原子化CSS" },
                  { name: "TypeScript", desc: "类型安全" }
                ].map((tech, index) => (
                  <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                    className="p-4"
                  >
                    <Badge variant="outline" className="border-blue-500/30 text-blue-300 mb-2">
                      {tech.name}
                    </Badge>
                    <p className="text-gray-400 text-sm">{tech.desc}</p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 设计亮点 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="text-center"
        >
          <Card className="bg-gray-900/60 backdrop-blur-sm border-gray-700/50">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold text-white mb-6">设计亮点</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">视觉层次</h3>
                  <ul className="text-gray-400 space-y-2">
                    <li>• 高对比度主要内容</li>
                    <li>• 渐进式信息展示</li>
                    <li>• 彩色强调元素</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">交互体验</h3>
                  <ul className="text-gray-400 space-y-2">
                    <li>• 流畅的动画过渡</li>
                    <li>• 即时的视觉反馈</li>
                    <li>• 直观的操作逻辑</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">性能优化</h3>
                  <ul className="text-gray-400 space-y-2">
                    <li>• 代码分割加载</li>
                    <li>• 图片格式优化</li>
                    <li>• 动画性能优化</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
