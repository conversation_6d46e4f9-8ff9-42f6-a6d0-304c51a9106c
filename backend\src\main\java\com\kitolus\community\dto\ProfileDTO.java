package com.kitolus.community.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.util.Date;

@Data
public class ProfileDTO {
    private Long id;
    private String username;
    private String email;
    private String avatarUrl;
    private Integer avatarVersion;
    private String bannerUrl;
    private Integer bannerVersion;
    private String alipayAccount;
    private String wechatAccount;
    private String role;
    private String serialNumber;
    private Timestamp createdAt;
    private Integer postCount;
    private Integer commentCount;

    public String getFullAvatarUrl() {
        if (avatarUrl == null || avatarUrl.isEmpty()) {
            return null; // Or a default avatar URL
        }
        // Always return a root-relative path
        return "/storage/" + avatarUrl;
    }

    public String getFullBannerUrl() {
        if (bannerUrl == null || bannerUrl.isEmpty()) {
            return "/images/background/background.webp"; // Default banner is already root-relative
        }
        // Always return a root-relative path
        return "/storage/" + bannerUrl;
    }
} 