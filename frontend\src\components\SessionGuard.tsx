'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { sessionManager } from '@/lib/sessionManager';

/**
 * 简化的会话守护组件
 * 只负责基本的会话监控，不进行复杂的状态恢复
 */
export const SessionGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    console.log('🛡️ 启动会话守护...');

    // 只在已认证状态下进行会话监控
    if (isAuthenticated) {
      // 更新会话活跃时间
      sessionManager.updateActivity();
      
      // 定期更新活跃时间
      const activityInterval = setInterval(() => {
        if (sessionManager.isSessionValid()) {
          sessionManager.updateActivity();
        }
      }, 30000); // 每30秒更新一次活跃时间

      return () => {
        clearInterval(activityInterval);
      };
    }
  }, [isAuthenticated]);

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        console.log('👁️ 页面变为可见，更新会话活跃时间');
        sessionManager.updateActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated]);

  return <>{children}</>;
};

export default SessionGuard;
