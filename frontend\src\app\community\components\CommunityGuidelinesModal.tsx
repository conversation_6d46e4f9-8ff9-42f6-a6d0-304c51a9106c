'use client';

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { ShieldCheck, MessageCircle, Share2, Annoyed, Gavel } from 'lucide-react';

interface CommunityGuidelinesModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const guidelines = [
    {
        icon: <ShieldCheck className="h-6 w-6 text-green-400" />,
        title: "尊重友善，理性交流",
        description: "请尊重每一位社区成员，保持善意，避免人身攻击、骚扰或任何形式的歧视性言论。"
    },
    {
        icon: <MessageCircle className="h-6 w-6 text-blue-400" />,
        title: "保持主题，言之有物",
        description: "请围绕 GregTech: New Horizons 及其相关内容进行讨论，分享有价值的信息和见解，避免发布无关的水帖。"
    },
    {
        icon: <Share2 className="h-6 w-6 text-orange-400" />,
        title: "乐于分享，协同进步",
        description: "我们鼓励分享你的自动化设计、建筑作品和游戏心得。知识的分享是社区发展的基石。"
    },
    {
        icon: <Annoyed className="h-6 w-6 text-yellow-400" />,
        title: "禁止广告与不当宣传",
        description: "禁止发布任何形式的商业广告、服务器/QQ群的恶意宣传，或未经许可的外部链接。"
    },
    {
        icon: <Gavel className="h-6 w-6 text-red-400" />,
        title: "遵守法律，共建和谐",
        description: "请严格遵守国家法律法规，禁止发布任何违法、暴力、色情或令人不适的内容。"
    }
];


export const CommunityGuidelinesModal: React.FC<CommunityGuidelinesModalProps> = ({ isOpen, onClose }) => {
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="bg-zinc-900/80 border-zinc-700 text-zinc-200 backdrop-filter backdrop-blur-md">
                <DialogHeader>
                    <DialogTitle className="text-2xl flex items-center gap-2">
                        <Gavel className="h-6 w-6 text-zinc-400" />
                        社区公约
                    </DialogTitle>
                    <DialogDescription className="text-zinc-400 pt-2">
                        为了维护一个健康、积极、富有创造力的社区环境，请共同遵守以下准则。
                    </DialogDescription>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    {guidelines.map((rule, index) => (
                        <div key={index} className="flex items-start gap-4 p-3 bg-zinc-800/50 rounded-lg">
                            <div className="flex-shrink-0 mt-1">{rule.icon}</div>
                            <div>
                                <h4 className="font-semibold text-zinc-100">{rule.title}</h4>
                                <p className="text-sm text-zinc-400">{rule.description}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </DialogContent>
        </Dialog>
    );
}; 