'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Loader2, User, FileText, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { getSearchSuggestions } from '@/services/api';
import { SearchSuggestion } from '@/types/GlobalSearchResult';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBoxProps {
  className?: string;
  isMobile?: boolean;
}

export const SearchBox: React.FC<SearchBoxProps> = ({ className, isMobile = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  
  // 防抖搜索查询
  const debouncedQuery = useDebounce(query, 300);

  // 获取搜索建议
  const fetchSuggestions = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);
    try {
      const results = await getSearchSuggestions(searchQuery);
      setSuggestions(results);
      setShowSuggestions(results.length > 0);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('获取搜索建议失败:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 当防抖查询改变时获取建议
  useEffect(() => {
    if (isExpanded && debouncedQuery) {
      fetchSuggestions(debouncedQuery);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [debouncedQuery, isExpanded, fetchSuggestions]);

  // 展开搜索框
  const handleExpand = () => {
    setIsExpanded(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  // 收起搜索框
  const handleCollapse = () => {
    setIsExpanded(false);
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  // 执行搜索
  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (!finalQuery.trim()) return;

    router.push(`/search?q=${encodeURIComponent(finalQuery.trim())}`);
    handleCollapse();
  };

  // 选择建议项
  const handleSelectSuggestion = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'user') {
      router.push(`/profile/${suggestion.title}`);
    } else if (suggestion.type === 'post') {
      router.push(`/community/posts/${suggestion.id}`);
    } else if (suggestion.type === 'article') {
      router.push(`/kb/${suggestion.id}`);
    }
    handleCollapse();
  };

  // 键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCollapse();
      return;
    }

    if (!showSuggestions) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
        handleSelectSuggestion(suggestions[selectedIndex]);
      } else {
        handleSearch();
      }
    }
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current && 
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 获取建议项图标
  const getSuggestionIcon = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'user':
        return <User className="h-4 w-4" />;
      case 'post':
        return <FileText className="h-4 w-4" />;
      case 'article':
        return <BookOpen className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  return (
    <div className={cn("relative", className)}>
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          // 搜索按钮
          <motion.div
            key="button"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={handleExpand}
              className="transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))]"
              title="搜索"
              aria-label="打开搜索"
            >
              <Search className="h-5 w-5" />
            </Button>
          </motion.div>
        ) : (
          // 搜索输入框
          <motion.div
            key="input"
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 'auto' }}
            exit={{ opacity: 0, width: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="flex items-center gap-2"
          >
            <div className="relative">
              <div className="relative flex items-center">
                <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  ref={inputRef}
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onFocus={() => query && setShowSuggestions(suggestions.length > 0)}
                  placeholder="搜索用户、帖子、文章..."
                  aria-label="搜索输入框"
                  aria-expanded={showSuggestions}
                  aria-autocomplete="list"
                  role="combobox"
                  className={cn(
                    "pl-10 pr-10 bg-background/50 backdrop-blur-sm border-border/50",
                    isMobile ? "w-48" : "w-64"
                  )}
                />
                {isLoading && (
                  <Loader2 className="absolute right-3 h-4 w-4 animate-spin text-muted-foreground" />
                )}
              </div>

              {/* 搜索建议下拉框 */}
              <AnimatePresence>
                {showSuggestions && suggestions.length > 0 && (
                  <motion.div
                    ref={suggestionsRef}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute top-full left-0 right-0 mt-1 bg-background/95 backdrop-blur-lg border border-border/50 rounded-md shadow-xl z-50 max-h-64 overflow-y-auto"
                    role="listbox"
                    aria-label="搜索建议"
                  >
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={`${suggestion.type}-${suggestion.id}`}
                        onClick={() => handleSelectSuggestion(suggestion)}
                        className={cn(
                          "w-full px-3 py-2 text-left hover:bg-muted transition-colors flex items-center gap-3",
                          selectedIndex === index && "bg-muted"
                        )}
                        role="option"
                        aria-selected={selectedIndex === index}
                      >
                        <div className="text-muted-foreground">
                          {getSuggestionIcon(suggestion.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{suggestion.title}</div>
                          {suggestion.subtitle && (
                            <div className="text-sm text-muted-foreground truncate">
                              {suggestion.subtitle}
                            </div>
                          )}
                        </div>
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <Button
              variant="ghost"
              size="icon"
              onClick={handleCollapse}
              className="shrink-0"
              title="关闭搜索"
            >
              <X className="h-4 w-4" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
