package com.kitolus.community.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kitolus.community.dto.CreateCommentRequestDTO;
import com.kitolus.community.dto.CreatePostRequestDTO;
import com.kitolus.community.dto.UpdatePostRequestDTO;
import com.kitolus.community.dto.UpdatePostResponseDTO;
import com.kitolus.community.entity.CommunityPost;
import com.kitolus.community.entity.CommunityPostLike;
import com.kitolus.community.entity.CommunityTopic;
import com.kitolus.community.entity.CommunityComment;
import com.kitolus.community.entity.User;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.exception.InvalidDataException;
import com.kitolus.community.mapper.CommunityPostLikeMapper;
import com.kitolus.community.mapper.CommunityPostMapper;
import com.kitolus.community.mapper.CommunityTopicMapper;
import com.kitolus.community.mapper.UserMapper;
import com.kitolus.community.mapper.CommunityCommentMapper;
import com.kitolus.community.service.NotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CommunityService {

    private final CommunityPostMapper postMapper;
    private final CommunityTopicMapper topicMapper;
    private final UserMapper userMapper;
    private final CommunityPostLikeMapper likeMapper;
    private final CommunityCommentMapper commentMapper;
    private final NotificationService notificationService;

    private static final Pattern MENTION_PATTERN = Pattern.compile("@[a-zA-Z0-9_-]+");

    private Set<String> getValidMentionedUsernames(String content) {
        if (content == null || content.isEmpty()) {
            return Set.of();
        }
        Matcher matcher = MENTION_PATTERN.matcher(content);
        Set<String> mentionedUsernames = matcher.results()
                .map(matchResult -> matchResult.group().substring(1)) // Remove '@'
                .collect(Collectors.toSet());

        if (mentionedUsernames.isEmpty()) {
            return Set.of();
        }
        
        // Validate against the database to find which users actually exist
        return userMapper.selectList(new QueryWrapper<User>().in("username", mentionedUsernames))
                .stream()
                .map(User::getUsername)
                .collect(Collectors.toSet());
    }

    private void processAndNotifyMentions(User author, CommunityPost post, CommunityComment comment, Set<String> validUsernames) {
        if (validUsernames.isEmpty()) {
            return;
        }

        String content = (comment != null) ? comment.getContent() : post.getContent();

        for (String username : validUsernames) {
            // No need to query again if we assume usernames are unique
            // But a query is safer to get the full user object
            User mentionedUser = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));

            // Check if mentioned user exists and is not the author of the content
            if (mentionedUser != null && !mentionedUser.getId().equals(author.getId())) {
                if (comment != null) {
                    notificationService.createNotificationForMention(author, mentionedUser, post, comment);
                } else {
                    notificationService.createNotificationForMention(author, mentionedUser, post, null);
                }
            }
        }
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || "anonymousUser".equals(authentication.getPrincipal())) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        String username;
        if (principal instanceof UserDetails) {
            username = ((UserDetails)principal).getUsername();
        } else {
            username = principal.toString();
        }

        User currentUser = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (currentUser == null) {
            // This case should ideally not happen for an authenticated user.
            throw new RuntimeException("Authenticated user not found in database: " + username);
        }
        return currentUser;
    }

    private void processPostAvatarUrls(List<CommunityPost> posts) {
        if (posts == null) return;
        posts.forEach(this::processSinglePostAvatars);
    }

    private void processSinglePostAvatars(CommunityPost post) {
        if (post == null) return;

        // Process author avatar
        if (post.getAuthor() != null) {
            User author = post.getAuthor();
            if (author.getAvatarUrl() == null || author.getAvatarUrl().trim().isEmpty()) {
                author.setAvatarUrl(author.getSerialNumber());
            }
        }

        // Process comments avatars
        if (post.getComments() != null) {
            processCommentAvatarUrls(post.getComments());
        }
    }

    private void processCommentAvatarUrls(List<CommunityComment> comments) {
        if (comments == null) return;
        comments.forEach(comment -> {
            if (comment != null && comment.getAuthor() != null) {
                User author = comment.getAuthor();
                if (author.getAvatarUrl() == null || author.getAvatarUrl().trim().isEmpty()) {
                    author.setAvatarUrl(author.getSerialNumber());
                }
            }
        });
    }

    public List<CommunityPost> getAllPosts() {
        User currentUser = getCurrentUser();
        Long currentUserId = (currentUser != null) ? currentUser.getId() : null;
        List<CommunityPost> posts = postMapper.selectAllPostsWithDetails(currentUserId);
        processPostAvatarUrls(posts);
        return posts;
    }

    public CommunityPost getPostById(Long id) {
        User currentUser = getCurrentUser();
        Long currentUserId = (currentUser != null) ? currentUser.getId() : null;
        CommunityPost post = postMapper.selectPostWithDetailsById(id, currentUserId);
        if (post == null) {
            throw new ResourceNotFoundException("Post with id " + id + " not found");
        }
        // Process a single post by wrapping it in a list
        processPostAvatarUrls(List.of(post));
        
        // Also process avatars for all comments within the post
        if (post.getComments() != null) {
            post.getComments().stream()
                .filter(Objects::nonNull)
                .forEach(comment -> {
                    if (comment.getAuthor() != null) {
                         User author = comment.getAuthor();
                        if (author.getAvatarUrl() == null || author.getAvatarUrl().trim().isEmpty()) {
                            author.setAvatarUrl(author.getSerialNumber());
                        }
                    }
                    // Populate valid mentions for each comment
                    comment.setMentionedUsers(getValidMentionedUsernames(comment.getContent()));
                });
        }
        
        // Populate valid mentions for the post itself
        post.setMentionedUsers(getValidMentionedUsernames(post.getContent()));
        
        return post;
    }

    @Transactional
    public void deletePost(Long postId) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new IllegalStateException("User must be logged in to delete posts.");
        }

        CommunityPost post = postMapper.selectById(postId);
        if (post == null) {
            throw new ResourceNotFoundException("Post with id " + postId + " not found.");
        }

        // 检查权限：帖子作者或管理员可以删除帖子
        boolean isAuthor = Objects.equals(post.getAuthorId(), currentUser.getId());
        boolean isAdmin = "KitolusAdmin".equals(currentUser.getRole());

        if (!isAuthor && !isAdmin) {
            throw new AccessDeniedException("User is not authorized to delete this post.");
        }

        // Delete associated likes
        likeMapper.delete(new QueryWrapper<CommunityPostLike>().eq("post_id", postId));

        // Delete all top-level comments for the post, which will trigger recursive deletion of replies
        List<CommunityComment> topLevelComments = commentMapper.selectList(
            new QueryWrapper<CommunityComment>().eq("post_id", postId).isNull("parent_id")
        );
        for (CommunityComment comment : topLevelComments) {
            deleteCommentAndReplies(comment.getId());
        }

        // Delete the post
        postMapper.deleteById(postId);
    }

    @Transactional
    public void deleteComment(Long commentId) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new IllegalStateException("User must be logged in to delete comments.");
        }

        CommunityComment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            throw new ResourceNotFoundException("Comment with id " + commentId + " not found.");
        }

        // 检查权限：评论作者或管理员可以删除评论
        boolean isAuthor = Objects.equals(comment.getAuthorId(), currentUser.getId());
        boolean isAdmin = "KitolusAdmin".equals(currentUser.getRole());

        if (!isAuthor && !isAdmin) {
            throw new AccessDeniedException("User is not authorized to delete this comment.");
        }

        deleteCommentAndReplies(commentId);
    }

    private void deleteCommentAndReplies(Long commentId) {
        // Find all direct replies to the current comment
        List<CommunityComment> replies = commentMapper.selectList(
                new QueryWrapper<CommunityComment>().eq("parent_id", commentId)
        );

        // Recursively delete each reply
        for (CommunityComment reply : replies) {
            deleteCommentAndReplies(reply.getId());
        }

        // Finally, delete the comment itself
        // Notifications are now deleted via ON DELETE CASCADE at the database level.
        commentMapper.deleteById(commentId);
    }

    public List<CommunityComment> getCommentsByPostId(Long postId) {
        List<CommunityComment> allComments = commentMapper.selectCommentsByPostId(postId);
        
        // Group comments by their parent ID
        Map<Long, List<CommunityComment>> commentsByParentId = allComments.stream()
                .filter(c -> c.getParentId() != null)
                .collect(Collectors.groupingBy(CommunityComment::getParentId));

        // Assign replies to their top-level comments
        List<CommunityComment> topLevelComments = allComments.stream()
                .filter(c -> c.getParentId() == null)
                .peek(c -> c.setReplies(commentsByParentId.get(c.getId())))
                .collect(Collectors.toList());

        return topLevelComments;
    }

    @Transactional
    public CommunityComment createComment(CreateCommentRequestDTO commentDto) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new IllegalStateException("User must be logged in to comment.");
        }

        CommunityPost post = postMapper.selectById(commentDto.getPostId());
        if (post == null) {
            throw new ResourceNotFoundException("Cannot comment on a post that does not exist. Post ID: " + commentDto.getPostId());
        }

        CommunityComment newComment = new CommunityComment();
        newComment.setPostId(commentDto.getPostId());
        newComment.setAuthorId(currentUser.getId());
        newComment.setContent(commentDto.getContent());
        newComment.setParentId(commentDto.getParentId()); // Can be null
        newComment.setCreatedAt(new Timestamp(System.currentTimeMillis()));

        commentMapper.insert(newComment); // Insert first to get the ID for the notification

        // Handle reply/new comment notifications
        if (commentDto.getParentId() != null) {
            CommunityComment parentComment = commentMapper.selectCommentById(commentDto.getParentId());
            if (parentComment != null) {
                // Don't notify if replying to own comment
                if (!parentComment.getAuthorId().equals(currentUser.getId())) {
                    notificationService.createNotificationForReply(newComment, parentComment, post, currentUser);
                }
            }
        } else {
            // Don't notify if commenting on own post
            if (!post.getAuthorId().equals(currentUser.getId())) {
                notificationService.createNotificationForComment(newComment, post, currentUser);
            }
        }
        
        // Handle @mentions
        Set<String> validUsernames = getValidMentionedUsernames(newComment.getContent());
        processAndNotifyMentions(currentUser, post, newComment, validUsernames);

        // Fetch the newly created comment with author details to return
        CommunityComment createdCommentWithDetails = commentMapper.selectCommentById(newComment.getId());
        processCommentAvatarUrls(List.of(createdCommentWithDetails));
        return createdCommentWithDetails;
    }

    @Transactional
    public boolean toggleLike(Long postId) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            // Or throw an exception, depending on whether anonymous users can see posts
            // but not like them. Throwing an exception is clearer.
            throw new IllegalStateException("User must be logged in to like posts.");
        }
        Long userId = currentUser.getId();

        CommunityPost post = postMapper.selectById(postId);
        if (post == null) {
            throw new ResourceNotFoundException("Cannot like a post that does not exist. Post ID: " + postId);
        }

        QueryWrapper<CommunityPostLike> likeQuery = new QueryWrapper<>();
        likeQuery.eq("user_id", userId).eq("post_id", postId);
        CommunityPostLike existingLike = likeMapper.selectOne(likeQuery);

        if (existingLike != null) {
            // Unlike
            likeMapper.deleteById(existingLike.getId());
            // The count is now calculated on-the-fly when fetching posts.
            return false; // User is no longer liking the post
        } else {
            // Like
            CommunityPostLike newLike = new CommunityPostLike(userId, postId);
            likeMapper.insert(newLike);
            // The count is now calculated on-the-fly when fetching posts.
            return true; // User is now liking the post
        }
    }

    @Transactional
    public CommunityPost createPost(CreatePostRequestDTO postDto) {
        User currentUser = getCurrentUser();

        // --- Find or create the topic ---
        String[] parts = postDto.getPartition().split("\\|");
        if (parts.length != 3) {
            throw new InvalidDataException("分区格式不正确，必须是 Era|Tier|Topic 的格式。");
        }
        String era = parts[0];
        String tier = parts[1];
        String topicName = parts[2];

        // Try to find the topic first
        CommunityTopic topic = topicMapper.selectOne(new QueryWrapper<CommunityTopic>()
                .eq("era", era)
                .eq("tier", tier)
                .eq("name", topicName)
        );

        // If not found, create it
        if (topic == null) {
            topic = new CommunityTopic();
            topic.setEra(era);
            topic.setTier(tier);
            topic.setName(topicName);
            topicMapper.insert(topic); // The ID will be set on this object after insert
        }

        CommunityPost newPost = new CommunityPost();
        newPost.setTitle(postDto.getTitle());
        newPost.setContent(postDto.getContent());
        newPost.setAuthorId(currentUser.getId());
        newPost.setTopicId(topic.getId());
        newPost.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        
        postMapper.insert(newPost);
        
        // Handle @mentions
        Set<String> validUsernames = getValidMentionedUsernames(newPost.getContent());
        processAndNotifyMentions(currentUser, newPost, null, validUsernames);

        // After inserting, the newPost object has the generated ID.
        // Now, fetch the full post details to return to the frontend.
        // We need to pass the current user's ID to correctly determine `likedByCurrentUser`.
        CommunityPost createdPost = postMapper.selectPostWithDetailsById(newPost.getId(), currentUser.getId());
        processSinglePostAvatars(createdPost);
        return createdPost;
    }

    public List<CommunityTopic> getAllTopics() {
        return topicMapper.selectList(null);
    }

    public CommunityComment addComment(CreateCommentRequestDTO requestDTO, Long authorId) {
        User author = userMapper.selectById(authorId);
        if (author == null) {
            throw new ResourceNotFoundException("Author with id " + authorId + " not found.");
        }

        CommunityPost post = postMapper.selectById(requestDTO.getPostId());
        if (post == null) {
            throw new ResourceNotFoundException("Post with id " + requestDTO.getPostId() + " not found.");
        }

        CommunityComment comment = new CommunityComment();
        comment.setPostId(requestDTO.getPostId());
        comment.setAuthorId(authorId);
        comment.setContent(requestDTO.getContent());
        comment.setParentId(requestDTO.getParentId());
        comment.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        commentMapper.insert(comment);

        // Fetch the full comment details to return
        CommunityComment fullComment = commentMapper.selectCommentById(comment.getId());

        // Handle notifications
        if (requestDTO.getParentId() != null) {
            CommunityComment parentComment = commentMapper.selectCommentById(requestDTO.getParentId());
            if (parentComment != null && !parentComment.getAuthorId().equals(authorId)) {
                notificationService.createNotificationForReply(fullComment, parentComment, post, author);
            }
        } else {
            if (!post.getAuthorId().equals(authorId)) {
                notificationService.createNotificationForComment(fullComment, post, author);
            }
        }
        
        // Handle @mentions in this legacy method as well
        Set<String> validUsernames = getValidMentionedUsernames(fullComment.getContent());
        processAndNotifyMentions(author, post, fullComment, validUsernames);
        
        return fullComment;
    }

    @Transactional
    public UpdatePostResponseDTO updatePost(Long postId, UpdatePostRequestDTO updateDto) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new IllegalStateException("User must be logged in to update posts.");
        }

        CommunityPost post = postMapper.selectById(postId);
        if (post == null) {
            throw new ResourceNotFoundException("Post not found");
        }

        if (!post.getAuthorId().equals(currentUser.getId())) {
            throw new AccessDeniedException("User is not the author of this post.");
        }

        post.setTitle(updateDto.getTitle());
        post.setContent(updateDto.getContent());
        post.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        postMapper.updateById(post);

        // Handle @mentions
        Set<String> validUsernames = getValidMentionedUsernames(post.getContent());
        processAndNotifyMentions(currentUser, post, null, validUsernames);

        return new UpdatePostResponseDTO(post.getUpdatedAt());
    }

    @Transactional
    public CommunityPost togglePinPost(Long postId) {
        User currentUser = getCurrentUser();
        if (currentUser == null || !"KitolusAdmin".equals(currentUser.getRole())) {
            throw new AccessDeniedException("只有管理员可以置顶帖子");
        }

        CommunityPost post = postMapper.selectById(postId);
        if (post == null) {
            throw new ResourceNotFoundException("帖子不存在");
        }

        if (Boolean.TRUE.equals(post.getIsPinned())) {
            // 取消置顶
            post.setIsPinned(false);
            post.setPinnedAt(null);
            post.setPinnedBy(null);
        } else {
            // 检查当前置顶帖子数量
            QueryWrapper<CommunityPost> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_pinned", true);
            List<CommunityPost> pinnedPosts = postMapper.selectList(queryWrapper);

            if (pinnedPosts.size() >= 5) {
                // 找到最早置顶的帖子并取消置顶
                CommunityPost oldestPinned = pinnedPosts.stream()
                    .min((p1, p2) -> p1.getPinnedAt().compareTo(p2.getPinnedAt()))
                    .orElse(null);

                if (oldestPinned != null) {
                    oldestPinned.setIsPinned(false);
                    oldestPinned.setPinnedAt(null);
                    oldestPinned.setPinnedBy(null);
                    postMapper.updateById(oldestPinned);
                }
            }

            // 置顶当前帖子
            post.setIsPinned(true);
            post.setPinnedAt(new Timestamp(System.currentTimeMillis()));
            post.setPinnedBy(currentUser.getId());
        }

        postMapper.updateById(post);

        // 返回包含完整信息的帖子对象
        CommunityPost detailedPost = postMapper.selectPostWithDetailsById(postId, currentUser.getId());
        processSinglePostAvatars(detailedPost);
        return detailedPost;
    }

    public List<CommunityPost> getPinnedPosts() {
        try {
            User currentUser = getCurrentUser();
            Long currentUserId = (currentUser != null) ? currentUser.getId() : null;

            QueryWrapper<CommunityPost> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_pinned", true)
                       .orderByDesc("pinned_at")
                       .last("LIMIT 5");

            List<CommunityPost> pinnedPosts = postMapper.selectList(queryWrapper);

            // 为每个置顶帖子获取详细信息
            return pinnedPosts.stream()
                .map(post -> {
                    try {
                        CommunityPost detailedPost = postMapper.selectPostWithDetailsById(post.getId(), currentUserId);
                        processSinglePostAvatars(detailedPost);
                        return detailedPost;
                    } catch (Exception e) {
                        System.err.println("Error processing pinned post with ID: " + post.getId() + ", Error: " + e.getMessage());
                        e.printStackTrace();
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("Error in getPinnedPosts: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public List<CommunityPost> getNonPinnedPosts() {
        try {
            User currentUser = getCurrentUser();
            Long currentUserId = (currentUser != null) ? currentUser.getId() : null;

            QueryWrapper<CommunityPost> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper -> wrapper
                       .ne("is_pinned", true)
                       .or()
                       .isNull("is_pinned"))
                       .orderByDesc("created_at");

            List<CommunityPost> posts = postMapper.selectList(queryWrapper);

            // 为每个帖子获取详细信息
            return posts.stream()
                .map(post -> {
                    try {
                        CommunityPost detailedPost = postMapper.selectPostWithDetailsById(post.getId(), currentUserId);
                        processSinglePostAvatars(detailedPost);
                        return detailedPost;
                    } catch (Exception e) {
                        System.err.println("Error processing non-pinned post with ID: " + post.getId() + ", Error: " + e.getMessage());
                        e.printStackTrace();
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("Error in getNonPinnedPosts: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户最近发布的帖子
     */
    public List<CommunityPost> getUserRecentPosts(Long userId, int limit) {
        try {
            QueryWrapper<CommunityPost> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("author_id", userId)
                       .orderByDesc("created_at")
                       .last("LIMIT " + limit);

            List<CommunityPost> posts = postMapper.selectList(queryWrapper);

            // 为每个帖子获取详细信息
            return posts.stream()
                .map(post -> {
                    try {
                        CommunityPost detailedPost = postMapper.selectPostWithDetailsById(post.getId(), userId);
                        processSinglePostAvatars(detailedPost);
                        return detailedPost;
                    } catch (Exception e) {
                        System.err.println("Error processing user post with ID: " + post.getId() + ", Error: " + e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("Error in getUserRecentPosts: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户最近发布的评论
     */
    public List<CommunityComment> getUserRecentComments(Long userId, int limit) {
        try {
            QueryWrapper<CommunityComment> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("author_id", userId)
                       .orderByDesc("created_at")
                       .last("LIMIT " + limit);

            List<CommunityComment> comments = commentMapper.selectList(queryWrapper);

            // 为每个评论获取作者信息
            return comments.stream()
                .map(comment -> {
                    try {
                        // 获取评论作者信息
                        User author = userMapper.selectById(comment.getAuthorId());
                        comment.setAuthor(author);

                        return comment;
                    } catch (Exception e) {
                        System.err.println("Error processing user comment with ID: " + comment.getId() + ", Error: " + e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("Error in getUserRecentComments: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
}