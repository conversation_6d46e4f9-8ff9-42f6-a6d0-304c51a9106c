'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle } from 'lucide-react';

interface ErrorRecoveryProps {
  children: React.ReactNode;
}

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({ children }) => {
  const [hasError, setHasError] = useState(false);
  const [isRecovering, setIsRecovering] = useState(false);

  useEffect(() => {
    // 检测页面是否无响应
    let interactionTimeout: NodeJS.Timeout;
    let hasInteracted = false;

    const resetTimeout = () => {
      hasInteracted = true;
      clearTimeout(interactionTimeout);
      interactionTimeout = setTimeout(() => {
        if (!hasInteracted) {
          console.warn('⚠️ 检测到页面可能无响应');
          setHasError(true);
        }
      }, 10000); // 10秒无交互则认为可能有问题
    };

    const handleInteraction = () => {
      hasInteracted = true;
      resetTimeout();
    };

    // 监听用户交互
    document.addEventListener('click', handleInteraction);
    document.addEventListener('keydown', handleInteraction);
    document.addEventListener('scroll', handleInteraction);

    resetTimeout();

    return () => {
      clearTimeout(interactionTimeout);
      document.removeEventListener('click', handleInteraction);
      document.removeEventListener('keydown', handleInteraction);
      document.removeEventListener('scroll', handleInteraction);
    };
  }, []);

  const handleRecovery = () => {
    setIsRecovering(true);
    
    // 清理可能的问题状态
    try {
      // 清理localStorage中的问题数据
      const keysToCheck = ['session_lock', 'sso-event', 'messageSystemState'];
      keysToCheck.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
          console.log(`🧹 清理可能的问题数据: ${key}`);
          localStorage.removeItem(key);
        }
      });

      // 延迟刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('❌ 恢复过程中出错:', error);
      // 直接刷新页面
      window.location.reload();
    }
  };

  const handleForceRefresh = () => {
    window.location.reload();
  };

  if (hasError) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-zinc-900 border border-zinc-700 rounded-xl p-8 max-w-md mx-4 text-center">
          <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">页面可能无响应</h2>
          <p className="text-zinc-400 mb-6">
            检测到页面可能出现了问题。您可以尝试恢复或刷新页面。
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={handleRecovery}
              disabled={isRecovering}
              className="w-full"
            >
              {isRecovering ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  正在恢复...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  智能恢复
                </>
              )}
            </Button>
            
            <Button
              onClick={handleForceRefresh}
              variant="outline"
              className="w-full"
            >
              强制刷新页面
            </Button>
            
            <Button
              onClick={() => setHasError(false)}
              variant="ghost"
              className="w-full text-zinc-400"
            >
              继续使用（可能仍有问题）
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
