/**
 * 现代化实时通信管理器
 * 混合使用 WebSocket + Server-Sent Events + WebRTC
 * 优势：更可靠的连接、更低的延迟、更好的扩展性
 */

import { EventEmitter } from 'events';

interface RealtimeConfig {
  enableWebSocket: boolean;
  enableSSE: boolean;
  enableWebRTC: boolean;
  fallbackStrategy: 'websocket' | 'sse' | 'polling';
  reconnectAttempts: number;
  heartbeatInterval: number;
}

class RealtimeManager extends EventEmitter {
  private config: RealtimeConfig;
  private ws: WebSocket | null = null;
  private sse: EventSource | null = null;
  private rtcConnection: RTCPeerConnection | null = null;
  private currentTransport: 'websocket' | 'sse' | 'webrtc' | 'polling' = 'websocket';
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private token: string | null = null;
  private userId: number | null = null;

  constructor(config: Partial<RealtimeConfig> = {}) {
    super();
    this.config = {
      enableWebSocket: true,
      enableSSE: true,
      enableWebRTC: false,
      fallbackStrategy: 'sse',
      reconnectAttempts: 5,
      heartbeatInterval: 30000,
      ...config,
    };
  }

  // 智能连接 - 自动选择最佳传输方式
  async connect(token: string, userId: number) {
    this.token = token;
    this.userId = userId;

    // 按优先级尝试连接
    const transports = ['websocket', 'sse', 'polling'] as const;
    
    for (const transport of transports) {
      try {
        await this.connectWithTransport(transport);
        this.currentTransport = transport;
        this.emit('connected', { transport });
        this.startHeartbeat();
        return;
      } catch (error) {
        console.warn(`Failed to connect with ${transport}:`, error);
        continue;
      }
    }
    
    throw new Error('All transport methods failed');
  }

  // WebSocket连接
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${location.host}/ws/messages?token=${this.token}&userId=${this.userId}`;
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        this.reconnectAttempts = 0;
        resolve();
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('WebSocket message parse error:', error);
        }
      };
      
      this.ws.onclose = () => {
        this.ws = null;
        this.emit('disconnected');
        this.handleReconnect();
      };
      
      this.ws.onerror = (error) => {
        reject(error);
      };
      
      // 连接超时
      setTimeout(() => {
        if (this.ws?.readyState !== WebSocket.OPEN) {
          this.ws?.close();
          reject(new Error('WebSocket connection timeout'));
        }
      }, 10000);
    });
  }

  // Server-Sent Events连接
  private async connectSSE(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sseUrl = `/api/messages/stream?token=${this.token}&userId=${this.userId}`;
      
      this.sse = new EventSource(sseUrl);
      
      this.sse.onopen = () => {
        this.reconnectAttempts = 0;
        resolve();
      };
      
      this.sse.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('SSE message parse error:', error);
        }
      };
      
      this.sse.onerror = (error) => {
        this.sse?.close();
        this.sse = null;
        this.emit('disconnected');
        reject(error);
      };
      
      // 连接超时
      setTimeout(() => {
        if (this.sse?.readyState !== EventSource.OPEN) {
          this.sse?.close();
          reject(new Error('SSE connection timeout'));
        }
      }, 10000);
    });
  }

  // WebRTC数据通道（用于低延迟消息）
  private async connectWebRTC(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        this.rtcConnection = new RTCPeerConnection({
          iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        });

        // 创建数据通道
        const dataChannel = this.rtcConnection.createDataChannel('messages', {
          ordered: true
        });

        dataChannel.onopen = () => {
          resolve();
        };

        dataChannel.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('WebRTC message parse error:', error);
          }
        };

        // 信令服务器交换
        const offer = await this.rtcConnection.createOffer();
        await this.rtcConnection.setLocalDescription(offer);

        // 通过HTTP API交换信令
        const response = await fetch('/api/webrtc/offer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({ offer, userId: this.userId })
        });

        const { answer } = await response.json();
        await this.rtcConnection.setRemoteDescription(answer);

      } catch (error) {
        reject(error);
      }
    });
  }

  // 统一的传输方式连接
  private async connectWithTransport(transport: 'websocket' | 'sse' | 'webrtc' | 'polling'): Promise<void> {
    switch (transport) {
      case 'websocket':
        if (this.config.enableWebSocket) {
          return this.connectWebSocket();
        }
        break;
      case 'sse':
        if (this.config.enableSSE) {
          return this.connectSSE();
        }
        break;
      case 'webrtc':
        if (this.config.enableWebRTC) {
          return this.connectWebRTC();
        }
        break;
      case 'polling':
        return this.connectPolling();
    }
    throw new Error(`Transport ${transport} not enabled or supported`);
  }

  // 轮询连接（最后的备选方案）
  private async connectPolling(): Promise<void> {
    // 实现长轮询逻辑
    const poll = async () => {
      try {
        const response = await fetch(`/api/messages/poll?token=${this.token}&userId=${this.userId}`, {
          method: 'GET',
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        
        if (response.ok) {
          const data = await response.json();
          if (data.messages) {
            data.messages.forEach((message: any) => this.handleMessage(message));
          }
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
      
      // 继续轮询
      setTimeout(poll, 2000);
    };
    
    poll();
    return Promise.resolve();
  }

  // 发送消息 - 智能路由
  send(type: string, data: any) {
    const message = { type, data, timestamp: Date.now() };
    
    switch (this.currentTransport) {
      case 'websocket':
        if (this.ws?.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify(message));
          return;
        }
        break;
      case 'webrtc':
        if (this.rtcConnection) {
          const dataChannel = this.rtcConnection.createDataChannel('messages');
          if (dataChannel.readyState === 'open') {
            dataChannel.send(JSON.stringify(message));
            return;
          }
        }
        break;
      case 'sse':
      case 'polling':
        // 通过HTTP API发送
        fetch('/api/messages/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify(message)
        });
        return;
    }
    
    // 如果当前传输失败，尝试备选方案
    this.handleReconnect();
  }

  // 消息处理
  private handleMessage(data: any) {
    switch (data.type) {
      case 'NEW_MESSAGE':
        this.emit('message', data.payload);
        break;
      case 'MESSAGE_READ':
        this.emit('messageRead', data.payload);
        break;
      case 'USER_TYPING':
        this.emit('userTyping', data.payload);
        break;
      case 'USER_ONLINE':
        this.emit('userOnline', data.payload);
        break;
      case 'HEARTBEAT':
        this.emit('heartbeat');
        break;
      default:
        this.emit('data', data);
    }
  }

  // 心跳机制
  private startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.send('HEARTBEAT', { userId: this.userId });
    }, this.config.heartbeatInterval);
  }

  // 重连机制
  private async handleReconnect() {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    
    setTimeout(async () => {
      try {
        await this.connect(this.token!, this.userId!);
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    }, delay);
  }

  // 断开连接
  disconnect() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    this.ws?.close();
    this.sse?.close();
    this.rtcConnection?.close();
    
    this.ws = null;
    this.sse = null;
    this.rtcConnection = null;
    
    this.emit('disconnected');
  }

  // 获取连接状态
  get isConnected() {
    switch (this.currentTransport) {
      case 'websocket':
        return this.ws?.readyState === WebSocket.OPEN;
      case 'sse':
        return this.sse?.readyState === EventSource.OPEN;
      case 'webrtc':
        return this.rtcConnection?.connectionState === 'connected';
      case 'polling':
        return true; // 轮询总是"连接"的
      default:
        return false;
    }
  }

  // 获取连接质量指标
  getConnectionMetrics() {
    return {
      transport: this.currentTransport,
      reconnectAttempts: this.reconnectAttempts,
      isConnected: this.isConnected,
      latency: this.measureLatency(),
    };
  }

  // 测量延迟
  private measureLatency(): number {
    // 实现延迟测量逻辑
    return 0;
  }
}

// 全局实例
export const realtimeManager = new RealtimeManager();

// React Hook
export const useRealtime = () => {
  const connect = (token: string, userId: number) => realtimeManager.connect(token, userId);
  const disconnect = () => realtimeManager.disconnect();
  const send = (type: string, data: any) => realtimeManager.send(type, data);
  
  return {
    connect,
    disconnect,
    send,
    isConnected: realtimeManager.isConnected,
    metrics: realtimeManager.getConnectionMetrics(),
    on: (event: string, handler: (...args: any[]) => void) => realtimeManager.on(event, handler),
    off: (event: string, handler: (...args: any[]) => void) => realtimeManager.off(event, handler),
  };
};
