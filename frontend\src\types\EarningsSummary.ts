import { Product } from './Product';

// These should match the DTOs from the backend
interface MonthlyRevenueDTO {
    month: string;
    revenue: number;
}

interface DailyRevenueDTO {
    date: string;
    revenue: number;
}

interface OrderDTO {
    id: string;
    totalFee: number;
    paidAt: string;
    product: {
        name: string;
    };
    username: string;
}

export interface EarningsSummaryDTO {
    // Shared fields
    totalSales: number;
    monthlyRevenue: MonthlyRevenueDTO[];
    dailyRevenue: DailyRevenueDTO[];
    topSellingProduct: Product | null;
    topSellingProducts?: Product[];
    averageOrderValue: number;
    productCount: number;
    recentOrders: OrderDTO[];
    
    // Global specific
    totalRevenue?: number;
    communityRevenue?: number;
    developerCount?: number;

    // Developer specific
    developerRevenue?: number;
    availableBalance?: number;
} 