/**
 * 在线状态指示器组件
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface OnlineStatusIndicatorProps {
  isOnline: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
}

export function OnlineStatusIndicator({ 
  isOnline, 
  size = 'md', 
  className,
  showText = false 
}: OnlineStatusIndicatorProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div
        className={cn(
          'rounded-full border-2 border-white shadow-sm transition-colors duration-200',
          sizeClasses[size],
          isOnline
            ? 'bg-green-500'
            : 'bg-gray-400'
        )}
        title={isOnline ? '在线' : '离线'}
      />
      {showText && (
        <span className={cn(
          'font-medium transition-colors duration-200',
          textSizeClasses[size],
          isOnline ? 'text-green-600' : 'text-gray-500'
        )}>
          {isOnline ? '在线' : '离线'}
        </span>
      )}
    </div>
  );
}

/**
 * 输入状态指示器组件
 */
interface TypingIndicatorProps {
  isTyping: boolean;
  userName?: string;
  className?: string;
  style?: React.CSSProperties;
}

export function TypingIndicator({
  isTyping,
  userName = '对方',
  className,
  style
}: TypingIndicatorProps) {
  if (!isTyping) return null;

  return (
    <div
      className={cn(
        'flex items-center gap-2 text-sm text-gray-500 animate-fade-in',
        className
      )}
      style={style}
    >
      <div className="flex gap-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span>{userName} 正在输入...</span>
    </div>
  );
}

/**
 * 组合的用户状态组件
 */
interface UserStatusProps {
  userId: number;
  userName: string;
  avatarUrl?: string;
  isOnline: boolean;
  isTyping?: boolean;
  conversationId?: string;
  showOnlineText?: boolean;
  className?: string;
}

export function UserStatus({
  userId,
  userName,
  avatarUrl,
  isOnline,
  isTyping = false,
  showOnlineText = false,
  className
}: UserStatusProps) {
  return (
    <div className={cn('flex items-center gap-3', className)}>
      {/* 头像和在线状态 */}
      <div className="relative">
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt={userName}
            className="w-10 h-10 rounded-full object-cover"
          />
        ) : (
          <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
            <span className="text-gray-600 font-medium">
              {userName.charAt(0).toUpperCase()}
            </span>
          </div>
        )}
        
        {/* 在线状态指示器 */}
        <div className="absolute -bottom-1 -right-1">
          <OnlineStatusIndicator isOnline={isOnline} size="sm" />
        </div>
      </div>

      {/* 用户信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-gray-900 truncate">{userName}</h3>
          {showOnlineText && (
            <OnlineStatusIndicator 
              isOnline={isOnline} 
              size="sm" 
              showText 
            />
          )}
        </div>
        
        {/* 输入状态 */}
        {isTyping && (
          <TypingIndicator 
            isTyping={true} 
            userName={userName}
            className="mt-1"
          />
        )}
      </div>
    </div>
  );
}

/**
 * 在线用户列表组件
 */
interface OnlineUsersListProps {
  onlineUsers: Array<{
    id: number;
    username: string;
    avatarUrl?: string;
  }>;
  className?: string;
}

export function OnlineUsersList({ onlineUsers, className }: OnlineUsersListProps) {
  if (onlineUsers.length === 0) {
    return (
      <div className={cn('text-center text-gray-500 py-4', className)}>
        暂无在线用户
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <h4 className="text-sm font-medium text-gray-700 mb-3">
        在线用户 ({onlineUsers.length})
      </h4>
      {onlineUsers.map(user => (
        <UserStatus
          key={user.id}
          userId={user.id}
          userName={user.username}
          avatarUrl={user.avatarUrl}
          isOnline={true}
          className="p-2 rounded-lg hover:bg-gray-50 transition-colors"
        />
      ))}
    </div>
  );
}
