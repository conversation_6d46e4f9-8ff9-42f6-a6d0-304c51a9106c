package com.kitolus.community.controller;

import com.kitolus.community.dto.CreateWithdrawalRequestDTO;
import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.ProductSalesStatsDTO;
import com.kitolus.community.dto.WithdrawalRequestDetailsDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.entity.WithdrawalRequest;
import com.kitolus.community.service.EarningsService;
import com.kitolus.community.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import com.kitolus.community.dto.OrderDTO;

@RestController
@RequestMapping("/api/earnings")
public class EarningsController {

    @Autowired
    private EarningsService earningsService;

    @Autowired
    private UserService userService;

    @GetMapping("/my-summary")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<EarningsSummaryDTO> getMyEarningsSummary() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalName = authentication.getName();
        User currentUser = userService.findByUsername(currentPrincipalName);

        EarningsSummaryDTO summary = earningsService.getDeveloperEarningsSummary(currentUser.getId());
        return ResponseEntity.ok(summary);
    }

    @PostMapping("/withdrawal-requests")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<WithdrawalRequest> createWithdrawalRequest(@Valid @RequestBody CreateWithdrawalRequestDTO requestDTO) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        WithdrawalRequest createdRequest = earningsService.createWithdrawalRequest(requestDTO, username);
        return ResponseEntity.ok(createdRequest);
    }

    @GetMapping("/withdrawal-requests")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<List<WithdrawalRequestDetailsDTO>> getMyWithdrawalRequests() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        List<WithdrawalRequestDetailsDTO> requests = earningsService.getWithdrawalRequestsForUser(username);
        return ResponseEntity.ok(requests);
    }

    @GetMapping("/my-transactions")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<List<OrderDTO>> getMyTransactions() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        List<OrderDTO> transactions = earningsService.getDeveloperTransactionHistory(username);
        return ResponseEntity.ok(transactions);
    }

    @GetMapping("/my-product-sales")
    @PreAuthorize("hasAuthority('DEVELOPER')")
    public ResponseEntity<List<ProductSalesStatsDTO>> getMyProductSalesStats() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalName = authentication.getName();
        User currentUser = userService.findByUsername(currentPrincipalName);

        List<ProductSalesStatsDTO> stats = earningsService.getDeveloperProductSalesStats(currentUser.getId());
        return ResponseEntity.ok(stats);
    }
}