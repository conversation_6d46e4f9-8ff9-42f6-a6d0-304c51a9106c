'use client';

import { motion } from 'framer-motion';
import { But<PERSON> } from './ui/button';
import { BookOpenText, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface StoreHeroProps {
    onExploreClick: () => void;
}

const StoreHero = ({ onExploreClick }: StoreHeroProps) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-center max-w-4xl mx-auto"
        >
            <h1 
                className="text-5xl md:text-7xl font-bold tracking-tight mb-4 font-serif"
                style={{ color: '#5c4529' }}
            >
                社区酒馆
            </h1>
            <p className="text-lg md:text-xl max-w-2xl mx-auto mb-8" style={{ color: '#7a5c3e' }}>
                “一本由玩家为玩家书写的冒险日志。在这里发现、分享并获取由社区精心制作的独家蓝图、实用工具与精美材质。”
            </p>
            <Link href="/products/all" passHref>
                <Button 
                    asChild
                    variant="outline"
                    className="text-lg px-8 py-6 rounded-lg border-2 
                               border-[#8a6c4a] text-[#6b533a] 
                               hover:bg-[#eaddc7] hover:border-[#6b533a] hover:text-[#4f422e]
                               transition-all duration-300 transform hover:scale-105"
                >
                    <a>
                        <BookOpenText className="mr-3 h-6 w-6" />
                        进入宝库，发现所有宝藏
                        <ArrowRight className="ml-3 h-6 w-6" />
                    </a>
                </Button>
            </Link>
        </motion.div>
    );
};

export default StoreHero; 