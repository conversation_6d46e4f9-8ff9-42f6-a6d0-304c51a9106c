export enum NotificationType {
    NEW_COMMENT = 'NEW_COMMENT',
    REPLY_TO_COMMENT = 'REPLY_TO_COMMENT',
    NEW_REPLY_TO_COMMENT = 'NEW_REPLY_TO_COMMENT',
    DEVELOPER_APP_APPROVED = 'DEVELOPER_APP_APPROVED',
    DEVELOPER_APP_REJECTED = 'DEVELOPER_APP_REJECTED',
    PRODUCT_APPROVED = 'PRODUCT_APPROVED',
    PRODUCT_REJECTED = 'PRODUCT_REJECTED',
    PRODUCT_DELISTED = 'PRODUCT_DELISTED'
}

export interface Notification {
    id: number;
    type: NotificationType;
    isRead: boolean;
    createdAt: string;
    contentPreview: string | null;

    // For comment/reply notifications
    postId?: number;
    postTitle?: string;
    commentId?: number;
    parentCommentPreview?: string;
    
    // Sender info (for comments, replies, etc.)
    senderId?: number;
    senderUsername?: string;
    senderAvatarUrl?: string;
    senderSerialNumber?: string;
    senderAvatarVersion?: number;
} 