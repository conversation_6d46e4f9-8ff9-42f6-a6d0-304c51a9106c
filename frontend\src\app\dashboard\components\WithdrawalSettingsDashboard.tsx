'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { getMyProfile, updateUserWithdrawalSettings } from '@/services/api';

const WithdrawalSettingsDashboard = () => {
  const { user } = useAuth();
  const [alipayAccount, setAlipayAccount] = useState('');
  const [wechatAccount, setWechatAccount] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);

  useEffect(() => {
    if (user) {
      const fetchProfile = async () => {
        try {
          setIsFetching(true);
          const profile = await getMyProfile();
          setAlipayAccount(profile.alipayAccount || '');
          setWechatAccount(profile.wechatAccount || '');
        } catch (error) {
          toast.error('无法加载您的提现设置信息。');
        } finally {
          setIsFetching(false);
        }
      };
      fetchProfile();
    }
  }, [user]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await updateUserWithdrawalSettings({ alipayAccount, wechatAccount });
      toast.success('提现设置已成功更新！');
    } catch (error) {
      toast.error('更新失败，请稍后再试。');
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>提现设置</CardTitle>
          <CardDescription>加载中...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>提现设置</CardTitle>
        <CardDescription>管理您的提现方式。请确保信息准确无误，以保证资金顺利到账。</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="alipayAccount">支付宝账号</Label>
            <Input
              id="alipayAccount"
              value={alipayAccount}
              onChange={(e) => setAlipayAccount(e.target.value)}
              placeholder="您的手机号或者支付宝账号"
            />
          </div>
          <div>
            <Label htmlFor="wechatAccount">微信账号 (暂不支持)</Label>
            <Input
              id="wechatAccount"
              value={wechatAccount}
              onChange={(e) => setWechatAccount(e.target.value)}
              placeholder="您的微信号"
              disabled
            />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? '保存中...' : '保存更改'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WithdrawalSettingsDashboard; 