/**
 * WebSocket连接管理器
 * 用于实时消息推送
 */

import { WebSocketMessageEvent } from '@/types/Message';
import { ssoManager } from './ssoManager';

type EventHandler = (data: any) => void;

class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000;
  private eventHandlers: Map<string, EventHandler[]> = new Map();
  private isConnecting = false;
  private token: string | null = null;
  private shouldReconnect = true; // 控制是否应该重连

  constructor() {
    // 从localStorage获取token - 统一使用jwt_token
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('jwt_token');

      // 监听SSO事件
      ssoManager.on('logout', () => {
        this.shouldReconnect = false;
        this.disconnect();
      });

      ssoManager.on('session_conflict', () => {
        this.shouldReconnect = false;
        this.disconnect();
      });

      ssoManager.on('login', (event) => {
        this.shouldReconnect = true;
        this.updateToken(event.token || null);
      });
    }
  }

  // 连接WebSocket
  connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    if (!this.token) {
      return;
    }

    this.isConnecting = true;

    try {
      // 构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws/messages?token=${this.token}`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.emit('connected', null);
      };

      this.ws.onmessage = (event) => {
        try {
          const data: WebSocketMessageEvent = JSON.parse(event.data);
          this.emit(data.type, data.data);
        } catch (error) {
          // Handle parsing error silently
        }
      };

      this.ws.onclose = (event) => {
        this.isConnecting = false;
        this.ws = null;
        this.emit('disconnected', null);

        // 检查关闭原因，如果是SSO相关的关闭，不要重连
        if (event.reason === 'New connection established' || !this.shouldReconnect) {
          return;
        }

        this.handleReconnect();
      };

      this.ws.onerror = () => {
        this.isConnecting = false;
        this.emit('error', null);
      };
    } catch (error) {
      this.isConnecting = false;
    }
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止重连
  }

  // 发送消息
  send(type: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: new Date().toISOString()
      };
      this.ws.send(JSON.stringify(message));
    }
  }

  // 添加事件监听器
  on(event: string, handler: EventHandler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  // 移除事件监听器
  off(event: string, handler: EventHandler) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(event: string, data: any) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }

  // 处理重连
  private handleReconnect() {
    if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, delay);
  }

  // 更新token
  updateToken(token: string | null) {
    this.token = token;
    if (token) {
      this.shouldReconnect = true;
      this.connect();
    } else {
      this.shouldReconnect = false;
      this.disconnect();
    }
  }

  // 获取连接状态
  get isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  // 获取连接状态
  get connectionState() {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }
}

// 创建全局WebSocket管理器实例
export const wsManager = new WebSocketManager();

// React Hook for WebSocket
export const useWebSocket = () => {
  const connect = () => wsManager.connect();
  const disconnect = () => wsManager.disconnect();
  const send = (type: string, data: any) => wsManager.send(type, data);
  const on = (event: string, handler: EventHandler) => wsManager.on(event, handler);
  const off = (event: string, handler: EventHandler) => wsManager.off(event, handler);
  
  return {
    connect,
    disconnect,
    send,
    on,
    off,
    isConnected: wsManager.isConnected,
    connectionState: wsManager.connectionState
  };
};

// 自动连接管理
if (typeof window !== 'undefined') {
  // 监听token变化 - 统一使用jwt_token
  window.addEventListener('storage', (e) => {
    if (e.key === 'jwt_token') {
      wsManager.updateToken(e.newValue);
    }
  });

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      wsManager.connect();
    }
  });

  // 页面加载时自动连接
  if (document.readyState === 'complete') {
    wsManager.connect();
  } else {
    window.addEventListener('load', () => {
      wsManager.connect();
    });
  }
}
