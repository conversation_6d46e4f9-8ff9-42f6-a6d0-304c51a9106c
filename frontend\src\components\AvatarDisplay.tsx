'use client';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { motion, AnimatePresence } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { User } from 'lucide-react';
import { resolveAvatarUrl } from "@/lib/utils";
import React from "react";

interface AvatarDisplayProps {
  avatarUrl?: string | null;
  serialNumber?: string | null;
  avatarVersion?: number | null;
  size?: number;
  className?: string;
  isLoading?: boolean;
}

const AvatarDisplay = ({ avatarUrl, serialNumber, avatarVersion, size = 32, className, isLoading = false }: AvatarDisplayProps) => {
  const containerSize = { width: size, height: size };
  const [imageUrl, setImageUrl] = React.useState('');

  React.useEffect(() => {
    let finalUrl = '';
    // If we have a full URL, use it directly. This is the most common and preferred case.
    if (avatarUrl && (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://'))) {
      finalUrl = avatarUrl;
      // Append version if available, to bust cache
      if (avatarVersion) {
        finalUrl += `?v=${avatarVersion}`;
      }
    } else {
      // Fallback for cases where a full URL is not provided, or for the default SVG.
      let resolvedPath = resolveAvatarUrl(avatarUrl, serialNumber);
      if (resolvedPath.startsWith('<svg')) {
        finalUrl = `data:image/svg+xml;base64,${btoa(resolvedPath)}`;
      } else {
        // This case should ideally not be hit if useAuth provides full URLs,
        // but as a failsafe, we construct it.
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
        finalUrl = `${baseUrl}${resolvedPath.startsWith('/') ? '' : '/'}${resolvedPath}`;
        if (avatarVersion) {
            finalUrl += `?v=${avatarVersion}`;
        }
      }
    }
    setImageUrl(finalUrl);
  }, [avatarUrl, avatarVersion, serialNumber]);


  return (
    <div style={containerSize} className="relative">
      <AnimatePresence>
        {isLoading ? (
          <motion.div
            key="skeleton"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="absolute inset-0"
          >
            <Skeleton className={className} style={containerSize} />
          </motion.div>
        ) : (
          <motion.div
            key="avatar"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Avatar className={className} style={containerSize}>
              <AvatarImage src={imageUrl} alt="User avatar" />
              <AvatarFallback style={{ width: size, height: size }}>
                <User />
              </AvatarFallback>
            </Avatar>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AvatarDisplay; 