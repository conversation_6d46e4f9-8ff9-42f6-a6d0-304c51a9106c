package com.kitolus.community.service;

import com.kitolus.community.dto.CreateWithdrawalRequestDTO;
import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.ProductSalesStatsDTO;
import com.kitolus.community.dto.ReviewWithdrawalRequestDTO;
import com.kitolus.community.dto.WithdrawalRequestDetailsDTO;
import com.kitolus.community.entity.WithdrawalRequest;
import com.kitolus.community.dto.OrderDTO;

import java.util.List;
 
public interface EarningsService {
    EarningsSummaryDTO getGlobalEarningsSummary();
    EarningsSummaryDTO getDeveloperEarningsSummary(Long developerId);

    WithdrawalRequest createWithdrawalRequest(CreateWithdrawalRequestDTO requestDTO, String username);

    List<WithdrawalRequestDetailsDTO> getWithdrawalRequestsForUser(String username);

    List<WithdrawalRequestDetailsDTO> getAllWithdrawalRequests();

    WithdrawalRequest reviewWithdrawalRequest(Long requestId, ReviewWithdrawalRequestDTO reviewDTO, String adminUsername);
    List<OrderDTO> getDeveloperTransactionHistory(String username);
    List<ProductSalesStatsDTO> getDeveloperProductSalesStats(Long developerId);
} 