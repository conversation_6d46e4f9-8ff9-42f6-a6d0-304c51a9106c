'use client';

import { useState, useEffect } from 'react';
import apiService from '@/services/api';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Send, CheckCircle, Clock } from 'lucide-react';

const DeveloperApplicationForm = () => {
    const [applicationMessage, setApplicationMessage] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [applicationStatus, setApplicationStatus] = useState<'LOADING' | 'NO_APPLICATION' | 'PENDING' | 'REJECTED' | 'SUBMIT_SUCCESS' | 'ERROR'>('LOADING');

    useEffect(() => {
        const fetchApplicationStatus = async () => {
            try {
                const response = await apiService.get('/api/user/developer-application');
                if (response.data) {
                    switch (response.data.status) {
                        case 'PENDING':
                            setApplicationStatus('PENDING');
                            break;
                        case 'REJECTED':
                            setApplicationStatus('REJECTED');
                            break;
                        default:
                            setApplicationStatus('NO_APPLICATION');
                            break;
                    }
                } else {
                    setApplicationStatus('NO_APPLICATION');
                }
            } catch (err: any) {
                if (err.response && err.response.status === 404) {
                    setApplicationStatus('NO_APPLICATION');
                } else {
                    setError('无法加载您的申请状态，请刷新页面重试。');
                    setApplicationStatus('ERROR');
                }
            }
        };

        fetchApplicationStatus();
    }, []);

    const handleApply = async () => {
        if (!applicationMessage.trim()) {
            setError('申请理由不能为空。');
            return;
        }
        setIsSubmitting(true);
        setError('');
        try {
            await apiService.post('/api/user/apply-developer', { message: applicationMessage });
            setApplicationStatus('SUBMIT_SUCCESS');
        } catch (err: any) {
            setError(err.response?.data?.message || '提交申请失败，请稍后再试。');
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderContent = () => {
        switch (applicationStatus) {
            case 'LOADING':
                return <p>正在加载您的申请状态...</p>;
            case 'ERROR':
                return <p className="text-red-500">{error}</p>;
            case 'PENDING':
                return (
                    <div className="text-blue-500 flex items-center gap-2">
                        <Clock />
                        您的申请正在审核中，请耐心等待。
                    </div>
                );
            case 'SUBMIT_SUCCESS':
                return (
                    <div className="text-green-500 flex items-center gap-2">
                        <CheckCircle />
                        您的申请已成功提交！管理员审核通过后将会通知您。
                    </div>
                );
            case 'NO_APPLICATION':
            case 'REJECTED':
                return (
                    <>
                        <p className="mb-4 text-sm">
                            {applicationStatus === 'REJECTED' && "您之前的申请未通过。"}
                            准备好分享您的作品了吗？请在下方简要介绍您希望发布的内容或您的开发经验，然后提交申请。
                        </p>
                        <Textarea
                            placeholder="例如：我是一位插件开发者，希望能在这里分享我的作品..."
                            value={applicationMessage}
                            onChange={(e) => setApplicationMessage(e.target.value)}
                            className="mb-4"
                        />
                        <Button onClick={handleApply} disabled={isSubmitting || !applicationMessage.trim()}>
                            <Send className="mr-2 h-4 w-4" />
                            {isSubmitting ? '正在提交...' : '提交开发者申请'}
                        </Button>
                        {error && <p className="mt-2 text-red-500">{error}</p>}
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-lg">
            <CardHeader>
                <CardTitle>成为开发者</CardTitle>
                <CardDescription>分享您的创作，为社区贡献力量，并获得收益。</CardDescription>
            </CardHeader>
            <CardContent>
                {renderContent()}
            </CardContent>
        </Card>
    );
};

export default DeveloperApplicationForm; 