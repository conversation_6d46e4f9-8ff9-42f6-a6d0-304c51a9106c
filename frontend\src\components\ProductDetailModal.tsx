'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { X, ShoppingCart, Info, User, Tag, ImageOff, Loader2, CheckCircle } from 'lucide-react';
import { useState, useEffect, useRef, useContext } from 'react';
import { Product } from '@/types/Product';
import { createPaymentOrder, getPaymentStatus, getPurchasedProducts } from '@/services/api';
import { toast } from 'sonner';
import { QRCodeCanvas } from 'qrcode.react';
import { useAuth } from '@/contexts/AuthContext';
import Confetti from 'react-confetti';
import useWindowSize from '@/hooks/useWindowSize';

// A simple component for the QR Code Modal
const QRCodeModal = ({ codeUrl, orderId, onClose, onPaymentSuccess, onRefresh }: { codeUrl: string; orderId: string; onClose: () => void; onPaymentSuccess: () => void; onRefresh: () => void; }) => {
    const pollingIntervalRef = useRef<NodeJS.Timeout>();
    const refreshIntervalRef = useRef<NodeJS.Timeout>();
    const countdownIntervalRef = useRef<NodeJS.Timeout>();
    const [countdown, setCountdown] = useState(60);
    const [paymentProcessed, setPaymentProcessed] = useState(false);

    useEffect(() => {
        const resetCountdown = () => {
            setCountdown(60);
        };
        
        // Start polling for payment status
        pollingIntervalRef.current = setInterval(async () => {
            // 如果支付已处理，停止轮询
            if (paymentProcessed) {
                if (pollingIntervalRef.current) {
                    clearInterval(pollingIntervalRef.current);
                    pollingIntervalRef.current = undefined;
                }
                return;
            }

            try {
                const response = await getPaymentStatus(orderId);
                if (response.status === 'PAID') {
                    setPaymentProcessed(true);
                    // 清除所有定时器
                    if (pollingIntervalRef.current) {
                        clearInterval(pollingIntervalRef.current);
                        pollingIntervalRef.current = undefined;
                    }
                    if (refreshIntervalRef.current) {
                        clearInterval(refreshIntervalRef.current);
                        refreshIntervalRef.current = undefined;
                    }
                    if (countdownIntervalRef.current) {
                        clearInterval(countdownIntervalRef.current);
                        countdownIntervalRef.current = undefined;
                    }
                    onPaymentSuccess();
                }
            } catch (error) {
                console.error("Error checking payment status:", error);
            }
        }, 3000);

        // Start QR code refresh timer
        refreshIntervalRef.current = setInterval(() => {
            onRefresh();
            resetCountdown();
        }, 60000);

        // Countdown timer
        countdownIntervalRef.current = setInterval(() => {
            setCountdown(prev => (prev > 0 ? prev - 1 : 0));
        }, 1000);

        // Cleanup on component unmount
        return () => {
            // 清理所有定时器
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = undefined;
            }
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
                refreshIntervalRef.current = undefined;
            }
            if (countdownIntervalRef.current) {
                clearInterval(countdownIntervalRef.current);
                countdownIntervalRef.current = undefined;
            }
        };
    }, [orderId, onPaymentSuccess, onRefresh, paymentProcessed]);

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-lg flex justify-center items-center z-[60] font-sans"
            onClick={onClose}
        >
            <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-gray-900 p-8 rounded-xl shadow-lg border border-gray-700 flex flex-col items-center gap-4"
                onClick={(e) => e.stopPropagation()}
            >
                <h3 className="text-xl font-bold text-gray-100">请使用微信扫码支付</h3>
                <div className="p-2 bg-white rounded-md">
                    <QRCodeCanvas value={codeUrl} size={256} />
                </div>
                <div className='text-center'>
                    <p className="text-gray-400 text-sm">支付成功后页面将自动跳转</p>
                    <p className="text-gray-400 text-xs mt-1">
                        二维码将在 <span className='font-bold'>{countdown}</span> 秒后刷新
                    </p>
                </div>
                <button
                    onClick={onClose}
                    className="mt-2 px-6 py-2 bg-gray-700 text-gray-200 font-bold rounded-lg hover:bg-gray-600 transition-colors"
                >
                    关闭
                </button>
            </motion.div>
        </motion.div>
    );
};

type ProductDetailModalProps = {
    isOpen: boolean;
    onClose: () => void;
    product: Product | null;
    onPurchaseSuccess?: () => void;
};

const ProductDetailModal = ({ isOpen, onClose, product, onPurchaseSuccess }: ProductDetailModalProps) => {
    const { isAuthenticated } = useAuth();
    const [imageError, setImageError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isQrModalOpen, setQrModalOpen] = useState(false);
    const [codeUrl, setCodeUrl] = useState('');
    const [currentOrderId, setCurrentOrderId] = useState('');
    const [purchasedProductIds, setPurchasedProductIds] = useState<Set<number>>(new Set());
    const [isCheckingOwnership, setIsCheckingOwnership] = useState(true);
    const [showConfetti, setShowConfetti] = useState(false);
    const [confettiComplete, setConfettiComplete] = useState(false);
    const [animationCountdown, setAnimationCountdown] = useState(5);
    const { width, height } = useWindowSize();
    const confettiTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        if (product) {
            setImageError(false);
        }
    }, [product]);

    // 清理定时器
    useEffect(() => {
        return () => {
            if (confettiTimeoutRef.current) {
                clearTimeout(confettiTimeoutRef.current);
            }
            if (countdownIntervalRef.current) {
                clearInterval(countdownIntervalRef.current);
            }
        };
    }, []);

    useEffect(() => {
        const fetchPurchased = async () => {
            if (isAuthenticated) {
                setIsCheckingOwnership(true);
                try {
                    const products = await getPurchasedProducts();
                    setPurchasedProductIds(new Set(products.map(p => p.id)));
                } catch (error) {
                    console.error("Failed to fetch purchased products:", error);
                    toast.error("无法获取您的购买记录，请稍后重试。");
                } finally {
                    setIsCheckingOwnership(false);
                }
            } else {
                setPurchasedProductIds(new Set());
                setIsCheckingOwnership(false);
            }
        };

        if (isOpen) {
            fetchPurchased();
        }
    }, [isOpen, isAuthenticated]);

    if (!product) return null;

    const isOwned = purchasedProductIds.has(product.id);

    const handlePurchaseClick = async () => {
        if (!product) return;
        setIsLoading(true);
        try {
            const response = await createPaymentOrder(product.id);
            if (response && response.codeUrl && response.orderId) {
                setCodeUrl(response.codeUrl);
                setCurrentOrderId(response.orderId);
                setQrModalOpen(true);
            } else {
                toast.error('无法创建支付二维码，请稍后重试。');
            }
        } catch (error: any) {
            console.error("Payment initiation failed:", error);
            const errorMessage = error.response?.data?.message || '支付请求失败，请检查网络或联系管理员。';
            toast.error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefreshQrCode = async () => {
        if (!product) return;
        toast.info("二维码已过期，正在为您生成新的二维码...");
        try {
            const response = await createPaymentOrder(product.id);
            if (response && response.codeUrl && response.orderId) {
                setCodeUrl(response.codeUrl);
                setCurrentOrderId(response.orderId);
                toast.success("新的二维码已生成！");
            } else {
                toast.error('无法刷新支付二维码，请关闭后重试。');
            }
        } catch (error) {
            toast.error('刷新二维码失败，请检查网络或联系管理员。');
        }
    };

    const executeDownloadAndClose = () => {
        if (product?.downloadUrl && product.downloadUrl.trim() !== '') {
            // 尝试打开下载链接
            const downloadWindow = window.open(product.downloadUrl, '_blank');

            // 检查是否成功打开了新窗口
            if (downloadWindow) {
                toast.success('下载已开始，感谢您的购买！');
            } else {
                // 如果被浏览器阻止了弹窗，提供手动下载选项
                toast.error('浏览器阻止了自动下载，请手动点击下载按钮');
            }
        } else {
            toast.error('下载链接不可用，请联系客服获取下载链接。');
        }

        // 延迟关闭模态框，让用户看到最终的提示信息
        setTimeout(() => {
            onClose();
        }, 2000);
    };

    const handlePaymentSuccess = () => {
        setQrModalOpen(false);
        setShowConfetti(true);
        setConfettiComplete(false);
        setAnimationCountdown(5);
        toast.success('支付成功！正在播放庆祝动画...');
        onPurchaseSuccess?.();

        // 启动倒计时
        countdownIntervalRef.current = setInterval(() => {
            setAnimationCountdown(prev => {
                if (prev <= 1) {
                    if (countdownIntervalRef.current) {
                        clearInterval(countdownIntervalRef.current);
                    }
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        // 设置Confetti动画完成的定时器 - 精确控制在5秒
        confettiTimeoutRef.current = setTimeout(() => {
            setConfettiComplete(true);
            setShowConfetti(false);

            // 清理倒计时
            if (countdownIntervalRef.current) {
                clearInterval(countdownIntervalRef.current);
            }

            // 显示跳转提示
            toast.success('正在为您跳转到下载页面...');

            // 稍微延迟执行下载，让用户看到跳转提示
            setTimeout(() => {
                executeDownloadAndClose();
            }, 800);
        }, 5000); // 精确5秒等待Confetti动画完成
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/70 backdrop-blur-lg flex justify-center items-center z-50 p-4 font-sans"
                    onClick={onClose}
                >
                    {showConfetti && (
                        <>
                            <Confetti
                                width={width}
                                height={height}
                                numberOfPieces={150}
                                gravity={0.12}
                                recycle={false}
                                initialVelocityY={3}
                                initialVelocityX={0}
                                colors={[
                                    '#000000', // 黑色
                                    '#FFFFFF', // 白色
                                    '#333333', // 深灰
                                    '#666666', // 中灰
                                    '#999999', // 浅灰
                                    '#CCCCCC'  // 淡灰
                                ]}
                                confettiSource={{
                                    x: 0,
                                    y: -50,
                                    w: width,
                                    h: 20
                                }}
                                wind={0.015}
                                friction={0.96}
                                run={showConfetti}
                                tweenDuration={5000}
                            />
                            {/* 庆祝成功提示 */}
                            <div className="fixed inset-0 flex items-center justify-center z-[65] pointer-events-none">
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.5, y: 50 }}
                                    animate={{ opacity: 1, scale: 1, y: 0 }}
                                    transition={{
                                        type: "spring",
                                        stiffness: 200,
                                        damping: 20,
                                        delay: 0.3
                                    }}
                                    className="text-center"
                                >
                                    <div className="bg-black/80 backdrop-blur-md rounded-2xl px-8 py-6">
                                        <motion.div
                                            initial={{ scale: 0 }}
                                            animate={{ scale: 1 }}
                                            transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                                            className="text-6xl mb-4"
                                        >
                                            ✓
                                        </motion.div>
                                        <h2 className="text-2xl font-bold text-white mb-2">购买成功！</h2>
                                        <p className="text-gray-300 text-sm">感谢您的购买，即将为您跳转到下载页面</p>
                                        <div className="mt-3 text-xs text-gray-400">
                                            {animationCountdown > 0 ? (
                                                <>动画将在 <span className="text-white font-mono">{animationCountdown}</span> 秒后自动结束</>
                                            ) : (
                                                '正在跳转...'
                                            )}
                                        </div>
                                    </div>
                                </motion.div>
                            </div>

                            {/* 跳过动画按钮 */}
                            <div className="fixed top-4 right-4 z-[70]">
                                <motion.button
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 2.5 }}
                                    onClick={() => {
                                        // 清理所有定时器
                                        if (confettiTimeoutRef.current) {
                                            clearTimeout(confettiTimeoutRef.current);
                                        }
                                        if (countdownIntervalRef.current) {
                                            clearInterval(countdownIntervalRef.current);
                                        }

                                        setShowConfetti(false);
                                        setConfettiComplete(true);
                                        setAnimationCountdown(0);
                                        toast.success('正在为您跳转到下载页面...');
                                        setTimeout(() => {
                                            executeDownloadAndClose();
                                        }, 500);
                                    }}
                                    className="bg-black/70 hover:bg-black/90 text-white px-4 py-2 rounded-lg text-sm font-medium backdrop-blur-sm transition-all duration-200 hover:scale-105 shadow-lg"
                                >
                                    跳过动画 →
                                </motion.button>
                            </div>
                        </>
                    )}
                    <motion.div
                        initial={{ scale: 0.9, opacity: 0, y: -50 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.9, opacity: 0, y: -50 }}
                        transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                        className="relative rounded-2xl w-full max-w-5xl h-[600px] text-gray-300 overflow-hidden flex"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Full Background Image */}
                        <div className="absolute inset-0">
                            {imageError || !product.imageUrl ? (
                                <div className="w-full h-full flex items-center justify-center bg-gray-900">
                                    <ImageOff className="w-16 h-16 text-gray-600" />
                                </div>
                            ) : (
                                <Image
                                    src={product.imageUrl}
                                    alt={product.name}
                                    fill
                                    className="object-cover"
                                    onError={() => setImageError(true)}
                                />
                            )}
                        </div>

                        {/* Enhanced Progressive Blur Overlay - Modern Design */}
                        <div className="absolute inset-0 pointer-events-none">
                            {/* Enhanced base gradient overlay with stronger contrast */}
                            <div
                                className="absolute inset-0 transition-all duration-700"
                                style={{
                                    background: 'linear-gradient(to right, transparent 0%, transparent 30%, rgba(0,0,0,0.1) 45%, rgba(0,0,0,0.25) 60%, rgba(0,0,0,0.45) 75%, rgba(0,0,0,0.65) 90%, rgba(0,0,0,0.8) 100%)'
                                }}
                            />

                            {/* Light blur layer - creates subtle depth */}
                            <div
                                className="absolute inset-0 transition-all duration-500"
                                style={{
                                    backdropFilter: 'blur(2px) saturate(1.2)',
                                    WebkitBackdropFilter: 'blur(2px) saturate(1.2)',
                                    mask: 'linear-gradient(to right, transparent 0%, transparent 35%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.7) 65%, rgba(0,0,0,0.9) 80%, rgba(0,0,0,1) 100%)',
                                    WebkitMask: 'linear-gradient(to right, transparent 0%, transparent 35%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.7) 65%, rgba(0,0,0,0.9) 80%, rgba(0,0,0,1) 100%)'
                                }}
                            />

                            {/* Medium blur layer - main blur effect */}
                            <div
                                className="absolute inset-0 transition-all duration-500"
                                style={{
                                    backdropFilter: 'blur(6px) saturate(1.3) brightness(0.95)',
                                    WebkitBackdropFilter: 'blur(6px) saturate(1.3) brightness(0.95)',
                                    mask: 'linear-gradient(to right, transparent 0%, transparent 45%, rgba(0,0,0,0.3) 60%, rgba(0,0,0,0.6) 75%, rgba(0,0,0,0.85) 90%, rgba(0,0,0,1) 100%)',
                                    WebkitMask: 'linear-gradient(to right, transparent 0%, transparent 45%, rgba(0,0,0,0.3) 60%, rgba(0,0,0,0.6) 75%, rgba(0,0,0,0.85) 90%, rgba(0,0,0,1) 100%)'
                                }}
                            />

                            {/* Strong blur layer - creates dramatic effect */}
                            <div
                                className="absolute inset-0 transition-all duration-500"
                                style={{
                                    backdropFilter: 'blur(12px) saturate(1.4) brightness(0.9)',
                                    WebkitBackdropFilter: 'blur(12px) saturate(1.4) brightness(0.9)',
                                    mask: 'linear-gradient(to right, transparent 0%, transparent 55%, rgba(0,0,0,0.2) 70%, rgba(0,0,0,0.5) 85%, rgba(0,0,0,0.8) 95%, rgba(0,0,0,1) 100%)',
                                    WebkitMask: 'linear-gradient(to right, transparent 0%, transparent 55%, rgba(0,0,0,0.2) 70%, rgba(0,0,0,0.5) 85%, rgba(0,0,0,0.8) 95%, rgba(0,0,0,1) 100%)'
                                }}
                            />

                            {/* Ultra blur layer - maximum depth effect */}
                            <div
                                className="absolute inset-0 transition-all duration-500"
                                style={{
                                    backdropFilter: 'blur(20px) saturate(1.5) brightness(0.85)',
                                    WebkitBackdropFilter: 'blur(20px) saturate(1.5) brightness(0.85)',
                                    mask: 'linear-gradient(to right, transparent 0%, transparent 65%, rgba(0,0,0,0.1) 80%, rgba(0,0,0,0.3) 90%, rgba(0,0,0,0.6) 97%, rgba(0,0,0,1) 100%)',
                                    WebkitMask: 'linear-gradient(to right, transparent 0%, transparent 65%, rgba(0,0,0,0.1) 80%, rgba(0,0,0,0.3) 90%, rgba(0,0,0,0.6) 97%, rgba(0,0,0,1) 100%)'
                                }}
                            />


                        </div>

                        {/* Left Side - Pure Information Layout */}
                        <div className="absolute inset-0 left-0 w-3/5 p-8 flex flex-col justify-between">
                            {/* Top - Pure Product Name */}
                            <div className="pt-6 animate-fade-in">
                                <h2 className="text-4xl font-bold text-white leading-tight tracking-wide drop-shadow-2xl">
                                    {product.name}
                                </h2>

                                {/* Simplified accent line */}
                                <div className="mt-3 w-12 h-0.5 bg-gray-400 opacity-60"></div>
                            </div>

                            {/* Bottom - Pure Author Info */}
                            <div className="pb-6 animate-fade-in-up">
                                <div className="flex items-center gap-3">
                                    {/* Pure Avatar */}
                                    {product.authorAvatarUrl ? (
                                        <Image
                                            src={product.authorAvatarUrl}
                                            alt={product.authorName}
                                            width={36}
                                            height={36}
                                            className="rounded-full drop-shadow-lg"
                                        />
                                    ) : (
                                        <div className="w-9 h-9 rounded-full bg-gray-700 flex items-center justify-center drop-shadow-lg">
                                            <User className="w-4 h-4 text-gray-300" />
                                        </div>
                                    )}

                                    {/* Pure Author Name */}
                                    <div className="flex flex-col">
                                        <span className="text-xs text-gray-400 font-medium uppercase tracking-wide drop-shadow-lg">作者</span>
                                        <span className="text-lg text-gray-100 font-medium drop-shadow-xl">
                                            {product.authorName}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Side - Refined Content Area */}
                        <div className="absolute right-0 top-0 w-2/5 h-full flex flex-col justify-center p-8 z-10">
                            <div className="space-y-7 animate-fade-in-right">
                                {/* Refined Description */}
                                <div className="p-5 backdrop-blur-sm">
                                    <p className="text-gray-200 text-base leading-relaxed font-normal">
                                        {product.description}
                                    </p>
                                </div>

                                {/* Refined Price Display */}
                                <div className="p-5 backdrop-blur-sm">
                                    <div className="flex items-baseline gap-2 mb-2">
                                        <span className="text-xs text-gray-400 font-medium uppercase tracking-wide">价格</span>
                                    </div>
                                    <div className="text-4xl font-semibold text-white">
                                        ¥{product.price.toFixed(2)}
                                    </div>
                                </div>

                                {/* Refined Purchase Button */}
                                <motion.button
                                    whileHover={{
                                        scale: 1.01,
                                        boxShadow: "0 8px 25px rgba(0,0,0,0.4)"
                                    }}
                                    whileTap={{ scale: 0.99 }}
                                    onClick={isOwned ? () => {
                                        if (product.downloadUrl && product.downloadUrl.trim() !== '') {
                                            // 显示下载开始提示
                                            toast.success('正在为您打开下载链接...');

                                            // 稍微延迟一下，让用户看到提示
                                            setTimeout(() => {
                                                const downloadWindow = window.open(product.downloadUrl, '_blank');

                                                if (downloadWindow) {
                                                    toast.success('下载已开始，感谢您的使用！');
                                                } else {
                                                    toast.error('浏览器阻止了下载窗口，请检查弹窗设置或手动复制链接');
                                                }
                                            }, 300);
                                        } else {
                                            toast.error('下载链接不可用，请联系客服获取下载链接。');
                                        }
                                    } : handlePurchaseClick}
                                    disabled={isLoading || isCheckingOwnership}
                                    className={`relative w-full font-medium py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg backdrop-blur-sm overflow-hidden group ${
                                        isOwned
                                            ? 'bg-gray-700/80 hover:bg-gray-600/80 text-gray-100'
                                            : 'bg-gray-800/80 hover:bg-gray-700/80 text-white'
                                    }`}
                                >
                                    {/* Subtle hover effect */}
                                    <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    {/* Button content */}
                                    <div className="relative flex items-center gap-3 text-base">
                                        {isLoading || isCheckingOwnership ? (
                                            <>
                                                <Loader2 className="animate-spin" size={20} />
                                                <span>处理中...</span>
                                            </>
                                        ) : isOwned ? (
                                            <>
                                                <CheckCircle size={20} />
                                                <span>已拥有，立即下载</span>
                                            </>
                                        ) : (
                                            <>
                                                <ShoppingCart size={20} />
                                                <span>立即购买</span>
                                            </>
                                        )}
                                    </div>
                                </motion.button>
                            </div>
                        </div>

                        {/* Refined Close Button */}
                        <motion.button
                            type="button"
                            aria-label="关闭弹窗"
                            whileHover={{
                                scale: 1.05,
                                boxShadow: "0 4px 12px rgba(0,0,0,0.3)"
                            }}
                            whileTap={{ scale: 0.95 }}
                            className="absolute top-6 right-6 z-20 bg-black/60 backdrop-blur-sm p-2.5 rounded-full text-gray-400 hover:text-white hover:bg-black/80 shadow-lg transition-all duration-200"
                            onClick={onClose}
                        >
                            <X size={18} />
                        </motion.button>
                    </motion.div>

                    {isQrModalOpen && (
                        <QRCodeModal 
                            codeUrl={codeUrl} 
                            orderId={currentOrderId}
                            onClose={() => setQrModalOpen(false)}
                            onPaymentSuccess={handlePaymentSuccess}
                            onRefresh={handleRefreshQrCode}
                        />
                    )}
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default ProductDetailModal; 