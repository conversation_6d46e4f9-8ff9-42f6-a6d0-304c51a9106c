'use client';

import { Product } from "@/types/Product";
import { motion } from "framer-motion";
import { Button } from "./ui/button";
import { Eye, ImageOff, CheckCircle } from 'lucide-react';
import Image from "next/image";

interface ProductCardProps {
    product: Product;
    onClick: () => void;
    isOwned: boolean;
}

const ProductCard = ({ product, onClick, isOwned }: ProductCardProps) => {
    return (
        <motion.div
            onClick={onClick}
            className="w-full h-96 flex flex-col cursor-pointer group rounded-2xl overflow-hidden 
                       border border-amber-800/50 hover:border-amber-400
                       bg-stone-900/50 backdrop-blur-md
                       shadow-lg hover:shadow-2xl hover:shadow-black/50
                       transition-all duration-300 relative"
            whileHover={{ y: -8, scale: 1.02 }}
            data-owned={isOwned}
        >
            {isOwned && (
                <div className="absolute top-3 right-3 z-20 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center gap-1">
                    <CheckCircle size={14} />
                    <span>已拥有</span>
                </div>
            )}

            <div className={`absolute inset-0 bg-gradient-to-br from-amber-500/10 via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${isOwned ? '!opacity-20' : ''}`}></div>

            <div className="relative w-full h-48 overflow-hidden">
                {product.imageUrl ? (
                    <Image 
                        src={product.imageUrl} 
                        alt={product.name} 
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                ) : (
                    <div className="w-full h-full bg-stone-800/70 flex items-center justify-center">
                        <ImageOff className="h-12 w-12 text-stone-500" />
                    </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-stone-900/80 via-stone-900/30 to-transparent"></div>
            </div>
            <div className="p-5 flex flex-col flex-grow z-10">
                <h3 className="text-xl font-bold text-amber-100 truncate transition-colors duration-300">{product.name}</h3>
                <p className="text-sm mt-2 line-clamp-3 flex-grow text-stone-300/80 group-hover:text-stone-200 transition-colors">
                    {product.description}
                </p>
                <div className="mt-4 flex justify-between items-center">
                    <p className={`text-2xl font-bold font-sans ${isOwned ? 'text-green-400' : 'text-amber-400'}`}>
                        {isOwned ? '已拥有' : `¥${product.price.toFixed(2)}`}
                    </p>
                    <div className="flex items-center gap-2 text-amber-400/80 group-hover:text-amber-300 group-hover:font-semibold transition-all">
                        <Eye className="h-5 w-5"/>
                        <span>查看详情</span>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default ProductCard;
