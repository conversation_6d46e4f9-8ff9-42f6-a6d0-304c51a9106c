'use client';

import { useState, useEffect, useCallback } from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from '@/components/ui/data-table';
import { getAllProductsForAdmin, delistProduct } from '@/services/api';
import { Product, ProductStatus } from '@/types/Product';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import { Eye, PackageX } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import ProductStatusHistoryComponent from '@/components/ProductStatusHistory';

const ProductManagementDashboard = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [isDelistModalOpen, setIsDelistModalOpen] = useState(false);
    const [delistReason, setDelistReason] = useState('');
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

    const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

    const fetchAllProducts = useCallback(async () => {
        setIsLoading(true);
        try {
            const data = await getAllProductsForAdmin();
            const formattedProducts = data.map((p: Product) => ({
                ...p,
                imageUrl: p.imageUrl && !p.imageUrl.startsWith('http')
                    ? `${apiBaseUrl}${p.imageUrl}`
                    : p.imageUrl,
                downloadUrl: p.downloadUrl && !p.downloadUrl.startsWith('http')
                    ? `${apiBaseUrl}${p.downloadUrl}`
                    : p.downloadUrl,
            }));
            setProducts(formattedProducts);
        } catch (error) {
            toast.error('获取所有产品列表失败');
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    }, [apiBaseUrl]);

    useEffect(() => {
        fetchAllProducts();
    }, [fetchAllProducts]);

    const handleViewDetails = async (product: Product) => {
        setSelectedProduct(product);
        setIsDetailModalOpen(true);
    };

    const handleDelistClick = (product: Product) => {
        setSelectedProduct(product);
        setDelistReason('');
        setIsDelistModalOpen(true);
    };

    const handleConfirmDelist = async () => {
        if (!selectedProduct) return;

        if (!delistReason.trim()) {
            toast.error('请输入下架理由。');
            return;
        }

        try {
            await delistProduct(selectedProduct.id, delistReason);
            toast.success(`产品 "${selectedProduct.name}" 已成功下架。`);
            setIsDelistModalOpen(false);
            fetchAllProducts();
        } catch (error) {
            toast.error('下架产品失败。');
        }
    };

    const statusBadgeMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: 'bg-yellow-500 hover:bg-yellow-600',
        [ProductStatus.APPROVED]: 'bg-green-500 hover:bg-green-600',
        [ProductStatus.REJECTED]: 'bg-red-500 hover:bg-red-600',
        [ProductStatus.DELISTED]: 'bg-gray-500 hover:bg-gray-600',
    };

    const statusTextMap: { [key in ProductStatus]: string } = {
        [ProductStatus.PENDING_APPROVAL]: '待审核',
        [ProductStatus.APPROVED]: '已上架',
        [ProductStatus.REJECTED]: '已驳回',
        [ProductStatus.DELISTED]: '已下架',
    };

    const columns: ColumnDef<Product>[] = [
        {
            accessorKey: "productInfo",
            header: "商品信息",
            cell: ({ row }) => {
                const product = row.original;
                return (
                    <div className="space-y-1">
                        <div className="font-medium text-sm">{product.name}</div>
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                            <span>作者: {product.authorName}</span>
                            <span>•</span>
                            <span className="font-semibold text-primary">¥{product.price.toFixed(2)}</span>
                        </div>
                    </div>
                );
            },
        },
        {
            accessorKey: "status",
            header: "状态",
            cell: ({ row }) => {
                const status = row.getValue("status") as ProductStatus;
                const badgeClass = statusBadgeMap[status] || 'bg-gray-400';
                const statusText = statusTextMap[status] || '未知状态';
                return <Badge className={`${badgeClass} text-white`}>{statusText}</Badge>;
            },
        },
        {
            accessorKey: "timeInfo",
            header: "时间信息",
            cell: ({ row }) => {
                const product = row.original;
                return (
                    <div className="text-xs space-y-1">
                        <div className="flex items-center gap-2">
                            <span className="text-muted-foreground">创建:</span>
                            <span>{new Date(product.createdAt).toLocaleDateString()}</span>
                        </div>
                        {product.updatedAt && (
                            <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">更新:</span>
                                <span>{new Date(product.updatedAt).toLocaleDateString()}</span>
                            </div>
                        )}
                        {product.reviewedAt && (
                            <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">审核:</span>
                                <span>{new Date(product.reviewedAt).toLocaleDateString()}</span>
                            </div>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "reviewInfo",
            header: "审核信息",
            cell: ({ row }) => {
                const product = row.original;
                if (product.status === ProductStatus.REJECTED) {
                    return (
                        <div className="text-xs max-w-sm space-y-1">
                            <div className="flex items-center gap-2">
                                <Badge variant="destructive" className="text-xs">驳回</Badge>
                                {product.reviewedBy && (
                                    <span className="text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                            </div>
                            {product.rejectionReason && (
                                <div className="text-muted-foreground line-clamp-2" title={product.rejectionReason}>
                                    {product.rejectionReason}
                                </div>
                            )}
                        </div>
                    );
                }
                if (product.status === ProductStatus.APPROVED) {
                    return (
                        <div className="text-xs max-w-sm space-y-1">
                            <div className="flex items-center gap-2">
                                <Badge variant="default" className="text-xs bg-green-500">通过</Badge>
                                {product.reviewedBy && (
                                    <span className="text-muted-foreground">by {product.reviewedBy}</span>
                                )}
                            </div>
                            {product.approvalNotes && (
                                <div className="text-muted-foreground line-clamp-2" title={product.approvalNotes}>
                                    {product.approvalNotes}
                                </div>
                            )}
                        </div>
                    );
                }
                if (product.status === ProductStatus.PENDING_APPROVAL) {
                    return (
                        <div className="flex items-center gap-2 text-xs">
                            <Badge variant="secondary" className="text-xs">等待审核</Badge>
                            <span className="text-muted-foreground">待处理</span>
                        </div>
                    );
                }
                if (product.status === ProductStatus.DELISTED) {
                    return (
                        <div className="flex items-center gap-2 text-xs">
                            <Badge variant="outline" className="text-xs">已下架</Badge>
                            <span className="text-muted-foreground">已移除</span>
                        </div>
                    );
                }
                return <span className="text-xs text-muted-foreground">-</span>;
            },
        },
        {
            id: 'actions',
            header: '操作',
            cell: ({ row }) => (
                <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleViewDetails(row.original)}>
                        <Eye className="h-4 w-4" />
                    </Button>
                    {row.original.status === ProductStatus.APPROVED && (
                         <Button variant="destructive" size="sm" onClick={() => handleDelistClick(row.original)}>
                            <PackageX className="h-4 w-4" />
                        </Button>
                    )}
                </div>
            ),
        },
    ];

    return (
        <>
            <div className="bg-card/50 backdrop-blur-sm border border-border/30 rounded-lg p-6 shadow-lg">
                <h2 className="text-2xl font-bold mb-4">商品综合管理</h2>
                <p className="text-muted-foreground mb-6">在这里可以查看平台上的所有商品及其状态。</p>
                {isLoading ? (
                    <div className="space-y-2">
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                    </div>
                ) : (
                    <DataTable columns={columns} data={products} />
                )}
            </div>

            <Dialog open={isDelistModalOpen} onOpenChange={setIsDelistModalOpen}>
                <DialogContent className="bg-card/60 backdrop-blur-lg border border-border/20 shadow-xl rounded-lg sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-bold">下架商品: {selectedProduct?.name}</DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            请输入下架该商品的原因，该原因将会通知给开发者。
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="delist-reason" className="font-semibold">
                                下架理由
                            </Label>
                            <Textarea
                                id="delist-reason"
                                value={delistReason}
                                onChange={(e) => setDelistReason(e.target.value)}
                                className="min-h-[120px] bg-background/50 border-border/30 focus:ring-primary"
                                placeholder="例如：违反社区规定、内容涉及侵权等..."
                            />
                        </div>
                    </div>
                    <DialogFooter className="gap-2 sm:justify-end">
                        <Button variant="ghost" onClick={() => setIsDelistModalOpen(false)}>取消</Button>
                        <Button variant="destructive" onClick={handleConfirmDelist}>确认下架</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
                <DialogContent className="sm:max-w-[600px] bg-card/80 backdrop-blur-xl">
                    <DialogHeader>
                        <DialogTitle>{selectedProduct?.name}</DialogTitle>
                        <DialogDescription>
                            由 {selectedProduct?.authorName} 提交
                        </DialogDescription>
                    </DialogHeader>
                    {selectedProduct && (
                        <div className="mt-4 space-y-4 max-h-[70vh] overflow-y-auto pr-4 custom-scrollbar">
                             <div className="relative w-full h-64 bg-black/20 rounded-md">
                                <Image
                                    src={selectedProduct.imageUrl}
                                    alt={selectedProduct.name}
                                    fill
                                    className="object-contain"
                                />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">价格</h3>
                                    <p className="text-primary text-xl font-bold">¥{selectedProduct.price.toFixed(2)}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">状态</h3>
                                    <Badge className={`${statusBadgeMap[selectedProduct.status as ProductStatus]} text-white`}>
                                        {statusTextMap[selectedProduct.status as ProductStatus]}
                                    </Badge>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/20 rounded-lg">
                                <div>
                                    <h4 className="font-medium text-sm text-muted-foreground mb-1">提交时间</h4>
                                    <p className="text-sm">{new Date(selectedProduct.createdAt).toLocaleString()}</p>
                                </div>
                                {selectedProduct.updatedAt && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-1">最后更新</h4>
                                        <p className="text-sm">{new Date(selectedProduct.updatedAt).toLocaleString()}</p>
                                    </div>
                                )}
                                {selectedProduct.reviewedAt && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-1">审核时间</h4>
                                        <p className="text-sm">{new Date(selectedProduct.reviewedAt).toLocaleString()}</p>
                                        {selectedProduct.reviewedBy && (
                                            <p className="text-xs text-muted-foreground mt-1">审核人: {selectedProduct.reviewedBy}</p>
                                        )}
                                    </div>
                                )}
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">下载链接</h3>
                                <a href={selectedProduct.downloadUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline break-all">
                                    {selectedProduct.downloadUrl}
                                </a>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">产品描述</h3>
                                <div className="prose prose-sm prose-invert max-w-none bg-black/10 p-4 rounded-md">
                                    <ReactMarkdown>{selectedProduct.description}</ReactMarkdown>
                                </div>
                            </div>
                             {selectedProduct.rejectionReason && (
                                <div>
                                    <h3 className="font-semibold text-lg mb-2 text-destructive">驳回理由</h3>
                                    <p className="text-sm text-destructive/80 italic p-4 bg-destructive/10 border-l-4 border-destructive rounded-md">{selectedProduct.rejectionReason}</p>
                                </div>
                            )}
                             {selectedProduct.approvalNotes && (
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">批准备注</h3>
                                    <p className="text-sm text-muted-foreground italic p-4 bg-black/10 rounded-md">{selectedProduct.approvalNotes}</p>
                                </div>
                            )}

                            {/* 产品状态变更历史 */}
                            <div className="border-t pt-6">
                                <ProductStatusHistoryComponent productId={selectedProduct.id} />
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </>
    );
};

export default ProductManagementDashboard; 