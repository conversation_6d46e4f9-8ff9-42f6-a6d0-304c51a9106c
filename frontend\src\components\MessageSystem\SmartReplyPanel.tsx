'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  ThumbsUp, 
  MessageCircle, 
  Heart, 
  Smile,
  X,
  Send
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface SmartReplyOption {
  text: string;
  confidence: number;
  tone: 'formal' | 'casual' | 'friendly' | 'professional';
  category: 'question' | 'agreement' | 'thanks' | 'greeting' | 'custom';
}

interface SmartReplyPanelProps {
  replies: SmartReplyOption[];
  onSelect: (reply: string) => void;
  onClose: () => void;
  className?: string;
}

export const SmartReplyPanel: React.FC<SmartReplyPanelProps> = ({
  replies,
  onSelect,
  onClose,
  className
}) => {
  // 获取类别图标
  const getCategoryIcon = (category: SmartReplyOption['category']) => {
    switch (category) {
      case 'question':
        return <MessageCircle className="w-3 h-3" />;
      case 'agreement':
        return <ThumbsUp className="w-3 h-3" />;
      case 'thanks':
        return <Heart className="w-3 h-3" />;
      case 'greeting':
        return <Smile className="w-3 h-3" />;
      default:
        return <Send className="w-3 h-3" />;
    }
  };

  // 获取语调颜色
  const getToneColor = (tone: SmartReplyOption['tone']) => {
    switch (tone) {
      case 'formal':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'casual':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'friendly':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'professional':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.6) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 20, scale: 0.95 }}
        transition={{ duration: 0.2, ease: 'easeOut' }}
        className={cn(
          "fixed bottom-20 left-4 right-4 z-50 max-w-md mx-auto",
          "bg-background/95 backdrop-blur-lg border border-border rounded-xl shadow-2xl",
          className
        )}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-primary" />
            <h3 className="font-semibold text-sm">快速回复</h3>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="w-6 h-6"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 回复选项 */}
        <div className="p-4 space-y-2 max-h-60 overflow-y-auto">
          {replies.map((reply, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onSelect(reply.text)}
              className={cn(
                "w-full text-left p-3 rounded-lg border transition-all duration-200",
                "hover:bg-muted/50 hover:border-primary/30 hover:shadow-md",
                "focus:outline-none focus:ring-2 focus:ring-primary/20",
                "group"
              )}
            >
              {/* 回复内容 */}
              <div className="flex items-start justify-between mb-2">
                <p className="text-sm text-foreground group-hover:text-primary transition-colors">
                  {reply.text}
                </p>
                <div className="flex items-center gap-1 ml-2">
                  {getCategoryIcon(reply.category)}
                </div>
              </div>

              {/* 元数据 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {/* 语调标签 */}
                  <Badge
                    variant="outline"
                    className={cn(
                      "text-xs px-2 py-0.5 border",
                      getToneColor(reply.tone)
                    )}
                  >
                    {reply.tone}
                  </Badge>

                  {/* 类别标签 */}
                  <Badge
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {reply.category}
                  </Badge>
                </div>

                {/* 置信度 */}
                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">置信度:</span>
                  <span className={cn(
                    "text-xs font-medium",
                    getConfidenceColor(reply.confidence)
                  )}>
                    {Math.round(reply.confidence * 100)}%
                  </span>
                </div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* 底部提示 */}
        <div className="p-3 border-t border-border bg-muted/30">
          <p className="text-xs text-muted-foreground text-center">
            点击选择回复，或按 ESC 关闭
          </p>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
