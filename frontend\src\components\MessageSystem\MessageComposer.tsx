'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Send, 
  Search, 
  User,
  AlertTriangle,
  Info,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAuth } from '@/contexts/AuthContext';
import { MessageType, MessagePriority, SendMessageRequest } from '@/types/Message';
import { sendMessage, sendPrivateMessage } from '@/services/messageApi';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface MessageComposerProps {
  isOpen: boolean;
  onClose: () => void;
  onMessageSent: () => void;
  recipientId?: number;
  recipientUsername?: string;
}

export const MessageComposer: React.FC<MessageComposerProps> = ({
  isOpen,
  onClose,
  onMessageSent,
  recipientId,
  recipientUsername
}) => {
  const { user } = useAuth();
  const [messageType, setMessageType] = useState<MessageType>(MessageType.PRIVATE);
  const [priority, setPriority] = useState<MessagePriority>(MessagePriority.NORMAL);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedRecipientId, setSelectedRecipientId] = useState<number | undefined>(recipientId);
  const [selectedRecipientUsername, setSelectedRecipientUsername] = useState<string | undefined>(recipientUsername);
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [sending, setSending] = useState(false);

  // 重置表单
  const resetForm = () => {
    setMessageType(MessageType.PRIVATE);
    setPriority(MessagePriority.NORMAL);
    setTitle('');
    setContent('');
    if (!recipientId) {
      setSelectedRecipientId(undefined);
      setSelectedRecipientUsername(undefined);
    }
    setUserSearchQuery('');
    setSearchResults([]);
  };

  // 当对话框关闭时重置表单
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // 设置初始收件人
  useEffect(() => {
    if (recipientId && recipientUsername) {
      setSelectedRecipientId(recipientId);
      setSelectedRecipientUsername(recipientUsername);
    }
  }, [recipientId, recipientUsername]);

  // 搜索用户（真实实现）
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // 使用真实的用户搜索API
      const response = await fetch(`/api/user/search?query=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const users = await response.json();
        // 转换为组件期望的格式
        setSearchResults(users.map((user: any) => ({
          id: parseInt(user.id), // 使用真实的用户ID
          username: user.username,
          fullAvatarUrl: user.avatarUrl ? `/api/user/avatar/${user.avatarUrl}` : null
        })));
      } else {
        console.error('User search failed:', response.statusText);
        setSearchResults([]);
      }
    } catch (error) {
      console.error('User search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理用户搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      searchUsers(userSearchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [userSearchQuery]);

  // 选择收件人
  const selectRecipient = (userId: number, username: string) => {
    setSelectedRecipientId(userId);
    setSelectedRecipientUsername(username);
    setUserSearchQuery('');
    setSearchResults([]);
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!content.trim()) {
      toast.error('请输入消息内容');
      return;
    }

    if (messageType === MessageType.PRIVATE && !selectedRecipientId) {
      toast.error('请选择收件人');
      return;
    }

    setSending(true);
    try {
      // 所有私信都使用专用的私信API
      if (messageType === MessageType.PRIVATE && selectedRecipientId) {
        await sendPrivateMessage(selectedRecipientId, content, title || '私信');
        toast.success('私信发送成功');
      } else {
        // 其他类型的消息暂时不支持发送
        toast.error('暂不支持发送此类型的消息');
        return;
      }

      onMessageSent();
      onClose();
    } catch (error) {
      toast.error('消息发送失败，请稍后重试');
    } finally {
      setSending(false);
    }
  };

  // 获取优先级图标
  const getPriorityIcon = (priority: MessagePriority) => {
    switch (priority) {
      case MessagePriority.URGENT:
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case MessagePriority.HIGH:
        return <Bell className="w-4 h-4 text-orange-500" />;
      case MessagePriority.NORMAL:
        return <Info className="w-4 h-4 text-blue-500" />;
      case MessagePriority.LOW:
        return <Info className="w-4 h-4 text-gray-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            发送消息
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 消息类型选择 */}
          <div className="space-y-2">
            <Label>消息类型</Label>
            <Select value={messageType} onValueChange={(value) => setMessageType(value as MessageType)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={MessageType.PRIVATE}>私信</SelectItem>
                <SelectItem value={MessageType.SYSTEM}>系统通知</SelectItem>
                <SelectItem value={MessageType.ANNOUNCEMENT}>公告</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 收件人选择（仅私信） */}
          {messageType === MessageType.PRIVATE && (
            <div className="space-y-2">
              <Label>收件人</Label>
              {selectedRecipientId ? (
                <div className="flex items-center gap-2 p-2 bg-muted rounded-lg">
                  <Avatar className="w-6 h-6">
                    <AvatarFallback className="text-xs">
                      {selectedRecipientUsername?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{selectedRecipientUsername}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-4 h-4 ml-auto"
                    onClick={() => {
                      setSelectedRecipientId(undefined);
                      setSelectedRecipientUsername(undefined);
                    }}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索用户..."
                      value={userSearchQuery}
                      onChange={(e) => setUserSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  {searchResults.length > 0 && (
                    <ScrollArea className="max-h-32 border rounded-lg">
                      <div className="p-1">
                        {searchResults.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-center gap-2 p-2 hover:bg-muted rounded cursor-pointer"
                            onClick={() => selectRecipient(user.id, user.username)}
                          >
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={user.fullAvatarUrl || undefined} />
                              <AvatarFallback className="text-xs">
                                {user.username?.charAt(0).toUpperCase() || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm">{user.username}</span>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 优先级选择 */}
          <div className="space-y-2">
            <Label>优先级</Label>
            <Select value={priority} onValueChange={(value) => setPriority(value as MessagePriority)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={MessagePriority.LOW}>
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(MessagePriority.LOW)}
                    低优先级
                  </div>
                </SelectItem>
                <SelectItem value={MessagePriority.NORMAL}>
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(MessagePriority.NORMAL)}
                    普通
                  </div>
                </SelectItem>
                <SelectItem value={MessagePriority.HIGH}>
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(MessagePriority.HIGH)}
                    重要
                  </div>
                </SelectItem>
                <SelectItem value={MessagePriority.URGENT}>
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(MessagePriority.URGENT)}
                    紧急
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 标题 */}
          <div className="space-y-2">
            <Label>标题</Label>
            <Input
              placeholder="请输入消息标题..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* 内容 */}
          <div className="space-y-2">
            <Label>内容</Label>
            <Textarea
              placeholder="请输入消息内容..."
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={6}
              className="resize-none"
            />
            <div className="text-xs text-muted-foreground text-right">
              {content.length}/1000
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button onClick={handleSendMessage} disabled={sending}>
              {sending ? '发送中...' : '发送'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
