'use client';

import { useState, useEffect } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '../../../contexts/AuthContext';
import { getAllUsers, toggleUserStatus, deleteUser, updateUserRole } from '@/services/api';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { format } from 'date-fns';

// Define the User type for the admin view
export interface UserAdminView {
    id: number;
    username: string;
    email: string;
    role: string;
    createdAt: string; // Keep as string for direct display
    enabled: boolean;
    lastLoginIp: string;
}

const roleMap: { [key: string]: string } = {
    'USER': '用户',
    'DEVELOPER': '开发者',
    'KitolusAdmin': '管理员'
};

const UserManagementDashboard = () => {
    const { token } = useAuth();
    const [users, setUsers] = useState<UserAdminView[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const data: UserAdminView[] = await getAllUsers();
                // A little hack to make sure old users have a valid date
                const processedData = data.map(user => ({
                    ...user,
                    createdAt: user.createdAt || new Date(0).toISOString(), // Fallback for null/undefined dates
                }));
                setUsers(processedData);
            } catch (err) {
                setError('获取用户列表失败。');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchUsers();
    }, [token]);

    const handleToggleUserStatus = async (userId: number) => {
        if (!token) return;
        try {
            await toggleUserStatus(userId);
            setUsers(users.map(u => u.id === userId ? { ...u, enabled: !u.enabled } : u));
        } catch (error) {
            console.error('Failed to toggle user status', error);
            setError('切换用户状态失败。');
        }
    };

    const handleDeleteUser = async (userId: number) => {
        if (!token) return;

        const user = users.find(u => u.id === userId);
        const confirmMessage = `您确定要完全删除用户 "${user?.username}" 吗？\n\n警告：\n• 这将永久删除用户及其所有相关数据\n• 包括：登录记录、订单、产品、帖子、评论、通知、消息等\n• 此操作无法撤销，数据将无法恢复\n• 请确保您真的需要完全删除此用户\n\n是否继续？`;

        if (window.confirm(confirmMessage)) {
            try {
                setError(''); // 清除之前的错误
                await deleteUser(userId);

                // 重新获取用户列表以反映最新状态
                const updatedUsers = await getAllUsers();
                setUsers(updatedUsers);

                // 显示成功消息
                const updatedUser = updatedUsers.find(u => u.id === userId);
                if (!updatedUser) {
                    setError(`用户 "${user?.username}" 及其所有相关数据已被完全删除`);
                } else {
                    setError(`用户 "${user?.username}" 删除操作已完成`);
                }

            } catch (error: any) {
                console.error('删除用户失败', error);
                let errorMessage = '删除用户失败，请稍后重试。';

                if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                } else if (error.response?.status === 500) {
                    errorMessage = '服务器内部错误，可能是数据库约束问题。请检查用户是否有关联数据。';
                } else if (error.response?.status === 403) {
                    errorMessage = '权限不足，无法删除用户。';
                } else if (error.message) {
                    errorMessage = error.message;
                }

                setError(errorMessage);
            }
        }
    };

    const handleRoleChange = async (userId: number, newRole: string) => {
        if (!token) return;
        try {
            await updateUserRole(userId, newRole);
            setUsers(users.map(u => u.id === userId ? { ...u, role: newRole } : u));
        } catch (error) {
            console.error('更新用户角色失败', error);
            setError('更新用户角色失败。');
        }
    };

    const filteredUsers = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (loading) return <div>加载中...</div>;
    if (error) return <Alert variant="destructive">
        <AlertTitle>错误</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
    </Alert>;

    return (
        <div className="p-4 md:p-6 bg-card/60 backdrop-blur-sm border rounded-lg">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">用户管理</h2>
                <Input
                    type="text"
                    placeholder="按用户名搜索..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                />
            </div>
            <div className="overflow-auto border rounded-lg max-h-[70vh]">
                <Table>
                    <TableHeader className="sticky top-0 bg-background/80 z-10 backdrop-blur-sm">
                        <TableRow>
                            <TableHead className="px-4 py-4">ID</TableHead>
                            <TableHead className="px-4 py-4" style={{ minWidth: '120px' }}>用户名</TableHead>
                            <TableHead className="px-4 py-4" style={{ minWidth: '180px' }}>邮箱</TableHead>
                            <TableHead className="px-4 py-4" style={{ minWidth: '100px' }}>角色</TableHead>
                            <TableHead className="px-4 py-4" style={{ minWidth: '150px' }}>注册时间</TableHead>
                            <TableHead className="px-4 py-4" style={{ minWidth: '120px' }}>上次登录IP</TableHead>
                            <TableHead className="px-4 py-4">状态</TableHead>
                            <TableHead className="px-4 py-4 text-center" style={{ minWidth: '230px' }}>操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredUsers.map((user) => (
                            <TableRow key={user.id} className="hover:bg-muted/50">
                                <TableCell className="px-4 py-4">{user.id}</TableCell>
                                <TableCell className="px-4 py-4 font-medium">{user.username}</TableCell>
                                <TableCell className="px-4 py-4">{user.email}</TableCell>
                                <TableCell className="px-4 py-4">{roleMap[user.role] || user.role}</TableCell>
                                <TableCell className="px-4 py-4">
                                    {format(new Date(user.createdAt), 'yyyy-MM-dd HH:mm:ss')}
                                </TableCell>
                                <TableCell className="px-4 py-4">{user.lastLoginIp || 'N/A'}</TableCell>
                                <TableCell className="px-4 py-4">
                                    <span className={`px-2 py-1 rounded-full text-xs font-semibold whitespace-nowrap ${user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {user.enabled ? '正常' : '已封禁'}
                                    </span>
                                </TableCell>
                                <TableCell className="px-4 py-4 flex items-center justify-center gap-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" size="sm">更改角色</Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                            <DropdownMenuItem onSelect={() => handleRoleChange(user.id, 'USER')}>
                                                用户
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onSelect={() => handleRoleChange(user.id, 'DEVELOPER')}>
                                                开发者
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onSelect={() => handleRoleChange(user.id, 'KitolusAdmin')}>
                                                管理员
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>

                                    <Button
                                        variant={user.enabled ? 'destructive' : 'secondary'}
                                        size="sm"
                                        onClick={() => handleToggleUserStatus(user.id)}
                                    >
                                        {user.enabled ? '封禁' : '解封'}
                                    </Button>
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleDeleteUser(user.id)}
                                        className="bg-red-700 hover:bg-red-800"
                                    >
                                        删除
                                    </Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};

export default UserManagementDashboard; 