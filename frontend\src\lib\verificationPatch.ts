import { Dispatch, SetStateAction } from 'react';

/**
 * 应用一个验证码倒计时补丁。
 * 这个函数会设置一个视觉上的60秒倒计时，但会给后端逻辑增加5秒的缓冲时间。
 * 
 * @param setCountdown - React状态更新函数，用于设置倒计时秒数。
 * @param setActualExpiresIn - 一个函数，用于将实际的过期时间（65秒）通知给后端。
 */
export function applyVerificationCountdownPatch(
  setCountdown: Dispatch<SetStateAction<number>>,
  setActualExpiresIn: (expiresIn: number) => void
) {
  // 1. 设置视觉上的倒计时为60秒
  setCountdown(60);

  // 2. 告诉调用者，我们期望的实际过期时间是65秒
  //    这样，可以在发送到后端的请求中包含这个时间。
  //    如果后端不接受这个参数，它将使用自己的默认值。
  setActualExpiresIn(65);

  console.log("验证码倒计时补丁已应用：显示60秒，逻辑过期65秒。");
}

/**
 * 这是一个更独立的版本，它不依赖外部设置过期时间，
 * 而是返回一个对象，包含视觉倒计时和逻辑过期时间。
 */
export function getPatchedCountdown() {
  return {
    displaySeconds: 60, // 用于UI显示的秒数
    logicalExpireSeconds: 65, // 用于逻辑判断的秒数
  };
} 