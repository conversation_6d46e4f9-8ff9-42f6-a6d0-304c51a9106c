'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
  ChevronDown,
  ChevronUp,
  Zap,
  Settings,
  FolderOpen,
  BookOpen,
  Trophy,
  Handshake,
  Pickaxe,
  Cpu,
  Atom,
  Crown,
  Bot,
  Cog,
  Sparkles,
  FileText,
  Download,
  Wrench,
  GraduationCap,
  Target,
  HelpCircle,
  Factory,
  Award,
  Palette,
  Package,
  Users,
  Server
} from 'lucide-react';
import { communitySections, CommunitySection, CommunityCategory } from '@/lib/community-sections-data';
import { cn } from '@/lib/utils';

// 图标映射
const iconMap = {
  Zap, Settings, FolderOpen, BookOpen, Trophy, Handshake,
  Pickaxe, Cpu, Atom, Crown, Bot, Cog, Sparkles, FileText,
  Download, Wrench, GraduationCap, Target, HelpCircle,
  Factory, Award, Palette, Package, Users, Server
};

interface CommunitySectionsProps {
  activeFilter: { type: 'section' | 'category' | 'subcategory' | 'era' | 'tier' | 'topic', value: string } | null;
  onFilterChange: (filter: { type: 'section' | 'category' | 'subcategory' | 'era' | 'tier' | 'topic', value: string } | null) => void;
}

export const CommunitySections: React.FC<CommunitySectionsProps> = ({
  activeFilter,
  onFilterChange
}) => {
  // 根据当前过滤器确定初始活动分区
  const getInitialActiveSection = () => {
    if (activeFilter?.type === 'era' || activeFilter?.type === 'tier' || activeFilter?.type === 'topic') {
      return 'progression';
    }
    if (activeFilter?.type === 'section') {
      return activeFilter.value;
    }
    return 'progression';
  };

  const [activeSection, setActiveSection] = useState<string>(getInitialActiveSection());
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleSectionChange = (sectionId: string) => {
    setActiveSection(sectionId);
    onFilterChange({ type: 'section', value: sectionId });
  };

  const handleCategoryClick = (categoryId: string) => {
    onFilterChange({ type: 'category', value: categoryId });
  };

  const handleSubcategoryClick = (subcategoryId: string) => {
    // 对于阶段分区的子类别，需要特殊处理以保持向后兼容
    if (activeSection === 'progression') {
      // 解析传统的阶段分区格式: "era|tier|topic"
      const parts = subcategoryId.split('|');
      if (parts.length === 3) {
        // 传递完整的分区信息用于精确匹配
        onFilterChange({ type: 'subcategory', value: subcategoryId });
        return;
      }
    }
    onFilterChange({ type: 'subcategory', value: subcategoryId });
  };

  const currentSection = communitySections.find(s => s.id === activeSection);

  return (
    <div className="bg-black/20 backdrop-blur-md border-zinc-800 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-zinc-800/50">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-zinc-200">社区分区</h3>
          <div className="flex items-center gap-2">
            {activeFilter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  onFilterChange(null);
                  setActiveSection('progression'); // 重置到默认分区
                }}
                className="text-zinc-400 hover:text-zinc-100 text-xs"
              >
                显示全部
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="text-zinc-400 hover:text-zinc-100 p-1"
            >
              {isCollapsed ? <ChevronDown size={16} /> : <ChevronUp size={16} />}
            </Button>
          </div>
        </div>
      </div>

      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {/* Section Tabs */}
            <div className="p-4 border-b border-zinc-700/30">
              <div className="grid grid-cols-2 gap-2">
                {communitySections.map((section) => {
                  const IconComponent = iconMap[section.icon as keyof typeof iconMap];
                  const isActive = activeSection === section.id;
                  
                  return (
                    <Button
                      key={section.id}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSectionChange(section.id)}
                      className={cn(
                        "flex items-center gap-2 p-3 rounded-lg transition-all duration-200 text-left justify-start",
                        isActive
                          ? "bg-zinc-700 text-zinc-100 shadow-lg"
                          : "text-zinc-400 hover:text-zinc-100 hover:bg-zinc-800/50"
                      )}
                    >
                      {IconComponent && <IconComponent size={16} />}
                      <span className="text-xs font-medium truncate">{section.name}</span>
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Current Section Content */}
            {currentSection && (
              <div className="p-4">
                <div className="mb-3">
                  <h4 className="text-sm font-medium text-zinc-200 mb-1">{currentSection.name}</h4>
                  <p className="text-xs text-zinc-400">{currentSection.description}</p>
                </div>

                <Accordion type="multiple" className="w-full space-y-2">
                  {currentSection.categories.map((category) => {
                    const CategoryIcon = iconMap[category.icon as keyof typeof iconMap];
                    const isActiveCategory = activeFilter?.type === 'category' && activeFilter.value === category.id;
                    
                    return (
                      <AccordionItem 
                        key={category.id} 
                        value={category.id} 
                        className="border-none"
                      >
                        <AccordionTrigger 
                          onClick={() => handleCategoryClick(category.id)}
                          className={cn(
                            "hover:no-underline p-3 rounded-lg transition-colors",
                            isActiveCategory
                              ? "bg-zinc-800/50 text-zinc-100"
                              : "hover:bg-zinc-800/40 text-zinc-300"
                          )}
                        >
                          <div className="flex items-center gap-2">
                            {CategoryIcon && <CategoryIcon size={16} />}
                            <span className="text-sm font-medium">{category.name}</span>
                            <Badge variant="secondary" className="ml-auto text-xs bg-zinc-800/60 text-zinc-300 border-zinc-700/40">
                              {category.subcategories.length}
                            </Badge>
                          </div>
                        </AccordionTrigger>
                        
                        <AccordionContent className="pt-2 pb-0">
                          <div className="space-y-1 ml-4">
                            {category.subcategories.map((subcategory) => {
                              // 检查是否为活动的子类别，支持传统的过滤类型
                              const isActiveSubcategory =
                                (activeFilter?.type === 'subcategory' && activeFilter.value === subcategory.id) ||
                                (activeFilter?.type === 'topic' && activeSection === 'progression' && subcategory.id.endsWith(`|${activeFilter.value}`));
                              
                              return (
                                <button
                                  key={subcategory.id}
                                  onClick={() => handleSubcategoryClick(subcategory.id)}
                                  className={cn(
                                    "w-full text-left p-2 rounded-md transition-colors text-sm",
                                    isActiveSubcategory
                                      ? "bg-zinc-700/60 text-zinc-100"
                                      : "text-zinc-400 hover:text-zinc-100 hover:bg-zinc-800/40"
                                  )}
                                >
                                  <div className="font-medium">{subcategory.name}</div>
                                  <div className="text-xs text-zinc-400 mt-1">{subcategory.description}</div>
                                  {subcategory.tags && subcategory.tags.length > 0 && (
                                    <div className="flex gap-1 mt-2">
                                      {subcategory.tags.map((tag) => (
                                        <Badge key={tag} variant="outline" className="text-xs border-zinc-700/40 text-zinc-300">
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                                </button>
                              );
                            })}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </Accordion>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
