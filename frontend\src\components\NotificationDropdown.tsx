'use client';

import { useState, useEffect, useMemo } from 'react';
import { getNotifications, markAllNotificationsAsRead, markNotificationAsRead, deleteNotification, deleteAllNotifications } from '@/services/api';
import { Notification, NotificationType } from '@/types/Notification';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Bell, CheckCheck, MessageSquare, ShieldCheck, Users, X, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { format, formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import AvatarDisplay from './AvatarDisplay';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface NotificationDropdownProps {
  isUnread: boolean;
  onStateChange: () => void;
}

/**
 * Renders a specific notification item based on its type.
 * This component is designed to be robust against missing data.
 */
const NotificationItem = ({ notification, onItemClick, onDeleteClick }: {
    notification: Notification,
    onItemClick: (id: number) => void,
    onDeleteClick: (id: number) => void
}) => {
    
    // Helper function to render the timestamp
    const renderTimestamp = () => (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <p className="text-xs text-zinc-500 mt-1 cursor-default">
            {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true, locale: zhCN })}
        </p>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{format(new Date(notification.createdAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );

    // Generic container for all notification items
    const NotificationContainer = ({ href, onClick, children }: { href?: string, onClick?: () => void, children: React.ReactNode }) => {
        const content = (
            <div className="flex items-start gap-3 p-3 hover:bg-zinc-800/50 rounded-lg transition-colors group">
                <div
                    onClick={onClick}
                    className="flex-1 cursor-pointer"
                >
                    {children}
                </div>
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {!notification.isRead && (
                        <div className="h-2.5 w-2.5 rounded-full bg-sky-500 flex-shrink-0"></div>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            onDeleteClick(notification.id);
                        }}
                        className="h-6 w-6 p-0 text-zinc-400 hover:text-red-400 hover:bg-red-400/10"
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            </div>
        );
        
        // If href is provided, wrap with Link. Otherwise, return the div with onClick.
        if (href) {
            return <Link href={href} passHref>{content}</Link>;
        }
        return content;
    };
    
    // --- Render logic for each notification type ---

    const handleClick = () => {
        onItemClick(notification.id);
    };

    switch (notification.type) {
        // --- System Notifications ---
        case NotificationType.DEVELOPER_APP_APPROVED:
        case NotificationType.DEVELOPER_APP_REJECTED:
        case NotificationType.PRODUCT_APPROVED:
        case NotificationType.PRODUCT_REJECTED:
        case NotificationType.PRODUCT_DELISTED:
            // For system notifications, we handle the click to mark as read, but still navigate.
            return (
                <NotificationContainer href="/dashboard" onClick={handleClick}>
                    <div className="w-10 h-10 flex-shrink-0 flex items-center justify-center bg-zinc-700 rounded-full">
                        <ShieldCheck className="w-5 h-5 text-zinc-300" />
                    </div>
                    <div className="flex-1">
                        <p className="text-sm text-zinc-200 break-words">{notification.contentPreview}</p>
                        {renderTimestamp()}
                    </div>
                </NotificationContainer>
            );

        // --- Community Notifications ---
        case NotificationType.NEW_COMMENT:
        case NotificationType.REPLY_TO_COMMENT:
            const postLink = `/community/post/${notification.postId}?commentId=${notification.commentId}`;
            const senderUsername = notification.senderUsername || '一位用户';
            const postTitle = notification.postTitle || '一个帖子';

            return (
                <NotificationContainer href={postLink} onClick={handleClick}>
                    <AvatarDisplay
                        avatarUrl={notification.senderAvatarUrl}
                        serialNumber={notification.senderSerialNumber}
                        size={40}
                    />
                    <div className="flex-1">
                        <p className="text-sm text-zinc-300">
                            <span className="font-bold text-white">{senderUsername}</span>
                            {notification.type === NotificationType.REPLY_TO_COMMENT ? ' 回复了你的评论。' : ' 评论了你的帖子 '}
                            <span className="font-semibold text-sky-400">«{postTitle}»</span>
                        </p>
                        {notification.contentPreview && (
                             <blockquote className="mt-2 pl-3 border-l-2 border-zinc-700 text-xs text-zinc-400 italic break-words">
                                {notification.contentPreview}
                            </blockquote>
                        )}
                        {renderTimestamp()}
                    </div>
                </NotificationContainer>
            );
        
        // --- Fallback for unknown types ---
        default:
            return null; // Don't render unknown types
    }
};

const NotificationSection = ({ title, icon, notifications, onItemClick, onDeleteClick }: {
    title: string,
    icon: React.ReactNode,
    notifications: Notification[],
    onItemClick: (id: number) => void,
    onDeleteClick: (id: number) => void
}) => {
    if (notifications.length === 0) return null;
    
    return (
        <div className="p-2">
            <h4 className="text-sm font-semibold text-zinc-400 px-3 py-2 flex items-center gap-2">
                {icon}
                {title}
            </h4>
            <div className="space-y-1">
                {notifications.map(n => <NotificationItem key={n.id} notification={n} onItemClick={onItemClick} onDeleteClick={onDeleteClick} />)}
            </div>
        </div>
    );
};

export const NotificationDropdown = ({ isUnread, onStateChange }: NotificationDropdownProps) => {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchNotifications = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const data = await getNotifications();
            setNotifications(data);
        } catch (error) {
            console.error("Failed to fetch notifications", error);
            setError("无法加载通知。");
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        // Fetch notifications whenever the unread status indicates a potential change.
        if (isUnread) {
            fetchNotifications();
        }
    }, [isUnread]);

    useEffect(() => {
        // Also fetch on initial mount regardless of isUnread status
        fetchNotifications();
    }, []);

    const { systemNotifications, communityNotifications } = useMemo(() => {
        const system: Notification[] = [];
        const community: Notification[] = [];
        notifications.forEach(n => {
            if ([
                NotificationType.DEVELOPER_APP_APPROVED, 
                NotificationType.DEVELOPER_APP_REJECTED,
                NotificationType.PRODUCT_APPROVED,
                NotificationType.PRODUCT_REJECTED,
                NotificationType.PRODUCT_DELISTED
            ].includes(n.type)) {
                system.push(n);
            } else {
                community.push(n);
            }
        });
        
        // Sort both lists by date, most recent first.
        const sortByDate = (a: Notification, b: Notification) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        system.sort(sortByDate);
        community.sort(sortByDate);

        return { systemNotifications: system, communityNotifications: community };
    }, [notifications]);

    const handleMarkAsRead = async (notificationId: number) => {
        // Find the notification to see if we even need to do anything.
        const notification = notifications.find(n => n.id === notificationId);
        if (notification && !notification.isRead) {
            try {
                // Only make API call if it's not already read.
                await markNotificationAsRead(notificationId);
                setNotifications(prev =>
                    prev.map(n => (n.id === notificationId ? { ...n, isRead: true } : n))
                );
                onStateChange(); // Notify header to update its count
            } catch (error) {
                console.error(`Failed to mark notification ${notificationId} as read`, error);
            }
        }
    };
    
    const handleMarkAllAsRead = async () => {
        try {
            await markAllNotificationsAsRead();
            setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
            onStateChange(); // Notify header to update its count
        } catch (error) {
            console.error("Failed to mark all notifications as read", error);
        }
    };

    const handleDeleteNotification = async (notificationId: number) => {
        try {
            await deleteNotification(notificationId);
            setNotifications(prev => prev.filter(n => n.id !== notificationId));
            onStateChange(); // Notify header to update its count
        } catch (error) {
            console.error(`Failed to delete notification ${notificationId}`, error);
        }
    };

    const handleDeleteAllNotifications = async () => {
        try {
            await deleteAllNotifications();
            setNotifications([]);
            onStateChange(); // Notify header to update its count
        } catch (error) {
            console.error("Failed to delete all notifications", error);
        }
    };

    return (
        <div className="w-80 md:w-96 bg-zinc-900/80 backdrop-blur-md border border-zinc-700 rounded-xl shadow-2xl">
            <div className="p-4 border-b border-zinc-700 flex justify-between items-center">
                <h3 className="font-semibold text-lg flex items-center gap-2">
                    <Bell className="h-5 w-5 text-sky-400" />
                    通知
                </h3>
                <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead} disabled={notifications.every(n => n.isRead) || notifications.length === 0}>
                    <CheckCheck className="mr-2 h-4 w-4" />
                    全部已读
                </Button>
            </div>
            <ScrollArea className="h-[400px]">
                {isLoading ? (
                    <div className="p-4 text-center text-zinc-400">正在加载...</div>
                ) : error ? (
                     <div className="p-4 text-center text-red-400">{error}</div>
                ) : notifications.length === 0 ? (
                    <div className="p-4 text-center text-zinc-400">没有新的通知</div>
                ) : (
                    <>
                        <NotificationSection
                            title="系统通知"
                            icon={<ShieldCheck className="h-4 w-4" />}
                            notifications={systemNotifications}
                            onItemClick={handleMarkAsRead}
                            onDeleteClick={handleDeleteNotification}
                        />
                        {systemNotifications.length > 0 && communityNotifications.length > 0 && <div className="border-t border-zinc-800 mx-4 my-1"></div>}
                        <NotificationSection
                            title="社区互动"
                            icon={<Users className="h-4 w-4" />}
                            notifications={communityNotifications}
                            onItemClick={handleMarkAsRead}
                            onDeleteClick={handleDeleteNotification}
                        />
                    </>
                )}
            </ScrollArea>
        </div>
    );
}; 