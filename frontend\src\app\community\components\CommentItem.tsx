'use client';

import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { CommunityComment } from '@/types/CommunityComment';
import AvatarDisplay from '@/components/AvatarDisplay';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { CornerDownRight, Minus, Plus, Trash2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Link from 'next/link';

interface CommentItemProps {
    comment: CommunityComment;
    onReply: (comment: { id: number; username: string }) => void;
    onDelete: (commentId: number) => void;
    children?: React.ReactNode;
    depth: number;
}

// Function to process mentions into clickable links
const processMentions = (text: string, validMentions?: string[]) => {
    if (!validMentions || validMentions.length === 0) {
        return text; // Return original text if no valid mentions
    }
    // This regex finds @ followed by allowed username characters
    return text.replace(/@([a-zA-Z0-9_-]+)/g, (match, username) => {
        if (validMentions.includes(username)) {
            return `[${match}](/profile/${username})`;
        }
        return match; // Return the original @username text if not valid
    });
};

export const CommentItem: React.FC<CommentItemProps> = ({
    comment,
    onReply,
    onDelete,
    children,
    depth,
}) => {
    const { user } = useAuth();
    const [isCollapsed, setIsCollapsed] = useState(false);
    const hasChildren = React.Children.count(children) > 0;

    return (
        <div id={`comment-${comment.id}`} className="flex items-start space-x-4 min-w-0 w-full overflow-hidden">
            <AvatarDisplay
                avatarUrl={comment.author.avatarUrl}
                serialNumber={comment.author.serialNumber}
                avatarVersion={comment.author.avatarVersion}
                size={32}
                className="h-8 w-8 flex-shrink-0"
            />
            <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center gap-2">
                    <span className="font-semibold text-white">{comment.author.username}</span>
                    <span className="text-xs text-zinc-400">
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true, locale: zhCN })}
                    </span>
                </div>
                <div className="mt-1 text-zinc-300 comment-content-container">
                    {comment.parentAuthorUsername && (
                        <span className="text-sm text-zinc-400 mr-2 break-words word-break-break-all">
                            回复 @<Link href={`/profile/${comment.parentAuthorUsername}`} className="text-blue-400 hover:underline break-all word-break-break-all">{comment.parentAuthorUsername}</Link>:
                        </span>
                    )}
                    <div className="prose prose-invert max-w-none text-sm prose-p:my-0">
                        <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                                // 自定义代码块组件
                                pre: ({ children, ...props }) => (
                                    <pre {...props} className="bg-zinc-800 p-2 rounded text-xs">
                                        {children}
                                    </pre>
                                ),
                                // 自定义行内代码组件
                                code: ({ children, ...props }: any) => {
                                    const isInline = !props.className?.includes('language-');
                                    return isInline ? (
                                        <code {...props} className="bg-zinc-800 px-1 rounded text-xs">
                                            {children}
                                        </code>
                                    ) : (
                                        <code {...props}>
                                            {children}
                                        </code>
                                    );
                                },
                                // 自定义链接组件
                                a: ({ children, href, ...props }) => (
                                    <a {...props} href={href} className="text-blue-400 hover:underline">
                                        {children}
                                    </a>
                                ),
                                // 自定义表格组件
                                table: ({ children, ...props }) => (
                                    <div className="overflow-x-auto">
                                        <table {...props} className="table-auto">
                                            {children}
                                        </table>
                                    </div>
                                ),
                                // 自定义表格单元格
                                td: ({ children, ...props }) => (
                                    <td {...props} className="p-2 border border-zinc-600">
                                        {children}
                                    </td>
                                ),
                                th: ({ children, ...props }) => (
                                    <th {...props} className="p-2 border border-zinc-600 bg-zinc-700">
                                        {children}
                                    </th>
                                ),
                                // 自定义引用块
                                blockquote: ({ children, ...props }) => (
                                    <blockquote {...props} className="border-l-4 border-zinc-600 pl-4 italic">
                                        {children}
                                    </blockquote>
                                )
                            }}
                        >
                            {processMentions(comment.content, comment.mentionedUsers)}
                        </ReactMarkdown>
                    </div>
                </div>
                <div className="mt-2 flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onReply({ id: comment.id, username: comment.author.username })}
                        className="text-zinc-400 hover:text-white hover:bg-zinc-800 h-auto px-2 py-1"
                    >
                        <CornerDownRight size={14} className="mr-1" />
                        回复
                    </Button>

                    {user && (user.id === comment.authorId || user.role === 'KitolusAdmin') && (
                         <AlertDialog>
                            <AlertDialogTrigger asChild>
                                 <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-500 hover:text-red-400 hover:bg-zinc-800 h-auto px-2 py-1"
                                >
                                    <Trash2 size={14} className="mr-1" />
                                    删除
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle>确定要删除此评论吗？</AlertDialogTitle>
                                    <AlertDialogDescription>
                                        此操作无法撤销。这将永久删除此评论及其所有回复。
                                        {user.role === 'KitolusAdmin' && user.id !== comment.authorId && (
                                            <span className="block mt-2 text-orange-600 font-medium">
                                                注意：您正在以管理员身份删除其他用户的评论。
                                            </span>
                                        )}
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => onDelete(comment.id)}>继续删除</AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                    )}

                    {hasChildren && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={() => setIsCollapsed(!isCollapsed)}
                           className="text-zinc-400 hover:text-white hover:bg-zinc-800 h-auto px-2 py-1"
                       >
                           {isCollapsed ? <Plus size={14} className="mr-1" /> : <Minus size={14} className="mr-1" />}
                           {isCollapsed ? `展开 (${React.Children.count(children)})` : '收起'}
                       </Button>
                   )}
                </div>

                {/* Nested Replies Section with conditional rendering and animation */}
                <AnimatePresence initial={false}>
                    {!isCollapsed && hasChildren && (
                        <motion.div
                            key="content"
                            initial="collapsed"
                            animate="open"
                            exit="collapsed"
                            variants={{
                                open: { opacity: 1, height: 'auto' },
                                collapsed: { opacity: 0, height: 0 },
                            }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className="overflow-hidden" // Important for height animation
                        >
                            <div className={`mt-4 space-y-4 ${depth < 1 ? 'border-l-2 border-zinc-800 pl-4' : ''}`}>
                                {children}
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
}; 