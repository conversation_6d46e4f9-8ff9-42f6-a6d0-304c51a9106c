'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { getUnreadNotificationCount, markAllNotificationsAsRead } from '@/services/api';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { Home, Rocket, Users, ImageIcon, Library, LogIn, UserPlus, UserCircle, Cog, Wrench, Bell, LayoutDashboard, LogOut, MessageSquare } from 'lucide-react';
import AvatarDisplay from './AvatarDisplay';
import { NotificationDropdown } from './NotificationDropdown';
import { useMessageStore, useUnreadCount } from '@/stores/messageStore';
import { Badge } from '@/components/ui/badge';
import { OnlineUserCount } from '@/components/ui/GlobalOnlineStatus';
import { SearchBox } from '@/components/SearchBox';

const Header = () => {
  const pathname = usePathname();
  const { isAuthenticated, logout, user, loading: isAuthLoading } = useAuth();
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [isNotificationOpen, setIsNotificationOpen] = React.useState(false);

  // 消息系统状态
  const { isOpen: isMessageOpen, setOpen: setMessageOpen, initializeConnection } = useMessageStore();
  const messageUnreadCount = useUnreadCount();



  const fetchUnreadCount = React.useCallback(async () => {
    if (!isAuthenticated) return;
    try {
      const data = await getUnreadNotificationCount();
      setUnreadCount(data.count || 0);
    } catch (error) {
      // 优雅处理错误，设置为0而不是显示错误
      console.warn('Failed to fetch unread notification count:', error);
      setUnreadCount(0);
    }
  }, [isAuthenticated]);

  React.useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const poll = async () => {
        await fetchUnreadCount();

        if (isMounted) {
            timeoutId = setTimeout(poll, 60000);
        }
    };

    poll();

    return () => {
        isMounted = false;
        if(timeoutId) {
            clearTimeout(timeoutId);
        }
    };
  }, [fetchUnreadCount]);

  // 初始化消息系统连接
  React.useEffect(() => {
    if (isAuthenticated && user?.id) {
      const token = localStorage.getItem('token');
      if (token) {
        initializeConnection(token, user.id);
      }
    }
  }, [isAuthenticated, user?.id, initializeConnection]);

  const handleNotificationOpenChange = (isOpen: boolean) => {
    setIsNotificationOpen(isOpen);
  };
  
  const handleStateChange = () => {
    fetchUnreadCount();
  };



  const mainRoutes = [
    { href: '/', label: '主页', icon: <Home className="h-4 w-4" /> },
    { href: '/products', label: '酒馆', icon: <Rocket className="h-4 w-4" /> },
    { href: '/community', label: '社区', icon: <Users className="h-4 w-4" /> },
    { href: '/showcase', label: '展示', icon: <ImageIcon className="h-4 w-4" /> },
    { href: '/kb', label: '知识库', icon: <Library className="h-4 w-4" /> },
    { href: '/about', label: '关于', icon: <UserCircle className="h-4 w-4" /> },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/30 backdrop-blur-lg border-b border-border">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2 text-lg font-bold text-foreground">
          <Cog className="h-6 w-6" />
          <span>Kitolus Community</span>
        </Link>

        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            {mainRoutes.map((route) => (
              <NavigationMenuItem key={route.href}>
                <NavigationMenuLink asChild active={pathname === route.href} className={navigationMenuTriggerStyle()}>
                  <Link href={route.href} className='bg-transparent transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))]'>
                    <div className="flex items-center gap-1.5">
                      {route.icon}
                      {route.label}
                    </div>
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        <div className="flex items-center gap-2">
          {/* 搜索框 - 桌面端 */}
          <SearchBox className="hidden sm:block" />

          {/* 移动端搜索按钮 */}
          <SearchBox className="sm:hidden" isMobile={true} />

          {isAuthenticated ? (
            <>
              {/* 消息按钮 */}
              <Button
                variant="ghost"
                size="icon"
                className="relative transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))]"
                onClick={() => setMessageOpen(!isMessageOpen)}
                title="消息中心"
              >
                <MessageSquare className="h-5 w-5" />

                {/* 未读消息徽章 */}
                {messageUnreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 min-w-[18px] h-[18px] text-xs flex items-center justify-center p-0 rounded-full"
                  >
                    {messageUnreadCount > 99 ? '99+' : messageUnreadCount}
                  </Badge>
                )}
              </Button>

              {/* 在线用户计数 */}
              <OnlineUserCount className="hidden md:flex" />

              <Popover open={isNotificationOpen} onOpenChange={handleNotificationOpenChange}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="relative transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))]">
                    <Bell className="h-5 w-5" />
                    {unreadCount > 0 && (
                      <span className="absolute top-0 right-0 flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
                      </span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 md:w-96 p-0 border-zinc-700 bg-transparent shadow-2xl" align="end">
                  <NotificationDropdown isUnread={unreadCount > 0} onStateChange={handleStateChange} />
                </PopoverContent>
              </Popover>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="w-10 h-10 transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))] rounded-full">
                  <AvatarDisplay 
                    avatarUrl={user?.fullAvatarUrl}
                    serialNumber={user?.serialNumber}
                    avatarVersion={user?.avatarVersion}
                    size={32}
                    className="h-8 w-8 rounded-full object-cover"
                    isLoading={isAuthLoading}
                  />
                </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48" align="end">
                  <DropdownMenuItem asChild>
                    <Link href={`/profile/${user?.username}`}>
                      <Home className="mr-2 h-4 w-4" />
                      <span>我的主页</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard">
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>仪表板</span>
              </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>登出</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Link href="/login">
                <Button variant="ghost" className="transition-shadow duration-300 hover:shadow-[0_0_15px_hsl(var(--primary))]">
                  <LogIn className="mr-2 h-4 w-4" />
                  登录
                </Button>
              </Link>
              <Link href="/register">
                <Button className="transition-shadow duration-300 hover:shadow-lg hover:shadow-primary/50">
                  <UserPlus className="mr-2 h-4 w-4" />
                  注册
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header; 