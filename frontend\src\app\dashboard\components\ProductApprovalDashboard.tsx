'use client';

import { useState, useEffect, useCallback } from 'react';
import { getPendingProducts, approveProduct, rejectProduct, getAdminProductDetails } from '@/services/api';
import { Product } from '@/types/Product';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import { Label } from '@/components/ui/label';

const ProductApprovalDashboard = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [rejectionReason, setRejectionReason] = useState('');
    const [approvalNotes, setApprovalNotes] = useState('');
    const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
    const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    
    const fetchPendingProducts = useCallback(async () => {
        setIsLoading(true);
        try {
            const data = await getPendingProducts();
            setProducts(data);
        } catch (error) {
            toast.error('获取待审核产品列表失败');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchPendingProducts();
    }, [fetchPendingProducts]);

    const handleApproveClick = (product: Product) => {
        setSelectedProduct(product);
        setApprovalNotes(''); // Reset notes
        setIsApproveModalOpen(true);
    };

    const handleConfirmApprove = async (productId: number) => {
        try {
            const approvedProduct = await approveProduct(productId, approvalNotes || '');
            toast.success(`商品 "${approvedProduct.name}" 已批准`);
            setIsApproveModalOpen(false);
            setProducts(prevProducts => prevProducts.filter(p => p.id !== productId));
        } catch (error) {
            toast.error('批准商品失败');
        }
    };

    const handleRejectClick = (product: Product) => {
        setSelectedProduct(product);
        setRejectionReason('');
        setIsRejectModalOpen(true);
    };

    const handleConfirmReject = async () => {
        if (!selectedProduct || !rejectionReason.trim()) {
            toast.error('请输入拒绝理由');
            return;
        }
        try {
            const rejectedProduct = await rejectProduct(selectedProduct.id, rejectionReason);
            toast.success(`商品 "${rejectedProduct.name}" 已驳回`);
            setIsRejectModalOpen(false);
            setProducts(prevProducts => prevProducts.filter(p => p.id !== selectedProduct.id));
        } catch (error) {
            toast.error('驳回商品失败');
        }
    };

    const handleViewDetails = async (product: Product) => {
        try {
            const fullProductDetails = await getAdminProductDetails(product.id);
            setSelectedProduct(fullProductDetails);
            setIsDetailModalOpen(true);
        } catch (error) {
            toast.error('无法获取产品详情');
            setSelectedProduct(null);
        }
    };

    if (isLoading) return <div className="text-center p-8">正在加载待审核产品...</div>;

    return (
        <div className="bg-card/50 backdrop-blur-sm border border-border/30 rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-bold mb-4">商品审核</h2>
            {products.length === 0 ? (
                <p>没有待审核的商品。</p>
            ) : (
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>商品名称</TableHead>
                            <TableHead>作者</TableHead>
                            <TableHead>价格</TableHead>
                            <TableHead>提交时间</TableHead>
                            <TableHead>最后更新</TableHead>
                            <TableHead className="text-right">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                    {products.map((product) => (
                            <TableRow key={product.id}>
                                <TableCell className="font-medium">{product.name}</TableCell>
                                <TableCell>{product.authorName}</TableCell>
                                <TableCell>¥{product.price.toFixed(2)}</TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                    {new Date(product.createdAt).toLocaleString()}
                                </TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                    {product.updatedAt ? new Date(product.updatedAt).toLocaleString() : '-'}
                                </TableCell>
                                <TableCell className="text-right">
                                    <div className="flex justify-end space-x-2">
                                        <Button variant="outline" size="sm" onClick={() => handleViewDetails(product)}>查看详情</Button>
                                        <Button size="sm" onClick={() => handleApproveClick(product)}>批准</Button>
                                        <Button variant="destructive" size="sm" onClick={() => handleRejectClick(product)}>驳回</Button>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}

            {/* Approve Modal */}
            <Dialog open={isApproveModalOpen} onOpenChange={setIsApproveModalOpen}>
                <DialogContent className="bg-card/60 backdrop-blur-lg border border-border/20 shadow-xl rounded-lg sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-bold">批准商品: {selectedProduct?.name}</DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            您可以为本次批准添加一些可选的备注。
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="approval-notes" className="font-semibold">
                                批准备注 (可选)
                            </Label>
                            <Textarea
                                id="approval-notes"
                                value={approvalNotes}
                                onChange={(e) => setApprovalNotes(e.target.value)}
                                className="min-h-[120px] bg-background/50 border-border/30 focus:ring-primary"
                                placeholder="例如：符合上架标准，设计很有创意..."
                            />
                        </div>
                                </div>
                    <DialogFooter className="gap-2 sm:justify-end">
                        <Button variant="ghost" onClick={() => setIsApproveModalOpen(false)}>取消</Button>
                        <Button onClick={() => selectedProduct && handleConfirmApprove(selectedProduct.id)}>确认批准</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Rejection Reason Modal */}
            <Dialog open={isRejectModalOpen} onOpenChange={setIsRejectModalOpen}>
                <DialogContent className="bg-card/60 backdrop-blur-lg border border-border/20 shadow-xl rounded-lg sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-bold">驳回商品: {selectedProduct?.name}</DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            请提供详细的驳回原因，这将帮助开发者改进他们的产品。
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                                        <div className="grid gap-2">
                            <Label htmlFor="reason" className="font-semibold">
                                驳回原因
                            </Label>
                                            <Textarea
                                                id="reason"
                                                value={rejectionReason}
                                                onChange={(e) => setRejectionReason(e.target.value)}
                                className="min-h-[120px] bg-background/50 border-border/30 focus:ring-primary"
                                placeholder="例如：产品截图不清晰、描述过于简单、下载链接无效等..."
                            />
                        </div>
                    </div>
                    <DialogFooter className="gap-2 sm:justify-end">
                        <Button variant="ghost" onClick={() => setIsRejectModalOpen(false)}>取消</Button>
                        <Button variant="destructive" onClick={handleConfirmReject}>确认驳回</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Product Detail Modal */}
            <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
                <DialogContent className="sm:max-w-[600px] bg-card/80 backdrop-blur-xl">
                    <DialogHeader>
                        <DialogTitle>{selectedProduct?.name}</DialogTitle>
                        <DialogDescription>
                            由 {selectedProduct?.authorName} 提交
                        </DialogDescription>
                    </DialogHeader>
                    {selectedProduct && (
                        <div className="mt-4 space-y-4 max-h-[70vh] overflow-y-auto pr-4 custom-scrollbar">
                             <div className="relative w-full h-64 bg-black/20 rounded-md">
                                <Image
                                    src={selectedProduct.imageUrl}
                                    alt={selectedProduct.name}
                                    layout="fill"
                                    className="object-contain"
                                            />
                                        </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">价格</h3>
                                    <p className="text-primary text-xl font-bold">¥{selectedProduct.price.toFixed(2)}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">提交时间</h3>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(selectedProduct.createdAt).toLocaleString()}
                                    </p>
                                </div>
                            </div>
                            {selectedProduct.updatedAt && selectedProduct.updatedAt !== selectedProduct.createdAt && (
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">最后更新时间</h3>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(selectedProduct.updatedAt).toLocaleString()}
                                    </p>
                                </div>
                            )}
                            <div>
                                <h3 className="font-semibold text-lg mb-2">下载链接</h3>
                                <a href={selectedProduct.downloadUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline break-all">
                                    {selectedProduct.downloadUrl}
                                </a>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">产品描述</h3>
                                <div className="prose prose-sm prose-invert max-w-none bg-black/10 p-4 rounded-md">
                                    <ReactMarkdown>{selectedProduct.description}</ReactMarkdown>
                                </div>
                            </div>
                </div>
            )}
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ProductApprovalDashboard; 