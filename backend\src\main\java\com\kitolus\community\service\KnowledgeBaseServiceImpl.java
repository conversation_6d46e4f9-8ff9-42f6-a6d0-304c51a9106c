package com.kitolus.community.service;

import com.kitolus.community.entity.KnowledgeBaseArticle;
import com.kitolus.community.entity.SearchResult;
import jakarta.annotation.PostConstruct;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer;
import org.apache.lucene.analysis.miscellaneous.PerFieldAnalyzerWrapper;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.StringField;
import org.apache.lucene.document.TextField;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.RAMDirectory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileTime;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    private final Map<String, KnowledgeBaseArticle> articleMap = new ConcurrentHashMap<>();
    private Directory memoryIndex;
    private Analyzer analyzer;
    private final Path kbRootLocation;
    private final ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeBaseServiceImpl.class);

    // Regex patterns for parsing markdown front matter
    private static final Pattern FRONT_MATTER_PATTERN = Pattern.compile("(?s)^---\\s*\\n(.*?)\\n---\\s*\\n");
    private static final Pattern TITLE_PATTERN = Pattern.compile("^title:\\s*[\"']?(.*?)[\"']?$", Pattern.MULTILINE);
    private static final Pattern DATE_PATTERN = Pattern.compile("^date:\\s*[\"']?(.*?)[\"']?$", Pattern.MULTILINE);


    public KnowledgeBaseServiceImpl(@Value("${kb.upload-path}") String uploadPath) {
        this.kbRootLocation = Paths.get(uploadPath).toAbsolutePath().normalize();
        logger.info("知识库外部文件根目录设置为: {}", this.kbRootLocation.toString());
    }

    @PostConstruct
    public void init() {
        try {
            // Ensure the upload directory exists
            if (!Files.exists(this.kbRootLocation)) {
                Files.createDirectories(this.kbRootLocation);
                logger.info("已创建知识库目录: {}", this.kbRootLocation);
            }
            
            // 1. Load articles from classpath (inside JAR)
            List<KnowledgeBaseArticle> classpathArticles = loadArticlesFromResources();
            for(KnowledgeBaseArticle article : classpathArticles) {
                articleMap.put(article.getId(), article);
            }
            logger.info("成功从类路径加载 {} 篇知识库文章。", classpathArticles.size());

            // 2. Load articles from external file system (uploads)
            List<KnowledgeBaseArticle> filesystemArticles = loadArticlesFromFileSystem();
            for(KnowledgeBaseArticle article : filesystemArticles) {
                // This will overwrite any existing article with the same ID
                if (article != null) {
                articleMap.put(article.getId(), article);
                }
            }
            logger.info("成功从外部文件系统加载 {} 篇知识库文章。", filesystemArticles.size());

            // 3. Index all articles
            indexArticles();
            logger.info("成功为全部 {} 篇文章创建内存搜索引擎索引。", articleMap.size());
        } catch (IOException e) {
            logger.error("初始化知识库失败", e);
        }
    }

    private void indexArticles() throws IOException {
        memoryIndex = new RAMDirectory();

        Map<String, Analyzer> analyzerPerField = new HashMap<>();
        analyzerPerField.put("title", new SmartChineseAnalyzer());
        analyzerPerField.put("summary", new SmartChineseAnalyzer());
        analyzerPerField.put("content", new SmartChineseAnalyzer());
        analyzerPerField.put("id", new StandardAnalyzer());

        analyzer = new PerFieldAnalyzerWrapper(new SmartChineseAnalyzer(), analyzerPerField);

        IndexWriterConfig indexWriterConfig = new IndexWriterConfig(analyzer);
        try (IndexWriter writer = new IndexWriter(memoryIndex, indexWriterConfig)) {
            writer.deleteAll();
            for (KnowledgeBaseArticle article : articleMap.values()) {
                if (article == null) continue;
                Document doc = new Document();
                doc.add(new StringField("id", article.getId(), Field.Store.YES));
                doc.add(new TextField("title", article.getTitle(), Field.Store.YES));
                if(article.getSummary() != null) {
                    doc.add(new TextField("summary", article.getSummary(), Field.Store.YES));
                }
                if(article.getContent() != null) {
                    doc.add(new TextField("content", article.getContent(), Field.Store.NO));
                }
                writer.addDocument(doc);
            }
        }
    }

    @Override
    public List<KnowledgeBaseArticle> getAllArticles() {
        return articleMap.values().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(KnowledgeBaseArticle::getPublishDate).reversed())
                .map(this::toListItem)
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<String, List<KnowledgeBaseArticle>> getAllArticlesGroupedByDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return articleMap.values().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(KnowledgeBaseArticle::getPublishDate).reversed())
                .collect(Collectors.groupingBy(
                        article -> article.getPublishDate().format(formatter),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
    }

    @Override
    public long countArticles() {
        return articleMap.values().stream().filter(Objects::nonNull).count();
    }

    @Override
    public KnowledgeBaseArticle getArticleById(String id) {
        return articleMap.get(id);
    }

    @Override
    public List<KnowledgeBaseArticle> getRandomArticles(int count) {
        List<KnowledgeBaseArticle> allArticles = new ArrayList<>(articleMap.values());
        Collections.shuffle(allArticles);
        return allArticles.stream().limit(count).map(this::toListItem).collect(Collectors.toList());
    }

    @Override
    public SearchResult searchArticles(String queryStr) {
        if (queryStr == null || queryStr.isBlank() || memoryIndex == null) {
            return new SearchResult(new ArrayList<>(), new ArrayList<>());
        }
        try {
            IndexReader reader = DirectoryReader.open(memoryIndex);
            IndexSearcher searcher = new IndexSearcher(reader);

            List<KnowledgeBaseArticle> titleMatches = performSearch(searcher, queryStr, "title");
            Set<String> titleMatchIds = titleMatches.stream().map(KnowledgeBaseArticle::getId).collect(Collectors.toSet());

            List<KnowledgeBaseArticle> contentAndSummaryMatches = performSearch(searcher, queryStr, "summary", "content");

            List<KnowledgeBaseArticle> contentMatches = contentAndSummaryMatches.stream()
                .filter(article -> !titleMatchIds.contains(article.getId()))
                .collect(Collectors.toList());

            reader.close();
            return new SearchResult(titleMatches, contentMatches);
            
        } catch (IOException | ParseException e) {
            logger.error("搜索文章时出错，查询: '{}'", queryStr, e);
            return new SearchResult(new ArrayList<>(), new ArrayList<>());
        }
    }

    private List<KnowledgeBaseArticle> performSearch(IndexSearcher searcher, String queryStr, String... fields) throws ParseException, IOException {
        MultiFieldQueryParser parser = new MultiFieldQueryParser(fields, analyzer);
        parser.setDefaultOperator(MultiFieldQueryParser.Operator.AND);
        Query query = parser.parse(queryStr);
        TopDocs topDocs = searcher.search(query, 100);

        List<KnowledgeBaseArticle> results = new ArrayList<>();
        for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
            Document doc = searcher.doc(scoreDoc.doc);
            KnowledgeBaseArticle found = articleMap.get(doc.get("id"));
            if (found != null) {
                results.add(toListItem(found));
            }
        }
        return results;
    }

    private KnowledgeBaseArticle toListItem(KnowledgeBaseArticle article) {
        if (article == null) return null;
                    KnowledgeBaseArticle listItem = new KnowledgeBaseArticle();
                    listItem.setId(article.getId());
                    listItem.setTitle(article.getTitle());
                    listItem.setSummary(article.getSummary() != null ? article.getSummary() : "");
        listItem.setPublishDate(article.getPublishDate());
                    return listItem;
    }

    private List<KnowledgeBaseArticle> loadArticlesFromResources() {
        List<KnowledgeBaseArticle> articles = new ArrayList<>();
        try {
            Resource[] resources = resourcePatternResolver.getResources("classpath:kb_articles/**/*.md");
            if (resources != null) {
                for (Resource resource : resources) {
                    try (InputStream inputStream = resource.getInputStream();
                         InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                         BufferedReader bufferedReader = new BufferedReader(reader)) {

                        String fileContent = bufferedReader.lines().collect(Collectors.joining("\n"));
                        
                        FileTime creationTime = null;
                        try {
                            // This works in dev environment (exploded), but throws for JAR resources.
                            creationTime = (FileTime) Files.getAttribute(Paths.get(resource.getURI()), "basic:creationTime");
                        } catch (Exception e) {
                            // Expected when running from a JAR. We'll use LocalDate.now() as fallback.
                            logger.trace("无法获取类路径资源的创建时间: {}. 在JAR中运行时这是正常现象。", resource.getFilename());
                        }

                        KnowledgeBaseArticle article = parseArticle(resource.getFilename(), fileContent, creationTime);
                        if (article != null) {
                            articles.add(article);
                        }
            } catch (IOException e) {
                        logger.error("无法读取知识库资源: {}", resource.getFilename(), e);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("无法从类路径加载知识库文章。", e);
        }
        return articles;
    }

    private List<KnowledgeBaseArticle> loadArticlesFromFileSystem() {
        List<KnowledgeBaseArticle> articles = new ArrayList<>();
        try (Stream<Path> paths = Files.walk(this.kbRootLocation)) {
            paths.filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".md"))
                    .forEach(path -> {
                        try {
                            String content = Files.readString(path, StandardCharsets.UTF_8);
                            FileTime creationTime = (FileTime) Files.getAttribute(path, "basic:creationTime");
                            KnowledgeBaseArticle article = parseArticle(path.getFileName().toString(), content, creationTime);
                            if (article != null) {
                                articles.add(article);
                            }
                        } catch (IOException e) {
                            logger.error("读取或解析文章失败: {}", path, e);
                        }
                    });
        } catch (IOException e) {
            logger.error("无法从外部文件系统加载知识库文章: {}", this.kbRootLocation, e);
        }
        return articles;
    }

    private KnowledgeBaseArticle parseArticle(String fileName, String content, FileTime creationTime) {
        if (fileName == null) return null;

        KnowledgeBaseArticle article = new KnowledgeBaseArticle();
        article.setId(fileName.replace(".md", ""));

        Matcher frontMatterMatcher = FRONT_MATTER_PATTERN.matcher(content);
        String mainContent = content;
        LocalDate publishDate = null;
        String title = null;

        if (frontMatterMatcher.find()) {
            String frontMatter = frontMatterMatcher.group(1);
            mainContent = content.substring(frontMatterMatcher.group(0).length()).trim();

            Matcher titleMatcher = TITLE_PATTERN.matcher(frontMatter);
            if (titleMatcher.find()) {
                title = titleMatcher.group(1).trim();
            }

            Matcher dateMatcher = DATE_PATTERN.matcher(frontMatter);
            if (dateMatcher.find()) {
                try {
                    publishDate = LocalDate.parse(dateMatcher.group(1).trim(), DateTimeFormatter.ISO_LOCAL_DATE);
                } catch (Exception e) {
                    logger.warn("无法解析文章 '{}' 中的日期格式。将使用回退日期。", fileName);
                }
            }
        }

        article.setTitle(title != null ? title : article.getId());
        article.setContent(mainContent);
        article.setSummary(extractSummary(mainContent));

        if (publishDate == null) {
            if (creationTime != null) {
                publishDate = creationTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            } else {
                publishDate = LocalDate.now();
            }
        }
        article.setPublishDate(publishDate);

        return article;
    }

    private String extractSummary(String content) {
        if (content == null) return "";
        // Remove markdown headings and formatting to get a cleaner summary
        String plainText = content.replaceAll("^#.*", "") // Headings
                                  .replaceAll("[*`>\\[\\]-]", "") // Other markdown chars
                                  .replaceAll("\\s+", " ").trim(); // Collapse whitespace
        int summaryLength = Math.min(plainText.length(), 250);
        return plainText.substring(0, summaryLength) + (plainText.length() > 250 ? "..." : "");
    }

    @Override
    public KnowledgeBaseArticle storeArticleFromMarkdownFile(MultipartFile file) throws IOException {
        String filename = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
        if (filename.isEmpty() || !filename.endsWith(".md")) {
            throw new IOException("文件名无效或不是Markdown文件: " + filename);
        }

        Path targetLocation = this.kbRootLocation.resolve(filename);
        Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        
        String content = new String(file.getBytes(), StandardCharsets.UTF_8);
        FileTime creationTime = (FileTime) Files.getAttribute(targetLocation, "basic:creationTime");
        KnowledgeBaseArticle newArticle = parseArticle(filename, content, creationTime);

        if(newArticle != null) {
        articleMap.put(newArticle.getId(), newArticle);
            indexArticles(); // Re-index after adding new article
        }
        return newArticle;
    }

    @Override
    public void deleteArticle(String id) throws IOException {
        KnowledgeBaseArticle article = articleMap.get(id);
        if(article == null) {
            throw new IOException("文章不存在: " + id);
        }
        
        Path articlePath = this.kbRootLocation.resolve(id + ".md");
        if (Files.exists(articlePath)) {
             Files.delete(articlePath);
        } else {
            // This could happen if the article was loaded from classpath
            logger.warn("无法在文件系统中找到文章 '{}' 进行删除，可能它是从类路径加载的。", id);
        }
        
        articleMap.remove(id);
        indexArticles(); // Re-index after deletion
    }
} 