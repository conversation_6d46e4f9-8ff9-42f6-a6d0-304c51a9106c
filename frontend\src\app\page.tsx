import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  ShieldCheck,
  Gamepad2,
} from 'lucide-react';
import Link from 'next/link';
import log from '@/lib/logger';
import FeatureCard from '@/components/FeatureCard';
import TestimonialCard from '@/components/TestimonialCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Footer from '@/components/Footer';
import ParallaxBackground from '@/components/ParallaxBackground';

// Define the type for a knowledge base article based on the backend entity
interface KnowledgeBaseArticle {
  id: string;
  title: string;
  summary: string;
}

export default function HomePage() {
  log.info('[主页] 开始渲染。');
  
  return (
    <div className="flex flex-col min-h-screen text-foreground">
      <div className="fixed inset-0 -z-10">
        <ParallaxBackground />
      </div>
      <main className="flex-1 bg-background/50 backdrop-blur-md">
        {/* Hero Section */}
        <section className="w-full h-screen flex items-center justify-center">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light tracking-tighter text-foreground mb-6">
              Kitolus Community
            </h1>
            <p className="max-w-3xl mx-auto text-muted-foreground md:text-xl mb-8">
              一个专为 <span className="text-gray-600">GregTech: New Horizons</span> 玩家打造的交流平台。在这里分享您的自动化工厂，讨论最前沿的聚变技术，或是在商店中获取能为您节省大量时间的宝贵开发工具。
            </p>
            <div className="flex justify-center gap-4">
              <Link href="/register">
                <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg shadow-primary/10 transition-all duration-300 transform hover:scale-105">
                  <Rocket className="mr-2 h-5 w-5" /> 立即加入
                </Button>
              </Link>
              <Link href="/about">
                <Button size="lg" variant="outline" className="text-foreground border-border hover:bg-accent hover:text-accent-foreground transition-all duration-300 transform hover:scale-105">
                  <BookOpen className="mr-2 h-5 w-5" /> 了解社区
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
