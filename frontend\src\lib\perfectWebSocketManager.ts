/**
 * 完美的WebSocket连接管理器
 * 实现可靠的实时消息推送
 */

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface WebSocketEventHandlers {
  [eventType: string]: ((data: any) => void)[];
}

export class PerfectWebSocketManager {
  private ws: WebSocket | null = null;
  private token: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 初始重连延迟1秒
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isDestroyed = false;
  private eventHandlers: WebSocketEventHandlers = {};

  // 新增：typing状态管理
  private typingTimers: Map<string, NodeJS.Timeout> = new Map();
  private currentTypingConversation: string | null = null;

  constructor(token: string) {
    this.token = token;
  }

  /**
   * 建立WebSocket连接
   */
  async connect(): Promise<void> {
    if (this.isConnecting || this.isDestroyed) {
      return;
    }

    this.isConnecting = true;

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws/messages?token=${this.token}`;

      console.log('🚀 建立WebSocket连接:', wsUrl);
      console.log('🔑 使用Token:', this.token.substring(0, 20) + '...');

      this.ws = new WebSocket(wsUrl);

      // 设置连接超时（10秒）
      this.connectionTimeout = setTimeout(() => {
        if (this.isConnecting && this.ws) {
          console.error('❌ WebSocket连接超时');
          this.ws.close();
          this.isConnecting = false;
          this.emit('error', new Error('Connection timeout'));
        }
      }, 10000);

      // 连接成功
      this.ws.onopen = () => {
        // 清除连接超时
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        
        console.log('✅ WebSocket连接成功');
        this.emit('connected', null);
        this.startHeartbeat();
      };

      // 接收消息
      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 收到WebSocket消息:', message);
          console.log('📨 消息类型:', message.type, '数据:', message);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ WebSocket消息解析失败:', error);
        }
      };

      // 连接关闭
      this.ws.onclose = (event) => {
        this.isConnecting = false;
        this.ws = null;
        this.stopHeartbeat();

        console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
        this.emit('disconnected', { code: event.code, reason: event.reason });

        // 非正常关闭时自动重连
        if (!this.isDestroyed && event.code !== 1000) {
          this.handleReconnect();
        }
      };

      // 连接错误
      this.ws.onerror = (error) => {
        this.isConnecting = false;
        console.error('❌ WebSocket连接错误:', error);
        console.error('❌ WebSocket错误详情:', {
          readyState: this.ws?.readyState,
          url: wsUrl,
          error: error
        });
        this.emit('error', error);
      };

    } catch (error) {
      this.isConnecting = false;
      console.error('❌ WebSocket连接异常:', error);
      throw error;
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage) {
    const { type, ...data } = message;

    switch (type) {
      case 'CONNECTION_ESTABLISHED':
        console.log('🎉 WebSocket连接建立确认');
        break;

      case 'NEW_MESSAGE':
        console.log('📬 收到新消息:', data);
        this.emit('newMessage', data);
        break;

      case 'CONVERSATION_UPDATE':
        console.log('💬 对话更新:', data);
        this.emit('conversationUpdate', data);
        break;

      case 'MESSAGE_READ_STATUS':
        console.log('👁️ 消息已读状态更新:', data);
        this.emit('messageReadStatus', data);
        break;

      case 'TYPING_STATUS':
        console.log('⌨️ 打字状态:', data);
        this.emit('typingStatus', data);
        break;

      case 'USER_TYPING_START':
        console.log('⌨️ 用户开始输入:', data);
        this.emit('USER_TYPING_START', data);
        break;

      case 'USER_TYPING_STOP':
        console.log('⌨️ 用户停止输入:', data);
        this.emit('USER_TYPING_STOP', data);
        break;

      case 'ONLINE_STATUS':
        console.log('🟢 在线状态:', data);
        this.emit('ONLINE_STATUS', data);
        break;

      case 'USER_STATUS_CHANGE':
        console.log('🔄 用户状态变化:', data);
        this.emit('USER_STATUS_CHANGE', data);
        break;

      case 'PONG':
        // 心跳响应，无需处理
        break;

      default:
        console.log('❓ 未知消息类型:', type, data);
    }
  }

  /**
   * 发送消息
   */
  send(type: string, data: any = {}) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket未连接，无法发送消息:', type);
      return false;
    }

    try {
      const message = { type, ...data, timestamp: Date.now() };
      this.ws.send(JSON.stringify(message));
      console.log('📤 发送WebSocket消息:', message);
      return true;
    } catch (error) {
      console.error('❌ 发送WebSocket消息失败:', error);
      return false;
    }
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send('PING');
      }
    }, 30000); // 30秒心跳
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect() {
    if (this.isDestroyed || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('🚫 达到最大重连次数，停止重连');
      this.emit('maxReconnectAttemptsReached', null);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    console.log(`🔄 ${delay}ms后进行第${this.reconnectAttempts}次重连...`);

    setTimeout(() => {
      if (!this.isDestroyed) {
        this.connect().catch(error => {
          console.error('❌ 重连失败:', error);
        });
      }
    }, delay);
  }

  /**
   * 添加事件监听器
   */
  on(eventType: string, handler: (data: any) => void) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(eventType: string, handler?: (data: any) => void) {
    if (!this.eventHandlers[eventType]) return;

    if (handler) {
      const index = this.eventHandlers[eventType].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[eventType].splice(index, 1);
      }
    } else {
      this.eventHandlers[eventType] = [];
    }
  }

  /**
   * 触发事件
   */
  private emit(eventType: string, data: any) {
    const handlers = this.eventHandlers[eventType];
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`❌ 事件处理器执行失败 [${eventType}]:`, error);
        }
      });
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 发送用户开始输入状态
   */
  startTyping(conversationId: string) {
    if (!this.isConnected()) {
      return;
    }

    // 如果已经在同一个对话中输入，重置定时器但不重复发送
    if (this.currentTypingConversation === conversationId) {
      // 重置定时器
      const existingTimer = this.typingTimers.get(conversationId);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      // 设置新的自动停止timer
      const timer = setTimeout(() => {
        this.stopTyping();
      }, 3000);

      this.typingTimers.set(conversationId, timer);
      return;
    }

    // 停止之前的typing状态
    this.stopTyping();

    this.currentTypingConversation = conversationId;

    this.send('TYPING_START', {
      conversationId: conversationId
    });

    // 设置自动停止timer（3秒后自动停止）
    const timer = setTimeout(() => {
      this.stopTyping();
    }, 3000);

    this.typingTimers.set(conversationId, timer);

    console.log('⌨️ 开始输入状态:', conversationId);
  }

  /**
   * 发送用户停止输入状态
   */
  stopTyping() {
    if (!this.currentTypingConversation) {
      return;
    }

    const conversationId = this.currentTypingConversation;
    this.currentTypingConversation = null;

    // 清理timer
    const timer = this.typingTimers.get(conversationId);
    if (timer) {
      clearTimeout(timer);
      this.typingTimers.delete(conversationId);
    }

    if (this.isConnected()) {
      this.send('TYPING_STOP', {
        conversationId: conversationId
      });
    }

    console.log('⌨️ 停止输入状态:', conversationId);
  }

  /**
   * 请求在线状态
   */
  requestOnlineStatus() {
    if (!this.isConnected()) {
      return;
    }

    this.send('GET_ONLINE_STATUS');
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.isDestroyed = true;
    this.stopHeartbeat();

    // 清理所有typing状态
    this.stopTyping();
    this.typingTimers.forEach(timer => clearTimeout(timer));
    this.typingTimers.clear();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.eventHandlers = {};
    console.log('🔌 WebSocket连接已断开');
  }

  /**
   * 获取连接状态信息
   */
  getStatus() {
    return {
      connected: this.isConnected(),
      connecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      destroyed: this.isDestroyed
    };
  }
}
