export enum ProductStatus {
    PENDING_APPROVAL = 'PENDING_APPROVAL',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    DELISTED = 'DELISTED',
}

export interface Product {
    id: number;
    name: string;
    price: number;
    description: string;
    imageUrl: string;
    downloadUrl: string;
    authorName: string;
    authorId?: number;
    authorAvatarUrl?: string;
    createdAt: string;
    updatedAt?: string;
    reviewedAt?: string;
    status: ProductStatus;
    rejectionReason?: string;
    approvalNotes?: string;
    reviewedBy?: string;
}

export interface ProductStatusHistory {
    id: number;
    productId: number;
    oldStatus?: ProductStatus;
    newStatus: ProductStatus;
    changedBy: string;
    changedAt: string;
    reason?: string;
    notes?: string;
}