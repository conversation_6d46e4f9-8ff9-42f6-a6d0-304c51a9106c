package com.kitolus.community.dto;

import com.kitolus.community.entity.ProductStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
public class ProductDTO {
    private Long id;
    private String name;
    private BigDecimal price;
    private String description;
    private String imageUrl;
    private String downloadUrl;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    private Timestamp reviewedAt;
    private String status;
    private String rejectionReason;
    private String approvalNotes;
    private String reviewedBy;

    // Author Info
    private Long authorId;
    private String authorName;
    private String authorAvatarUrl;
} 