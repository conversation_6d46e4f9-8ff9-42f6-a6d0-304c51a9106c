package com.kitolus.community.controller;

import com.kitolus.community.dto.CreateCommentRequestDTO;
import com.kitolus.community.dto.CreatePostRequestDTO;
import com.kitolus.community.dto.UpdatePostRequestDTO;
import com.kitolus.community.dto.UpdatePostResponseDTO;
import com.kitolus.community.entity.CommunityComment;
import com.kitolus.community.entity.CommunityPost;
import com.kitolus.community.service.CommunityService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/community")
@RequiredArgsConstructor
public class CommunityController {

    private final CommunityService communityService;

    @GetMapping("/posts")
    public ResponseEntity<List<CommunityPost>> getAllPosts() {
        List<CommunityPost> posts = communityService.getAllPosts();
        return ResponseEntity.ok(posts);
    }

    @GetMapping("/posts/{id}")
    public ResponseEntity<CommunityPost> getPostById(@PathVariable Long id) {
        CommunityPost post = communityService.getPostById(id);
        return ResponseEntity.ok(post);
    }

    @PostMapping("/posts/{id}/like")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Boolean> togglePostLike(@PathVariable Long id) {
        boolean isLiked = communityService.toggleLike(id);
        return ResponseEntity.ok(isLiked);
    }

    @PostMapping("/posts")
    @PreAuthorize("isAuthenticated()") // Ensures only logged-in users can create posts
    public ResponseEntity<CommunityPost> createPost(@Valid @RequestBody CreatePostRequestDTO requestDTO) {
        CommunityPost createdPost = communityService.createPost(requestDTO);
        return new ResponseEntity<>(createdPost, HttpStatus.CREATED);
    }

    @PutMapping("/posts/{id}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UpdatePostResponseDTO> updatePost(@PathVariable Long id, @Valid @RequestBody UpdatePostRequestDTO requestDTO) {
        UpdatePostResponseDTO response = communityService.updatePost(id, requestDTO);
        return ResponseEntity.ok(response);
    }

    // --- Comment Endpoints ---

    @GetMapping("/posts/{postId}/comments")
    public ResponseEntity<List<CommunityComment>> getCommentsForPost(@PathVariable Long postId) {
        List<CommunityComment> comments = communityService.getCommentsByPostId(postId);
        return ResponseEntity.ok(comments);
    }

    @PostMapping("/comments")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<CommunityComment> createComment(@Valid @RequestBody CreateCommentRequestDTO commentDTO) {
        CommunityComment createdComment = communityService.createComment(commentDTO);
        return new ResponseEntity<>(createdComment, HttpStatus.CREATED);
    }

    @DeleteMapping("/posts/{postId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> deletePost(@PathVariable Long postId) {
        communityService.deletePost(postId);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/comments/{commentId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> deleteComment(@PathVariable Long commentId) {
        communityService.deleteComment(commentId);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/posts/{id}/pin")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<CommunityPost> togglePinPost(@PathVariable Long id) {
        CommunityPost post = communityService.togglePinPost(id);
        return ResponseEntity.ok(post);
    }

    @GetMapping("/posts/pinned")
    public ResponseEntity<List<CommunityPost>> getPinnedPosts() {
        List<CommunityPost> pinnedPosts = communityService.getPinnedPosts();
        return ResponseEntity.ok(pinnedPosts);
    }

    @GetMapping("/posts/non-pinned")
    public ResponseEntity<List<CommunityPost>> getNonPinnedPosts() {
        List<CommunityPost> posts = communityService.getNonPinnedPosts();
        return ResponseEntity.ok(posts);
    }
}
