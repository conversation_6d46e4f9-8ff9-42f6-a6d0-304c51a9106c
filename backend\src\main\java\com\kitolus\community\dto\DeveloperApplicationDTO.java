package com.kitolus.community.dto;

import com.kitolus.community.entity.DeveloperApplicationStatus;
import lombok.Data;
import java.sql.Timestamp;

@Data
public class DeveloperApplicationDTO {
    private Long id;
    private Long userId;
    private String realName;
    private String identityNumber;
    private String message;
    private String status;
    private Timestamp createdAt;
    private String username;
    private String userAvatarUrl;
    private String userSerialNumber;
} 