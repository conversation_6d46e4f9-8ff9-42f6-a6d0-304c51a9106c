'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Package, Briefcase, ShieldCheck, DollarSign, PackageCheck, Users, UploadCloud, Landmark } from 'lucide-react';
import AvatarDisplay from '@/components/AvatarDisplay';
import MainLayout from '@/components/MainLayout';
import dynamic from 'next/dynamic';

const CreateProductForm = dynamic(() => import('./components/CreateProductForm').then(mod => mod.default), { ssr: false });
const DeveloperApplicationForm = dynamic(() => import('./components/DeveloperApplicationForm').then(mod => mod.default), { ssr: false });
const MyProductsDashboard = dynamic(() => import('./components/MyProductsDashboard').then(mod => mod.default), { ssr: false });
const MyEarningsDashboard = dynamic(() => import('./components/MyEarningsDashboard').then(mod => mod.default), { ssr: false });
const ProductApprovalDashboard = dynamic(() => import('./components/ProductApprovalDashboard').then(mod => mod.default), { ssr: false });
const DeveloperApplicationDashboard = dynamic(() => import('./components/DeveloperApplicationDashboard').then(mod => mod.default), { ssr: false });
const MyPurchasesDashboard = dynamic(() => import('./components/MyPurchasesDashboard').then(mod => mod.default), { ssr: false });
const UserManagementDashboard = dynamic(() => import('./components/UserManagementDashboard').then(mod => mod.default), { ssr: false });
const ProductManagementDashboard = dynamic(() => import('./components/ProductManagementDashboard').then(mod => mod.default), { ssr: false });
const WithdrawalManagementDashboard = dynamic(() => import('./components/WithdrawalManagementDashboard').then(mod => mod.WithdrawalManagementDashboard), { ssr: false });
const WithdrawalSettingsDashboard = dynamic(() => import('./components/WithdrawalSettingsDashboard').then(mod => mod.default), { ssr: false });
const AdminDataCenterDashboard = dynamic(() => import('./components/AdminDataCenterDashboard').then(mod => mod.default), { ssr: false });


// --- Main Dashboard Page ---
type View = 'myPurchases' | 'becomeDeveloper' | 'myProducts' | 'createProduct' | 'myEarnings' | 'productApprovals' | 'developerApplications' | 'userManagement' | 'productManagement' | 'withdrawalManagement' | 'withdrawalSettings' | 'adminDataCenter';

const DashboardPage = () => {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const [isMounted, setIsMounted] = useState(false);
    const [isInitialViewSetted, setIsInitialViewSetted] = useState(false);

    // Unified state for the active view
    const [activeView, setActiveView] = useState<View>('myPurchases');

    useEffect(() => {
        setIsMounted(true);
        if (!authLoading && !user) {
            router.push('/login');
        }
    }, [authLoading, user, router]);

    const isAdmin = user?.role === 'KitolusAdmin';
    const isDeveloper = user?.role === 'DEVELOPER';

    // Set the default view based on user role once user data is available
    useEffect(() => {
        if (user && !isInitialViewSetted) {
            if (isAdmin) {
                setActiveView('adminDataCenter');
            } else if (isDeveloper) {
                setActiveView('myProducts');
            } else {
                setActiveView('myPurchases');
            }
            setIsInitialViewSetted(true);
        }
    }, [user, isAdmin, isDeveloper, isInitialViewSetted]);

    const navItems = useMemo(() => {
        const items: { key: View; text: string; icon: React.ElementType; group: string }[] = [];

        if (isAdmin) {
            items.push(
                { key: 'adminDataCenter', text: '数据中心', icon: DollarSign, group: '管理面板' },
                { key: 'userManagement', text: '用户管理', icon: Users, group: '管理面板' },
                { key: 'productManagement', text: '商品管理', icon: Package, group: '管理面板' },
                { key: 'createProduct', text: '上架产品', icon: UploadCloud, group: '管理面板' },
                { key: 'productApprovals', text: '产品审核', icon: PackageCheck, group: '管理面板' },
                { key: 'developerApplications', text: '开发者申请', icon: ShieldCheck, group: '管理面板' },
                { key: 'withdrawalManagement', text: '提现管理', icon: DollarSign, group: '管理面板' }
            );
        }
        
        if (isDeveloper) {
            items.push(
                { key: 'myProducts', text: '我的产品', icon: Package, group: '开发者中心' },
                { key: 'createProduct', text: '上架产品', icon: UploadCloud, group: '开发者中心' },
                { key: 'myEarnings', text: '我的收益', icon: DollarSign, group: '开发者中心' },
                { key: 'withdrawalSettings', text: '提现设置', icon: Landmark, group: '开发者中心' }
            );
        }
        
        items.push({ key: 'myPurchases', text: '我的购买', icon: Briefcase, group: '用户中心' });

        if (!isDeveloper && !isAdmin) {
             items.push({ key: 'becomeDeveloper', text: '成为开发者', icon: Briefcase, group: '用户中心' });
        }
        
        return items;
    }, [isAdmin, isDeveloper]);

    const renderContent = () => {
        switch (activeView) {
            case 'myPurchases':
                return <MyPurchasesDashboard />;
            case 'becomeDeveloper':
                return <DeveloperApplicationForm />;
            case 'myProducts':
                return <MyProductsDashboard />;
            case 'createProduct':
                return <CreateProductForm onProductCreated={() => setActiveView('myProducts')} />;
            case 'myEarnings':
                return <MyEarningsDashboard />;
            case 'adminDataCenter':
                return <AdminDataCenterDashboard />;
            case 'productApprovals':
                return <ProductApprovalDashboard />;
            case 'developerApplications':
                return <DeveloperApplicationDashboard />;
            case 'userManagement':
                return <UserManagementDashboard />;
            case 'productManagement':
                return <ProductManagementDashboard />;
            case 'withdrawalManagement':
                return <WithdrawalManagementDashboard />;
            case 'withdrawalSettings':
                return <WithdrawalSettingsDashboard />;
            default:
                return <Card><CardContent><p>请从左侧选择一个面板。</p></CardContent></Card>;
        }
    };

    if (authLoading || !isMounted || !user) {
        return (
             <div className="relative flex justify-center items-center h-screen">
                <div
                    className="absolute inset-0 bg-cover bg-center bg-fixed"
                    style={{ backgroundImage: "url('/images/background/background.webp')" }}
                />
                <div className="absolute inset-0 bg-black/50" />
                <div className="text-white relative z-10">加载仪表盘...</div>
            </div>
        );
    }
    
    const groupedNavItems = navItems.reduce((acc, item) => {
        (acc[item.group] = acc[item.group] || []).push(item);
        return acc;
    }, {} as Record<string, typeof navItems>);
    
    return (
        <MainLayout>
             <div className="relative h-full min-h-screen bg-background">
                <div
                    className="absolute inset-0 bg-cover bg-center bg-fixed"
                    style={{ backgroundImage: "url('/images/background/background.webp')" }}
                />
                <div className="absolute inset-0 bg-black/30" />
                <div className="relative z-10 h-full overflow-y-auto pt-12">
                    <div className="container mx-auto p-4 md:p-8">
                        <div className="flex flex-col md:flex-row gap-8">
                             <aside className="w-full md:w-1/4 lg:w-1/5">
                                {Object.entries(groupedNavItems).map(([groupName, items]) => (
                                    <nav key={groupName} className="mb-6 flex flex-col gap-2 p-4 rounded-xl bg-background/50 backdrop-blur-sm border border-border/50">
                                        <h3 className="px-3 pb-2 text-lg font-semibold text-white border-b border-border/50">{groupName}</h3>
                                        {items.map(item => (
                                            <Button
                                                key={item.key}
                                                variant={activeView === item.key ? 'secondary' : 'ghost'}
                                                className="justify-start gap-3 px-3 h-12 text-base text-white hover:bg-white/10 hover:text-white"
                                                onClick={() => setActiveView(item.key)}
                                            >
                                                <item.icon className="h-5 w-5" />
                                                <span>{item.text}</span>
                                            </Button>
                                        ))}
                                    </nav>
                                ))}
                             </aside>
                             
                             <main className="w-full md:w-3/4 lg:w-4/5 p-6 rounded-xl bg-background/50 backdrop-blur-sm border border-border/50">
                                {renderContent()}
                             </main>
                        </div>
                    </div>
                </div>
             </div>
        </MainLayout>
    );
};

export default DashboardPage; 