package com.kitolus.community.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<?> handleBadCredentialsException(BadCredentialsException ex, WebRequest request) {
        logger.warn("认证失败: {}", ex.getMessage());
        return createErrorResponse(ex, HttpStatus.UNAUTHORIZED, "Authentication Failed", request, "用户名或密码错误");
    }

    @ExceptionHandler(DuplicateResourceException.class)
    public ResponseEntity<?> handleDuplicateResourceException(DuplicateResourceException ex, WebRequest request) {
        logger.warn("重复资源错误: {}", ex.getMessage());
        return createErrorResponse(ex, HttpStatus.CONFLICT, "Duplicate Resource", request);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<?> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        logger.warn("资源未找到: {}", ex.getMessage());
        return createErrorResponse(ex, HttpStatus.NOT_FOUND, "Resource Not Found", request);
    }

    @ExceptionHandler(InvalidDataException.class)
    public ResponseEntity<?> handleInvalidDataException(InvalidDataException ex, WebRequest request) {
        logger.warn("无效数据错误: {}", ex.getMessage());
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST, "Invalid Data", request);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> handleValidationExceptions(MethodArgumentNotValidException ex, WebRequest request) {
        String errors = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        logger.warn("验证错误: {}", errors);

        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", System.currentTimeMillis());
        body.put("status", HttpStatus.BAD_REQUEST.value());
        body.put("error", "Validation Failed");
        body.put("message", errors);
        body.put("path", request.getDescription(false).substring(4));

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(BindException.class)
    public ResponseEntity<?> handleBindException(BindException ex, WebRequest request) {
        String errors = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        logger.warn("绑定错误: {}", errors);

        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", System.currentTimeMillis());
        body.put("status", HttpStatus.BAD_REQUEST.value());
        body.put("error", "Binding Failed");
        body.put("message", errors);
        body.put("path", request.getDescription(false).substring(4));

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleGlobalException(Exception ex, WebRequest request) {
        logger.error("发生未预期的错误: ", ex);
        String errorMessage = "服务器内部发生未知错误，请联系管理员。";
        if (ex.getCause() != null) {
            errorMessage += " 原因: " + ex.getCause().getMessage();
        }
        return createErrorResponse(ex, HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", request, errorMessage);
    }

    private ResponseEntity<?> createErrorResponse(Exception ex, HttpStatus status, String error, WebRequest request) {
        return createErrorResponse(ex, status, error, request, ex.getMessage());
    }

    private ResponseEntity<?> createErrorResponse(Exception ex, HttpStatus status, String error, WebRequest request, String message) {
        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", System.currentTimeMillis());
        body.put("status", status.value());
        body.put("error", error);
        body.put("message", message);
        body.put("path", request.getDescription(false).substring(4)); // remove "uri="

        return new ResponseEntity<>(body, status);
    }
} 