'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

export default function TestWebSocketPage() {
  const { user, isAuthenticated } = useAuth();
  const [wsStatus, setWSStatus] = useState({
    isConnected: false,
    connectionState: 'disconnected',
    lastMessage: null as any,
    messageCount: 0,
  });

  useEffect(() => {
    // 监听WebSocket事件
    const handleConnect = () => {
      console.log('WebSocket连接成功');
      setWSStatus(prev => ({
        ...prev,
        isConnected: true,
        connectionState: 'connected',
      }));
    };

    const handleDisconnect = () => {
      console.log('WebSocket连接断开');
      setWSStatus(prev => ({
        ...prev,
        isConnected: false,
        connectionState: 'disconnected',
      }));
    };

    const handleMessage = (data: any) => {
      console.log('收到WebSocket消息:', data);
      setWSStatus(prev => ({
        ...prev,
        lastMessage: data,
        messageCount: prev.messageCount + 1,
      }));
    };

    const handleError = (error: any) => {
      console.error('WebSocket错误:', error);
      setWSStatus(prev => ({
        ...prev,
        connectionState: 'error',
      }));
    };

    // 添加事件监听器
    unifiedWebSocketManager.on('connected', handleConnect);
    unifiedWebSocketManager.on('disconnected', handleDisconnect);
    unifiedWebSocketManager.on('CONNECTION_ESTABLISHED', handleMessage);
    unifiedWebSocketManager.on('ONLINE_STATUS', handleMessage);
    unifiedWebSocketManager.on('error', handleError);

    // 定期更新状态
    const interval = setInterval(() => {
      setWSStatus(prev => ({
        ...prev,
        isConnected: unifiedWebSocketManager.isConnected,
        connectionState: unifiedWebSocketManager.connectionState,
      }));
    }, 1000);

    return () => {
      clearInterval(interval);
      unifiedWebSocketManager.off('connected', handleConnect);
      unifiedWebSocketManager.off('disconnected', handleDisconnect);
      unifiedWebSocketManager.off('CONNECTION_ESTABLISHED', handleMessage);
      unifiedWebSocketManager.off('ONLINE_STATUS', handleMessage);
      unifiedWebSocketManager.off('error', handleError);
    };
  }, []);

  const handleConnect = () => {
    const token = localStorage.getItem('jwt_token');
    if (token) {
      console.log('🔄 手动初始化WebSocket连接');
      unifiedWebSocketManager.initialize(token);
    } else {
      console.error('❌ 没有找到JWT token');
    }
  };

  const handleDisconnect = () => {
    unifiedWebSocketManager.disconnect();
  };

  const handleSendPing = () => {
    unifiedWebSocketManager.send({ type: 'PING', timestamp: Date.now() });
  };

  const handleRequestOnlineStatus = () => {
    unifiedWebSocketManager.requestOnlineStatus();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">WebSocket 连接测试</h1>
        <p className="text-muted-foreground mt-2">
          测试统一WebSocket管理器的连接和消息处理
        </p>
      </div>

      {/* 用户状态 */}
      <Card>
        <CardHeader>
          <CardTitle>用户状态</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center gap-2">
            <span>认证状态:</span>
            <Badge variant={isAuthenticated ? "default" : "destructive"}>
              {isAuthenticated ? "已登录" : "未登录"}
            </Badge>
          </div>
          {user && (
            <div>用户: {user.username} (ID: {user.id})</div>
          )}
        </CardContent>
      </Card>

      {/* WebSocket状态 */}
      <Card>
        <CardHeader>
          <CardTitle>WebSocket 状态</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <span>连接状态:</span>
            <Badge variant={wsStatus.isConnected ? "default" : "destructive"}>
              {wsStatus.connectionState}
            </Badge>
          </div>
          <div>消息计数: {wsStatus.messageCount}</div>
          {wsStatus.lastMessage && (
            <div className="space-y-2">
              <div className="font-semibold">最后消息:</div>
              <pre className="bg-muted p-2 rounded text-sm overflow-auto">
                {JSON.stringify(wsStatus.lastMessage, null, 2)}
              </pre>
            </div>
          )}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={handleConnect} variant="default" size="sm">
              连接
            </Button>
            <Button onClick={handleDisconnect} variant="destructive" size="sm">
              断开
            </Button>
            <Button onClick={handleSendPing} variant="outline" size="sm">
              发送Ping
            </Button>
            <Button onClick={handleRequestOnlineStatus} variant="outline" size="sm">
              请求在线状态
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试步骤</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>1. 确保已登录</p>
          <p>2. 点击"连接"按钮建立WebSocket连接</p>
          <p>3. 观察连接状态变化</p>
          <p>4. 点击"发送Ping"测试消息发送</p>
          <p>5. 点击"请求在线状态"测试特定消息</p>
          <p>6. 在浏览器控制台查看详细日志</p>
        </CardContent>
      </Card>
    </div>
  );
}
