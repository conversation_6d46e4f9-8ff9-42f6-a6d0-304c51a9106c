'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { flushSync } from 'react-dom';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  Send,
  Smile,
  Paperclip,
  MoreVertical,
  User,
  ArrowLeft,
  Check,
  CheckCheck,
  Loader2,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { getConversationMessages, sendPrivateMessage, markMessagesAsRead } from '@/services/messageApi';
import { Message as MessageType } from '@/types/Message';

import { useMessageStore } from '@/stores/messageStore';
import { WebSocketStatus } from './WebSocketStatus';
import { useGlobalWebSocket } from '@/contexts/GlobalWebSocketContext';
import { OnlineStatusIndicator, TypingIndicator } from '@/components/ui/OnlineStatusIndicator';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

interface ChatMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: Date;
  isRead: boolean;
  type: 'text' | 'image' | 'file' | 'error';
}

interface ConversationUser {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
}

interface ApiMessage {
  id?: number | string;
  content?: string;
  senderId?: number | string;
  sender_id?: number | string;
  senderName?: string;
  sender_name?: string;
  senderAvatar?: string;
  sender_avatar?: string;
  timestamp?: string;
  created_at?: string;
  isRead?: boolean;
  is_read?: boolean;
  type?: string;
}

interface ApiResponse {
  success?: boolean;
  data?: {
    messages?: ApiMessage[];
    id?: number | string;
  };
  messages?: ApiMessage[];
  messageId?: number | string;
  otherUser?: {
    id?: number | string;
    username?: string;
    name?: string;
    fullAvatarUrl?: string;
    avatar?: string;
    isOnline?: boolean;
  };
}

// 消息格式化工具函数
const formatApiMessage = (msg: ApiMessage): ChatMessage => ({
  id: msg.id?.toString() || Math.random().toString(),
  content: msg.content || '',
  senderId: msg.senderId?.toString() || msg.sender_id?.toString() || '',
  senderName: msg.senderName || msg.sender_name || '未知用户',
  senderAvatar: msg.senderAvatar || msg.sender_avatar || '',
  timestamp: new Date(msg.timestamp || msg.created_at || Date.now()),
  isRead: msg.isRead !== undefined ? msg.isRead : msg.is_read !== undefined ? msg.is_read : false,
  type: (msg.type === 'image' || msg.type === 'file' || msg.type === 'error') ? msg.type : 'text'
});

interface RealTimeChatInterfaceProps {
  conversationId: string | null;
  onBack?: () => void;
  onClose?: () => void;
  onClearUnread?: (conversationId: string) => void;
}

export const RealTimeChatInterface: React.FC<RealTimeChatInterfaceProps> = ({
  conversationId,
  onBack,
  onClose,
  onClearUnread
}) => {
  const { user, token } = useAuth();
  const router = useRouter();
  const { markConversationAsRead } = useMessageStore();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [otherUser, setOtherUser] = useState<ConversationUser | null>(null);
  const [serverError, setServerError] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const perfectWsRef = useRef<typeof unifiedWebSocketManager | null>(unifiedWebSocketManager);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageTimestamp = useRef<number>(0);
  const newMessageRef = useRef<string>('');
  const pollingErrorCountRef = useRef<number>(0);
  const pollingIntervalTimeRef = useRef<number>(3000); // 动态轮询间隔
  const lastActivityRef = useRef<number>(Date.now()); // 最后活动时间
  const typingDebounceRef = useRef<NodeJS.Timeout | null>(null);

  // 使用全局WebSocket和实时状态
  const globalWebSocket = useGlobalWebSocket();

  // 实时状态管理（保持向后兼容）
  const realtimeStatus = {
    isUserOnline: globalWebSocket.isUserOnline,
    isUserTyping: globalWebSocket.isUserTyping,
    startTyping: globalWebSocket.startTyping,
    stopTyping: globalWebSocket.stopTyping,
    isConnected: globalWebSocket.isConnected
  };

  // 消息去重辅助函数 - 优化性能
  const isDuplicateMessage = useCallback((newMsg: ChatMessage, existingMessages: ChatMessage[]): boolean => {
    // 优化：只检查最近的20条消息，避免全量扫描
    const recentMessages = existingMessages.slice(-20);

    return recentMessages.some(existing => {
      // 1. 相同ID - 最快的检查
      if (existing.id === newMsg.id) return true;

      // 2. 检查是否是临时消息对应的服务器消息
      if (existing.id.startsWith('temp_') &&
          existing.content === newMsg.content &&
          existing.senderId === newMsg.senderId &&
          Math.abs(existing.timestamp.getTime() - newMsg.timestamp.getTime()) < 10000) {
        return true;
      }

      // 3. 相同内容、发送者、时间戳（容忍5秒差异）
      if (existing.content === newMsg.content &&
          existing.senderId === newMsg.senderId &&
          Math.abs(existing.timestamp.getTime() - newMsg.timestamp.getTime()) < 5000) {
        return true;
      }

      return false;
    });
  }, []);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // 消息缓存 - 限制缓存大小避免内存泄漏
  const messageCache = useRef<Map<string, ChatMessage[]>>(new Map());
  const loadingCache = useRef<Set<string>>(new Set());
  const userCache = useRef<Map<string, ConversationUser>>(new Map()); // 用户信息缓存
  const MAX_CACHE_SIZE = 10; // 最多缓存10个对话

  // 清理缓存
  const cleanupCache = useCallback(() => {
    if (messageCache.current.size > MAX_CACHE_SIZE) {
      const entries = Array.from(messageCache.current.entries());
      // 保留最近的对话，删除最旧的
      const toDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE);
      toDelete.forEach(([key]) => messageCache.current.delete(key));
    }
    // 清理用户缓存
    if (userCache.current.size > MAX_CACHE_SIZE) {
      const userEntries = Array.from(userCache.current.entries());
      const toDeleteUsers = userEntries.slice(0, userEntries.length - MAX_CACHE_SIZE);
      toDeleteUsers.forEach(([key]) => userCache.current.delete(key));
    }
  }, []);

  // 获取用户信息（带缓存）
  const getUserInfo = useCallback(async (otherUserId: number): Promise<ConversationUser> => {
    const cacheKey = otherUserId.toString();

    // 检查缓存
    if (userCache.current.has(cacheKey)) {
      return userCache.current.get(cacheKey)!;
    }

    try {
      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const userResponse = await fetch(`/api/user/${otherUserId}`, {
        headers: {
          'Authorization': `Bearer ${token || localStorage.getItem('jwt_token')}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (userResponse.ok) {
        const userData = await userResponse.json();

        const userInfo: ConversationUser = {
          id: otherUserId.toString(),
          name: userData.username || `用户${otherUserId}`,
          avatar: userData.fullAvatarUrl || userData.avatarUrl || '',
          isOnline: false
        };

        // 缓存用户信息
        userCache.current.set(cacheKey, userInfo);
        return userInfo;
      }
    } catch (error: any) {
      // 静默处理错误
    }

    // 默认用户信息
    const defaultUser: ConversationUser = {
      id: otherUserId.toString(),
      name: `用户${otherUserId}`,
      avatar: '',
      isOnline: false
    };

    // 缓存默认信息
    userCache.current.set(cacheKey, defaultUser);
    return defaultUser;
  }, [token]);

  // 加载历史消息
  const loadMessages = useCallback(async (forceReload = false) => {
    if (!conversationId || !user) return;

    // 检查缓存
    if (!forceReload && messageCache.current.has(conversationId)) {
      const cachedMessages = messageCache.current.get(conversationId)!;
      setMessages(cachedMessages);
      setLoadingMessages(false);
      // 清理缓存
      cleanupCache();
      return;
    }

    // 防止重复加载
    if (loadingCache.current.has(conversationId)) {
      return;
    }

    try {
      setLoadingMessages(true);
      // 在加载期间清空消息列表，避免显示不完整的数据
      setMessages([]);
      loadingCache.current.add(conversationId);

      // 从conversationId中提取用户ID
      const otherUserId = parseInt(conversationId.replace('conv_', ''));

      const response = await getConversationMessages(otherUserId);

      if (response.messages && Array.isArray(response.messages)) {
        console.log(`加载消息: 共 ${response.messages.length} 条消息`);

        // 转换消息格式
        const formattedMessages: ChatMessage[] = response.messages.map((msg: ApiMessage) => ({
          ...formatApiMessage(msg),
          isRead: msg.isRead !== undefined ? msg.isRead : msg.is_read !== undefined ? msg.is_read : true
        }));

        // 验证消息完整性
        const myMessages = formattedMessages.filter(msg => msg.senderId === user.id.toString());
        const theirMessages = formattedMessages.filter(msg => msg.senderId === otherUserId.toString());
        console.log(`消息统计: 我的消息 ${myMessages.length} 条，对方消息 ${theirMessages.length} 条`);

        // 确保消息按时间正确排序（最新的在最后）
        const sortedMessages = formattedMessages.sort((a, b) =>
          a.timestamp.getTime() - b.timestamp.getTime()
        );

        // 只有在消息数据完整时才更新界面
        if (sortedMessages.length > 0) {
          // 检查是否有明显的数据不完整问题
          const hasValidSenders = sortedMessages.every(msg => msg.senderId && msg.senderId !== '');
          if (!hasValidSenders) {
            console.warn('检测到消息数据不完整，将重试加载');
            // 延迟重试
            setTimeout(() => {
              if (conversationId) {
                loadMessages(true);
              }
            }, 1000);
            return;
          }
        }

        // 缓存消息
        messageCache.current.set(conversationId, sortedMessages);

        // 使用批量更新避免中间状态
        // 添加短暂延迟确保状态完全更新
        setTimeout(() => {
          setMessages(sortedMessages);
          console.log(`消息加载完成: 共 ${sortedMessages.length} 条消息`);
        }, 50);

        // 设置最新消息时间戳
        if (sortedMessages.length > 0) {
          lastMessageTimestamp.current = Math.max(...sortedMessages.map((msg: ChatMessage) => msg.timestamp.getTime()));
        }

        // 异步标记未读消息为已读，不阻塞UI
        const unreadMessages = sortedMessages.filter(msg =>
          !msg.isRead && msg.senderId !== user?.id.toString()
        );
        if (unreadMessages.length > 0) {
          // 立即更新本地状态
          const updatedMessages = sortedMessages.map(msg =>
            unreadMessages.some(unread => unread.id === msg.id)
              ? { ...msg, isRead: true }
              : msg
          );
          messageCache.current.set(conversationId, updatedMessages);
          setMessages(updatedMessages);

          // 异步标记服务器端已读状态
          const messageIds = unreadMessages
            .map(msg => parseInt(msg.id))
            .filter(id => !isNaN(id));

          if (messageIds.length > 0) {


            // 只标记消息为已读，不调用markConversationAsRead避免Immer错误
            markMessagesAsRead(messageIds).then(() => {


              // 通过事件机制更新对话状态，而不是直接调用store函数
              window.dispatchEvent(new CustomEvent('conversationReadRequest', {
                detail: {
                  conversationId,
                  source: 'RealTimeChatInterface-loadMessages'
                }
              }));

            }).catch(error => {
              console.error('📨 [RealTimeChatInterface] 标记消息已读失败:', error);
            });
          }
        }

        // 设置对话用户信息 - 简化逻辑，优先从消息中获取
        let userInfo: ConversationUser | null = null;

        // 1. 优先使用API返回的用户信息
        if (response.otherUser) {
          userInfo = {
            id: response.otherUser.id?.toString() || otherUserId.toString(),
            name: response.otherUser.username || response.otherUser.name || `用户${otherUserId}`,
            avatar: response.otherUser.fullAvatarUrl || response.otherUser.avatar || '',
            isOnline: response.otherUser.isOnline || false
          };
          console.log('使用API返回的用户信息:', userInfo);
        }

        // 2. 从消息中提取对方用户信息
        else if (sortedMessages.length > 0) {
          const otherUserMessage = sortedMessages.find(msg => msg.senderId !== user?.id.toString());
          if (otherUserMessage) {
            userInfo = {
              id: otherUserMessage.senderId,
              name: otherUserMessage.senderName || `用户${otherUserId}`,
              avatar: otherUserMessage.senderAvatar || '',
              isOnline: false
            };
          }
        }

        // 3. 设置用户信息或异步获取
        if (userInfo) {
          setOtherUser(userInfo);
        } else {
          // 先设置基本信息，避免空白
          setOtherUser({
            id: otherUserId.toString(),
            name: `用户${otherUserId}`,
            avatar: '',
            isOnline: false
          });
          // 异步获取完整用户信息
          getUserInfo(otherUserId).then(setOtherUser).catch(() => {
            // 静默处理错误
          });
        }
      } else {
        console.warn('未收到有效的消息数据，将重试加载');
        setMessages([]);
        // 如果没有收到有效数据，延迟重试一次
        setTimeout(() => {
          if (conversationId && !loadingCache.current.has(conversationId)) {
            console.log('重试加载消息...');
            loadMessages(true);
          }
        }, 2000);
      }
    } catch (error) {
      console.error('加载消息失败:', error);
      // 加载消息失败
      setMessages([]);
      // 异步获取用户信息，不阻塞UI
      const otherUserId = parseInt(conversationId.replace('conv_', ''));
      getUserInfo(otherUserId).then(setOtherUser);
    } finally {
      setLoadingMessages(false);
      loadingCache.current.delete(conversationId);
    }
  }, [conversationId, token, user, markConversationAsRead, getUserInfo]);

  // 当conversationId变化时加载消息
  useEffect(() => {
    if (conversationId) {


      // 清理之前的状态，确保干净的开始
      setMessages([]);
      setOtherUser(null);
      setLoadingMessages(true);

      // 清理相关缓存，确保获取最新数据
      messageCache.current.delete(conversationId);

      // 🔥 关键修复：立即触发未读消息清除
      // 当用户打开对话界面时，应该立即清除该对话的未读消息


      if (onClearUnread) {
        onClearUnread(conversationId);
      } else {
        // 备用方案：使用事件
        window.dispatchEvent(new CustomEvent('conversationReadRequest', {
          detail: {
            conversationId,
            source: 'RealTimeChatInterface-mount'
          }
        }));
      }

      // 直接加载最新消息，不使用缓存
      loadMessages(false);
    } else {
      setMessages([]);
      setOtherUser(null);
      setLoadingMessages(false);
    }
  }, [conversationId]);

  // 监听会话删除事件
  useEffect(() => {
    const handleConversationDeleted = (event: CustomEvent) => {
      const { conversationId: deletedConversationId } = event.detail;
      // 如果当前查看的会话被删除，返回对话列表
      if (deletedConversationId === conversationId) {
        console.log(`当前会话 ${conversationId} 被删除，返回对话列表`);
        if (onBack) {
          onBack();
        }
      }
    };

    window.addEventListener('conversationDeleted', handleConversationDeleted as EventListener);

    return () => {
      window.removeEventListener('conversationDeleted', handleConversationDeleted as EventListener);
    };
  }, [conversationId, onBack]);

  // 同步newMessage到ref
  useEffect(() => {
    newMessageRef.current = newMessage;
  }, [newMessage]);

  // 启动轮询作为备选方案
  const MAX_POLLING_ERRORS = 3;

  // 🚀 智能轮询间隔调整 - 根据活动状态动态调整
  const adjustPollingInterval = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;

    if (timeSinceLastActivity < 30000) {
      // 30秒内有活动 - 快速轮询
      pollingIntervalTimeRef.current = 1000; // 1秒
    } else if (timeSinceLastActivity < 120000) {
      // 2分钟内有活动 - 中等轮询
      pollingIntervalTimeRef.current = 3000; // 3秒
    } else {
      // 长时间无活动 - 慢速轮询
      pollingIntervalTimeRef.current = 10000; // 10秒
    }
  }, []);

  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    const poll = async () => {
      if (!conversationId || !user) return;

      try {
        // 调整轮询间隔
        adjustPollingInterval();

        const otherUserId = parseInt(conversationId.replace('conv_', ''));
        const response = await getConversationMessages(otherUserId);

        // 重置错误计数
        pollingErrorCountRef.current = 0;
        setServerError(false);

        // 检查不同的响应结构
        const messages = response.data?.messages || response.messages;
        if (messages && Array.isArray(messages)) {
          const formattedMessages = messages.map(formatApiMessage);

          // 只检查新消息（优化：只处理最后几条消息）
          const recentMessages = formattedMessages.slice(-10) as ChatMessage[]; // 只检查最后10条消息
          const newMessages = recentMessages.filter((msg) =>
            msg.timestamp.getTime() > lastMessageTimestamp.current
          );

          if (newMessages.length > 0) {
            setMessages(prev => {
              // 使用强化的去重检查过滤新消息
              const uniqueNewMessages = newMessages.filter((m) => {
                // 跳过临时ID的消息（这些是本地立即显示的消息）
                if (m.id.startsWith('temp_')) {
                  return false;
                }

                // 注释掉这个过滤，允许轮询获取所有消息以确保数据完整性
                // 轮询应该能够获取到所有消息，包括自己发送的消息
                // if (m.senderId === user?.id.toString()) {
                //   return false;
                // }

                // 使用强化去重检查
                return !isDuplicateMessage(m, prev);
              });

              if (uniqueNewMessages.length > 0) {
                // 更新最后活动时间
                lastActivityRef.current = Date.now();
                lastMessageTimestamp.current = Math.max(...uniqueNewMessages.map((m) => m.timestamp.getTime()));
                return [...prev, ...uniqueNewMessages];
              }
              return prev;
            });
          }
        }
      } catch (error: any) {
        pollingErrorCountRef.current++;
        console.warn(`轮询失败 (${pollingErrorCountRef.current}/${MAX_POLLING_ERRORS}):`, error);

        // 检查是否是服务器错误
        if (error?.response?.status >= 500 || error?.name === 'NetworkError') {
          setServerError(true);
        }

        // 如果轮询失败太多次，停止轮询并显示错误
        if (pollingErrorCountRef.current >= MAX_POLLING_ERRORS) {
          console.error('轮询失败次数过多，停止轮询');
          setServerError(true);
          if (pollingIntervalRef.current) {
            clearTimeout(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          return;
        }

        // 错误时使用指数退避策略
        pollingIntervalTimeRef.current = Math.min(
          pollingIntervalTimeRef.current * 1.5,
          30000 // 最大30秒
        );
      }

      // 🚀 智能调度下一次轮询
      adjustPollingInterval(); // 动态调整轮询间隔
      pollingIntervalRef.current = setTimeout(poll, pollingIntervalTimeRef.current);
    };

    // 立即执行第一次轮询
    poll();
  }, [conversationId, user, isDuplicateMessage, adjustPollingInterval]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    console.log('🛑 停止轮询');
    if (pollingIntervalRef.current) {
      clearTimeout(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    pollingErrorCountRef.current = 0;
    setServerError(false);
  }, []);

  // 发送消息
  const handleSendMessage = useCallback(async () => {
    // 从ref获取最新的消息内容
    const currentMessage = newMessageRef.current.trim();
    if (!currentMessage || !conversationId || !user || !otherUser) return;

    const messageContent = currentMessage;
    const otherUserId = parseInt(conversationId.replace('conv_', ''));

    // 立即清空输入框
    setNewMessage('');
    setLoading(true);

    // 创建本地消息对象（立即显示）
    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const localMessage: ChatMessage = {
      id: tempMessageId,
      content: messageContent,
      senderId: user?.id.toString() || '',
      senderName: user?.username || '',
      senderAvatar: user?.fullAvatarUrl || '',
      timestamp: new Date(),
      isRead: false,
      type: 'text'
    };

    // 更新最后活动时间
    lastActivityRef.current = Date.now();

    // 立即添加到消息列表（使用flushSync强制同步更新）
    flushSync(() => {
      setMessages(prev => {
        const updatedMessages = [...prev, localMessage];
        // 更新缓存
        if (conversationId) {
          messageCache.current.set(conversationId, updatedMessages);
        }
        return updatedMessages;
      });
    });

    try {
      // 异步发送到服务器
      const response = await sendPrivateMessage(
        otherUserId,
        messageContent,
        '私信'
      );

      // 发送消息响应处理
      console.log('发送消息响应:', response);

      // 获取服务器返回的真实消息ID
      let serverMessageId = null;
      if (response.success) {
        // 后端返回格式可能包含messageId字段
        const responseData = response as any;
        if (responseData.messageId) {
          serverMessageId = responseData.messageId.toString();
        } else if (response.data && !Array.isArray(response.data) && (response.data as any).id) {
          serverMessageId = (response.data as any).id.toString();
        }
      }

      // 如果获取到服务器ID，更新消息ID
      if (serverMessageId && serverMessageId !== tempMessageId) {
        setMessages(prev => prev.map((msg: ChatMessage) =>
          msg.id === tempMessageId
            ? { ...msg, id: serverMessageId }
            : msg
        ));
        console.log(`消息ID已更新: ${tempMessageId} -> ${serverMessageId}`);
      } else {
        console.log('消息发送成功，但未获取到服务器消息ID');
      }

      // 🚀 消息发送成功后的智能处理
      // 1. 更新最后活动时间，触发快速轮询
      lastActivityRef.current = Date.now();

      // 2. 立即触发一次轮询获取最新消息
      setTimeout(() => {
        if (pollingIntervalRef.current) {
          clearTimeout(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        // 立即开始新的轮询周期
        if (!pollingIntervalRef.current) {
          const poll = async () => {
            try {
              const response = await getConversationMessages(otherUserId);
              const messages = response.data?.messages || response.messages;
              if (messages && Array.isArray(messages)) {
                const formattedMessages: ChatMessage[] = messages.map((msg: ApiMessage) => ({
                  ...formatApiMessage(msg),
                  isRead: msg.isRead !== undefined ? msg.isRead : msg.is_read !== undefined ? msg.is_read : true
                }));

                const sortedMessages = formattedMessages.sort((a, b) =>
                  a.timestamp.getTime() - b.timestamp.getTime()
                );

                setMessages(sortedMessages);
                if (conversationId) {
                  messageCache.current.set(conversationId, sortedMessages);
                }
              }
            } catch (error) {
              console.error('立即轮询失败:', error);
            }

            // 继续正常轮询
            adjustPollingInterval();
            pollingIntervalRef.current = setTimeout(poll, pollingIntervalTimeRef.current);
          };
          poll();
        }
      }, 200); // 200ms后立即轮询

      // 3. 智能更新对话列表，避免闪烁
      window.dispatchEvent(new CustomEvent('conversationUpdated', {
        detail: {
          otherUserId,
          lastMessage: messageContent,
          timestamp: Date.now()
        }
      }));

    } catch (error) {
      // 发送消息失败
      console.error('发送消息失败:', error);

      // 发送失败时，标记消息为失败状态并添加重试功能
      setMessages(prev => prev.map((msg: ChatMessage) =>
        msg.id === tempMessageId
          ? {
              ...msg,
              type: 'error' as any,
              // 可以添加重试数据
              retryData: {
                originalContent: messageContent,
                otherUserId,
                retryCount: 0
              }
            }
          : msg
      ));

      // 检查错误类型并设置相应的错误状态
      if ((error as any)?.response?.status >= 500 || (error as any)?.name === 'NetworkError') {
        setServerError(true);
      }

      // 显示错误提示
      // toast.error('消息发送失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [conversationId, user, otherUser]); // 移除newMessage依赖，避免频繁重新创建函数

  // 处理输入变化（发送打字状态）
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setNewMessage(value);
    newMessageRef.current = value;

    // 清除之前的防抖定时器
    if (typingDebounceRef.current) {
      clearTimeout(typingDebounceRef.current);
    }

    // 发送typing状态（防抖处理）
    if (conversationId && user) {
      const otherUserId = parseInt(conversationId.replace('conv_', ''));
      const conversationKey = `${Math.min(user.id, otherUserId)}_${Math.max(user.id, otherUserId)}`;

      if (value.trim().length > 0) {
        // 立即发送开始输入状态
        realtimeStatus.startTyping(conversationKey);

        // 设置防抖定时器，2秒后自动停止
        typingDebounceRef.current = setTimeout(() => {
          realtimeStatus.stopTyping();
        }, 2000);
      } else {
        // 立即停止输入状态
        realtimeStatus.stopTyping();
      }
    }
  }, [conversationId, user, realtimeStatus]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // 页面可见性检测
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，更新活动时间并触发快速轮询
        lastActivityRef.current = Date.now();
        adjustPollingInterval(); // 重新计算轮询间隔

        // 标记当前对话的未读消息为已读
        if (conversationId) {
          const unreadMessages = messages.filter(msg =>
            !msg.isRead && msg.senderId !== user?.id.toString()
          );
          if (unreadMessages.length > 0) {
            const messageIds = unreadMessages
              .map(msg => parseInt(msg.id))
              .filter(id => !isNaN(id));

            if (messageIds.length > 0) {
              markMessagesAsRead(messageIds).then(() => {
                // 更新全局未读统计
                const { updateUnreadStats } = useMessageStore.getState();
                const currentStats = useMessageStore.getState().unreadStats;
                if (currentStats) {
                  updateUnreadStats({
                    ...currentStats,
                    totalUnread: Math.max(0, currentStats.totalUnread - messageIds.length),
                    privateUnread: Math.max(0, currentStats.privateUnread - messageIds.length)
                  });
                }
              }).catch(error => {
                console.error('页面焦点时标记消息已读失败:', error);
              });

              // 立即更新本地状态
              setMessages(prev => prev.map(msg =>
                unreadMessages.some(unread => unread.id === msg.id)
                  ? { ...msg, isRead: true }
                  : msg
              ));

              // 通过事件机制标记对话为已读，避免直接调用store函数


              window.dispatchEvent(new CustomEvent('conversationReadRequest', {
                detail: {
                  conversationId,
                  source: 'RealTimeChatInterface-visibilityChange'
                }
              }));
            }
          }
        }
      } else {
        // 页面不可见时，使用正常的智能轮询间隔
        adjustPollingInterval(); // 重新计算轮询间隔
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [conversationId, messages, user?.id, markConversationAsRead]);

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages.length]); // 只依赖消息数量，避免循环

  // 🚀 使用统一WebSocket连接
  useEffect(() => {
    if (!user || !token) return;

    // 安全检查：确保unifiedWebSocketManager已定义
    if (typeof unifiedWebSocketManager === 'undefined') {
      console.error('❌ unifiedWebSocketManager未定义，启动轮询备用方案');
      startPolling();
      return;
    }

    console.log('🚀 使用统一WebSocket连接...', unifiedWebSocketManager.isConnected);

    // 使用统一WebSocket管理器
    perfectWsRef.current = unifiedWebSocketManager;

    // 确认使用的是统一管理器
    console.log('🔗 WebSocket管理器引用:', !!perfectWsRef.current);

    // 检查连接状态并启动相应的方案
    if (!unifiedWebSocketManager.isConnected) {
      console.log('⚠️ 统一WebSocket未连接，启动轮询备用方案');
      startPolling();
    } else {
      console.log('✅ 统一WebSocket已连接，停止轮询');
      try {
        stopPolling();
      } catch (error) {
        console.error('停止轮询时出错:', error);
      }
    }

    // 监听WebSocket新消息事件
    const handleNewMessage = (messageData: any) => {


      if (!conversationId || !user) return;

      // 检查消息是否属于当前对话
      const currentOtherUserId = parseInt(conversationId.replace('conv_', ''));
      const messageSenderId = parseInt(messageData.senderId || messageData.sender_id || '0');

      if (messageSenderId === currentOtherUserId) {


        // 格式化消息
        const newMessage: ChatMessage = {
          id: messageData.id?.toString() || Date.now().toString(),
          content: messageData.content || '',
          senderId: messageSenderId.toString(),
          senderName: messageData.senderName || messageData.sender_name || otherUser?.name || '未知用户',
          senderAvatar: messageData.senderAvatar || messageData.sender_avatar || otherUser?.avatar || '',
          timestamp: new Date(messageData.timestamp || messageData.created_at || Date.now()),
          isRead: true, // 立即标记为已读，因为用户正在查看对话
          type: 'text'
        };

        // 添加到消息列表
        setMessages(prev => {
          // 检查是否已存在（避免重复）
          if (prev.some(msg => msg.id === newMessage.id)) {
            return prev;
          }

          const updatedMessages = [...prev, newMessage];

          // 更新缓存
          if (conversationId) {
            messageCache.current.set(conversationId, updatedMessages);
          }

          return updatedMessages;
        });

        // 立即标记消息为已读
        const messageId = parseInt(messageData.id);
        if (!isNaN(messageId)) {


          markMessagesAsRead([messageId]).catch((error: any) => {
            console.error('📨 [RealTimeChatInterface] WebSocket消息标记已读失败:', error);
          });

          // 由于用户正在查看对话，这条消息不应该增加未读数量
          // 所以这里不需要额外的处理
        }

        // 更新最后活动时间
        lastActivityRef.current = Date.now();
        lastMessageTimestamp.current = newMessage.timestamp.getTime();

        // 触发对话列表更新，标明用户正在查看该对话


        window.dispatchEvent(new CustomEvent('conversationUpdated', {
          detail: {
            otherUserId: currentOtherUserId,
            lastMessage: messageData.content,
            timestamp: Date.now(),
            isCurrentlyViewing: true // 标明用户正在查看该对话
          }
        }));
      }
    };

    // 监听WebSocket连接状态变化
    const handleConnectionChange = () => {
      if (unifiedWebSocketManager.isConnected) {
        console.log('🔗 WebSocket重新连接，停止轮询');
        try {
          stopPolling();
        } catch (error) {
          console.error('停止轮询时出错:', error);
        }
      } else {
        console.log('🔌 WebSocket断开连接，启动轮询备用方案');
        startPolling();
      }
    };

    // 注册WebSocket事件监听器
    if (perfectWsRef.current && typeof perfectWsRef.current.on === 'function') {
      perfectWsRef.current.on('newMessage', handleNewMessage);
      perfectWsRef.current.on('connected', handleConnectionChange);
      perfectWsRef.current.on('disconnected', handleConnectionChange);
    }

    // 使用已经建立的全局连接，不需要再次连接
    if (!perfectWsRef.current || !perfectWsRef.current.isConnected) {
      console.log('⚠️ WebSocket未连接，启动轮询备用方案');
      startPolling();
    } else {
      console.log('✅ WebSocket已连接，不启动轮询');
    }

    console.log('✨ 完美WebSocket管理器已启动');

    // 清理函数
    return () => {
      // 移除WebSocket事件监听器
      if (perfectWsRef.current && typeof perfectWsRef.current.off === 'function') {
        perfectWsRef.current.off('newMessage', handleNewMessage);
        perfectWsRef.current.off('connected', handleConnectionChange);
        perfectWsRef.current.off('disconnected', handleConnectionChange);
      }

      // 不要断开全局WebSocket连接，只清理引用
      perfectWsRef.current = null;

      // 清理轮询
      try {
        stopPolling();
      } catch (error) {
        console.error('清理时停止轮询出错:', error);
        // 手动清理
        if (pollingIntervalRef.current) {
          clearTimeout(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      }

      // 清理防抖定时器
      if (typingDebounceRef.current) {
        clearTimeout(typingDebounceRef.current);
        typingDebounceRef.current = null;
      }

      // 停止输入状态
      realtimeStatus.stopTyping();

      // 重置错误计数
      pollingErrorCountRef.current = 0;
      setServerError(false);
    };
  }, [user, token, conversationId, globalWebSocket.wsManager, otherUser, markConversationAsRead]);

  // 自动聚焦输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [conversationId]);

  if (!conversationId) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div
            className="w-24 h-24 rounded-3xl flex items-center justify-center mb-6 mx-auto"
            style={{
              background: 'rgba(255, 255, 255, 0.05)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)'
            }}
          >
            <Send className="w-12 h-12 text-muted-foreground/40" />
          </div>
          <h3 className="font-semibold text-lg mb-3">选择一个对话</h3>
          <p className="text-sm text-muted-foreground max-w-[280px]">
            从左侧选择一个对话开始聊天，或者创建新的对话
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="h-full flex flex-col"
      style={{
        background: 'transparent'
      }}
    >
      {/* 调试信息 - 开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-2 bg-yellow-500/10 border-b border-yellow-500/20 text-xs text-yellow-300">
          <div>对话ID: {conversationId}</div>
          <div>当前用户: {user?.id} ({user?.username})</div>
          <div>对方用户: {otherUser?.id} ({otherUser?.name})</div>
          <div>消息数量: {messages.length}</div>
          <div>加载状态: {loadingMessages ? '加载中' : '已加载'}</div>
        </div>
      )}

      {/* 聊天头部 */}
      <div
        className="flex items-center justify-between p-4 border-b border-white/10 relative z-[115]"
        style={{
          background: 'rgba(255, 255, 255, 0.03)',
          backdropFilter: 'blur(5px)',
          WebkitBackdropFilter: 'blur(5px)'
        }}
      >
        <div className="flex items-center space-x-3">
          {onBack && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onBack}
              className="w-8 h-8 rounded-lg"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
          )}
          
          <div className="relative">
            <Avatar className="w-10 h-10">
              <AvatarImage src={otherUser?.avatar} />
              <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary font-semibold">
                {otherUser?.name?.charAt(0).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            {/* 使用新的在线状态指示器 */}
            {otherUser && (
              <div className="absolute -bottom-0.5 -right-0.5">
                <OnlineStatusIndicator
                  isOnline={realtimeStatus.isUserOnline(parseInt(otherUser.id))}
                  size="sm"
                />
              </div>
            )}
          </div>

          <div>
            <h3 className="font-semibold text-sm">{otherUser?.name || '加载中...'}</h3>
            <div className="text-xs text-muted-foreground">
              {loadingMessages ? (
                '加载中...'
              ) : otherUser && conversationId ? (
                // 检查对方是否在输入
                (() => {
                  const otherUserId = parseInt(conversationId.replace('conv_', ''));
                  const conversationKey = `${Math.min(user?.id || 0, otherUserId)}_${Math.max(user?.id || 0, otherUserId)}`;
                  const isTyping = realtimeStatus.isUserTyping(conversationKey, otherUserId);

                  if (isTyping) {
                    return '正在输入...';
                  } else if (realtimeStatus.isUserOnline(otherUserId)) {
                    return '在线';
                  } else {
                    return '离线';
                  }
                })()
              ) : (
                '离线'
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* WebSocket连接状态 */}
          <WebSocketStatus wsManager={perfectWsRef.current} />

          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 rounded-lg"
            title="刷新消息"
            onClick={() => loadMessages(true)}
            disabled={loadingMessages}
          >
            <RefreshCw className={`w-4 h-4 ${loadingMessages ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 rounded-lg"
            title="个人主页"
            onClick={() => {
              if (otherUser?.name && otherUser.name !== `用户${otherUser.id}`) {
                // 如果有真实用户名，跳转到个人主页
                router.push(`/profile/${otherUser.name}`);
                // 跳转后关闭消息中心
                if (onClose) {
                  onClose();
                }
              }
            }}
          >
            <User className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 消息列表 */}
      <ScrollArea className="flex-1 p-4" role="log" aria-label="聊天消息列表">
        <div className="max-w-4xl mx-auto">
          {/* 服务器错误提示 */}
          {serverError && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center justify-between">
              <p className="text-sm text-red-400">
                服务器暂时不可用，消息可能无法实时更新
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setServerError(false);
                  pollingErrorCountRef.current = 0;
                  startPolling();
                }}
                className="text-red-400 hover:text-red-300 hover:bg-red-500/20"
              >
                重试
              </Button>
            </div>
          )}
          {loadingMessages ? (
            <div className="flex items-center justify-center py-16">
              <div className="flex flex-col items-center space-y-3">
                <Loader2 className="w-8 h-8 animate-spin text-primary" />
                <div className="text-center">
                  <p className="text-muted-foreground">正在加载消息...</p>
                  <p className="text-xs text-muted-foreground/60 mt-1">确保加载完整的对话记录</p>
                </div>
              </div>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-muted/20 flex items-center justify-center mx-auto mb-4">
                  <Send className="w-8 h-8 text-muted-foreground/40" />
                </div>
                <h3 className="font-semibold text-lg mb-2">开始对话</h3>
                <p className="text-muted-foreground text-sm">发送第一条消息开始与 {otherUser?.name || '对方'} 的对话</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index: number) => {
                const isOwn = message.senderId === user?.id.toString();
                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;

                return (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={cn(
                      "flex space-x-2 items-start",
                      isOwn ? "justify-end" : "justify-start"
                    )}
                  >
                    {/* 头像 - 只在需要时显示 */}
                    {!isOwn && (
                      <Avatar className={cn("w-8 h-8 flex-shrink-0 mt-1", showAvatar ? "opacity-100" : "opacity-0")}>
                        <AvatarImage src={message.senderAvatar} />
                        <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary text-xs">
                          {message.senderName.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    <div className={cn(
                      "max-w-[70%] sm:max-w-[65%] lg:max-w-[55%] xl:max-w-[50%] flex flex-col space-y-1",
                      isOwn ? "items-end" : "items-start"
                    )}>
                      <div
                        className={cn(
                          "px-4 py-3 rounded-2xl text-sm leading-relaxed break-words",
                          isOwn
                            ? "bg-primary text-primary-foreground rounded-br-md"
                            : "bg-muted rounded-bl-md"
                        )}
                        style={{
                          background: isOwn
                            ? 'rgba(59, 130, 246, 0.8)'
                            : 'rgba(255, 255, 255, 0.1)',
                          color: isOwn
                            ? 'rgba(255, 255, 255, 0.95)'
                            : 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          WebkitBackdropFilter: 'blur(10px)',
                          border: isOwn
                            ? '1px solid rgba(59, 130, 246, 0.3)'
                            : '1px solid rgba(255, 255, 255, 0.2)',
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word'
                        }}
                      >
                        {message.content}
                      </div>

                      <div className={cn(
                        "flex items-center space-x-1 text-xs text-muted-foreground mt-1",
                        isOwn ? "justify-end" : "justify-start"
                      )}>
                        <span className="whitespace-nowrap">
                          {formatDistanceToNow(message.timestamp, { addSuffix: true, locale: zhCN })}
                        </span>
                        {isOwn && (
                          <div className="flex items-center ml-1">
                            {message.type === 'error' ? (
                              <div title="发送失败">
                                <AlertTriangle className="w-3 h-3 text-red-400" />
                              </div>
                            ) : message.isRead ? (
                              <CheckCheck className="w-3 h-3 text-primary" />
                            ) : (
                              <Check className="w-3 h-3" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 自己的头像 */}
                    {isOwn && (
                      <Avatar className={cn("w-8 h-8 flex-shrink-0 mt-1", showAvatar ? "opacity-100" : "opacity-0")}>
                        <AvatarImage src={user?.fullAvatarUrl || user?.avatarUrl || undefined} />
                        <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary text-xs">
                          {user?.username?.charAt(0).toUpperCase() || 'U'}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </motion.div>
                );
              })}

              {/* Typing指示器 */}
              {otherUser && conversationId && (() => {
                const otherUserId = parseInt(conversationId.replace('conv_', ''));
                const conversationKey = `${Math.min(user?.id || 0, otherUserId)}_${Math.max(user?.id || 0, otherUserId)}`;
                const isTyping = realtimeStatus.isUserTyping(conversationKey, otherUserId);

                return isTyping ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-start space-x-3 mb-4"
                  >
                    <Avatar className="w-8 h-8 flex-shrink-0 mt-1">
                      <AvatarImage src={otherUser.avatar} />
                      <AvatarFallback className="bg-gradient-to-br from-muted/20 to-muted/10 text-muted-foreground text-xs">
                        {otherUser.name?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>

                    <div
                      className="max-w-[70%] sm:max-w-[65%] lg:max-w-[55%] xl:max-w-[50%] px-4 py-3 rounded-2xl rounded-bl-md"
                      style={{
                        background: 'rgba(255, 255, 255, 0.1)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)'
                      }}
                    >
                      <TypingIndicator
                        isTyping={true}
                        userName={otherUser.name}
                      />
                    </div>
                  </motion.div>
                ) : null
              })()}

              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      </ScrollArea>

      {/* 输入区域 */}
      <div
        className="p-4 border-t border-white/10 relative z-[115]"
        style={{
          background: 'rgba(255, 255, 255, 0.03)',
          backdropFilter: 'blur(5px)',
          WebkitBackdropFilter: 'blur(5px)'
        }}
      >
        <div className="max-w-4xl mx-auto">
          <div className="flex items-end space-x-3">
          <Button variant="ghost" size="icon" className="w-9 h-9 rounded-lg">
            <Paperclip className="w-4 h-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Textarea
              ref={inputRef}
              value={newMessage}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="输入消息..."
              aria-label={`向 ${otherUser?.name || '对方'} 发送消息`}
              aria-describedby="send-message-help"
              className="min-h-[44px] max-h-32 resize-none rounded-xl pr-12 border-0"
              style={{
                background: 'rgba(255, 255, 255, 0.08)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                color: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)'
              }}
              rows={1}
            />
            <div id="send-message-help" className="sr-only">
              按 Enter 发送消息，Shift+Enter 换行
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 bottom-1 w-8 h-8 rounded-lg"
            >
              <Smile className="w-4 h-4" />
            </Button>
          </div>
          
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || loading}
            className="w-9 h-9 rounded-lg border-0"
            size="icon"
            aria-label={loading ? '正在发送消息...' : '发送消息'}
            style={{
              background: 'rgba(59, 130, 246, 0.8)',
              border: '1px solid rgba(59, 130, 246, 0.3)',
              color: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)'
            }}
          >
            <Send className="w-4 h-4" />
          </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
