package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.dto.DailyRevenueDTO;
import com.kitolus.community.dto.MonthlyRevenueDTO;
import com.kitolus.community.dto.ProductSalesStatsDTO;
import com.kitolus.community.entity.Order;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    @Select("SELECT COUNT(*) FROM orders WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Long userId);

    @Select("SELECT COUNT(*) FROM orders WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    @Select("SELECT SUM(total_fee) FROM orders WHERE status = #{status}")
    BigDecimal sumTotalFeeByStatus(@Param("status") String status);

    @Select("SELECT SUM(platform_fee) FROM orders WHERE status = #{status}")
    BigDecimal sumPlatformFeeByStatus(@Param("status") String status);

    @Select("SELECT p.user_id, SUM(o.total_fee) as revenue " +
            "FROM orders o JOIN products p ON o.product_id = p.id " +
            "WHERE o.status = #{status} " +
            "GROUP BY p.user_id")
    List<Map<String, Object>> calculateTotalRevenuePerDeveloper(@Param("status") String status);

    @Select("SELECT DATE_FORMAT(paid_at, '%Y-%m') as month, SUM(total_fee) as revenue " +
            "FROM orders " +
            "WHERE status = #{status} " +
            "GROUP BY month ORDER BY month")
    List<MonthlyRevenueDTO> findMonthlyRevenue(@Param("status") String status);

    @Select("SELECT DATE(paid_at) as date, SUM(total_fee) as revenue " +
            "FROM orders " +
            "WHERE status = #{status} AND paid_at >= CURDATE() - INTERVAL 30 DAY " +
            "GROUP BY date ORDER BY date")
    List<DailyRevenueDTO> findDailyRevenue(@Param("status") String status);

    @Select("SELECT product_id, COUNT(*) as sales_count FROM orders WHERE status = #{status} GROUP BY product_id ORDER BY sales_count DESC LIMIT 10")
    List<Map<String, Object>> findTopSellingProductCounts(@Param("status") String status);
    
    @Select("SELECT * FROM orders WHERE status = 'PAID' ORDER BY paid_at DESC LIMIT #{limit}")
    List<Order> findRecentPaidOrders(@Param("limit") int limit);

    // Methods for a specific developer
    @Select("SELECT COUNT(o.id) FROM orders o JOIN products p ON o.product_id = p.id WHERE o.status = #{status} AND p.user_id = #{developerId}")
    long countByStatusAndDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Select("SELECT SUM(o.total_fee) FROM orders o JOIN products p ON o.product_id = p.id WHERE o.status = #{status} AND p.user_id = #{developerId}")
    BigDecimal sumTotalFeeByStatusAndDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Select("SELECT SUM(o.developer_revenue) FROM orders o JOIN products p ON o.product_id = p.id WHERE o.status = #{status} AND p.user_id = #{developerId}")
    BigDecimal sumDeveloperRevenueByStatusAndDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Select("SELECT DATE_FORMAT(o.paid_at, '%Y-%m') as month, SUM(o.total_fee) as revenue " +
            "FROM orders o JOIN products p ON o.product_id = p.id " +
            "WHERE o.status = #{status} AND p.user_id = #{developerId} " +
            "GROUP BY month ORDER BY month")
    List<MonthlyRevenueDTO> findMonthlyRevenueByDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Select("SELECT DATE(o.paid_at) as date, SUM(o.total_fee) as revenue " +
            "FROM orders o JOIN products p ON o.product_id = p.id " +
            "WHERE o.status = #{status} AND p.user_id = #{developerId} AND o.paid_at >= CURDATE() - INTERVAL 30 DAY " +
            "GROUP BY date ORDER BY date")
    List<DailyRevenueDTO> findDailyRevenueByDeveloper(@Param("status") String status, @Param("developerId") Long developerId);
    
    @Select("SELECT o.product_id, COUNT(*) as sales_count " +
            "FROM orders o JOIN products p ON o.product_id = p.id " +
            "WHERE o.status = #{status} AND p.user_id = #{developerId} " +
            "GROUP BY o.product_id ORDER BY sales_count DESC")
    List<Map<String, Object>> findTopSellingProductCountsByDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Select("SELECT o.* FROM orders o JOIN products p ON o.product_id = p.id WHERE p.user_id = #{developerId} AND o.status = 'PAID' ORDER BY o.paid_at DESC LIMIT #{limit}")
    List<Order> findRecentPaidOrdersByDeveloper(@Param("developerId") Long developerId, @Param("limit") int limit);

    @Select("SELECT o.* FROM orders o JOIN products p ON o.product_id = p.id WHERE p.user_id = #{developerId} AND o.status = 'PAID' ORDER BY o.paid_at DESC")
    List<Order> findAllPaidOrdersByDeveloper(@Param("developerId") Long developerId);

    @Select("SELECT " +
            "p.id as productId, " +
            "p.name as productName, " +
            "p.image_url as productImageUrl, " +
            "p.price as productPrice, " +
            "COALESCE(COUNT(o.id), 0) as salesCount, " +
            "COALESCE(SUM(o.total_fee), 0) as totalRevenue, " +
            "COALESCE(SUM(o.developer_revenue), 0) as developerRevenue " +
            "FROM products p " +
            "LEFT JOIN orders o ON p.id = o.product_id AND o.status = #{status} " +
            "WHERE p.user_id = #{developerId} AND p.deleted = false " +
            "GROUP BY p.id, p.name, p.image_url, p.price " +
            "ORDER BY salesCount DESC, p.created_at DESC")
    List<ProductSalesStatsDTO> findProductSalesStatsByDeveloper(@Param("status") String status, @Param("developerId") Long developerId);

    @Delete("DELETE FROM orders WHERE out_trade_no LIKE CONCAT(#{prefix}, '%')")
    int deleteByOutTradeNoPrefix(@Param("prefix") String prefix);
}