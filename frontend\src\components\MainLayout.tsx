'use client';

import { usePathname } from 'next/navigation';
import InteractiveBackground from '@/components/InteractiveBackground';
import CircuitBackground from '@/components/CircuitBackground';
import Header from '@/components/Header';
import PageWrapper from '@/components/PageWrapper';
import { MessageSystem } from '@/components/MessageSystem';
import { Toaster } from "@/components/ui/sonner";

export default function MainLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  
  const isCommunityRoute = pathname === '/community' || pathname.startsWith('/community/');
  const isDashboardPage = pathname.startsWith('/dashboard');

  // Define pages that have their own layout and don't need the default top padding
  const pagesWithCustomLayout = [
    '/',
    '/login',
    '/register',
    '/dashboard'
  ];
  let needsTopPadding = !pagesWithCustomLayout.some(path => pathname.startsWith(path)) || pathname === '/';
  if(pathname === '/') needsTopPadding = false;

  const showInteractiveBackground = pathname.startsWith('/kb');

  return (
    <div className="flex flex-col min-h-screen">
      <InteractiveBackground isVisible={showInteractiveBackground} />
      <CircuitBackground isVisible={false} />
      <Header />
      <main className={`flex-grow ${needsTopPadding ? 'pt-16' : ''}`}>
        {isDashboardPage ? (
          children
        ) : (
          <PageWrapper>
            {children}
          </PageWrapper>
        )}
      </main>

      {/* 消息系统 */}
      <MessageSystem />

      <Toaster richColors position="top-center" />
    </div>
  );
} 