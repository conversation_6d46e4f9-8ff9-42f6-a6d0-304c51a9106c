import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import * as mm from 'music-metadata';

export async function GET() {
  try {
    const musicDirectory = path.join(process.cwd(), 'public/music');
    const files = await fs.readdir(musicDirectory);
    
    const supportedExtensions = ['.mp3', '.flac', '.wav', '.ogg', '.m4a'];
    const audioFiles = files.filter(file =>
        supportedExtensions.some(ext => file.toLowerCase().endsWith(ext))
    );

    const songs = await Promise.all(audioFiles.map(async (file) => {
        const filePath = path.join(musicDirectory, file);
        const metadata = await mm.parseFile(filePath);
        
        const title = metadata.common.title || file.replace(/\.(mp3|flac|wav|ogg|m4a)$/i, '');
        const artist = metadata.common.artist || 'Unknown Artist';
        let cover: string | null = null;
        
        if (metadata.common.picture && metadata.common.picture.length > 0) {
            const picture = metadata.common.picture[0];
            cover = `data:${picture.format};base64,${Buffer.from(picture.data).toString('base64')}`;
        }

        return {
            title: title,
            artist: artist,
            url: `/music/${file}`,
            cover: cover,
        };
    }));

    // Sort songs to put "Fahrenheit" first
    songs.sort((a, b) => {
        const aIsFahrenheit = a.title.toLowerCase().includes('fahrenheit');
        const bIsFahrenheit = b.title.toLowerCase().includes('fahrenheit');

        if (aIsFahrenheit && !bIsFahrenheit) return -1;
        if (!aIsFahrenheit && bIsFahrenheit) return 1;

        // Optional: for other songs, sort alphabetically by title
        return a.title.localeCompare(b.title);
    });

    return NextResponse.json(songs);
  } catch (error) {
    console.error("Could not read music directory:", error);
    // Return empty array if directory doesn't exist or on other errors
    return NextResponse.json([]);
  }
}
