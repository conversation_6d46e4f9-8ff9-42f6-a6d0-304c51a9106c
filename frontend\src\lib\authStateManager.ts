/**
 * 认证状态管理器
 * 负责处理认证状态的持久化和恢复
 */

export class AuthStateManager {
  private static instance: AuthStateManager;
  private readonly TOKEN_KEY = 'jwt_token';
  private readonly AUTH_INIT_KEY = 'auth_initialized';
  private readonly STATE_CHECK_INTERVAL = 5000; // 5秒检查一次
  private checkInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  static getInstance(): AuthStateManager {
    if (!AuthStateManager.instance) {
      AuthStateManager.instance = new AuthStateManager();
    }
    return AuthStateManager.instance;
  }

  /**
   * 保存认证状态
   */
  saveAuthState(token: string, userData?: any): boolean {
    try {
      console.log('💾 保存认证状态:', {
        tokenPrefix: token.substring(0, 20) + '...',
        hasUserData: !!userData
      });

      // 保存token
      localStorage.setItem(this.TOKEN_KEY, token);
      
      // 保存初始化标记
      localStorage.setItem(this.AUTH_INIT_KEY, 'true');
      
      // 如果有用户数据，也保存
      if (userData) {
        localStorage.setItem('user_data', JSON.stringify(userData));
      }

      // 验证保存是否成功
      const savedToken = localStorage.getItem(this.TOKEN_KEY);
      if (savedToken !== token) {
        console.error('❌ Token保存验证失败');
        return false;
      }

      console.log('✅ 认证状态保存成功');
      return true;
    } catch (error) {
      console.error('❌ 保存认证状态失败:', error);
      return false;
    }
  }

  /**
   * 恢复认证状态
   */
  restoreAuthState(): { token: string | null; userData: any | null; isInitialized: boolean } {
    try {
      console.log('🔄 尝试恢复认证状态...');

      const token = localStorage.getItem(this.TOKEN_KEY);
      const isInitialized = localStorage.getItem(this.AUTH_INIT_KEY) === 'true';
      let userData = null;

      try {
        const userDataStr = localStorage.getItem('user_data');
        if (userDataStr) {
          userData = JSON.parse(userDataStr);
        }
      } catch (error) {
        console.warn('⚠️ 用户数据解析失败:', error);
      }

      console.log('🔍 认证状态恢复结果:', {
        hasToken: !!token,
        tokenPrefix: token?.substring(0, 20) + '...',
        hasUserData: !!userData,
        isInitialized
      });

      return { token, userData, isInitialized };
    } catch (error) {
      console.error('❌ 恢复认证状态失败:', error);
      return { token: null, userData: null, isInitialized: false };
    }
  }

  /**
   * 清除认证状态
   */
  clearAuthState(): boolean {
    try {
      console.log('🧹 清除认证状态...');

      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.AUTH_INIT_KEY);
      localStorage.removeItem('user_data');

      // 验证清除是否成功
      const remainingToken = localStorage.getItem(this.TOKEN_KEY);
      if (remainingToken) {
        console.error('⚠️ Token清除失败，尝试强制清除');
        // 尝试清除所有localStorage
        try {
          localStorage.clear();
          console.log('✅ 强制清除localStorage成功');
        } catch (clearError) {
          console.error('❌ 强制清除localStorage失败:', clearError);
          return false;
        }
      }

      console.log('✅ 认证状态清除成功');
      return true;
    } catch (error) {
      console.error('❌ 清除认证状态失败:', error);
      return false;
    }
  }

  /**
   * 检查localStorage是否可用
   */
  isLocalStorageAvailable(): boolean {
    try {
      const testKey = 'test_' + Date.now();
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return true;
    } catch (error) {
      console.error('❌ localStorage不可用:', error);
      return false;
    }
  }

  /**
   * 开始状态监控
   */
  startStateMonitoring(onStateChange?: (state: any) => void): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    console.log('🔍 开始认证状态监控...');

    this.checkInterval = setInterval(() => {
      const currentState = this.restoreAuthState();
      
      // 检查状态一致性
      if (onStateChange) {
        onStateChange(currentState);
      }
    }, this.STATE_CHECK_INTERVAL);
  }

  /**
   * 停止状态监控
   */
  stopStateMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🛑 停止认证状态监控');
    }
  }

  /**
   * 验证token格式
   */
  validateTokenFormat(token: string): boolean {
    try {
      // 简单的JWT格式验证
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }

      // 尝试解析header和payload
      JSON.parse(atob(parts[0]));
      JSON.parse(atob(parts[1]));

      return true;
    } catch (error) {
      console.error('❌ Token格式验证失败:', error);
      return false;
    }
  }

  /**
   * 获取token过期时间
   */
  getTokenExpiration(token: string): Date | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      if (payload.exp) {
        return new Date(payload.exp * 1000);
      }
      return null;
    } catch (error) {
      console.error('❌ 获取token过期时间失败:', error);
      return null;
    }
  }

  /**
   * 检查token是否过期
   */
  isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) {
      return false; // 如果无法获取过期时间，假设未过期
    }
    return Date.now() >= expiration.getTime();
  }
}

// 导出单例实例
export const authStateManager = AuthStateManager.getInstance();
