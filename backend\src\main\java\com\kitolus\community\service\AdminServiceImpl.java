package com.kitolus.community.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import java.util.List;
import com.kitolus.community.dto.DeveloperApplicationDTO;
import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.MonthlyRevenueDTO;
import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.dto.UserAdminViewDTO;
import com.kitolus.community.dto.AdminDashboardStatsDTO;
import com.kitolus.community.entity.*;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AdminServiceImpl implements AdminService {

    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private DeveloperApplicationMapper developerApplicationMapper;

    @Autowired
    private LoginHistoryMapper loginHistoryMapper;

    @Autowired
    private CommunityPostMapper postMapper;

    @Autowired
    private CommunityCommentMapper commentMapper;

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private WithdrawalRequestMapper withdrawalRequestMapper;

    @Autowired
    private CommunityPostLikeMapper postLikeMapper;

    @Autowired
    private ProductStatusHistoryMapper productStatusHistoryMapper;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ProductStatusHistoryService statusHistoryService;

    @Autowired
    private EarningsService earningsService;

    @Override
    public EarningsSummaryDTO getEarningsSummary() {
        return earningsService.getGlobalEarningsSummary();
    }

    @Override
    public AdminDashboardStatsDTO getAdminDashboardStats() {
        AdminDashboardStatsDTO stats = new AdminDashboardStatsDTO();

        long newUsersToday = userMapper.countNewUsersToday();
        stats.setNewUsersToday(newUsersToday);

        QueryWrapper<DeveloperApplication> devAppWrapper = new QueryWrapper<>();
        devAppWrapper.eq("status", DeveloperApplicationStatus.PENDING);
        long pendingDeveloperApplications = developerApplicationMapper.selectCount(devAppWrapper);
        stats.setPendingDeveloperApplications(pendingDeveloperApplications);

        QueryWrapper<Product> productWrapper = new QueryWrapper<>();
        productWrapper.eq("status", ProductStatus.PENDING_APPROVAL);
        long pendingProducts = productMapper.selectCount(productWrapper);
        stats.setPendingProducts(pendingProducts);

        return stats;
    }

    private User getCurrentAdminUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalName = authentication.getName();
        return userService.findByUsername(currentPrincipalName);
    }

    @Override
    public List<UserAdminViewDTO> getAllUsers() {
        List<User> users = userMapper.selectList(null);
        return users.stream().map(user -> {
            UserAdminViewDTO dto = new UserAdminViewDTO();
            dto.setId(user.getId());
            dto.setUsername(user.getUsername());
            dto.setEmail(user.getEmail());
            dto.setRole(user.getRole());
            dto.setCreatedAt(user.getCreatedAt());
            dto.setEnabled(user.isEnabled());

            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", user.getId())
                    .orderByDesc("login_timestamp")
                    .last("LIMIT 1");
            LoginHistory lastLogin = loginHistoryMapper.selectOne(queryWrapper);
            if (lastLogin != null) {
                dto.setLastLoginIp(lastLogin.getIpAddress());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public User updateUserRole(Long userId, String newRole) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with id: " + userId);
        }
        user.setRole(newRole);
        userMapper.updateById(user);
        return user;
    }

    @Override
    public List<Product> getProductsForApproval() {
        return productMapper.selectList(new QueryWrapper<Product>()
                .in("status", ProductStatus.PENDING_APPROVAL, ProductStatus.REJECTED));
        // @TableLogic annotation will automatically filter out logically deleted products
    }

    @Override
    @Transactional
    public Product approveProduct(Long productId, String notes) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new ResourceNotFoundException("Product not found with id: " + productId);
        }

        User admin = getCurrentAdminUser();
        ProductStatus oldStatus = product.getStatus();

        product.setStatus(ProductStatus.APPROVED);
        product.setApprovalNotes(notes);
        product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        product.setReviewedBy(admin.getUsername());
        productMapper.updateById(product);

        // 记录状态变更历史
        statusHistoryService.recordStatusChange(
            productId, oldStatus, ProductStatus.APPROVED,
            admin.getUsername(), "管理员审核通过", notes
        );

        notificationService.createNotificationForProductApproval(product, notes, admin);

        return product;
    }

    @Override
    @Transactional
    public Product rejectProduct(Long productId, String reason) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new ResourceNotFoundException("Product not found with id: " + productId);
        }

        User admin = getCurrentAdminUser();

        // If an admin rejects an already approved product, it means "delist and delete".
        if (product.getStatus() == ProductStatus.APPROVED) {
            // Check if the product has any orders
            boolean hasOrders = hasProductOrders(productId);

            if (hasOrders) {
                // If there are orders, use logical deletion
                product.setDeleted(true);
                product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setReviewedBy(admin.getUsername());
                productMapper.updateById(product);
            } else {
                // If no orders, safe to physically delete
                productMapper.deleteById(productId);
            }

            notificationService.createNotificationForProductRejection(product, "该商品已被管理员下架并移除。", admin);
            return product;
        }

        // For products that are not yet approved (e.g., PENDING), just reject them.
        ProductStatus oldStatus = product.getStatus();
        product.setStatus(ProductStatus.REJECTED);
        product.setRejectionReason(reason);
        product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        product.setReviewedBy(admin.getUsername());
        productMapper.updateById(product);

        // 记录状态变更历史
        statusHistoryService.recordStatusChange(
            productId, oldStatus, ProductStatus.REJECTED,
            admin.getUsername(), "管理员审核驳回", reason
        );

        notificationService.createNotificationForProductRejection(product, reason, admin);

        return product;
    }

    @Override
    public List<DeveloperApplicationDTO> getDeveloperApplicationsByStatus(String status) {
        QueryWrapper<DeveloperApplication> queryWrapper = new QueryWrapper<>();
        if (status != null && !status.isEmpty()) {
            try {
                DeveloperApplicationStatus applicationStatus = DeveloperApplicationStatus.valueOf(status.toUpperCase());
                queryWrapper.eq("status", applicationStatus);
            } catch (IllegalArgumentException e) {
                // Optional: handle invalid status string, maybe log a warning or throw an exception
            }
        }
        List<DeveloperApplication> applications = developerApplicationMapper.selectList(queryWrapper);
        return applications.stream().map(app -> {
            User user = userMapper.selectById(app.getUserId());
            DeveloperApplicationDTO dto = new DeveloperApplicationDTO();
            dto.setId(app.getId());
            dto.setUserId(app.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
                dto.setUserAvatarUrl(user.getAvatarUrl());
                dto.setUserSerialNumber(user.getSerialNumber());
            }
            dto.setStatus(app.getStatus().name());
            dto.setMessage(app.getMessage());
            dto.setCreatedAt(app.getCreatedAt());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DeveloperApplication approveDeveloperApplication(Long applicationId) {
        DeveloperApplication application = developerApplicationMapper.selectById(applicationId);
        if (application == null) {
            throw new ResourceNotFoundException("Application not found with id: " + applicationId);
        }
        application.setStatus(DeveloperApplicationStatus.APPROVED);
        application.setReviewedAt(new Timestamp(System.currentTimeMillis()));
        
        User admin = getCurrentAdminUser();
        application.setReviewedBy(admin.getId());
        
        developerApplicationMapper.updateById(application);

        User user = userMapper.selectById(application.getUserId());
        if (user != null) {
            user.setRole("DEVELOPER");
            userMapper.updateById(user);
        }
        
        notificationService.createNotificationForApplicationApproval(application);
        
        return application;
    }

    @Override
    @Transactional
    public DeveloperApplication rejectDeveloperApplication(Long applicationId, String reason) {
        DeveloperApplication application = developerApplicationMapper.selectById(applicationId);
        if (application == null) {
            throw new ResourceNotFoundException("Application not found with id: " + applicationId);
        }
        application.setStatus(DeveloperApplicationStatus.REJECTED);
        application.setRejectionReason(reason);
        application.setReviewedAt(new Timestamp(System.currentTimeMillis()));

        User admin = getCurrentAdminUser();
        application.setReviewedBy(admin.getId());

        developerApplicationMapper.updateById(application);

        notificationService.createNotificationForApplicationRejection(application, reason);

        return application;
    }

    @Override
    @Transactional
    public void toggleUserStatus(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with id: " + userId);
        }
        user.setEnabled(!user.isEnabled());
        userMapper.updateById(user);
    }

    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("User not found with id: " + userId);
        }

        try {
            logger.info("开始删除用户 {} ({})", userId, user.getUsername());

            // 1. 删除登录历史记录
            loginHistoryMapper.delete(new QueryWrapper<LoginHistory>().eq("user_id", userId));
            logger.info("已删除用户 {} 的登录历史记录", userId);

            // 2. 删除通知记录
            notificationMapper.delete(new QueryWrapper<Notification>().eq("recipient_id", userId));
            notificationMapper.delete(new QueryWrapper<Notification>().eq("sender_id", userId));
            logger.info("已删除用户 {} 的通知记录", userId);

            // 3. 删除消息记录
            messageMapper.delete(new QueryWrapper<Message>().eq("sender_id", userId));
            messageMapper.delete(new QueryWrapper<Message>().eq("receiver_id", userId));
            logger.info("已删除用户 {} 的消息记录", userId);

            // 4. 删除提现请求（作为申请者和审核者）
            withdrawalRequestMapper.delete(new QueryWrapper<WithdrawalRequest>().eq("user_id", userId));
            // 清除作为审核者的记录（不删除记录，只清除审核者ID）
            UpdateWrapper<WithdrawalRequest> withdrawalUpdateWrapper = new UpdateWrapper<>();
            withdrawalUpdateWrapper.eq("reviewed_by", userId).set("reviewed_by", null);
            withdrawalRequestMapper.update(null, withdrawalUpdateWrapper);
            logger.info("已删除用户 {} 的提现请求", userId);

            // 5. 删除开发者申请（作为申请者和审核者）
            developerApplicationMapper.delete(new QueryWrapper<DeveloperApplication>().eq("user_id", userId));
            // 清除作为审核者的记录
            UpdateWrapper<DeveloperApplication> applicationUpdateWrapper = new UpdateWrapper<>();
            applicationUpdateWrapper.eq("reviewed_by", userId).set("reviewed_by", null);
            developerApplicationMapper.update(null, applicationUpdateWrapper);
            logger.info("已删除用户 {} 的开发者申请", userId);

            // 6. 删除社区评论
            commentMapper.delete(new QueryWrapper<CommunityComment>().eq("author_id", userId));
            logger.info("已删除用户 {} 的社区评论", userId);

            // 7. 删除社区帖子点赞
            postLikeMapper.delete(new QueryWrapper<CommunityPostLike>().eq("user_id", userId));
            logger.info("已删除用户 {} 的帖子点赞记录", userId);

            // 8. 删除社区帖子（作为作者）
            postMapper.delete(new QueryWrapper<CommunityPost>().eq("author_id", userId));
            // 清除作为置顶者的记录
            UpdateWrapper<CommunityPost> postUpdateWrapper = new UpdateWrapper<>();
            postUpdateWrapper.eq("pinned_by", userId)
                .set("pinned_by", null)
                .set("is_pinned", false)
                .set("pinned_at", null);
            postMapper.update(null, postUpdateWrapper);
            logger.info("已删除用户 {} 的社区帖子", userId);

            // 9. 获取用户的产品列表（用于删除相关记录）
            // 使用原生SQL查询，包括软删除的记录
            List<Product> userProducts = productMapper.selectAllByUserIdIncludeDeleted(userId);
            List<Long> productIds = userProducts.stream().map(Product::getId).collect(java.util.stream.Collectors.toList());
            logger.info("用户 {} 共有 {} 个产品需要处理（包括软删除的）", userId, productIds.size());

            // 验证产品是否真的存在
            if (!productIds.isEmpty()) {
                logger.info("产品ID列表: {}", productIds);
                for (Product product : userProducts) {
                    logger.info("产品详情: ID={}, 名称={}, 删除状态={}", product.getId(), product.getName(), product.isDeleted());
                }
            }

            // 10. 使用JDBC删除产品状态历史记录
            if (!productIds.isEmpty()) {
                String deleteHistorySql = "DELETE psh FROM product_status_history psh JOIN products p ON psh.product_id = p.id WHERE p.user_id = ?";
                int deletedHistoryCount = jdbcTemplate.update(deleteHistorySql, userId);
                logger.info("已使用JDBC删除用户 {} 的产品状态历史记录，共 {} 条", userId, deletedHistoryCount);
            }

            // 11. 使用JDBC删除订单记录（作为买家）
            String deleteUserOrdersSql = "DELETE FROM orders WHERE user_id = ?";
            int deletedUserOrdersCount = jdbcTemplate.update(deleteUserOrdersSql, userId);
            logger.info("已使用JDBC删除用户 {} 的订单记录（作为买家），共 {} 条", userId, deletedUserOrdersCount);

            // 12. 使用JDBC删除产品相关的订单记录（作为卖家）
            if (!productIds.isEmpty()) {
                String deleteProductOrdersSql = "DELETE o FROM orders o JOIN products p ON o.product_id = p.id WHERE p.user_id = ?";
                int deletedProductOrdersCount = jdbcTemplate.update(deleteProductOrdersSql, userId);
                logger.info("已使用JDBC删除用户 {} 的产品相关订单记录（作为卖家），共 {} 条", userId, deletedProductOrdersCount);
            }

            // 13. 物理删除产品记录（作为创建者）
            // 由于Product使用@TableLogic软删除，我们需要使用SQL直接删除
            try {
                // 使用JDBC直接执行删除，绕过MyBatis-Plus的软删除机制
                String deleteProductsSql = "DELETE FROM products WHERE user_id = ?";
                int deletedProductsCount = jdbcTemplate.update(deleteProductsSql, userId);
                logger.info("已使用JDBC物理删除用户 {} 的所有产品记录，共 {} 个产品", userId, deletedProductsCount);
            } catch (Exception e) {
                logger.error("物理删除用户 {} 的产品记录失败: {}", userId, e.getMessage());
                throw e;
            }

            // 清除作为审核者的记录（对于其他用户的产品）
            UpdateWrapper<Product> productUpdateWrapper = new UpdateWrapper<>();
            productUpdateWrapper.eq("reviewed_by", user.getUsername()).set("reviewed_by", null);
            int updatedReviewerCount = productMapper.update(null, productUpdateWrapper);
            logger.info("已清除用户 {} 作为审核者的产品记录，共 {} 条", userId, updatedReviewerCount);

            // 14. 验证产品是否真的被删除
            String checkProductsSql = "SELECT COUNT(*) FROM products WHERE user_id = ?";
            Integer remainingProductCount = jdbcTemplate.queryForObject(checkProductsSql, Integer.class, userId);
            if (remainingProductCount != null && remainingProductCount > 0) {
                logger.error("用户 {} 仍有 {} 个产品未被删除", userId, remainingProductCount);
                throw new RuntimeException("产品删除失败，仍有 " + remainingProductCount + " 个产品存在");
            }
            logger.info("验证通过：用户 {} 的所有产品已被删除", userId);

            // 15. 最后删除用户
            userMapper.deleteById(userId);
            logger.info("用户 {} ({}) 已完全删除", userId, user.getUsername());

        } catch (Exception e) {
            logger.error("删除用户 {} 时发生错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("删除用户失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ProductDTO> getAllProducts() {
        List<Product> products = productMapper.selectList(new QueryWrapper<Product>()
                .orderByDesc("created_at"));
        // @TableLogic annotation will automatically filter out logically deleted products
        return products.stream().map(product -> {
            User author = userMapper.selectById(product.getUserId());
            ProductDTO dto = new ProductDTO();
            dto.setId(product.getId());
            dto.setName(product.getName());
            dto.setPrice(product.getPrice());
            dto.setDescription(product.getDescription());
            dto.setImageUrl(product.getImageUrl());
            dto.setDownloadUrl(product.getDownloadUrl());
            dto.setCreatedAt(product.getCreatedAt());
            dto.setUpdatedAt(product.getUpdatedAt());
            dto.setReviewedAt(product.getReviewedAt());
            dto.setStatus(product.getStatus().name());
            dto.setRejectionReason(product.getRejectionReason());
            dto.setApprovalNotes(product.getApprovalNotes());
            dto.setReviewedBy(product.getReviewedBy());

            if (author != null) {
                dto.setAuthorId(author.getId());
                dto.setAuthorName(author.getUsername());
                dto.setAuthorAvatarUrl(author.getAvatarUrl());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Product delistProduct(Long productId, String reason) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new ResourceNotFoundException("Product not found with id: " + productId);
        }

        if (product.getStatus() != ProductStatus.APPROVED) {
            throw new IllegalStateException("Only approved products can be delisted. This product's status is " + product.getStatus());
        }

        User productOwner = userMapper.selectById(product.getUserId());
        if (productOwner == null) {
            throw new ResourceNotFoundException("Product owner not found for product id: " + productId);
        }

        User admin = getCurrentAdminUser();

        // Check if the product has any orders
        boolean hasOrders = hasProductOrders(productId);

        ProductStatus oldStatus = product.getStatus();

        if ("KitolusAdmin".equals(productOwner.getRole())) {
            // If the owner is an admin, use logical deletion if there are orders
            if (hasOrders) {
                product.setDeleted(true);
                product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setReviewedBy(admin.getUsername());
                productMapper.updateById(product);

                // 记录状态变更历史
                statusHistoryService.recordStatusChange(
                    productId, oldStatus, ProductStatus.DELISTED,
                    admin.getUsername(), "管理员下架并删除", "该商品已被管理员下架并移除。"
                );
            } else {
                // 记录状态变更历史（在物理删除前）
                statusHistoryService.recordStatusChange(
                    productId, oldStatus, ProductStatus.DELISTED,
                    admin.getUsername(), "管理员下架并删除", "该商品已被管理员下架并移除。"
                );
                productMapper.deleteById(productId);
            }
            notificationService.createNotificationForProductRejection(product, "该商品已被管理员下架并移除。", admin);
            return product;
        } else if ("DEVELOPER".equals(productOwner.getRole())) {
            // If the owner is a developer, set status to REJECTED and provide a reason
            product.setStatus(ProductStatus.REJECTED);
            product.setRejectionReason(reason);
            product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
            product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
            product.setReviewedBy(admin.getUsername());
            productMapper.updateById(product);

            // 记录状态变更历史
            statusHistoryService.recordStatusChange(
                productId, oldStatus, ProductStatus.REJECTED,
                admin.getUsername(), "管理员下架产品", reason
            );

            notificationService.createNotificationForProductDelisting(product, reason, admin);
            return product;
        } else {
            // Default behavior for other roles, or if role is not set
            if (hasOrders) {
                product.setDeleted(true);
                product.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setUpdatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
                product.setReviewedBy(admin.getUsername());
                productMapper.updateById(product);

                // 记录状态变更历史
                statusHistoryService.recordStatusChange(
                    productId, oldStatus, ProductStatus.DELISTED,
                    admin.getUsername(), "管理员下架并删除", "该商品已被管理员下架并移除。"
                );
            } else {
                // 记录状态变更历史（在物理删除前）
                statusHistoryService.recordStatusChange(
                    productId, oldStatus, ProductStatus.DELISTED,
                    admin.getUsername(), "管理员下架并删除", "该商品已被管理员下架并移除。"
                );
                productMapper.deleteById(productId);
            }
            notificationService.createNotificationForProductRejection(product, "该商品已被管理员下架并移除。", admin);
            return product;
        }
    }

    /**
     * 检查产品是否有相关的订单记录
     * @param productId 产品ID
     * @return 如果有订单记录返回true，否则返回false
     */
    private boolean hasProductOrders(Long productId) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", productId);
        Long count = orderMapper.selectCount(queryWrapper);
        return count > 0;
    }
}