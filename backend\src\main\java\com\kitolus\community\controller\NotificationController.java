package com.kitolus.community.controller;

import com.kitolus.community.dto.NotificationDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.service.NotificationService;
import com.kitolus.community.service.UserService;
import com.kitolus.community.util.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/notifications")
@CrossOrigin(origins = {"https://kitolus.top", "http://localhost:3000"})
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 验证JWT token并获取用户名
     */
    private String validateTokenAndGetUsername(String authHeader) {
        try {
            String token = authHeader.replace("Bearer ", "");
            return jwtTokenUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            return null;
        }
    }

    @GetMapping
    public ResponseEntity<List<NotificationDTO>> getNotifications(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            User user = userService.findByUsername(username);
            return ResponseEntity.ok(notificationService.getNotificationsForUser(user.getId()));
        } catch (Exception e) {
            return ResponseEntity.ok(Collections.emptyList());
        }
    }

    @GetMapping("/unread-count")
    public ResponseEntity<Map<String, Integer>> getUnreadNotificationCount(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.ok(Collections.singletonMap("count", 0));
            }
            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.ok(Collections.singletonMap("count", 0));
            }
            int count = notificationService.getUnreadNotificationCount(user.getId());
            return ResponseEntity.ok(Collections.singletonMap("count", count));
        } catch (Exception e) {
            // 记录错误但返回默认值，确保前端不会因为后端错误而崩溃
            System.err.println("Error getting unread notification count: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.ok(Collections.singletonMap("count", 0));
        }
    }

    @PostMapping("/{notificationId}/mark-as-read")
    public ResponseEntity<?> markAsRead(@PathVariable Long notificationId, @RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).build();
            }
            // Optional: Add extra validation to ensure the notification belongs to the user
            notificationService.markNotificationAsRead(notificationId);
            return ResponseEntity.ok(Map.of("message", "通知已标记为已读。"));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    @PostMapping("/read-all")
    public ResponseEntity<?> markAllAsRead(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).build();
            }
            User user = userService.findByUsername(username);
            notificationService.markAllNotificationsAsRead(user.getId());
            return ResponseEntity.ok(Map.of("message", "所有通知已标记为已读。"));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    @DeleteMapping("/{notificationId}")
    public ResponseEntity<?> deleteNotification(@PathVariable Long notificationId, @RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).build();
            }

            User user = userService.findByUsername(username);
            boolean deleted = notificationService.deleteNotification(notificationId, user.getId());

            if (deleted) {
                return ResponseEntity.ok(Map.of("message", "通知已删除。"));
            } else {
                return ResponseEntity.status(404).body(Map.of("error", "通知不存在或无权限删除。"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    @DeleteMapping("/delete-all")
    public ResponseEntity<?> deleteAllNotifications(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).build();
            }

            User user = userService.findByUsername(username);
            notificationService.deleteAllNotifications(user.getId());
            return ResponseEntity.ok(Map.of("message", "所有通知已删除。"));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }
}