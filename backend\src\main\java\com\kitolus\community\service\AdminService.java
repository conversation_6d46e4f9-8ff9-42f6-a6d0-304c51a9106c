package com.kitolus.community.service;

import java.util.List;
import com.kitolus.community.dto.DeveloperApplicationDTO;
import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.MonthlyRevenueDTO;
import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.dto.RejectReasonDTO;
import com.kitolus.community.dto.UserAdminViewDTO;
import com.kitolus.community.dto.AdminDashboardStatsDTO;
import com.kitolus.community.entity.DeveloperApplication;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;

public interface AdminService {
    List<UserAdminViewDTO> getAllUsers();

    EarningsSummaryDTO getEarningsSummary();

    AdminDashboardStatsDTO getAdminDashboardStats();

    User updateUserRole(Long userId, String newRole);

    List<Product> getProductsForApproval();

    Product approveProduct(Long productId, String notes);

    Product rejectProduct(Long productId, String reason);

    List<DeveloperApplicationDTO> getDeveloperApplicationsByStatus(String status);

    DeveloperApplication approveDeveloperApplication(Long applicationId);

    DeveloperApplication rejectDeveloperApplication(Long applicationId, String reason);

    void toggleUserStatus(Long userId);

    void deleteUser(Long userId);

    List<ProductDTO> getAllProducts();

    Product delistProduct(Long productId, String reason);
} 