"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"

export interface ComboboxOption {
  value: string
  label: string
}

interface ComboboxProps {
  options: ComboboxOption[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyPlaceholder?: string
}

export const Combobox: React.FC<ComboboxProps> = ({ 
  options, 
  value, 
  onChange, 
  placeholder, 
  searchPlaceholder, 
  emptyPlaceholder 
}) => {
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState("")
  const inputRef = React.useRef<HTMLInputElement>(null)

  React.useEffect(() => {
    if (open) {
      const timeoutId = setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
      return () => clearTimeout(timeoutId)
    }
  }, [open])

  const filteredOptions = React.useMemo(() => {
    if (!search) return options;
    return options.filter(option => 
      option.label.toLowerCase().includes(search.toLowerCase())
    );
  }, [search, options]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between border-zinc-700 bg-zinc-900 hover:bg-zinc-800/80 hover:text-zinc-100 text-zinc-300"
        >
          {value
            ? options.find((option) => option.value === value)?.label
            : placeholder || "Select option"}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-[--radix-popover-trigger-width] p-0 border-zinc-800 bg-zinc-900 text-zinc-200" 
        align="start"
      >
        <div className="p-2 border-b border-zinc-800">
          <Input 
            ref={inputRef}
            placeholder={searchPlaceholder}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="h-9 bg-zinc-800 border-zinc-700"
          />
        </div>
        <ScrollArea className="h-[200px]">
          <div className="p-1">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                    key={option.value}
                  className="flex items-center p-2 text-sm rounded-sm cursor-pointer hover:bg-zinc-800"
                  onClick={() => {
                    onChange(option.value === value ? "" : option.value)
                      setOpen(false)
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === option.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {option.label}
                </div>
              ))
            ) : (
              <p className="p-4 text-center text-sm text-zinc-500">
                {emptyPlaceholder}
              </p>
            )}
          </div>
        </ScrollArea>
        </PopoverContent>
    </Popover>
  )
} 
