/** @type {import('next').NextConfig} */
const nextConfig = {
  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // 确保framer-motion正确加载
  experimental: {
    optimizePackageImports: ['framer-motion'],
  },

  // Webpack配置
  webpack: (config, { isServer }) => {
    // 确保framer-motion在客户端正确加载
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }

    return config;
  },
  // In development, we need to proxy requests to the backend server.
  // This is not needed in production because we will use a reverse proxy like Nginx.
  async rewrites() {
    // 只在开发环境中启用代理
    if (process.env.NODE_ENV === 'development') {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:8080/api/:path*', // Proxy to Backend
        },
        {
          source: '/storage/:path*',
          destination: 'http://localhost:8080/storage/:path*', // Proxy to Backend Storage
        },
      ]
    }
    // 生产环境不使用代理，直接返回空数组
    return []
  },

  // 生产环境CSP配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "connect-src 'self' https://kitolus.top https://api.kitolus.top wss://kitolus.top https://cdn.jsdelivr.net https://translate.googleapis.com https://m1.openfpcdn.io translate.googleapis.com translate.google.com www.google.com www.gstatic.com chrome-extension://bfdogplmndidlpjfhoijckpakkdjkkil/"
          }
        ]
      }
    ]
  },
  // assetPrefix: process.env.NODE_ENV === 'production' ? 'https://kitolus.top' : '',
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'robohash.org',
      },
      {
        protocol: 'https',
        hostname: 'api.multiavatar.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8080',
      },
      {
        protocol: 'https',
        hostname: 'kitolus.top',
      },
      {
        protocol: 'https',
        hostname: 'api.ltzf.cn',
      },
    ],
  },
};

export default nextConfig;