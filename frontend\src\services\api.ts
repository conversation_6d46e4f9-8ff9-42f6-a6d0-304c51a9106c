import axios, { InternalAxiosRequestConfig } from 'axios';
import FingerprintJS from '@fingerprintjs/fingerprintjs';
import { KnowledgeBaseArticle } from '../types/KnowledgeBaseArticle.js';
import { SearchResult } from '../types/SearchResult.js';
import { GlobalSearchResult, SearchSuggestion } from '../types/GlobalSearchResult';
import { User } from '../types/User';
import { UpdatePostRequestDTO } from '../types/UpdatePostRequestDTO';
import { CreateCommentRequestDTO } from '@/types/CreateCommentRequestDTO';
import { CommunityComment } from '@/types/CommunityComment';
import { UpdatePostResponseDTO } from '@/types/UpdatePostResponseDTO';
import { CommunityTopic } from '@/types/CommunityTopic';
import { ProfileDTO } from '@/types/ProfileDTO';
import { UpdateProfileRequestDTO } from '@/types/UpdateProfileRequestDTO';
import { VerificationRequestDTO } from '@/types/VerificationRequestDTO';
import { Product } from '@/types/Product';
import { toast } from 'sonner';
import { WithdrawalRequest } from '@/types/WithdrawalRequest';
import { CreateWithdrawalRequest } from '@/types/CreateWithdrawalRequest';
import { ReviewWithdrawalRequest } from '@/types/ReviewWithdrawalRequest';
import { EarningsSummaryDTO } from '@/types/EarningsSummary';
import { UpdateWithdrawalSettingsDTO } from '@/types/UpdateWithdrawalSettingsDTO';
import { OrderDTO } from '@/types/Order';
import { ProductSalesStats } from '@/types/ProductSalesStats';

// By using module augmentation, we add our custom property to the AxiosRequestConfig.
declare module 'axios' {
    export interface AxiosRequestConfig {
        authRequired?: boolean;
    }
}

const apiService = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',
    timeout: 10000, // 10秒超时
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add a request interceptor to include the token in headers
apiService.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
        // This code can run on server or client.
        // We must check if `window` is available before using browser-specific APIs.
        if (typeof window !== 'undefined') {
            // By default, auth is required. A request can opt out by setting authRequired to false.
            const authRequired = config.authRequired !== false;

            if (authRequired) {
            const token = localStorage.getItem('jwt_token');
            if (token) {
                config.headers['Authorization'] = `Bearer ${token}`;
                }
            }

            // Generate and add device fingerprint
            try {
                const fp = await FingerprintJS.load();
                const result = await fp.get();
                config.headers['X-Device-Fingerprint'] = result.visitorId;
            } catch (error) {
                console.error("Error generating fingerprint:", error);
            }
        }

        return config;
    },
    (error: any) => {
        return Promise.reject(error);
    }
);

// Optional: Add a response interceptor for global error handling
apiService.interceptors.response.use(
    (response: any) => response,
    (error: any) => {
        const { status, data, config } = error.response || {};
        const isAuthError = status === 401 || status === 403;

        if (typeof window !== 'undefined' && isAuthError) {
            // 检查是否在初始化阶段 - 避免在页面加载时因为401错误导致误登出
            const isInitializing = !localStorage.getItem('auth_initialized');

            if (!isInitializing) {
                // Check for specific "disabled account" message from backend
                if (status === 401 && data && typeof data === 'string' && data.includes('disabled')) {
                    toast.error('您的账户已被封禁，请联系管理员。');
                }


                localStorage.removeItem('jwt_token');
                window.dispatchEvent(new Event('auth-error'));
            } else {

            }
        }

        return Promise.reject(error);
    }
);


export default apiService;

// User API
export const getPublicUserProfile = async (username: string): Promise<ProfileDTO> => {
    // This is a public endpoint, so no auth is required.
    const response = await apiService.get(`/api/user/profile/${username}`, { authRequired: false });
    return response.data;
};

export const getMyProfile = async (): Promise<ProfileDTO> => {
    const response = await apiService.get('/api/user/profile');
    return response.data;
};

export const searchUsers = async (query: string): Promise<Array<{id: string, username: string, avatarUrl: string | null}>> => {
    const response = await apiService.get(`/api/user/search?query=${encodeURIComponent(query)}`);
    return response.data;
};

// 获取用户最近活动
export const getUserRecentActivity = async (username: string) => {
    try {
        const response = await apiService.get(`/api/user/${username}/recent-activity`);
        return response.data;
    } catch (error) {
        // 如果API不存在，返回模拟数据
        return {
            recentPosts: [],
            recentComments: []
        };
    }
};

export const getProfile = async (username: string): Promise<ProfileDTO> => {
    // Auth is required by default to get sensitive info
    const response = await apiService.get(`/api/user/profile/${username}`);
    return response.data;
};

export const updateUserProfile = async (profileData: UpdateProfileRequestDTO): Promise<{ profile: ProfileDTO; token?: string }> => {
    const response = await apiService.put('/api/user/profile', profileData);

    // 检查响应是否包含新token（用户名变更时会返回）
    if (response.data.token) {
        return {
            profile: profileData as ProfileDTO, // 使用请求数据作为profile
            token: response.data.token
        };
    } else {
        return {
            profile: profileData as ProfileDTO // 使用请求数据作为profile
        };
    }
};

export const updateUserWithdrawalSettings = async (settings: UpdateWithdrawalSettingsDTO): Promise<any> => {
    const response = await apiService.put('/api/user/withdrawal-settings', settings);
    return response.data;
};

// Knowledge Base API
export const getRandomArticles = async (count: number = 4): Promise<KnowledgeBaseArticle[]> => {
    const response = await apiService.get(`/api/kb/random?count=${count}`);
    return response.data;
};

export const getAllArticles = async (): Promise<KnowledgeBaseArticle[]> => {
    const response = await apiService.get('/api/kb/all');
    return response.data;
};

export const getGroupedArticles = async (): Promise<Record<string, KnowledgeBaseArticle[]>> => {
    const response = await apiService.get('/api/kb/all/grouped');
    return response.data;
}

export const getKbStats = async (): Promise<{ totalArticles: number }> => {
    const response = await apiService.get('/api/kb/stats');
    return response.data;
}

export const getArticleById = async (id: string): Promise<KnowledgeBaseArticle> => {
    const response = await apiService.get(`/api/kb/${id}`);
    return response.data;
};

// Product API
export function getProducts() {
    // ... existing code ...
}

export const searchKb = async (query: string): Promise<SearchResult> => {
    const response = await apiService.get(`/api/kb/search?query=${encodeURIComponent(query)}`);
    return response.data; // The search components expect the data directly.
};

export const getKnowledgeBaseArticle = async (id: string) => {
    const response = await apiService.get(`/kb/${id}`);
    return response.data;
};

export const searchKnowledgeBase = async (query: string) => {
    const response = await apiService.get(`/kb/search?q=${query}`);
    return response.data;
};

// 全局搜索API
export const globalSearch = async (query: string): Promise<GlobalSearchResult> => {
    try {
        const response = await apiService.get(`/api/search/global?query=${encodeURIComponent(query)}`);
        return response.data;
    } catch (error) {
        // 如果后端API不存在，返回模拟数据进行演示

        // 并行搜索用户和帖子
        const [users, posts] = await Promise.allSettled([
            searchUsers(query).catch(() => []),
            searchCommunityPosts(query).catch(() => [])
        ]);

        const usersResult = users.status === 'fulfilled' ? users.value : [];
        const postsResult = posts.status === 'fulfilled' ? posts.value : [];

        // 转换用户数据为完整的User对象
        const transformedUsers = usersResult.map(user => {
            // 生成一个基于用户名的稳定序列号，用于头像生成
            const serialNumber = `search-${user.username}-${user.id}`;

            // 后端已经返回完整的头像URL路径，直接使用
            const fullAvatarUrl = user.avatarUrl || null;

            return {
                id: parseInt(user.id),
                username: user.username,
                email: '', // 搜索结果中不包含邮箱
                avatarUrl: user.avatarUrl,
                avatarVersion: 1, // 默认版本
                bannerUrl: null,
                bannerVersion: 1,
                fullAvatarUrl: fullAvatarUrl,
                fullBannerUrl: null,
                role: 'USER',
                serialNumber: serialNumber,
                createdAt: new Date().toISOString(),
                enabled: true,
                isAdmin: false,
                isDeveloper: false,
                postCount: 0,
                commentCount: 0
            };
        });

        return {
            users: transformedUsers,
            posts: postsResult,
            articles: [], // 暂时为空，可以后续添加
            totalResults: transformedUsers.length + postsResult.length,
            query
        };
    }
};

// 搜索建议API
export const getSearchSuggestions = async (query: string): Promise<SearchSuggestion[]> => {
    if (!query.trim()) return [];

    try {
        const response = await apiService.get(`/api/search/suggestions?query=${encodeURIComponent(query)}`);
        return response.data;
    } catch (error) {
        // 如果后端API不存在，返回基于现有API的建议

        try {
            // 并行搜索用户和帖子
            const [usersResult, postsResult] = await Promise.allSettled([
                searchUsers(query).catch(() => []),
                searchCommunityPosts(query).catch(() => [])
            ]);

            const users = usersResult.status === 'fulfilled' ? usersResult.value : [];
            const posts = postsResult.status === 'fulfilled' ? postsResult.value : [];

            const suggestions: SearchSuggestion[] = [];

            // 添加用户建议（最多3个）
            suggestions.push(...users.slice(0, 3).map(user => ({
                type: 'user' as const,
                id: user.username,
                title: user.username,
                subtitle: '用户',
                avatarUrl: user.avatarUrl // 后端已经返回完整路径
            })));

            // 添加帖子建议（最多3个）
            suggestions.push(...posts.slice(0, 3).map(post => ({
                type: 'post' as const,
                id: post.id,
                title: post.title,
                subtitle: `by ${post.author?.username || '未知用户'}`
            })));

            return suggestions.slice(0, 6); // 总共最多6个建议
        } catch {
            return [];
        }
    }
};

// 搜索社区帖子
export const searchCommunityPosts = async (query: string) => {
    try {
        // 尝试使用专门的搜索端点
        const response = await apiService.get(`/api/community/posts/search?query=${encodeURIComponent(query)}`);
        return response.data;
    } catch (error) {
        // 如果搜索端点不存在，使用降级方案：获取所有帖子然后前端过滤

        try {
            const allPosts = await getCommunityPosts();

            if (!query.trim()) {
                return allPosts;
            }

            const searchTerm = query.toLowerCase().trim();

            // 前端过滤：搜索标题和内容
            const filteredPosts = allPosts.filter((post: any) => {
                const titleMatch = post.title?.toLowerCase().includes(searchTerm);
                const contentMatch = post.content?.toLowerCase().includes(searchTerm);
                const authorMatch = post.author?.username?.toLowerCase().includes(searchTerm);

                return titleMatch || contentMatch || authorMatch;
            });

            return filteredPosts;
        } catch (fallbackError) {
            return [];
        }
    }
};



// --- Community Posts ---
export const getCommunityPosts = async () => {
    const response = await apiService.get('/api/community/posts');
    return response.data;
};

export const getCommunityPostById = async (id: string) => {
    const response = await apiService.get(`/api/community/posts/${id}`);
    return response.data;
};

export const deletePost = async (postId: number): Promise<void> => {
    const response = await apiService.delete(`/api/community/posts/${postId}`);
    return response.data;
};

export const togglePostLike = async (id: number | string) => {
    // Auth is required by default, no need to specify token here
    const response = await apiService.post(`/api/community/posts/${id}/like`);
    return response.data;
};

export const updateCommunityPost = async (postId: number, data: UpdatePostRequestDTO): Promise<UpdatePostResponseDTO> => {
    const response = await apiService.put<UpdatePostResponseDTO>(`/api/community/posts/${postId}`, data);
    return response.data;
};

// --- Community Comments ---
export const getComments = async (postId: number | string) => {
    const response = await apiService.get(`/api/community/posts/${postId}/comments`);
    return response.data;
};

export const createComment = async (data: CreateCommentRequestDTO): Promise<CommunityComment> => {
    const response = await apiService.post<CommunityComment>('/api/community/comments', data);
    return response.data;
};

export const deleteComment = async (commentId: number | string) => {
    const response = await apiService.delete(`/api/community/comments/${commentId}`);
    return response.data;
};

export const togglePinPost = async (postId: number) => {
    const response = await apiService.post(`/api/community/posts/${postId}/pin`);
    return response.data;
};

export const getPinnedPosts = async () => {
    const response = await apiService.get('/api/community/posts/pinned');
    return response.data;
};

export const getNonPinnedPosts = async () => {
    const response = await apiService.get('/api/community/posts/non-pinned');
    return response.data;
};

export const createCommunityPost = async (postData: { title: string; content: string; partition: string }, token: string) => {
    const response = await apiService.post('/api/community/posts', postData, {
        headers: {
            Authorization: `Bearer ${token}`
        }
    });
    return response.data;
};

// --- Notifications ---

export const getNotifications = async () => {
    const response = await apiService.get('/api/notifications');
    return response.data;
};

export const getUnreadNotificationCount = async (): Promise<{ count: number }> => {
    try {
        const response = await apiService.get<{ count: number }>('/api/notifications/unread-count');
        return {
            count: response.data?.count || 0
        };
    } catch (error) {

        // 返回默认值而不是抛出错误，确保UI不会崩溃
        return { count: 0 };
    }
};

export const markNotificationAsRead = async (notificationId: number) => {
    const response = await apiService.post(`/api/notifications/${notificationId}/mark-as-read`);
    return response.data;
};

export const markAllNotificationsAsRead = async () => {
    const response = await apiService.post('/api/notifications/read-all');
    return response.data;
};

export const deleteNotification = async (notificationId: number) => {
    const response = await apiService.delete(`/api/notifications/${notificationId}`);
    return response.data;
};

export const deleteAllNotifications = async () => {
    const response = await apiService.delete('/api/notifications/delete-all');
    return response.data;
};

export const getDeveloperTransactions = async (): Promise<OrderDTO[]> => {
    const response = await apiService.get('/api/earnings/my-transactions');
    return response.data;
};

export const getMyProductSalesStats = async (): Promise<ProductSalesStats[]> => {
    const response = await apiService.get('/api/earnings/my-product-sales');
    return response.data;
};

export const getCommunityTopics = async (): Promise<CommunityTopic[]> => {
    const response = await apiService.get<CommunityTopic[]>('/community/topics');
    return response.data;
};

// --- Payment ---
export const createPaymentOrder = async (productId: number): Promise<{ codeUrl: string, orderId: string }> => {
    const response = await apiService.post('/api/payment/create', { productId }, {
        authRequired: true
    });
    // The backend is expected to return a map which might be directly the `data` part of the Lantu response
    // e.g. { codeUrl: "...", orderId: "..." }
    return response.data;
};

export const getPaymentStatus = async (orderId: string): Promise<{ status: string }> => {
    const response = await apiService.get(`/api/payment/status`, { params: { orderId } });
    return response.data;
};

// --- Earnings & Withdrawals API ---

export const getMyEarningsSummary = async (): Promise<EarningsSummaryDTO> => {
    const response = await apiService.get('/api/earnings/my-summary');
    return response.data;
};

export const createWithdrawalRequest = async (data: CreateWithdrawalRequest): Promise<WithdrawalRequest> => {
    const response = await apiService.post<WithdrawalRequest>('/api/earnings/withdrawal-requests', data);
    return response.data;
};

export const getMyWithdrawalRequests = async (): Promise<WithdrawalRequest[]> => {
    const response = await apiService.get<WithdrawalRequest[]>('/api/earnings/withdrawal-requests');
    return response.data;
};

export const getAllWithdrawalRequests = async (): Promise<WithdrawalRequest[]> => {
    const response = await apiService.get<WithdrawalRequest[]>('/api/admin/withdrawal-requests');
    return response.data;
};

export const reviewWithdrawalRequest = async (requestId: number, data: ReviewWithdrawalRequest): Promise<WithdrawalRequest> => {
    const response = await apiService.put<WithdrawalRequest>(`/api/admin/withdrawal-requests/${requestId}/review`, data);
    return response.data;
};


// --- Admin Product Management ---
export const getPendingProducts = async () => {
    const response = await apiService.get('/api/admin/products/pending');
    return response.data;
};

export const approveProduct = async (productId: number, approvalNotes: string) => {
    const response = await apiService.post(`/api/admin/products/${productId}/approve`, { approvalNotes });
    return response.data;
};

export const rejectProduct = async (productId: number, reason: string) => {
    const response = await apiService.post(`/api/admin/products/${productId}/reject`, { reason });
    return response.data;
};

// --- Developer Product Creation ---
export const createProduct = async (formData: FormData) => {
    const response = await apiService.post('/api/products/upload', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return response.data;
};

export const getDeveloperProducts = async () => {
    const response = await apiService.get('/api/products/my-products');
    return response.data;
};

export const updateProduct = async (productId: number, productData: { name: string; price: number; description: string; downloadUrl: string; imageUrl?: string; }) => {
    // The data will be sent as JSON by default
    const response = await apiService.put(`/api/products/${productId}`, productData);
    return response.data;
};

export const deleteProduct = async (productId: number) => {
    const response = await apiService.delete(`/api/products/by-developer/${productId}`);
    return response.data;
};

export const getPurchasedProducts = async (): Promise<Product[]> => {
    const response = await apiService.get('/api/products/purchased');
    return response.data;
};

// --- Admin User Management ---
export const getAllUsers = async () => {
    const response = await apiService.get('/api/admin/users');
    return response.data;
};

export const updateUserRole = async (userId: number, newRole: string) => {
    // The interceptor will handle the token.
    const response = await apiService.post(`/api/admin/users/${userId}/role`, { newRole });
    return response.data;
};

export const toggleUserStatus = async (userId: number) => {
    // The interceptor will handle the token.
    await apiService.post(`/api/admin/users/${userId}/toggle-status`, {});
};

export const deleteUser = async (userId: number) => {
    // The interceptor will handle the token.
    await apiService.delete(`/api/admin/users/${userId}`);
};

// Developer Applications
export const getPendingDeveloperApplications = async (token: string) => {
    // ... existing code ...
};

export const getProductById = async (productId: number) => {
    const response = await apiService.get(`/api/products/${productId}`);
    return response.data;
};

export const delistProduct = async (productId: number, reason: string) => {
    const response = await apiService.post(`/api/admin/products/${productId}/delist`, { reason });
    return response.data;
};

export const getAdminProductDetails = async (id: number): Promise<Product> => {
    const response = await apiService.get(`/api/admin/products/${id}`);
    return response.data;
};

export const getAllProductsForAdmin = async (): Promise<Product[]> => {
    const response = await apiService.get('/api/admin/products/all');
    return response.data;
};

export const getDeveloperApplications = async (status: string): Promise<any> => {
    const response = await apiService.get(`/api/admin/developer-applications?status=${status}`);
    return response.data;
};

export const getAdminDashboardStats = async () => {
    const response = await apiService.get('/api/admin/dashboard-stats');
    return response.data;
};

export const getAdminEarningsSummary = async (): Promise<any> => {
    const response = await apiService.get(`/api/admin/earnings-summary`);
    return response.data;
};

export const getDeveloperEarningsSummary = async (): Promise<any> => {
    const response = await apiService.get(`/api/earnings/my-summary`);
    return response.data;
};

// Product Status History
export const getProductStatusHistory = async (productId: number): Promise<any[]> => {
    const response = await apiService.get(`/api/admin/products/${productId}/status-history`);
    return response.data;
};

// User-specific product fetches
export const getMyProducts = async (): Promise<Product[]> => {
    const response = await apiService.get<Product[]>('/api/admin/my-products');
    return response.data;
};

// Export the apiService for use in other modules
export { apiService };
