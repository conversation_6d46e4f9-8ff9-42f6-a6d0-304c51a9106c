package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 消息实体
 * 用于持久化存储用户消息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("messages")
public class Message {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("sender_id")
    private Long senderId;

    @TableField("receiver_id")
    private Long receiverId; // null表示系统消息或广播消息

    @TableField("conversation_id")
    private String conversationId; // 会话ID，用于群聊

    @TableField("message_type")
    private MessageType messageType;

    @TableField("title")
    private String title;

    @TableField("content")
    private String content;

    @TableField("status")
    private MessageStatus status = MessageStatus.SENT;

    @TableField("is_read")
    private Boolean isRead = false;

    @TableField("read_at")
    private LocalDateTime readAt;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("metadata")
    private String metadata; // 存储额外的消息元数据

    // 软删除字段（暂时注释掉，等数据库迁移完成后再启用）
    // @TableField("deleted_for_sender")
    // private Boolean deletedForSender = false; // 发送方是否删除了此消息

    // @TableField("deleted_for_receiver")
    // private Boolean deletedForReceiver = false; // 接收方是否删除了此消息

    // 查询时的额外字段（不存储在数据库中）
    @TableField(exist = false)
    private String senderName;

    @TableField(exist = false)
    private String senderAvatar;

    @TableField(exist = false)
    private String receiverName;

    @TableField(exist = false)
    private String receiverAvatar;

    public enum MessageType {
        PRIVATE,        // 私信
        SYSTEM,         // 系统消息
        BROADCAST,      // 广播消息
        GROUP           // 群组消息
        // 注意：通知功能使用独立的Notification实体，不在此处定义
    }

    public enum MessageStatus {
        SENT,           // 已发送
        DELIVERED,      // 已送达
        READ,           // 已读
        FAILED,         // 发送失败
        DELETED         // 已删除
    }

    // 构造函数
    public Message() {
        this.createdAt = LocalDateTime.now();
    }

    public Message(Long senderId, Long receiverId, MessageType messageType, String content) {
        this();
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.messageType = messageType;
        this.content = content;
    }

    // 便利方法
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
        this.status = MessageStatus.READ;
        this.updatedAt = LocalDateTime.now();
    }

    public boolean isSystemMessage() {
        return messageType == MessageType.SYSTEM;
    }

    public boolean isPrivateMessage() {
        return messageType == MessageType.PRIVATE;
    }

    public void setStatus(MessageStatus status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
        if (isRead && this.readAt == null) {
            this.readAt = LocalDateTime.now();
            this.status = MessageStatus.READ;
        }
        this.updatedAt = LocalDateTime.now();
    }

}
