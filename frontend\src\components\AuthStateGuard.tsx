'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { authStateManager } from '@/lib/authStateManager';

/**
 * 认证状态守护组件
 * 负责监控和恢复认证状态，防止页面刷新后状态丢失
 */
export const AuthStateGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token, isAuthenticated, login } = useAuth();

  useEffect(() => {
    console.log('🛡️ 启动认证状态守护...');

    // 检查是否需要恢复状态
    const checkAndRestoreState = () => {
      const { token: storedToken, userData } = authStateManager.restoreAuthState();

      if (storedToken && !token) {
        console.log('🔄 检测到存储的token但当前状态为空，尝试恢复状态');

        // 验证token格式和有效性
        if (authStateManager.validateTokenFormat(storedToken) &&
            !authStateManager.isTokenExpired(storedToken)) {
          console.log('✅ Token有效，恢复认证状态');
          // 延迟重新加载，避免频繁刷新
          setTimeout(() => {
            const currentToken = localStorage.getItem('jwt_token');
            if (currentToken && !token) {
              console.log('🔄 确认需要恢复状态，重新加载页面');
              window.location.reload();
            }
          }, 2000);
        } else {
          console.log('❌ Token无效或已过期，清除状态');
          authStateManager.clearAuthState();
        }
      }
    };

    // 页面加载后检查状态
    const timeoutId = setTimeout(checkAndRestoreState, 1000);

    // 监听页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('👁️ 页面变为可见，检查认证状态');
        checkAndRestoreState();
      }
    };

    // 监听焦点变化
    const handleFocus = () => {
      console.log('🎯 页面获得焦点，检查认证状态');
      checkAndRestoreState();
    };

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'jwt_token') {
        console.log('📦 检测到token变化，检查状态一致性');
        setTimeout(checkAndRestoreState, 100);
      }
    };

    // 定期检查状态一致性
    const intervalId = setInterval(() => {
      const { token: storedToken } = authStateManager.restoreAuthState();
      
      if (storedToken && !token) {
        console.log('⚠️ 定期检查发现状态不一致，尝试恢复');
        checkAndRestoreState();
      } else if (!storedToken && token) {
        console.log('⚠️ 定期检查发现token被清除但状态仍存在');
        // 这种情况可能是其他标签页登出了，需要同步状态
      }
    }, 10000); // 每10秒检查一次

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearTimeout(timeoutId);
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [token]);

  // 监控认证状态变化
  useEffect(() => {
    if (isAuthenticated && token) {
      console.log('✅ 认证状态正常，保存到状态管理器');
      authStateManager.saveAuthState(token);
    }
  }, [isAuthenticated, token]);

  return <>{children}</>;
};

export default AuthStateGuard;
