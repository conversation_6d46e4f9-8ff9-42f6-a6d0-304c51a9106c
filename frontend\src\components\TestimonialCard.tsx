import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface TestimonialCardProps {
  name: string;
  role: string;
  avatarSrc: string;
  avatarFallback: string;
  children: React.ReactNode;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ name, role, avatarSrc, avatarFallback, children }) => {
  return (
    <Card className="bg-background/50 border-border">
      <CardContent className="pt-6">
        <div className="flex items-center mb-4">
          <Avatar>
            <AvatarImage src={avatarSrc} alt={name} />
            <AvatarFallback>{avatarFallback}</AvatarFallback>
          </Avatar>
          <div className="ml-4">
            <p className="font-bold text-foreground">{name}</p>
            <p className="text-sm text-muted-foreground">{role}</p>
          </div>
        </div>
        <p className="text-foreground/80">{children}</p>
      </CardContent>
    </Card>
  );
};

export default TestimonialCard; 