/**
 * 消息系统API服务
 */

import { apiService } from './api';
import {
  Message,
  MessageType,
  MessageQueryParams,
  SendMessageRequest,
  MessageResponse,
  MessagePageResponse,
  ConversationPageResponse,
  MessageStats,
  Conversation,
  MessageStatus,
  OnlineStatus
} from '@/types/Message';

// 获取消息统计
export const getMessageStats = async (): Promise<MessageStats> => {
  try {
    const response = await apiService.get('/api/messages/unread/count');
    return {
      totalUnread: response.data.count || 0,
      privateUnread: response.data.count || 0,
      systemUnread: 0,
      communityUnread: 0,
      announcementUnread: 0
    };
  } catch (error) {
    console.error('Failed to get message stats:', error);
    return {
      totalUnread: 0,
      privateUnread: 0,
      systemUnread: 0,
      communityUnread: 0,
      announcementUnread: 0
    };
  }
};

// 获取消息列表
export const getMessages = async (params: MessageQueryParams = {}): Promise<MessagePageResponse> => {
  try {
    let endpoint = '/api/messages/list';

    // 根据消息类型选择不同的端点
    if (params.type === MessageType.PRIVATE) {
      endpoint = '/api/messages/private';
    } else if (params.type === MessageType.SYSTEM) {
      endpoint = '/api/messages/system';
    }

    const response = await apiService.get(endpoint, {
      params: {
        page: params.page || 0,
        size: params.limit || 20
      }
    });
    return {
      messages: response.data.messages || [],
      total: response.data.totalElements || 0,
      page: response.data.currentPage || 0,
      limit: 20,
      hasMore: response.data.hasNext || false
    };
  } catch (error) {
    console.error('Failed to get messages:', error);
    return {
      messages: [],
      total: 0,
      page: 0,
      limit: 20,
      hasMore: false
    };
  }
};

// 获取会话列表（新的聊天界面）
export const getConversations = async () => {
  const response = await apiService.get('/api/messages/conversations');
  return response.data;
};

// 获取与特定用户的对话消息 - 简化版本
export const getConversationMessages = async (otherUserId: number, page: number = 0, size: number = 50) => {
  const response = await apiService.get(`/api/messages/conversation/${otherUserId}`, {
    params: { page, size }
  });
  return response.data;
};

// 旧的会话列表方法（保持兼容性）
export const getConversationsOld = async (page = 1, limit = 20): Promise<ConversationPageResponse> => {
  try {
    const response = await apiService.get('/api/messages/contacts');
    const contacts = response.data.contacts || [];

    // 转换为会话格式
    const conversations = contacts.map((contact: any, index: number) => ({
      id: `conv_${contact[0]}_${Date.now()}`,
      participantIds: [contact[0]],
      lastMessage: null,
      unreadCount: 0,
      updatedAt: contact[1] || new Date().toISOString(),
      type: 'PRIVATE' as const
    }));

    return {
      conversations,
      total: conversations.length,
      page: page - 1,
      limit,
      hasMore: false
    };
  } catch (error) {
    console.error('Failed to get conversations:', error);
    return {
      conversations: [],
      total: 0,
      page: 0,
      limit,
      hasMore: false
    };
  }
};

// 获取特定会话的消息 - 旧版本（保持兼容性）
export const getConversationMessagesOld = async (
  conversationId: string,
  page = 1,
  limit = 50
): Promise<{
  messages: Message[];
  hasMore: boolean;
  page: number;
  total: number;
}> => {
  // 暂时使用私信列表，后续可以扩展
  const response = await apiService.get('/api/messages/private', {
    params: { page: page - 1, size: limit }
  });
  return {
    messages: response.data.messages || [],
    hasMore: response.data.hasNext || false,
    page,
    total: response.data.totalElements || 0
  };
};

// 发送消息
export const sendMessage = async (messageData: SendMessageRequest): Promise<MessageResponse> => {
  const response = await apiService.post('/api/message-stream/send', messageData);
  return response.data;
};

// 发送私信
export const sendPrivateMessage = async (
  receiverId: number,
  content: string,
  title?: string
): Promise<MessageResponse> => {
  const response = await apiService.post('/api/messages/private', {
    receiverId,
    content,
    title: title || '私信'
  });
  return response.data;
};

// 标记消息为已读
export const markMessageAsRead = async (messageId: number): Promise<MessageResponse> => {
  try {
    const response = await apiService.post(`/api/messages/${messageId}/read`);
    return {
      success: response.data.success || false,
      message: response.data.messageId ? `Message ${response.data.messageId} marked as read` : 'Message marked as read'
    };
  } catch (error: any) {
    console.error('Failed to mark message as read:', error);
    return {
      success: false,
      message: error.response?.data?.error || 'Failed to mark message as read'
    };
  }
};

// 删除消息 - 带重试机制
export const deleteMessage = async (messageId: number, retries = 2): Promise<MessageResponse> => {
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await apiService.delete(`/api/messages/${messageId}`, {
        timeout: 10000 // 10秒超时
      });

      return {
        success: response.data.success || true,
        message: 'Message deleted successfully'
      };
    } catch (error: any) {
      console.error(`Delete message ${messageId} attempt ${attempt + 1} failed:`, error);

      // 如果是最后一次尝试，返回错误
      if (attempt === retries) {
        return {
          success: false,
          message: error.response?.data?.error || 'Failed to delete message'
        };
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
    }
  }

  return {
    success: false,
    message: 'Failed to delete message after retries'
  };
};

// 批量标记消息为已读 - 暂时使用单个标记的方式
export const markMessagesAsRead = async (messageIds: number[]): Promise<void> => {
  // 暂时使用循环调用单个标记API
  for (const messageId of messageIds) {
    try {
      await markMessageAsRead(messageId);
    } catch (error) {
      console.error(`Failed to mark message ${messageId} as read:`, error);
    }
  }
};

// 标记所有消息为已读 - 暂时禁用
export const markAllMessagesAsRead = async (): Promise<void> => {
  console.warn('markAllMessagesAsRead: Not implemented yet');
  // TODO: 实现批量标记功能
};

// 删除消息功能已在上面定义，这里删除重复定义

// 批量删除消息 - 优化性能，减少并发数
export const deleteMessages = async (messageIds: number[]): Promise<MessageResponse> => {
  if (messageIds.length === 0) {
    return {
      success: true,
      message: '没有消息需要删除'
    };
  }

  console.log(`开始批量删除 ${messageIds.length} 条消息`);
  let successCount = 0;
  let errorCount = 0;

  // 减少批次大小，避免服务器压力
  const batchSize = 3; // 从5减少到3
  const totalBatches = Math.ceil(messageIds.length / batchSize);

  for (let i = 0; i < messageIds.length; i += batchSize) {
    const batch = messageIds.slice(i, i + batchSize);
    const currentBatch = Math.floor(i / batchSize) + 1;

    console.log(`处理批次 ${currentBatch}/${totalBatches}，包含 ${batch.length} 条消息`);

    // 串行处理，避免并发压力
    for (const messageId of batch) {
      try {
        const result = await deleteMessage(messageId, 1); // 减少重试次数
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
          console.warn(`删除消息 ${messageId} 失败: ${result.message}`);
        }
      } catch (error: any) {
        errorCount++;
        console.error(`删除消息 ${messageId} 异常:`, error);
      }

      // 每个消息之间添加小延迟
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // 批次之间添加较长延迟
    if (i + batchSize < messageIds.length) {
      console.log(`批次 ${currentBatch} 完成，等待 500ms 后继续...`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  console.log(`批量删除完成: 成功 ${successCount} 条，失败 ${errorCount} 条`);

  return {
    success: errorCount === 0,
    message: errorCount === 0
      ? `成功删除 ${successCount} 条消息`
      : `删除了 ${successCount} 条消息，${errorCount} 条失败`
  };
};

// 搜索消息 - 暂时禁用
export const searchMessages = async (
  query: string,
  page = 1,
  limit = 50
): Promise<{
  messages: Message[];
  hasMore: boolean;
  page: number;
  total: number;
}> => {
  console.warn('searchMessages: Not implemented yet');
  // TODO: 实现消息搜索功能
  return {
    messages: [],
    hasMore: false,
    page,
    total: 0
  };
};

// 获取与特定用户的会话 - 暂时禁用
export const getOrCreateConversation = async (userId: number): Promise<Conversation> => {
  console.warn('getOrCreateConversation: Not implemented yet');
  // TODO: 实现会话创建功能
  return {
    id: `temp_${userId}_${Date.now()}`,
    participants: [{
      id: userId,
      username: `user_${userId}`,
      email: '',
      avatarUrl: null,
      avatarVersion: 0,
      bannerUrl: null,
      bannerVersion: 0,
      fullAvatarUrl: null,
      fullBannerUrl: null,
      role: 'USER',
      serialNumber: '',
      createdAt: new Date().toISOString(),
      enabled: true,
      isAdmin: false,
      isDeveloper: false,
      postCount: 0,
      commentCount: 0
    }],
    lastMessage: undefined,
    lastMessageAt: new Date().toISOString(),
    unreadCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
};

// 获取在线用户状态
export const getOnlineStatus = async (userIds: number[]): Promise<OnlineStatus[]> => {
  const response = await apiService.post('/api/messages/online-status', { userIds });
  return response.data;
};

// 更新消息状态
export const updateMessageStatus = async (
  messageId: number,
  status: MessageStatus
): Promise<MessageResponse> => {
  const response = await apiService.patch(`/api/messages/${messageId}/status`, { status });
  return response.data;
};

// 获取未读消息数量
export const getUnreadMessageCount = async (): Promise<{ count: number }> => {
  const response = await apiService.get('/api/messages/unread-count');
  return response.data;
};

// 创建系统通知
export const createSystemNotification = async (
  title: string,
  content: string,
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT' = 'NORMAL'
): Promise<MessageResponse> => {
  const response = await apiService.post('/api/messages/system-notification', {
    title,
    content,
    priority
  });
  return response.data;
};

// 获取消息详情
export const getMessageById = async (messageId: number): Promise<Message> => {
  const response = await apiService.get(`/api/messages/${messageId}`);
  return response.data;
};

// 回复消息
export const replyToMessage = async (
  messageId: number,
  content: string
): Promise<MessageResponse> => {
  const response = await apiService.post(`/api/messages/${messageId}/reply`, { content });
  return response.data;
};

// 转发消息
export const forwardMessage = async (
  messageId: number,
  receiverIds: number[]
): Promise<MessageResponse> => {
  const response = await apiService.post(`/api/messages/${messageId}/forward`, { receiverIds });
  return response.data;
};

// 举报消息
export const reportMessage = async (
  messageId: number,
  reason: string
): Promise<MessageResponse> => {
  const response = await apiService.post(`/api/messages/${messageId}/report`, { reason });
  return response.data;
};

// 屏蔽用户
export const blockUser = async (userId: number): Promise<MessageResponse> => {
  const response = await apiService.post('/api/messages/block-user', { userId });
  return response.data;
};

// 取消屏蔽用户
export const unblockUser = async (userId: number): Promise<MessageResponse> => {
  const response = await apiService.delete(`/api/messages/block-user/${userId}`);
  return response.data;
};

// 获取屏蔽用户列表
export const getBlockedUsers = async (): Promise<{ users: any[] }> => {
  const response = await apiService.get('/api/messages/blocked-users');
  return response.data;
};

// 删除会话 - 使用后端批量删除接口
export const deleteConversation = async (otherUserId: number): Promise<MessageResponse> => {
  try {
    const response = await apiService.delete(`/api/messages/conversation/${otherUserId}`);

    return {
      success: response.data.success || true,
      message: response.data.message || `成功删除与用户 ${otherUserId} 的会话`
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.error || '删除会话失败'
    };
  }
};

// 清空所有会话记录 - 暂时禁用，提供友好提示
export const clearAllConversations = async (): Promise<MessageResponse> => {
  // 由于后端接口限制，暂时不支持批量清空
  // 用户需要逐个删除会话
  return {
    success: false,
    message: '暂时不支持批量清空功能，请逐个删除会话'
  };
};


