'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import {
    getCommunityPostById,
    createComment,
    deletePost,
    deleteComment,
    togglePostLike,
    updateCommunityPost,
    togglePinPost
} from '@/services/api';
import { CommunityPost } from '@/types/CommunityPost';
import { CommunityComment } from '@/types/CommunityComment';
import { useAuth } from '../../../../contexts/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import UserMentionInput from '@/components/UserMentionInput';
import { Skeleton } from '@/components/ui/skeleton';
import { resolveAvatarUrl } from '@/lib/utils';
import { toast } from 'sonner';
import { ArrowLeft, Send, X, ThumbsUp, MessageSquare, AtSign, Pin, PinOff } from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { getPartitionDisplayInfoFromTopic } from '@/lib/partition-mapping';
import { CommentItem } from '../../components/CommentItem';
import AvatarDisplay from '@/components/AvatarDisplay';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Bold, Italic, Code, Link as LinkIcon, List, Quote, Image as ImageIcon } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const PostPage = () => {
    const params = useParams();
    const router = useRouter();
    const searchParams = useSearchParams();
    const id = Number(params.id);
    const { user, loading: authLoading } = useAuth();

    const [post, setPost] = useState<CommunityPost | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [comment, setComment] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [replyingTo, setReplyingTo] = useState<{ id: number; username: string } | null>(null);
    const commentInputRef = useRef<HTMLTextAreaElement>(null);
    
    const [isEditing, setIsEditing] = useState(false);
    const [editedTitle, setEditedTitle] = useState('');
    const [editedContent, setEditedContent] = useState('');
    const [isSaving, setIsSaving] = useState(false);


    const editorRef = useRef<any>(null); // For markdown editor

    // Function to process mentions into clickable links
    const processMentions = (text: string, validMentions?: string[]) => {
        if (!validMentions || validMentions.length === 0) {
            return text; // Return original text if no valid mentions
        }
        // This regex finds @ followed by allowed username characters
        return text.replace(/@([a-zA-Z0-9_-]+)/g, (match, username) => {
            if (validMentions.includes(username)) {
                return `[${match}](/profile/${username})`;
            }
            return match; // Return the original @username text if not valid
        });
    };

    const handleEditClick = () => {
        if (!post) return;
        setIsEditing(true);
        setEditedTitle(post.title);
        setEditedContent(post.content);
    };

    const handleCancelEdit = () => {
        setIsEditing(false);
    };

    const handleSaveClick = async () => {
        setIsSaving(true);
        try {
            if (post) {
                const updateResponse = await updateCommunityPost(post.id, { title: editedTitle, content: editedContent });
                
                setPost(currentPost => {
                    if (!currentPost) return null;
                    return {
                        ...currentPost,
                        title: editedTitle,
                        content: editedContent,
                        updatedAt: updateResponse.updatedAt,
                    };
                });
                
                setIsEditing(false);
                toast.success('帖子已成功更新！');
            }
        } catch (error) {
            console.error('Failed to save post', error);
            toast.error('保存帖子失败，请稍后重试');
        } finally {
            setIsSaving(false);
        }
    };

    const handleToggleLike = async () => {
        if (!user) {
            toast.error("请先登录再点赞");
            return;
        }
        if (!post) return;

        try {
            const isNowLiked: boolean = await togglePostLike(post.id);

            setPost(currentPost => {
                if (!currentPost) return null;
                const currentLikes = currentPost._count?.likes ?? 0;
                const newLikes = isNowLiked ? currentLikes + 1 : Math.max(0, currentLikes - 1);

                return {
                    ...currentPost,
                    likedByCurrentUser: isNowLiked,
                    _count: {
                        ...currentPost._count,
                        comments: currentPost._count?.comments ?? 0,
                        likes: newLikes,
                    },
                };
            });
        } catch (error) {
            toast.error("操作失败，请稍后重试");
            console.error(`Failed to toggle like for post ${post.id}:`, error);
        }
    };

    useEffect(() => {
        if (id && !authLoading) {
            const fetchPost = async () => {
                try {
                    setIsLoading(true);
                    const data = await getCommunityPostById(String(id));
                    setPost(data);
                } catch (error) {
                    console.error('Failed to fetch post', error);
                    toast.error('帖子加载失败');
                } finally {
                    setIsLoading(false);
                }
            };
            fetchPost();
        }
    }, [id, authLoading, user]);

    useEffect(() => {
        // This effect should only run if there's a specific comment to scroll to.
        const commentIdToScroll = searchParams.get('commentId');
        if (post && post.comments && commentIdToScroll) {
            const commentElement = document.getElementById(`comment-${commentIdToScroll}`);
            if (commentElement) {
                setTimeout(() => {
                    commentElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                    });
                    commentElement.classList.add('highlight-comment');
                    setTimeout(() => {
                        commentElement.classList.remove('highlight-comment');
                    }, 2000); // Highlight for 2 seconds
                }, 100); // Small delay to ensure DOM is fully ready
            }
        }
    }, [post, searchParams]);

    const handleCommentDelete = async (commentIdToDelete: number) => {
        // Find all comments to be deleted (the comment and all its descendants)
        const commentsToDelete = new Set<number>([commentIdToDelete]);
        const findChildren = (parentId: number) => {
            post?.comments?.forEach(c => {
                if (c.parentId === parentId) {
                    commentsToDelete.add(c.id);
                    findChildren(c.id);
                }
            });
        };
        findChildren(commentIdToDelete);

        // Optimistically update the UI
        const originalPost = post;
        setPost(prevPost => {
            if (!prevPost) return null;
            const updatedComments = prevPost.comments?.filter(c => !commentsToDelete.has(c.id));
            const newCommentCount = (prevPost._count?.comments ?? 0) - commentsToDelete.size;
            return {
                ...prevPost,
                comments: updatedComments,
                _count: {
                    ...(prevPost._count),
                    likes: prevPost._count?.likes ?? 0,
                    comments: newCommentCount,
                },
            };
        });

        try {
            await deleteComment(commentIdToDelete);
            toast.success("评论已成功删除");
        } catch (error) {
            console.error('Failed to delete comment', error);
            toast.error("删除评论失败，正在恢复...");
            setPost(originalPost); // Revert on failure
        }
    };

    const handlePostDelete = async () => {
        setIsDeleting(true);
        try {
            await deletePost(id);
            toast.success("帖子已成功删除");
            router.push('/community');
        } catch (error) {
            console.error('Failed to delete post', error);
            toast.error("删除失败，请稍后重试");
        } finally {
            setIsDeleting(false);
        }
    };

    const handleReplyClick = (target: { id: number; username: string }) => {
        setReplyingTo(target);
        // 自动添加@用户名到评论中
        setComment(prev => prev + `@${target.username} `);
    };

    const handleTogglePin = async () => {
        if (!post) return;

        try {
            const updatedPost = await togglePinPost(post.id);
            setPost(updatedPost);
            toast.success(
                updatedPost.isPinned ? "帖子已置顶" : "已取消置顶",
                {
                    description: updatedPost.isPinned ? "该帖子已成功置顶" : "该帖子已取消置顶",
                }
            );
        } catch (error) {
            console.error('置顶操作失败:', error);
            toast.error("操作失败", {
                description: "置顶操作失败，请稍后重试",
            });
        }
    };



    const handleCommentSubmit = async () => {
        if (!comment.trim() || !post) return;

        setIsSubmitting(true);
        try {
            const newComment = await createComment({
                postId: post.id,
                content: comment,
                parentId: replyingTo ? replyingTo.id : undefined,
            });

            // Optimistically update the comments list
            setPost(prevPost => {
                if (!prevPost) return null;
                const updatedComments = [...(prevPost.comments || []), newComment].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                const newCommentCount = (prevPost._count?.comments ?? 0) + 1;
                return {
                    ...prevPost,
                    comments: updatedComments,
                    _count: {
                        ...(prevPost._count),
                        likes: prevPost._count?.likes ?? 0,
                        comments: newCommentCount,
                    },
                };
            });

            setComment('');
            setReplyingTo(null);
            toast.success('评论已发布！');
        } catch (error) {
            console.error('Failed to submit comment', error);
            toast.error('评论失败，请重试');
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderComments = (commentList: CommunityComment[]) => {
        const commentMap = new Map(commentList.map(c => [c.id, { ...c, children: [] as CommunityComment[] }]));
        const rootComments: CommunityComment[] = [];

        commentList.forEach(c => {
            const commentWithChildren = commentMap.get(c.id)!;
            if (c.parentId && commentMap.has(c.parentId)) {
                commentMap.get(c.parentId)!.children.push(commentWithChildren);
            } else {
                rootComments.push(commentWithChildren);
            }
        });

        const renderCommentTree = (comments: CommunityComment[], depth: number) => {
            return comments.map(comment => (
                <CommentItem key={comment.id} comment={comment} onReply={handleReplyClick} onDelete={handleCommentDelete} depth={depth}>
                    {comment.children && comment.children.length > 0 && renderCommentTree(comment.children, depth + 1)}
                </CommentItem>
            ));
        };
        
        return renderCommentTree(rootComments, 0);
    };

    if (isLoading || authLoading) {
        return <PostPageSkeleton />;
    }

    if (!post) {
        return <div className="text-center py-20 text-red-500">帖子未找到或加载失败。</div>;
    }

    return (
        <div className="relative min-h-screen text-white">
            <div className="fixed inset-0 -z-10">
                <div
                    className="absolute inset-0 w-full h-full bg-cover bg-center"
                    style={{ backgroundImage: "url('/images/background/Community.webp')" }}
                />
                <div className="absolute inset-0 bg-black/60" />
            </div>

            <div className="container mx-auto px-4 py-8">
                <Button variant="ghost" onClick={() => router.back()} className="mb-4">
                    <ArrowLeft className="mr-2 h-4 w-4" /> 返回社区
                </Button>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                     {/* Sidebar */}
                     <aside className="lg:col-span-1 space-y-6 sticky top-24 self-start">
                        {/* Author Card */}
                        <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6 flex flex-col items-center space-y-4">
                            <h3 className="text-lg font-semibold self-stretch text-center">关于作者</h3>
                            <AvatarDisplay
                                avatarUrl={post.author.avatarUrl}
                                serialNumber={post.author.serialNumber}
                                avatarVersion={post.author.avatarVersion}
                                size={80}
                                className="h-20 w-20"
                            />
                            <h4 className="text-xl font-bold whitespace-nowrap">{post.author.username}</h4>
                            <div className="flex justify-center items-center gap-2">
                                {post.author.isAdmin && (
                                    <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-red-500/80 text-white">管理员</span>
                                )}
                                {post.author.isDeveloper && (
                                    <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-sky-500/80 text-white">开发者</span>
                                )}
                                {!post.author.isAdmin && !post.author.isDeveloper && (
                                    <span className="px-2.5 py-1 text-xs font-semibold rounded-full bg-zinc-600/80 text-white">用户</span>
                                )}
                            </div>
                            <p className="text-sm text-zinc-400">
                                {post.author.createdAt ? `于 ${new Date(post.author.createdAt).toLocaleDateString()} 加入` : ''}
                            </p>
                            <Link href={`/profile/${post.author.username}`} passHref className="w-full">
                                <Button className="w-full mt-4">查看个人资料</Button>
                            </Link>
                        </div>

                        {/* Stats Card */}
                        <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6">
                            <h3 className="text-lg font-semibold mb-4">帖子统计</h3>
                            <ul className="space-y-3 text-sm">
                                <li className="flex justify-between items-center">
                                    <span className="text-zinc-400">点赞</span>
                                    <span>{post._count?.likes ?? 0}</span>
                                </li>
                                <li className="flex justify-between items-center">
                                    <span className="text-zinc-400">评论</span>
                                    <span>{post._count?.comments ?? 0}</span>
                                </li>
                                <li className="flex justify-between items-center">
                                    <span className="text-zinc-400">发布于</span>
                                    <span>{formatDistanceToNow(new Date(post.createdAt), { addSuffix: true, locale: zhCN })}</span>
                                </li>
                                {post.updatedAt && new Date(post.updatedAt).getTime() !== new Date(post.createdAt).getTime() && (
                                    <li className="flex justify-between items-center">
                                        <span className="text-zinc-400">更新于</span>
                                        <span>{formatDistanceToNow(new Date(post.updatedAt), { addSuffix: true, locale: zhCN })}</span>
                                    </li>
                                )}
                            </ul>
                        </div>
                    </aside>

                    {/* Main Post Content */}
                    <div className="lg:col-span-3 space-y-8">
                        {/* Post container with glass effect */}
                        <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg">
                            {/* Post Header */}
                            <div className="px-6 py-4 border-b border-zinc-700/50 flex justify-between items-start gap-4">
                                <div className="flex-1">
                                    <h1 className="text-3xl md:text-4xl font-bold mb-2 break-words">{isEditing ? editedTitle : post.title}</h1>
                                    <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-zinc-400">
                                        <div className="flex items-center gap-2">
                                            <AvatarDisplay
                                                avatarUrl={post.author.avatarUrl}
                                                serialNumber={post.author.serialNumber}
                                                avatarVersion={post.author.avatarVersion}
                                                size={24}
                                                className="h-6 w-6"
                            />
                            <span>{post.author.username}</span>
                        </div>
                        <span>发布于 {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true, locale: zhCN })}</span>
                                        {post.updatedAt && new Date(post.updatedAt).getTime() !== new Date(post.createdAt).getTime() && (
                            <span className="text-xs text-zinc-500 italic">
                                (最后编辑于 {formatDistanceToNow(new Date(post.updatedAt), { addSuffix: true, locale: zhCN })})
                            </span>
                        )}
                        <div className="text-xs text-zinc-400 bg-zinc-800/50 px-2 py-1 rounded">
                            {(() => {
                                const partitionInfo = getPartitionDisplayInfoFromTopic(post.topic);
                                return partitionInfo ? partitionInfo.shortDisplayName : `${post.topic.era} - ${post.topic.name}`;
                            })()}
                        </div>
                    </div>
                </div>
                                <div className="flex items-center gap-2">
                                    {/* 管理员置顶按钮 */}
                                    {user?.role === 'KitolusAdmin' && !isEditing && (
                                        <Button
                                            variant={post.isPinned ? "default" : "outline"}
                                            size="sm"
                                            onClick={handleTogglePin}
                                            className="flex items-center gap-1"
                                        >
                                            {post.isPinned ? <PinOff size={16} /> : <Pin size={16} />}
                                            {post.isPinned ? '取消置顶' : '置顶'}
                                        </Button>
                                    )}

                                    {/* 作者编辑按钮 */}
                                    {user?.id === post.authorId && !isEditing && (
                                        <Button variant="outline" size="sm" onClick={handleEditClick}>编辑</Button>
                                    )}

                                    {/* 删除按钮 - 作者或管理员可见 */}
                                    {(user?.id === post.authorId || user?.role === 'KitolusAdmin') && !isEditing && (
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="sm">删除</Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        此操作无法撤销。您确定要永久删除这个帖子吗？
                                                        {user?.role === 'KitolusAdmin' && user?.id !== post.authorId && (
                                                            <span className="block mt-2 text-orange-600 font-medium">
                                                                注意：您正在以管理员身份删除其他用户的帖子。
                                                            </span>
                                                        )}
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handlePostDelete} disabled={isDeleting}>
                                                        {isDeleting ? '删除中...' : '确认删除'}
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    )}
                                </div>
                            </div>
            
            {/* Post Content */}
                            <div className="px-6 py-8">
            {isEditing ? (
                                    <div className="space-y-6">
                            <div>
                                <Label htmlFor="edit-title" className="text-zinc-400">标题</Label>
                                            <Input id="edit-title" type="text" value={editedTitle} onChange={(e) => setEditedTitle(e.target.value)} className="text-lg bg-zinc-900 border-zinc-700 h-12 mt-1" maxLength={20} />
                                            <div className="text-right text-sm text-zinc-500 mt-1">{editedTitle.length} / 20</div>
                                </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                <Label htmlFor="edit-content" className="text-zinc-400">内容 (Markdown)</Label>
                                                <Textarea id="edit-content" value={editedContent} onChange={(e) => setEditedContent(e.target.value)} rows={15} className="mt-1 bg-zinc-900/70 border-zinc-700 resize-y font-mono" maxLength={5000} />
                                                <div className="text-right text-sm text-zinc-500">{editedContent.length} / 5000</div>
                                </div>
                                            <div className="space-y-2">
                                <Label className="text-zinc-400">预览</Label>
                                                <div className="mt-1 p-4 rounded-md border border-zinc-800 bg-zinc-900/50 min-h-[300px] prose prose-invert prose-sm max-w-none">
                                                    <ReactMarkdown remarkPlugins={[remarkGfm]}>{editedContent}</ReactMarkdown>
                            </div>
                        </div>
                    </div>
                     <div className="flex justify-end gap-2 mt-4">
                        <Button onClick={handleCancelEdit} variant="ghost">取消</Button>
                                            <Button onClick={handleSaveClick} disabled={isSaving}>{isSaving ? '保存中...' : '保存更改'}</Button>
                    </div>
                </div>
            ) : (
                                    <div className="mt-4 prose prose-invert max-w-none prose-pre:bg-zinc-800 prose-pre:text-zinc-200 break-words overflow-wrap-anywhere">
                                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                            {post ? processMentions(post.content, post.mentionedUsers) : ''}
                                        </ReactMarkdown>
                                    </div>
                                )}
                            </div>

            {/* Post Actions */}
                            <div className="px-6 py-4 border-t border-zinc-700/50 flex justify-end items-center gap-4">
                <button
                    onClick={handleToggleLike}
                                    className={`flex items-center gap-1.5 text-sm font-medium transition-colors ${post.likedByCurrentUser ? 'text-zinc-200' : 'text-zinc-400 hover:text-white'}`}
                >
                                    <ThumbsUp size={16} />
                                    {post.likedByCurrentUser ? '已赞' : '点赞'} ({post._count?.likes ?? 0})
                </button>
                                <Link href="#comments" className="flex items-center gap-1.5 text-sm font-medium text-zinc-400 hover:text-white transition-colors">
                                    <MessageSquare size={16} />
                                    评论 ({post._count?.comments ?? 0})
                                </Link>
                </div>
            </div>

                        {/* Comments Section */}
                        <div id="comments" className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6">
                            <h2 className="text-2xl font-bold mb-6">评论 ({post._count?.comments})</h2>
                            {/* Comment submission form */}
                 {user ? (
                                <div className="flex flex-col gap-2 mb-8">
                        {replyingTo && (
                                        <div className="text-sm text-zinc-400 bg-zinc-800/50 p-2 rounded-md flex justify-between items-center">
                                            <span>回复 @{replyingTo.username}</span>
                                            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setReplyingTo(null)}>
                                                <X className="h-4 w-4" />
                                </Button>
                            </div>
                        )}
                        <UserMentionInput
                            value={comment}
                            onChange={setComment}
                            placeholder="添加评论..."
                            className="bg-zinc-800 border-zinc-700"
                            maxLength={500}
                            rows={4}
                            disabled={isSubmitting}
                            ref={commentInputRef}
                        />
                                    <div className="flex items-center justify-between mt-2">
                                        <div className="flex items-center gap-2">
                                            <Button
                                                size="sm"
                                                variant="ghost"
                                                onClick={() => {
                                                    if (!commentInputRef.current) return;

                                                    const textarea = commentInputRef.current;
                                                    const cursorPos = textarea.selectionStart;
                                                    const currentValue = textarea.value;
                                                    const textBefore = currentValue.substring(0, cursorPos);
                                                    const textAfter = currentValue.substring(cursorPos);

                                                    const newValue = textBefore + '@' + textAfter;
                                                    setComment(newValue);

                                                    // 设置光标位置到@符号后面
                                                    setTimeout(() => {
                                                        if (textarea) {
                                                            const newCursorPos = cursorPos + 1;
                                                            textarea.setSelectionRange(newCursorPos, newCursorPos);
                                                            textarea.focus();
                                                        }
                                                    }, 0);
                                                }}
                                                className="h-auto px-2 py-1 text-xs text-zinc-400 flex items-center gap-1 hover:text-white"
                                                disabled={isSubmitting}
                                            >
                                                <AtSign size={14} />
                                                提及
                                            </Button>
                                             {replyingTo && (
                                                <Button size="sm" variant="ghost" onClick={() => setReplyingTo(null)} className="h-auto px-2 py-1 text-xs">
                                                    <X size={14} className="mr-1" />
                                                    取消回复
                                                </Button>
                                            )}
                                        </div>
                            <Button
                                onClick={handleCommentSubmit}
                                disabled={isSubmitting || !comment.trim()}
                            >
                                            {isSubmitting ? '提交中...' : <Send className="h-4 w-4" />}
                            </Button>
                        </div>
                    </div>
                ) : (
                                <div className="text-center text-zinc-400 mb-8">
                                    <Link href="/login" className="text-sky-400 hover:underline">登录</Link> 后发表评论
                    </div>
                )}

                            {/* Comments List */}
                <div className="space-y-6">
                                {renderComments(post.comments || [])}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const PostPageSkeleton = () => (
    <div className="relative min-h-screen">
        <div className="container mx-auto px-4 py-8">
            <Skeleton className="h-10 w-36 mb-4" />
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                {/* Sidebar Skeleton */}
                <aside className="lg:col-span-1 space-y-6 sticky top-24 self-start">
                    <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6 text-center">
                        <Skeleton className="h-20 w-20 rounded-full mx-auto mb-4" />
                        <Skeleton className="h-6 w-3/4 mx-auto mb-2" />
                        <Skeleton className="h-4 w-1/2 mx-auto mb-4" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                    <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6">
                        <Skeleton className="h-6 w-1/3 mb-4" />
                        <div className="space-y-3">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                        </div>
                    </div>
                </aside>

                {/* Main Content Skeleton */}
                <div className="lg:col-span-3 space-y-8">
                    <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6">
                        <div className="border-b border-zinc-700/50 pb-4">
                            <Skeleton className="h-8 w-3/4 mb-4" />
                            <div className="flex items-center gap-4">
                                <Skeleton className="h-6 w-6 rounded-full" />
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-4 w-32" />
                            </div>
                        </div>
                        <div className="mt-6 space-y-3">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-5/6" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-3/4" />
                        </div>
                    </div>
                    <div className="bg-black/20 backdrop-blur-md rounded-xl shadow-lg p-6">
                        <Skeleton className="h-7 w-1/4 mb-6" />
                        <div className="flex gap-4">
                            <Skeleton className="h-10 w-10 rounded-full" />
                            <Skeleton className="h-10 flex-grow rounded-md" />
                        </div>
                    </div>
        </div>
        </div>
        </div>
    </div>
);

export default PostPage; 