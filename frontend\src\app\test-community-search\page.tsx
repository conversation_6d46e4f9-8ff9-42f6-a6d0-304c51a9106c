'use client';

import React, { useState } from 'react';
import { searchCommunityPosts, getCommunityPosts, getSearchSuggestions, debugSearchCommunityPosts } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, FileText, User } from 'lucide-react';

/**
 * 社区帖子搜索测试页面
 */
export default function TestCommunitySearchPage() {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [allPosts, setAllPosts] = useState<any[]>([]);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 测试获取所有帖子
  const testGetAllPosts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('开始获取所有帖子...');
      const posts = await getCommunityPosts();
      console.log('获取到的帖子数据:', posts);
      console.log('帖子数量:', posts?.length);

      if (posts && Array.isArray(posts)) {
        setAllPosts(posts);
        console.log('帖子设置成功');
      } else {
        console.warn('返回的数据不是数组:', typeof posts, posts);
        setAllPosts([]);
      }
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || err?.message || String(err);
      setError(`获取所有帖子失败: ${errorMessage}`);
      console.error('获取所有帖子失败:', {
        error: err,
        status: err?.response?.status,
        statusText: err?.response?.statusText,
        data: err?.response?.data
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 测试搜索社区帖子
  const testSearchPosts = async () => {
    if (!query.trim()) {
      setError('请输入搜索关键词');
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      console.log('开始搜索帖子，关键词:', query);
      const results = await searchCommunityPosts(query);
      console.log('搜索结果:', results);
      console.log('搜索结果数量:', results?.length);

      if (results && Array.isArray(results)) {
        setSearchResults(results);
        console.log('搜索结果设置成功');
      } else {
        console.warn('搜索返回的数据不是数组:', typeof results, results);
        setSearchResults([]);
      }
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || err?.message || String(err);
      setError(`搜索失败: ${errorMessage}`);
      console.error('搜索失败:', {
        error: err,
        status: err?.response?.status,
        statusText: err?.response?.statusText,
        data: err?.response?.data
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 测试搜索建议
  const testSearchSuggestions = async () => {
    if (!query.trim()) {
      setError('请输入搜索关键词');
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      const results = await getSearchSuggestions(query);
      setSuggestions(results);
      console.log('搜索建议:', results);
    } catch (err) {
      setError(`获取搜索建议失败: ${err}`);
      console.error('获取搜索建议失败:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试调试版搜索
  const testDebugSearch = async () => {
    if (!query.trim()) {
      setError('请输入搜索关键词');
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      console.log('🔍 开始调试搜索...');
      const results = await debugSearchCommunityPosts(query);
      setSearchResults(results);
      console.log('🎯 调试搜索完成，结果数量:', results?.length);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || err?.message || String(err);
      setError(`调试搜索失败: ${errorMessage}`);
      console.error('调试搜索失败:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">社区帖子搜索测试</h1>
      
      {/* 搜索输入 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>搜索测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="输入搜索关键词..."
                className="flex-1"
                onKeyDown={(e) => e.key === 'Enter' && testSearchPosts()}
              />
              <Button onClick={testSearchPosts} disabled={isLoading}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                搜索帖子
              </Button>
              <Button onClick={testDebugSearch} disabled={isLoading} variant="secondary">
                调试搜索
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={testGetAllPosts} disabled={isLoading} variant="outline">
                获取所有帖子
              </Button>
              <Button onClick={testSearchSuggestions} disabled={isLoading} variant="outline">
                获取搜索建议
              </Button>
            </div>

            {error && (
              <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md text-destructive">
                {error}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 搜索建议结果 */}
      {suggestions.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>搜索建议 ({suggestions.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <div key={index} className="flex items-center gap-3 p-2 border rounded-md">
                  <div className="text-muted-foreground">
                    {suggestion.type === 'user' ? <User className="h-4 w-4" /> : <FileText className="h-4 w-4" />}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{suggestion.title}</div>
                    {suggestion.subtitle && (
                      <div className="text-sm text-muted-foreground">{suggestion.subtitle}</div>
                    )}
                  </div>
                  <Badge variant="outline">{suggestion.type}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>搜索结果 ({searchResults.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.map((post) => (
                <div key={post.id} className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-2">{post.title}</h3>
                  <p className="text-muted-foreground mb-3 line-clamp-3">{post.content}</p>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <span>作者: {post.author?.username || '未知'}</span>
                      <span>发布: {formatDate(post.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>{post._count?.likes || post.likesCount || post.likes || 0} 点赞</span>
                      <span>{post._count?.comments || post.commentsCount || post.comments?.length || 0} 评论</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 所有帖子 */}
      {allPosts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>所有帖子 ({allPosts.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {allPosts.map((post) => (
                <div key={post.id} className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{post.title}</h3>
                  <p className="text-muted-foreground text-sm mb-2 line-clamp-2">{post.content}</p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>by {post.author?.username || '未知'}</span>
                    <span>{formatDate(post.createdAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 调试信息 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>当前查询:</strong> {query || '无'}</p>
            <p><strong>搜索结果数量:</strong> {searchResults.length}</p>
            <p><strong>所有帖子数量:</strong> {allPosts.length}</p>
            <p><strong>搜索建议数量:</strong> {suggestions.length}</p>
            <p><strong>加载状态:</strong> {isLoading ? '加载中' : '空闲'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
