package com.kitolus.community.service;

import com.kitolus.community.dto.NotificationDTO;
import com.kitolus.community.entity.CommunityComment;
import com.kitolus.community.entity.CommunityPost;
import com.kitolus.community.entity.User;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.DeveloperApplication;
import com.kitolus.community.mapper.NotificationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.kitolus.community.entity.Notification;
import com.kitolus.community.entity.NotificationType;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NotificationService {

    private final NotificationMapper notificationMapper;

    private void processNotificationDtoAvatarUrls(List<NotificationDTO> dtos) {
        if (dtos == null) return;
        dtos.forEach(dto -> {
            if (dto != null && (dto.getSenderAvatarUrl() == null || dto.getSenderAvatarUrl().trim().isEmpty())) {
                dto.setSenderAvatarUrl(dto.getSenderSerialNumber());
            }
        });
    }

    /**
     * Notifies the post author about a new comment.
     * This is a COMMUNITY type notification.
     */
    @Transactional
    public void createNotificationForComment(CommunityComment comment, CommunityPost post, User sender) {
        // Do not notify if the post author is the one commenting
        if (sender.getId().equals(post.getAuthorId())) {
            return;
        }

        Notification notification = new Notification();
        notification.setType(NotificationType.NEW_COMMENT);
        notification.setRecipientId(post.getAuthorId());
        notification.setSenderId(sender.getId());
        notification.setPostId(post.getId());
        notification.setCommentId(comment.getId());
        notification.setContentPreview(truncate(comment.getContent())); // Store truncated content
        notification.setRead(false);
        notificationMapper.insert(notification);
    }
    
    /**
     * Notifies the parent comment author about a new reply.
     * This is a COMMUNITY type notification.
     */
    @Transactional
    public void createNotificationForReply(CommunityComment reply, CommunityComment parentComment, CommunityPost post, User sender) {
        // Do not notify if the parent comment's author is the one replying
        if (sender.getId().equals(parentComment.getAuthorId())) {
            return;
        }

        Notification notification = new Notification();
        notification.setType(NotificationType.REPLY_TO_COMMENT);
        notification.setRecipientId(parentComment.getAuthorId());
        notification.setSenderId(sender.getId());
        notification.setPostId(post.getId());
        notification.setCommentId(reply.getId());
        notification.setContentPreview(truncate(reply.getContent())); // Store reply's content, not parent's
        notification.setRead(false);
        notificationMapper.insert(notification);
    }
    
    /**
     * Notifies a user that their developer application has been approved.
     * This is a SYSTEM type notification.
     */
    @Transactional
    public void createNotificationForApplicationApproval(DeveloperApplication application) {
        Notification notification = new Notification();
        notification.setType(NotificationType.DEVELOPER_APP_APPROVED);
        notification.setRecipientId(application.getUserId());
        notification.setSenderId(application.getReviewedBy());
        notification.setContentPreview("恭喜！您的开发者申请已通过。");
        notification.setRead(false);
        notificationMapper.insert(notification);
    }

    /**
     * Notifies a user that their developer application has been rejected.
     * This is a SYSTEM type notification.
     */
    @Transactional
    public void createNotificationForApplicationRejection(DeveloperApplication application, String reason) {
        Notification notification = new Notification();
        notification.setType(NotificationType.DEVELOPER_APP_REJECTED);
        notification.setRecipientId(application.getUserId());
        notification.setSenderId(application.getReviewedBy());
        notification.setContentPreview("您的开发者申请未通过，原因：" + reason);
        notification.setRead(false);
        notificationMapper.insert(notification);
    }

    /**
     * Notifies a user that their product has been approved.
     * This is a SYSTEM type notification.
     */
    @Transactional
    public void createNotificationForProductApproval(Product product, String notes, User sender) {
        if (product.getUserId() == null) {
            // Log this situation? For now, we just skip notification.
            return;
        }
        Notification notification = new Notification();
        notification.setType(NotificationType.PRODUCT_APPROVED);
        notification.setRecipientId(product.getUserId());
        notification.setSenderId(sender.getId());
        String content = "恭喜！您的产品 '" + product.getName() + "' 已通过审核并上架。";
        if (notes != null && !notes.trim().isEmpty()) {
            content += " 备注：" + notes;
        }
        notification.setContentPreview(content);
        notification.setRead(false);
        notificationMapper.insert(notification);
    }

    /**
     * Notifies a user that their product has been rejected.
     * This is a SYSTEM type notification.
     */
    @Transactional
    public void createNotificationForProductRejection(Product product, String reason, User sender) {
        if (product.getUserId() == null) {
            // Log this situation? For now, we just skip notification.
            return;
        }
        Notification notification = new Notification();
        notification.setType(NotificationType.PRODUCT_REJECTED);
        notification.setRecipientId(product.getUserId());
        notification.setSenderId(sender.getId());
        notification.setContentPreview("您的产品 '" + product.getName() + "' 未能通过审核，原因：" + reason);
        notification.setRead(false);
        notificationMapper.insert(notification);
    }

    /**
     * Notifies a user that their product has been delisted by an admin.
     * This is a SYSTEM type notification.
     */
    @Transactional
    public void createNotificationForProductDelisting(Product product, String reason, User sender) {
        if (product.getUserId() == null) {
            return;
        }
        Notification notification = new Notification();
        notification.setType(NotificationType.PRODUCT_DELISTED);
        notification.setRecipientId(product.getUserId());
        notification.setSenderId(sender.getId());
        notification.setContentPreview("您的产品 '" + product.getName() + "' 已被管理员下架，原因：" + reason);
        notification.setRead(false);
        notificationMapper.insert(notification);
    }

    /**
     * Notifies a user about being mentioned in a post or comment.
     * This is a COMMUNITY type notification.
     * @param sender The user who made the mention.
     * @param recipient The user who was mentioned.
     * @param post The post where the mention occurred.
     * @param comment The comment where the mention occurred (can be null if mention is in a post).
     */
    @Transactional
    public void createNotificationForMention(User sender, User recipient, CommunityPost post, CommunityComment comment) {
        Notification notification = new Notification();
        notification.setRecipientId(recipient.getId());
        notification.setSenderId(sender.getId());
        notification.setPostId(post.getId());
        notification.setRead(false);

        if (comment != null) {
            // Mention in a comment
            notification.setType(NotificationType.MENTION_IN_COMMENT);
            notification.setCommentId(comment.getId());
            notification.setContentPreview(truncate(comment.getContent()));
        } else {
            // Mention in a post
            notification.setType(NotificationType.MENTION_IN_POST);
            notification.setCommentId(null); // No specific comment
            notification.setContentPreview(truncate(post.getContent()));
        }

        notificationMapper.insert(notification);
    }
    
    private String truncate(String content) {
        if (content == null) return null;
        return content.length() > 100 ? content.substring(0, 100) + "..." : content;
    }

    public List<NotificationDTO> getNotificationsForUser(Long recipientId) {
        // Business logic, e.g., mapping to DTO
        return notificationMapper.findNotificationsByRecipientId(recipientId);
    }

    public int getUnreadNotificationCount(Long userId) {
        return notificationMapper.countUnreadNotificationsByRecipientId(userId);
    }

    public void markNotificationAsRead(Long notificationId) {
        Notification notification = notificationMapper.selectById(notificationId);
        if (notification != null && !notification.isRead()) {
            notification.setRead(true);
            notificationMapper.updateById(notification);
        }
    }
    
    @Transactional
    public void markAllNotificationsAsRead(Long userId) {
        notificationMapper.markAllAsReadByRecipientId(userId);
    }

    @Transactional
    public boolean deleteNotification(Long notificationId, Long userId) {
        Notification notification = notificationMapper.selectById(notificationId);
        if (notification != null && notification.getRecipientId().equals(userId)) {
            notificationMapper.deleteById(notificationId);
            return true;
        }
        return false;
    }

    @Transactional
    public void deleteAllNotifications(Long userId) {
        notificationMapper.deleteAllByRecipientId(userId);
    }
}