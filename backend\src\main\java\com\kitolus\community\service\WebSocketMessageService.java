package com.kitolus.community.service;

import com.kitolus.community.config.MessageWebSocketHandler;
import com.kitolus.community.entity.Message;
import com.kitolus.community.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket消息推送服务
 * 实现完美的实时消息推送
 */
@Service
public class WebSocketMessageService {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketMessageService.class);
    
    private final MessageWebSocketHandler webSocketHandler;
    private final UserService userService;

    public WebSocketMessageService(MessageWebSocketHandler webSocketHandler, UserService userService) {
        this.webSocketHandler = webSocketHandler;
        this.userService = userService;
    }

    /**
     * 推送新消息给接收者
     */
    public boolean pushNewMessage(Message message) {
        try {
            Long receiverId = message.getReceiverId();
            if (receiverId == null) {
                logger.warn("消息接收者ID为空，无法推送");
                return false;
            }

            // 检查接收者是否在线
            if (!webSocketHandler.isUserOnline(receiverId)) {
                logger.debug("用户 {} 不在线，跳过WebSocket推送", receiverId);
                return false;
            }

            // 获取发送者信息
            User sender = null;
            if (message.getSenderId() != null && message.getSenderId() > 0) {
                sender = userService.findById(message.getSenderId());
            }

            // 构建推送消息
            Map<String, Object> pushMessage = new HashMap<>();
            pushMessage.put("type", "NEW_MESSAGE");
            pushMessage.put("messageId", message.getId());
            pushMessage.put("content", message.getContent());
            pushMessage.put("messageType", message.getMessageType().toString());
            pushMessage.put("senderId", message.getSenderId());
            pushMessage.put("receiverId", message.getReceiverId());
            pushMessage.put("timestamp", message.getCreatedAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            pushMessage.put("isRead", message.getIsRead());

            // 添加发送者信息
            if (sender != null) {
                Map<String, Object> senderInfo = new HashMap<>();
                senderInfo.put("id", sender.getId());
                senderInfo.put("username", sender.getUsername());
                senderInfo.put("avatar", sender.getAvatarUrl());
                pushMessage.put("sender", senderInfo);
            } else {
                // 系统消息
                Map<String, Object> senderInfo = new HashMap<>();
                senderInfo.put("id", 0);
                senderInfo.put("username", "系统");
                senderInfo.put("avatar", null);
                pushMessage.put("sender", senderInfo);
            }

            // 推送消息
            boolean success = webSocketHandler.sendToUser(receiverId, pushMessage);
            
            if (success) {
                logger.info("成功推送消息给用户 {} - 消息ID: {}", receiverId, message.getId());
            } else {
                logger.warn("推送消息失败 - 用户: {}, 消息ID: {}", receiverId, message.getId());
            }
            
            return success;

        } catch (Exception e) {
            logger.error("推送消息异常 - 消息ID: {}", message.getId(), e);
            return false;
        }
    }

    /**
     * 推送对话更新通知
     */
    public boolean pushConversationUpdate(Long userId, Long otherUserId, String lastMessage) {
        try {
            if (!webSocketHandler.isUserOnline(userId)) {
                return false;
            }

            Map<String, Object> updateMessage = new HashMap<>();
            updateMessage.put("type", "CONVERSATION_UPDATE");
            updateMessage.put("otherUserId", otherUserId);
            updateMessage.put("lastMessage", lastMessage);
            updateMessage.put("timestamp", System.currentTimeMillis());

            return webSocketHandler.sendToUser(userId, updateMessage);

        } catch (Exception e) {
            logger.error("推送对话更新失败", e);
            return false;
        }
    }

    /**
     * 推送消息已读状态更新
     */
    public boolean pushMessageReadStatus(Long senderId, Long messageId, boolean isRead) {
        try {
            if (!webSocketHandler.isUserOnline(senderId)) {
                return false;
            }

            Map<String, Object> readStatusMessage = new HashMap<>();
            readStatusMessage.put("type", "MESSAGE_READ_STATUS");
            readStatusMessage.put("messageId", messageId);
            readStatusMessage.put("isRead", isRead);
            readStatusMessage.put("timestamp", System.currentTimeMillis());

            return webSocketHandler.sendToUser(senderId, readStatusMessage);

        } catch (Exception e) {
            logger.error("推送消息已读状态失败", e);
            return false;
        }
    }

    /**
     * 推送打字状态
     */
    public boolean pushTypingStatus(Long receiverId, Long senderId, boolean isTyping) {
        try {
            if (!webSocketHandler.isUserOnline(receiverId)) {
                return false;
            }

            Map<String, Object> typingMessage = new HashMap<>();
            typingMessage.put("type", "TYPING_STATUS");
            typingMessage.put("senderId", senderId);
            typingMessage.put("isTyping", isTyping);
            typingMessage.put("timestamp", System.currentTimeMillis());

            return webSocketHandler.sendToUser(receiverId, typingMessage);

        } catch (Exception e) {
            logger.error("推送打字状态失败", e);
            return false;
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return webSocketHandler.getOnlineUserCount();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        return webSocketHandler.isUserOnline(userId);
    }
}
