'use client';

import * as THREE from 'three';
import { useRef, useState, useMemo, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Maximize2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import projects from './projects.json';
import MusicPlayer, { MusicPlayerRef } from '@/components/MusicPlayer';
import type { ShowcaseClientProps } from './ShowcaseClient';
import { InteractionHints, MobileInteractionHints } from './components/InteractionHints';

const ShowcaseClient = dynamic<ShowcaseClientProps>(() => import('./ShowcaseClient'), {
    ssr: false,
    loading: () => <div className="w-full h-full flex items-center justify-center"><p className="text-white">加载星图中...</p></div>
});

function ProjectDetailView({ 
    project, 
    onClose,
    onNavigate,
}: { 
    project: typeof projects[0], 
    onClose: () => void,
    onNavigate: (direction: 'prev' | 'next') => void,
}) {
  if (!project) return null;

  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  useEffect(() => {
    setIsImageLoaded(false);
  }, [project.imageUrl]);

  return (
        <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
    >
      {/* Blurred Background */}
      <div className="absolute inset-0 w-full h-full">
        <Image src={project.imageUrl} alt={project.title} fill={true} className="object-cover filter blur-2xl brightness-50" />
      </div>

      {/* Frosted Glass Content */}
      <motion.div 
        layout
        className="relative w-[95%] max-w-[1800px] h-[90%] bg-black/30 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl flex p-8 overflow-hidden"
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        transition={{ duration: 0.4, ease: 'easeInOut' }}
        onClick={(e) => { e.stopPropagation(); setIsFullScreen(!isFullScreen) }}
      >
        <button
          onClick={(e) => { e.stopPropagation(); onClose(); }}
          className="absolute top-5 right-5 z-30 flex items-center justify-center w-10 h-10 bg-black/50 rounded-full text-white hover:bg-white/20 transition-colors"
          aria-label="返回"
        >
          <X size={24} />
        </button>

        {/* Navigation Arrows */}
      <button
            onClick={(e) => { e.stopPropagation(); onNavigate('prev'); }}
            className="absolute left-5 top-1/2 -translate-y-1/2 z-20 w-12 h-12 flex items-center justify-center bg-black/30 rounded-full text-white hover:bg-white/20 transition-colors"
            aria-label="上一个"
      >
            <ChevronLeft size={32} />
      </button>
      <button
            onClick={(e) => { e.stopPropagation(); onNavigate('next'); }}
            className="absolute right-5 top-1/2 -translate-y-1/2 z-20 w-12 h-12 flex items-center justify-center bg-black/30 rounded-full text-white hover:bg-white/20 transition-colors"
            aria-label="下一个"
      >
            <ChevronRight size={32} />
      </button>

        <motion.div 
            layout="position"
            className="relative h-full"
            animate={{ width: isFullScreen ? '100%' : '50%' }}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
        >
          <div className="absolute inset-0">
            <div className="relative w-full h-full shadow-2xl rounded-lg overflow-hidden bg-gray-900">
                <Image
                    src={project.imageUrl}
                    alt={project.title}
                    fill={true}
                    className={`object-cover transition-opacity duration-500 ease-in-out ${isImageLoaded ? 'opacity-100' : 'opacity-0'}`}
                    onLoad={() => setIsImageLoaded(true)}
                    priority={true}
                />
            </div>
          </div>
        </motion.div>

        <motion.div 
            className="relative h-full flex flex-col justify-center text-white overflow-hidden"
            animate={{ 
                width: isFullScreen ? '0%' : '50%',
                paddingLeft: isFullScreen ? '0rem' : '2rem'
            }}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
            onClick={(e) => e.stopPropagation()}
        >
            <motion.div
                animate={{ opacity: isFullScreen ? 0 : 1 }}
                transition={{ duration: 0.2, delay: isFullScreen ? 0 : 0.1 }}
            >
              <h1 className="text-5xl text-shadow-glow flex-shrink-0 relative bottom-[20px]">{project.title}</h1>
              <h2 className="text-2xl text-gray-300 mt-2 flex-shrink-0">By {project.author}</h2>
              <div className="w-24 h-1 bg-white/50 mt-6 mb-6 rounded-full flex-shrink-0"></div>
              <p className="text-lg text-gray-200 leading-relaxed overflow-y-auto pr-4">{project.description}</p>
            </motion.div>

            <motion.div
                className="absolute bottom-4 right-8 flex items-center text-white/40 text-xs pointer-events-none"
                initial={{ opacity: 0 }}
                animate={{ opacity: isFullScreen ? 0 : 1 }}
                transition={{ duration: 0.2, delay: isFullScreen ? 0 : 0.5 }}
            >
                <Maximize2 size={12} className="mr-1" />
                <span>点击图片切换全景模式</span>
            </motion.div>
        </motion.div>

        {/* Fullscreen Title */}
          <motion.div
            className="absolute bottom-9 left-0 right-0 text-center z-20 pointer-events-none"
            initial={{ opacity: 0, y: 20 }}
            animate={{ 
                opacity: isFullScreen ? 1 : 0, 
                y: isFullScreen ? 0 : 20 
            }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            <h1 className="text-4xl text-shadow-glow text-gray-300">《{project.title}》</h1>
            <h2 className="text-2xl text-gray-300 mt-1">By {project.author}</h2>
        </motion.div>
      </motion.div>
          </motion.div>
  );
}

const ShowcasePage = () => {
    const [activeId, setActiveId] = useState<number | null>(null);
    const [focusedId, setFocusedId] = useState<number | null>(null);
    const [returnFocusId, setReturnFocusId] = useState<number | null>(null);
    const [initialProjectId, setInitialProjectId] = useState<number | null>(null);
    const controlsRef = useRef<any>(null);
    const sceneGroupRef = useRef<THREE.Group>(null!);
    const [isInteracting, setIsInteracting] = useState(false);
    const interactionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [needsToSnap, setNeedsToSnap] = useState(false);
    const [isAnimating, setIsAnimating] = useState(true);
    const componentKey = useMemo(() => Date.now(), []);

    const musicPlayerRef = useRef<MusicPlayerRef>(null);
    const [hasPlayedMusic, setHasPlayedMusic] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const randomIndex = Math.floor(Math.random() * projects.length);
        setInitialProjectId(projects[randomIndex].id);

        // 检测移动设备
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => {
            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
            window.removeEventListener('resize', checkMobile);
        };
    }, []);

    useEffect(() => {
        if (activeId !== null) {
            const focusTimer = setTimeout(() => {
                setFocusedId(activeId);
            }, 200);
            return () => clearTimeout(focusTimer);
        }
    }, [activeId]);

    const handleCardClick = (id: number | null) => {
        if (id !== null) {
            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
            setIsInteracting(false);
            setNeedsToSnap(false);
            
            setActiveId(id);
            setFocusedId(id);
        }
    };

    const handleClose = () => {
        if (!hasPlayedMusic && musicPlayerRef.current) {
            musicPlayerRef.current.play();
            setHasPlayedMusic(true);
        }
        setReturnFocusId(activeId);
        setActiveId(null);
        setFocusedId(null);
    };

    const handleNavigate = (direction: 'prev' | 'next') => {
        if (activeId === null) return;
        const currentIndex = projects.findIndex(p => p.id === activeId);
        if (currentIndex === -1) return;

        let nextIndex;
        if (direction === 'next') {
            nextIndex = (currentIndex + 1) % projects.length;
        } else {
            nextIndex = (currentIndex - 1 + projects.length) % projects.length;
        }
        
        const nextProject = projects[nextIndex];
        setActiveId(nextProject.id);
        setFocusedId(nextProject.id);
        setReturnFocusId(null);
        setNeedsToSnap(true);
    };

    const handleInteractionStart = () => {
        if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
        }
        setIsInteracting(true);
        setNeedsToSnap(false);
        if (controlsRef.current && activeId === null) {
            controlsRef.current.target.set(0, 0, 0);
        }
    };

    const handleInteractionEnd = () => {
        if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
        }
        interactionTimeoutRef.current = setTimeout(() => {
            setIsInteracting(false);
            if (activeId === null) {
                setNeedsToSnap(true);
            }
        }, 1500);
    };

    const handleInitialPanComplete = (id: number) => {
        setFocusedId(id);
        setActiveId(id);
    };

    const activeProject = useMemo(() => {
        return projects.find(p => p.id === activeId) || null;
    }, [activeId]);

    return (
        <div className="w-screen h-screen overflow-hidden bg-black relative">
            <div className="absolute inset-0 z-0">
                {initialProjectId !== null && (
                    <ShowcaseClient
                        key={componentKey}
                        activeId={activeId}
                        focusedId={focusedId}
                        setFocusedId={setFocusedId}
                        returnFocusId={returnFocusId}
                        setReturnFocusId={setReturnFocusId}
                        isInteracting={isInteracting}
                        needsToSnap={needsToSnap}
                        setNeedsToSnap={setNeedsToSnap}
                        isAnimating={isAnimating}
                        setIsAnimating={setIsAnimating}
                        handleCardClick={handleCardClick}
                        handleInteractionStart={handleInteractionStart}
                        handleInteractionEnd={handleInteractionEnd}
                        controlsRef={controlsRef}
                        sceneGroupRef={sceneGroupRef}
                        handleInitialPanComplete={handleInitialPanComplete}
                        initialProjectId={initialProjectId}
                    />
                )}
            </div>

            <AnimatePresence>
                {activeProject && (
                    <ProjectDetailView
                        project={activeProject}
                        onClose={handleClose}
                        onNavigate={handleNavigate}
                    />
                )}
            </AnimatePresence>

            {/* 交互提示组件 */}
            {isMobile ? (
                <MobileInteractionHints />
            ) : (
                <InteractionHints />
            )}

            <div className="absolute bottom-4 right-4 z-10">
                <MusicPlayer ref={musicPlayerRef} />
            </div>
        </div>
    );
};

export default ShowcasePage;
