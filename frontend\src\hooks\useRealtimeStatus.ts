/**
 * 实时状态管理Hook
 * 管理用户在线状态和输入状态
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

interface TypingStatus {
  [conversationId: string]: {
    [userId: string]: boolean;
  };
}

interface OnlineStatus {
  [userId: string]: boolean;
}

interface UseRealtimeStatusReturn {
  // 在线状态
  onlineUsers: Set<number>;
  isUserOnline: (userId: number) => boolean;
  
  // 输入状态
  typingUsers: TypingStatus;
  isUserTyping: (conversationId: string, userId: number) => boolean;
  startTyping: (conversationId: string) => void;
  stopTyping: () => void;
  
  // 连接状态
  isConnected: boolean;
}

export function useRealtimeStatus(
  wsManager: typeof unifiedWebSocketManager | null
): UseRealtimeStatusReturn {
  const [onlineUsers, setOnlineUsers] = useState<Set<number>>(new Set());
  const [typingUsers, setTypingUsers] = useState<TypingStatus>({});
  const [isConnected, setIsConnected] = useState(false);
  
  // 防抖timer引用
  const typingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentConversationRef = useRef<string | null>(null);

  // 处理WebSocket消息
  useEffect(() => {
    if (!wsManager) return;

    // 连接状态变化
    const handleConnect = () => {
      setIsConnected(true);
      // 连接成功后请求在线状态
      wsManager.requestOnlineStatus();
    };

    const handleDisconnect = () => {
      setIsConnected(false);
      setOnlineUsers(new Set());
      setTypingUsers({});
    };

    // 在线状态更新
    const handleOnlineStatus = (data: any) => {
      console.log('🔍 处理在线状态更新:', data);
      if (data.onlineUsers && Array.isArray(data.onlineUsers)) {
        console.log('🟢 设置在线用户:', data.onlineUsers);
        setOnlineUsers(new Set(data.onlineUsers));
      }
    };

    // 用户状态变化
    const handleUserStatusChange = (data: any) => {
      console.log('🔄 处理用户状态变化:', data);
      const { userId, isOnline } = data;
      if (typeof userId === 'number' && typeof isOnline === 'boolean') {
        console.log(`🔄 用户 ${userId} ${isOnline ? '上线' : '下线'}`);
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          if (isOnline) {
            newSet.add(userId);
          } else {
            newSet.delete(userId);
          }
          return newSet;
        });
      }
    };

    // 用户开始输入
    const handleUserTypingStart = (data: any) => {
      console.log('⌨️ 处理用户开始输入:', data);
      const { userId, conversationId } = data;
      if (typeof userId === 'number' && typeof conversationId === 'string') {
        console.log(`⌨️ 用户 ${userId} 在对话 ${conversationId} 中开始输入`);
        setTypingUsers(prev => ({
          ...prev,
          [conversationId]: {
            ...prev[conversationId],
            [userId]: true
          }
        }));
      }
    };

    // 用户停止输入
    const handleUserTypingStop = (data: any) => {
      const { userId, conversationId } = data;
      if (typeof userId === 'number' && typeof conversationId === 'string') {
        setTypingUsers(prev => {
          const newTyping = { ...prev };
          if (newTyping[conversationId]) {
            const conversationTyping = { ...newTyping[conversationId] };
            delete conversationTyping[userId];
            
            // 如果对话中没有人在输入，删除整个对话记录
            if (Object.keys(conversationTyping).length === 0) {
              delete newTyping[conversationId];
            } else {
              newTyping[conversationId] = conversationTyping;
            }
          }
          return newTyping;
        });
      }
    };

    // 注册事件监听器
    wsManager.on('connected', handleConnect);
    wsManager.on('disconnected', handleDisconnect);
    wsManager.on('ONLINE_STATUS', handleOnlineStatus);
    wsManager.on('USER_STATUS_CHANGE', handleUserStatusChange);
    wsManager.on('USER_TYPING_START', handleUserTypingStart);
    wsManager.on('USER_TYPING_STOP', handleUserTypingStop);

    // 如果已经连接，立即更新状态
    if (wsManager.isConnected) {
      handleConnect();
    }

    return () => {
      wsManager.off('connected', handleConnect);
      wsManager.off('disconnected', handleDisconnect);
      wsManager.off('ONLINE_STATUS', handleOnlineStatus);
      wsManager.off('USER_STATUS_CHANGE', handleUserStatusChange);
      wsManager.off('USER_TYPING_START', handleUserTypingStart);
      wsManager.off('USER_TYPING_STOP', handleUserTypingStop);
    };
  }, [wsManager]);

  // 检查用户是否在线
  const isUserOnline = useCallback((userId: number): boolean => {
    return onlineUsers.has(userId);
  }, [onlineUsers]);

  // 检查用户是否在输入
  const isUserTyping = useCallback((conversationId: string, userId: number): boolean => {
    return typingUsers[conversationId]?.[userId] === true;
  }, [typingUsers]);

  // 开始输入（带防抖）
  const startTyping = useCallback((conversationId: string) => {
    if (!wsManager || !wsManager.isConnected) return;

    // 清除现有的定时器
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    // 如果是新对话，先停止之前的输入状态
    if (currentConversationRef.current && currentConversationRef.current !== conversationId) {
      wsManager.stopTyping();
    }

    // 发送开始输入状态（WebSocket管理器会处理防抖）
    wsManager.startTyping(conversationId);
    currentConversationRef.current = conversationId;

    // 设置3秒后自动停止
    typingTimerRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [wsManager]);

  // 停止输入
  const stopTyping = useCallback(() => {
    if (!wsManager) return;

    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    if (currentConversationRef.current) {
      wsManager.stopTyping();
      currentConversationRef.current = null;
    }
  }, [wsManager]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
      }
    };
  }, []);

  return {
    onlineUsers,
    isUserOnline,
    typingUsers,
    isUserTyping,
    startTyping,
    stopTyping,
    isConnected
  };
}
