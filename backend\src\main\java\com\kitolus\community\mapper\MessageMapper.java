package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kitolus.community.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息Mapper接口
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 查找用户的所有消息（发送和接收）
     */
    @Select("SELECT * FROM messages WHERE sender_id = #{userId} OR receiver_id = #{userId} ORDER BY created_at DESC")
    Page<Message> findUserMessages(Page<Message> page, @Param("userId") Long userId);

    /**
     * 查找用户接收的未读消息
     */
    @Select("SELECT * FROM messages WHERE receiver_id = #{userId} AND is_read = false ORDER BY created_at DESC")
    List<Message> findUnreadMessages(@Param("userId") Long userId);

    /**
     * 统计用户未读消息数量
     */
    @Select("SELECT COUNT(*) FROM messages WHERE receiver_id = #{userId} AND is_read = false")
    Long countUnreadMessages(@Param("userId") Long userId);

    /**
     * 查找两个用户之间的对话
     */
    @Select("SELECT * FROM messages WHERE " +
           "(sender_id = #{user1Id} AND receiver_id = #{user2Id}) OR " +
           "(sender_id = #{user2Id} AND receiver_id = #{user1Id}) " +
           "ORDER BY created_at ASC")
    List<Message> findConversationBetweenUsers(@Param("user1Id") Long user1Id, @Param("user2Id") Long user2Id);

    /**
     * 查找用户的系统消息
     */
    @Select("SELECT * FROM messages WHERE receiver_id = #{userId} AND message_type = 'SYSTEM' ORDER BY created_at DESC")
    Page<Message> findSystemMessages(Page<Message> page, @Param("userId") Long userId);

    /**
     * 查找用户的私信
     */
    @Select("SELECT * FROM messages WHERE (sender_id = #{userId} OR receiver_id = #{userId}) AND message_type = 'PRIVATE' ORDER BY created_at DESC")
    Page<Message> findPrivateMessages(Page<Message> page, @Param("userId") Long userId);

    /**
     * 查找指定时间后的消息（用于轮询）
     */
    @Select("SELECT * FROM messages WHERE receiver_id = #{userId} AND created_at > #{since} ORDER BY created_at ASC")
    List<Message> findMessagesSince(@Param("userId") Long userId, @Param("since") LocalDateTime since);

    /**
     * 查找用户的对话列表（最近联系人）
     */
    @Select("SELECT " +
           "CASE WHEN sender_id = #{userId} THEN receiver_id ELSE sender_id END as contact_id, " +
           "MAX(created_at) as last_message_time " +
           "FROM messages WHERE (sender_id = #{userId} OR receiver_id = #{userId}) AND message_type = 'PRIVATE' " +
           "GROUP BY contact_id ORDER BY last_message_time DESC")
    List<Object[]> findUserContacts(@Param("userId") Long userId);

    /**
     * 标记对话为已读
     */
    @Update("UPDATE messages SET is_read = true, read_at = NOW(), updated_at = NOW() " +
           "WHERE receiver_id = #{userId} AND sender_id = #{senderId} AND is_read = false")
    int markConversationAsRead(@Param("userId") Long userId, @Param("senderId") Long senderId);

    /**
     * 删除旧消息（数据清理）
     */
    @Update("DELETE FROM messages WHERE created_at < #{cutoffDate} AND message_type != 'SYSTEM'")
    int deleteOldMessages(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 查找广播消息
     */
    @Select("SELECT * FROM messages WHERE message_type = 'BROADCAST' AND created_at > #{since} ORDER BY created_at DESC")
    List<Message> findBroadcastMessagesSince(@Param("since") LocalDateTime since);

    /**
     * 按消息类型统计
     */
    @Select("SELECT message_type, COUNT(*) FROM messages WHERE receiver_id = #{userId} GROUP BY message_type")
    List<Object[]> countMessagesByType(@Param("userId") Long userId);

    /**
     * 获取用户的对话列表（按对话伙伴分组，包含用户信息）
     */
    @Select("""
        SELECT DISTINCT
            CASE
                WHEN m.sender_id = #{userId} THEN m.receiver_id
                ELSE m.sender_id
            END as other_user_id,
            u.username as other_user_name,
            u.avatar_url as other_user_avatar,
            (SELECT MAX(created_at) FROM messages m2
             WHERE ((m2.sender_id = #{userId} AND m2.receiver_id = CASE WHEN m.sender_id = #{userId} THEN m.receiver_id ELSE m.sender_id END)
                    OR (m2.receiver_id = #{userId} AND m2.sender_id = CASE WHEN m.sender_id = #{userId} THEN m.receiver_id ELSE m.sender_id END))
               AND m2.message_type = 'PRIVATE') as last_message_time,
            (SELECT content FROM messages m3
             WHERE ((m3.sender_id = #{userId} AND m3.receiver_id = CASE WHEN m.sender_id = #{userId} THEN m.receiver_id ELSE m.sender_id END)
                    OR (m3.receiver_id = #{userId} AND m3.sender_id = CASE WHEN m.sender_id = #{userId} THEN m.receiver_id ELSE m.sender_id END))
               AND m3.message_type = 'PRIVATE'
             ORDER BY m3.created_at DESC LIMIT 1) as last_message_content,
            (SELECT COUNT(*) FROM messages m4
             WHERE m4.receiver_id = #{userId}
               AND m4.sender_id = CASE WHEN m.sender_id = #{userId} THEN m.receiver_id ELSE m.sender_id END
               AND m4.message_type = 'PRIVATE'
               AND m4.is_read = false) as unread_count
        FROM messages m
        JOIN user u ON u.id = CASE
            WHEN m.sender_id = #{userId} THEN m.receiver_id
            ELSE m.sender_id
        END
        WHERE (m.sender_id = #{userId} OR m.receiver_id = #{userId})
          AND m.message_type = 'PRIVATE'
        ORDER BY last_message_time DESC
    """)
    List<Map<String, Object>> findConversations(@Param("userId") Long userId);

    /**
     * 获取与特定用户的对话消息（包含发送者信息）
     */
    @Select("""
        SELECT m.*,
               sender.username as sender_name,
               sender.avatar_url as sender_avatar,
               receiver.username as receiver_name,
               receiver.avatar_url as receiver_avatar
        FROM messages m
        LEFT JOIN user sender ON m.sender_id = sender.id
        LEFT JOIN user receiver ON m.receiver_id = receiver.id
        WHERE ((m.sender_id = #{userId} AND m.receiver_id = #{otherUserId})
               OR (m.sender_id = #{otherUserId} AND m.receiver_id = #{userId}))
          AND m.message_type = 'PRIVATE'
        ORDER BY m.created_at ASC
    """)
    Page<Message> findConversationMessages(Page<Message> page, @Param("userId") Long userId, @Param("otherUserId") Long otherUserId);

    /**
     * 批量删除两个用户之间的所有对话消息（暂时使用简单删除）
     */
    @Delete("""
        DELETE FROM messages
        WHERE ((sender_id = #{userId} AND receiver_id = #{otherUserId})
               OR (sender_id = #{otherUserId} AND receiver_id = #{userId}))
          AND message_type = 'PRIVATE'
    """)
    int deleteConversationMessages(@Param("userId") Long userId, @Param("otherUserId") Long otherUserId);

}
