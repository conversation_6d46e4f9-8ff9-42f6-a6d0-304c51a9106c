package com.kitolus.community.controller;

import com.kitolus.community.entity.Order;
import com.kitolus.community.entity.Product;
import com.kitolus.community.mapper.OrderMapper;
import com.kitolus.community.mapper.ProductMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Random;

@RestController
@RequestMapping("/api/test")
public class TestDataController {

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private ProductMapper productMapper;

    @PostMapping("/create-sample-orders")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<?> createSampleOrders() {
        try {
            // 获取所有已批准的产品
            List<Product> products = productMapper.selectList(null);
            if (products.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("message", "没有找到产品，无法创建测试订单"));
            }

            Random random = new Random();
            int ordersCreated = 0;

            // 为每个产品创建1-5个随机订单
            for (Product product : products) {
                int orderCount = random.nextInt(5) + 1; // 1-5个订单
                
                for (int i = 0; i < orderCount; i++) {
                    Order order = new Order();
                    order.setUserId(1L); // 使用默认用户ID
                    order.setProductId(product.getId());
                    order.setOutTradeNo("TEST_" + System.currentTimeMillis() + "_" + random.nextInt(1000));
                    order.setTotalFee(product.getPrice());
                    order.setStatus("PAID");
                    order.setCreatedAt(new Timestamp(System.currentTimeMillis() - random.nextInt(30) * 24 * 60 * 60 * 1000L)); // 过去30天内随机时间
                    order.setPaidAt(new Timestamp(order.getCreatedAt().getTime() + random.nextInt(3600) * 1000L)); // 创建后1小时内支付
                    
                    // 计算平台费用和开发者收入 (假设平台抽成20%)
                    BigDecimal platformFee = product.getPrice().multiply(new BigDecimal("0.20"));
                    BigDecimal developerRevenue = product.getPrice().subtract(platformFee);
                    order.setPlatformFee(platformFee);
                    order.setDeveloperRevenue(developerRevenue);
                    
                    orderMapper.insert(order);
                    ordersCreated++;
                }
            }

            return ResponseEntity.ok(Map.of(
                "message", "成功创建测试订单",
                "ordersCreated", ordersCreated,
                "productsProcessed", products.size()
            ));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "message", "创建测试订单失败: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/orders-stats")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<?> getOrdersStats() {
        try {
            long totalOrders = orderMapper.selectCount(null);
            long paidOrders = orderMapper.countByStatus("PAID");
            List<Map<String, Object>> topProducts = orderMapper.findTopSellingProductCounts("PAID");
            
            return ResponseEntity.ok(Map.of(
                "totalOrders", totalOrders,
                "paidOrders", paidOrders,
                "topProducts", topProducts
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "message", "获取订单统计失败: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/clear-test-orders")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<?> clearTestOrders() {
        try {
            // 删除所有以TEST_开头的订单
            int deletedCount = orderMapper.deleteByOutTradeNoPrefix("TEST_");
            
            return ResponseEntity.ok(Map.of(
                "message", "成功清除测试订单",
                "deletedCount", deletedCount
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "message", "清除测试订单失败: " + e.getMessage()
            ));
        }
    }
}
