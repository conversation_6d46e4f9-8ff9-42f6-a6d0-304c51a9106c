package com.kitolus.community.service;

import com.kitolus.community.entity.KnowledgeBaseArticle;
import com.kitolus.community.entity.SearchResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface KnowledgeBaseService {
    List<KnowledgeBaseArticle> getAllArticles();
    Map<String, List<KnowledgeBaseArticle>> getAllArticlesGroupedByDate();
    long countArticles();
    KnowledgeBaseArticle getArticleById(String id);
    List<KnowledgeBaseArticle> getRandomArticles(int count);
    SearchResult searchArticles(String query);
    KnowledgeBaseArticle storeArticleFromMarkdownFile(MultipartFile file) throws IOException;
    void deleteArticle(String id) throws IOException;
} 