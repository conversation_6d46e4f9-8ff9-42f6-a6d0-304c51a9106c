package com.kitolus.community.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kitolus.community.entity.Order;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;
import com.kitolus.community.exception.InvalidDataException;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.mapper.OrderMapper;
import com.kitolus.community.mapper.ProductMapper;
import com.kitolus.community.mapper.UserMapper;
import com.kitolus.community.util.LantuPaySignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.kitolus.community.dto.PaymentRequestDTO;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class PaymentServiceImpl implements PaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Value("${lantu.payment.mch_id}")
    private String mchId;

    @Value("${lantu.payment.secret_key}")
    private String secretKey;

    @Value("${lantu.payment.notify_url}")
    private String notifyUrl;

    @Value("${lantu.payment.api_url}")
    private String apiUrl;

    @Override
    public Map<String, String> createPayment(PaymentRequestDTO paymentRequest, HttpServletRequest request) throws Exception {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new InvalidDataException("用户未认证");
        }
        
        String username = null;
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            username = ((UserDetails)principal).getUsername();
        } else {
            username = principal.toString();
        }

        if (username == null) {
            throw new InvalidDataException("无法确定用户信息");
        }

        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在");
        }
        
        Map<String, Object> orderResult = createOrder(paymentRequest.getProductId(), user.getId());

        // The controller expects Map<String, String>, but createOrder returns Map<String, Object>.
        // Both 'codeUrl' and 'orderId' are strings, so this conversion is safe.
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : orderResult.entrySet()) {
            if (entry.getValue() instanceof String) {
                result.put(entry.getKey(), (String) entry.getValue());
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> createOrder(Long productId, Long userId) {
        // Check if the user has already purchased the product
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("product_id", productId)
                .eq("status", "PAID");
        Long count = orderMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new InvalidDataException("您已购买此商品，无需重复购买。");
        }
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new ResourceNotFoundException("商品不存在");
        }

        String outTradeNo = UUID.randomUUID().toString().replaceAll("-", "");

        Map<String, String> params = new HashMap<>();
        params.put("mch_id", mchId);
        params.put("out_trade_no", outTradeNo);
        params.put("total_fee", product.getPrice().toString());
        params.put("body", product.getName());
        params.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        params.put("notify_url", notifyUrl);

        String sign = LantuPaySignUtil.createSign(params, secretKey);
        params.put("sign", sign);

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        params.forEach(map::add);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, request, Map.class);

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();
            if ("0".equals(String.valueOf(responseBody.get("code")))) {
                Order order = new Order();
                order.setOutTradeNo(outTradeNo);
                order.setUserId(user.getId());
                order.setProductId(product.getId());
                order.setTotalFee(product.getPrice());
                order.setStatus("PENDING");
                order.setCreatedAt(Timestamp.from(Instant.now()));
                orderMapper.insert(order);
                
                Map<String, Object> dataToReturn = new HashMap<>();
                Object dataObject = responseBody.get("data");

                if (dataObject instanceof Map) {
                    Map<String, Object> paymentData = (Map<String, Object>) dataObject;
                    Object codeUrlObject = paymentData.get("code_url");
                    if (codeUrlObject != null) {
                        dataToReturn.put("codeUrl", codeUrlObject.toString());
                        dataToReturn.put("orderId", outTradeNo);
                        return dataToReturn;
                    }
                }
                
                logger.error("未能从LantuPay响应中解析 'data' 或 'code_url'. Response Body: {}", responseBody);
                throw new RuntimeException("未能从支付服务获取二维码URL");
            } else {
                logger.error("LantuPay API Error: {}", responseBody.get("msg"));
                throw new RuntimeException("创建支付失败: " + responseBody.get("msg"));
            }
        } else {
            throw new RuntimeException("请求LantuPay API失败");
        }
    }

    @Override
    public boolean handlePaymentNotification(Map<String, String> notificationData) {
        logger.info("收到支付通知: {}", notificationData);

        String signFromLantu = notificationData.get("sign");
        if (signFromLantu == null) {
            logger.error("支付通知中缺少签名");
            return false;
        }
        
        // According to the documentation, only required parameters should be included in the signature verification.
        Map<String, String> paramsToSign = new HashMap<>();
        paramsToSign.put("code", notificationData.get("code"));
        paramsToSign.put("timestamp", notificationData.get("timestamp"));
        paramsToSign.put("mch_id", notificationData.get("mch_id"));
        paramsToSign.put("order_no", notificationData.get("order_no"));
        paramsToSign.put("out_trade_no", notificationData.get("out_trade_no"));
        paramsToSign.put("pay_no", notificationData.get("pay_no"));
        paramsToSign.put("total_fee", notificationData.get("total_fee"));


        String expectedSign = LantuPaySignUtil.createSign(paramsToSign, secretKey);

        if (!expectedSign.equals(signFromLantu)) {
            logger.error("签名验证失败. Received: {}, Expected: {}", signFromLantu, expectedSign);
            return false;
        }

        if (!"0".equals(notificationData.get("code"))) {
            logger.warn("支付未成功. Code: {}", notificationData.get("code"));
            return false;
        }

        String outTradeNo = notificationData.get("out_trade_no");
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().eq("out_trade_no", outTradeNo));

        if (order == null) {
            logger.error("未找到订单: {}", outTradeNo);
            return false;
        }

        if ("PENDING".equals(order.getStatus())) {
            order.setStatus("PAID");
            order.setPaidAt(Timestamp.from(Instant.now()));

            // --- Revenue Split Logic ---
            Product product = productMapper.selectById(order.getProductId());
            if (product != null) {
                User productOwner = userMapper.selectById(product.getUserId());
                if (productOwner != null) {
                    BigDecimal totalFee = order.getTotalFee();
                    if ("DEVELOPER".equals(productOwner.getRole())) {
                        BigDecimal platformFee = totalFee.multiply(new BigDecimal("0.05")).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal developerRevenue = totalFee.subtract(platformFee);
                        order.setPlatformFee(platformFee);
                        order.setDeveloperRevenue(developerRevenue);
                    } else if ("KitolusAdmin".equals(productOwner.getRole())) {
                        order.setPlatformFee(totalFee);
                        order.setDeveloperRevenue(BigDecimal.ZERO);
                    } else {
                        // Default case, maybe for other roles or if role is null.
                        // All revenue goes to platform.
                        order.setPlatformFee(totalFee);
                        order.setDeveloperRevenue(BigDecimal.ZERO);
                    }
                }
            }
            // --- End of Revenue Split Logic ---

            orderMapper.updateById(order);
            logger.info("订单 {} 状态更新为 PAID.", outTradeNo);
        } else {
            logger.warn("订单 {} 已处理. 当前状态: {}", outTradeNo, order.getStatus());
        }

        return true;
    }

    @Override
    public String getOrderStatus(String orderId) throws Exception {
        if (orderId == null || orderId.trim().isEmpty()) {
            throw new InvalidDataException("订单ID不能为空。");
        }

        Order order = orderMapper.selectOne(new QueryWrapper<Order>().eq("out_trade_no", orderId));

        if (order == null) {
            throw new ResourceNotFoundException("找不到具有ID " + orderId + " 的订单。");
        }

        // Return the status from the order entity
        return order.getStatus();
    }
} 