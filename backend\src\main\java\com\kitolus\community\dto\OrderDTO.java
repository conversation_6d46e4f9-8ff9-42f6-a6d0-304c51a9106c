package com.kitolus.community.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
public class OrderDTO {
    private Long id;
    private Long userId;
    private Long productId;
    private String outTradeNo;
    private BigDecimal totalFee;
    private String status;
    private Timestamp createdAt;
    private Timestamp paidAt;
    private ProductDTO product;
    private String username;
} 