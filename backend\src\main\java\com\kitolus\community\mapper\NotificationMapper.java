package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.dto.NotificationDTO;
import com.kitolus.community.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {

    @Select("SELECT " +
            "n.id, n.type, n.post_id, n.comment_id, n.created_at, n.is_read as isRead, " +
            "p.title as postTitle, " +
            "s.username as senderUsername, s.avatar_url as senderAvatarUrl, s.serial_number as senderSerialNumber, s.avatar_version as senderAvatarVersion, " +
            "n.content_preview as contentPreview, " +
            "parent_c.content as parentCommentPreview " +
            "FROM notification n " +
            "LEFT JOIN `user` s ON n.sender_id = s.id " +
            "LEFT JOIN community_post p ON n.post_id = p.id " +
            "LEFT JOIN community_comment c ON n.comment_id = c.id " +
            "LEFT JOIN community_comment parent_c ON c.parent_id = parent_c.id " +
            "WHERE n.recipient_id = #{recipientId} " +
            "ORDER BY n.created_at DESC")
    List<NotificationDTO> findNotificationsByRecipientId(@Param("recipientId") Long recipientId);

    @Select("SELECT COUNT(DISTINCT n.id) FROM notification n " +
            "LEFT JOIN `user` s ON n.sender_id = s.id " +
            "LEFT JOIN community_post p ON n.post_id = p.id " +
            "LEFT JOIN community_comment c ON n.comment_id = c.id " +
            "WHERE n.recipient_id = #{recipientId} AND n.is_read = false")
    int countUnreadNotificationsByRecipientId(@Param("recipientId") Long recipientId);

    @Update("UPDATE notification SET is_read = true WHERE recipient_id = #{recipientId} AND is_read = false")
    void markAllAsReadByRecipientId(@Param("recipientId") Long recipientId);

    @Delete("DELETE FROM notification WHERE recipient_id = #{recipientId}")
    void deleteAllByRecipientId(@Param("recipientId") Long recipientId);
}