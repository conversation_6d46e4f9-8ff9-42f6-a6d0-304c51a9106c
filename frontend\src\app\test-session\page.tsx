'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { sessionManager } from '@/lib/sessionManager';
import { unifiedWebSocketManager } from '@/lib/unifiedWebSocketManager';

/**
 * 会话管理测试页面
 * 用于验证新的SSO会话管理功能
 */
export default function TestSessionPage() {
  const { user, isAuthenticated, token, logout } = useAuth();
  const [sessionInfo, setSessionInfo] = useState<any>({});
  const [wsInfo, setWsInfo] = useState<any>({});
  const [logs, setLogs] = useState<string[]>([]);

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-20), `[${timestamp}] ${message}`]);
  };

  // 更新会话信息
  const updateSessionInfo = () => {
    const session = sessionManager.getCurrentSession();
    const hasActiveSession = sessionManager.hasActiveSession();
    const isValid = sessionManager.isSessionValid();

    setSessionInfo({
      hasSession: !!session,
      sessionId: session?.sessionId?.substring(0, 12) + '...',
      userId: session?.userId,
      loginTime: session?.loginTime ? new Date(session.loginTime).toLocaleString() : null,
      lastActive: session?.lastActive ? new Date(session.lastActive).toLocaleString() : null,
      hasActiveSession,
      isValid,
      tokenPrefix: session?.token?.substring(0, 20) + '...',
      sessionAge: session ? Math.round((Date.now() - session.loginTime) / 1000) + 's' : null
    });

    // 更新WebSocket信息
    setWsInfo({
      isConnected: simpleWebSocketManager.isConnected,
      hasManager: !!simpleWebSocketManager
    });
  };

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(() => {
      updateSessionInfo();
    }, 1000);

    updateSessionInfo();
    return () => clearInterval(interval);
  }, []);

  // 监听会话变化
  useEffect(() => {
    if (isAuthenticated) {
      addLog('✅ 用户已认证');
    } else {
      addLog('❌ 用户未认证');
    }
  }, [isAuthenticated]);

  // 测试函数
  const testForceLogout = () => {
    addLog('🧪 测试强制登出其他标签页');
    // 创建一个新会话来模拟新登录
    if (user && token) {
      sessionManager.takeOverSession(user.id, token);
      addLog('✅ 新会话已创建，其他标签页应该被登出');
    }
  };

  const testSessionCheck = () => {
    addLog('🧪 测试会话有效性检查');
    const isValid = sessionManager.isSessionValid();
    addLog(`会话有效性: ${isValid ? '✅ 有效' : '❌ 无效'}`);
  };

  const testManualLogout = () => {
    addLog('🧪 测试手动登出');
    logout();
  };

  const testWebSocketConnection = () => {
    addLog('🧪 测试WebSocket连接');
    if (token) {
      unifiedWebSocketManager.initialize(token);
      addLog('✅ WebSocket连接已初始化');
    } else {
      addLog('❌ 没有token，无法连接WebSocket');
    }
  };

  const testWebSocketMessage = () => {
    addLog('🧪 测试WebSocket消息发送');
    const success = unifiedWebSocketManager.send({
      type: 'TEST_MESSAGE',
      timestamp: Date.now(),
      message: 'Hello from test page!'
    });
    addLog(success ? '✅ 消息发送成功' : '❌ 消息发送失败');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-4">会话管理测试页面</h1>
        
        {/* 用户状态 */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">用户状态</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>认证状态: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
              {isAuthenticated ? '✅ 已认证' : '❌ 未认证'}
            </span></div>
            <div>用户ID: {user?.id || 'N/A'}</div>
            <div>用户名: {user?.username || 'N/A'}</div>
            <div>Token前缀: {token?.substring(0, 20) + '...' || 'N/A'}</div>
          </div>
        </div>

        {/* 会话信息 */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">会话信息</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>有会话: <span className={sessionInfo.hasSession ? 'text-green-600' : 'text-red-600'}>
              {sessionInfo.hasSession ? '✅ 是' : '❌ 否'}
            </span></div>
            <div>会话ID: {sessionInfo.sessionId || 'N/A'}</div>
            <div>用户ID: {sessionInfo.userId || 'N/A'}</div>
            <div>登录时间: {sessionInfo.loginTime || 'N/A'}</div>
            <div>最后活跃: {sessionInfo.lastActive || 'N/A'}</div>
            <div>会话年龄: {sessionInfo.sessionAge || 'N/A'}</div>
            <div>有活跃会话: <span className={sessionInfo.hasActiveSession ? 'text-green-600' : 'text-red-600'}>
              {sessionInfo.hasActiveSession ? '✅ 是' : '❌ 否'}
            </span></div>
            <div>会话有效: <span className={sessionInfo.isValid ? 'text-green-600' : 'text-red-600'}>
              {sessionInfo.isValid ? '✅ 有效' : '❌ 无效'}
            </span></div>
          </div>
        </div>

        {/* WebSocket信息 */}
        <div className="mb-6 p-4 bg-purple-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">WebSocket信息</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>连接状态: <span className={wsInfo.isConnected ? 'text-green-600' : 'text-red-600'}>
              {wsInfo.isConnected ? '✅ 已连接' : '❌ 未连接'}
            </span></div>
            <div>管理器状态: <span className={wsInfo.hasManager ? 'text-green-600' : 'text-red-600'}>
              {wsInfo.hasManager ? '✅ 正常' : '❌ 异常'}
            </span></div>
          </div>
        </div>

        {/* 测试按钮 */}
        <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">测试功能</h2>
          <div className="space-x-2">
            <button 
              onClick={testForceLogout}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
              disabled={!isAuthenticated}
            >
              测试强制登出
            </button>
            <button 
              onClick={testSessionCheck}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              检查会话有效性
            </button>
            <button
              onClick={testManualLogout}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
              disabled={!isAuthenticated}
            >
              手动登出
            </button>
            <button
              onClick={testWebSocketConnection}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
              disabled={!isAuthenticated}
            >
              测试WebSocket连接
            </button>
            <button
              onClick={testWebSocketMessage}
              className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
              disabled={!isAuthenticated || !wsInfo.isConnected}
            >
              发送测试消息
            </button>
          </div>
        </div>

        {/* 日志 */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">操作日志</h2>
          <div className="bg-black text-green-400 p-3 rounded text-xs font-mono h-64 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>

        {/* 说明 */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">测试说明</h2>
          <ul className="text-sm space-y-1">
            <li>• 打开多个标签页访问此页面</li>
            <li>• 在一个标签页点击"测试强制登出"，其他标签页应该自动登出</li>
            <li>• 会话信息应该实时更新</li>
            <li>• 页面刷新后会话应该自动恢复</li>
            <li>• 同一账号只能在一个标签页保持活跃状态</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
