package com.kitolus.community.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kitolus.community.dto.ProfileDTO;
import com.kitolus.community.dto.RegisterRequestDTO;
import com.kitolus.community.dto.UpdateProfileRequestDTO;
import com.kitolus.community.dto.ChangePasswordRequestDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.entity.DeveloperApplication;
import com.kitolus.community.entity.DeveloperApplicationStatus;
import com.kitolus.community.exception.DuplicateResourceException;
import com.kitolus.community.exception.InvalidDataException;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.mapper.UserMapper;
import com.kitolus.community.mapper.DeveloperApplicationMapper;
import com.kitolus.community.mapper.CommunityPostMapper;
import com.kitolus.community.mapper.CommunityCommentMapper;
import com.kitolus.community.mapper.OrderMapper;
import com.kitolus.community.entity.Order;
import com.kitolus.community.entity.Product;
import com.kitolus.community.mapper.ProductMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.security.SecureRandom;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Collections;
import java.util.stream.Collectors;
import com.kitolus.community.dto.UpdateWithdrawalSettingsDTO;

@Service
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private final UserMapper userMapper;
    private final DeveloperApplicationMapper developerApplicationMapper;
    private final CommunityPostMapper postMapper;
    private final CommunityCommentMapper commentMapper;
    private final PasswordEncoder passwordEncoder;
    private final FileStorageService fileStorageService;
    private final StringRedisTemplate redisTemplate;
    private final EmailService emailService;
    private final ObjectMapper objectMapper;
    private final OrderMapper orderMapper;
    private final ProductMapper productMapper;

    @Value("${app.base-url}")
    private String baseUrl;

    @Value("${app.frontend-url}")
    private String frontendUrl;

    private static final String VERIFICATION_CODE_KEY_PREFIX = "verification:code:";
    private static final String USER_REGISTRATION_DATA_KEY_PREFIX = "user:register:";
    private static final String PASSWORD_RESET_TOKEN_KEY_PREFIX = "password-reset:token:";
    private static final long VERIFICATION_CODE_EXPIRATION_SECONDS = 300; // 5 minutes
    private static final long PASSWORD_RESET_TOKEN_EXPIRATION_MINUTES = 15;

    @Autowired
    public UserServiceImpl(UserMapper userMapper, DeveloperApplicationMapper developerApplicationMapper,
                           CommunityPostMapper postMapper, CommunityCommentMapper commentMapper,
                           PasswordEncoder passwordEncoder, FileStorageService fileStorageService, 
                           StringRedisTemplate redisTemplate, EmailService emailService, ObjectMapper objectMapper,
                           OrderMapper orderMapper, ProductMapper productMapper) {
        this.userMapper = userMapper;
        this.developerApplicationMapper = developerApplicationMapper;
        this.postMapper = postMapper;
        this.commentMapper = commentMapper;
        this.passwordEncoder = passwordEncoder;
        this.fileStorageService = fileStorageService;
        this.redisTemplate = redisTemplate;
        this.emailService = emailService;
        this.objectMapper = objectMapper;
        this.orderMapper = orderMapper;
        this.productMapper = productMapper;
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
    }

    @Override
    public User findById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    public Map<String, Object> requestRegistration(RegisterRequestDTO registerRequestDTO) {
        if (userMapper.selectOne(new QueryWrapper<User>().eq("username", registerRequestDTO.getUsername())) != null) {
            throw new DuplicateResourceException("用户名已被占用！");
        }
        if (userMapper.selectOne(new QueryWrapper<User>().eq("email", registerRequestDTO.getEmail())) != null) {
            throw new DuplicateResourceException("该邮箱已被注册！");
        }

        String verificationCodeKey = VERIFICATION_CODE_KEY_PREFIX + registerRequestDTO.getEmail();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(verificationCodeKey))) {
            throw new DuplicateResourceException("操作太频繁，请稍后再试！");
        }

        String verificationCode = generateVerificationCode();
        String email = registerRequestDTO.getEmail();
        String userDataKey = USER_REGISTRATION_DATA_KEY_PREFIX + email;

        logger.info("为邮箱 {} 生成的验证码是 {}", email, verificationCode);
        logger.info("准备向Redis缓存用户数据，键为: {}", userDataKey);
        logger.info("准备向Redis缓存验证码，键为: {}", verificationCodeKey);

        try {
            String userDataJson = objectMapper.writeValueAsString(registerRequestDTO);
            redisTemplate.opsForValue().set(userDataKey, userDataJson, VERIFICATION_CODE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(verificationCodeKey, verificationCode, VERIFICATION_CODE_EXPIRATION_SECONDS, TimeUnit.SECONDS);

            logger.info("已执行向Redis缓存数据的命令。");

            // 关键一步：立刻检查键是否真的存在
            Boolean hasUserDataKey = redisTemplate.hasKey(userDataKey);
            Boolean hasCodeKey = redisTemplate.hasKey(verificationCodeKey);
            logger.info("Redis写入后检查: 用户数据键是否存在? {}. 验证码键是否存在? {}", hasUserDataKey, hasCodeKey);

            if (Boolean.FALSE.equals(hasUserDataKey) || Boolean.FALSE.equals(hasCodeKey)) {
                logger.error("严重错误：向Redis写入键后立即检查，发现键不存在！请检查Redis服务器状态或配置。");
                // 在这种情况下，我们也许不应该继续发送邮件，直接抛出异常
                throw new RuntimeException("无法向缓存服务器写入验证数据。");
            }

        } catch (JsonProcessingException e) {
            logger.error("序列化用户 {} 的注册信息时出错", email, e);
            throw new RuntimeException("序列化用户注册信息时出错", e);
        } catch (Exception e) {
            logger.error("与Redis交互时发生意外错误，邮箱: {}", email, e);
            // 抛出异常以中断流程
            throw new RuntimeException("缓存服务通信异常", e);
        }

        emailService.sendVerificationCode(email, verificationCode);

        return Map.of(
            "message", "注册请求已发送，请检查您的邮箱以获取验证码。",
            "expiresIn", VERIFICATION_CODE_EXPIRATION_SECONDS
        );
    }

    @Override
    public User completeRegistration(String email, String code) {
        String storedCode = redisTemplate.opsForValue().get(VERIFICATION_CODE_KEY_PREFIX + email);

        if (storedCode == null) {
            throw new InvalidDataException("验证码已过期或不存在，请重新注册。");
        }
        if (!storedCode.equals(code)) {
            throw new InvalidDataException("验证码不正确。");
        }

        String userDataJson = redisTemplate.opsForValue().get(USER_REGISTRATION_DATA_KEY_PREFIX + email);
        if (userDataJson == null) {
            throw new InvalidDataException("用户注册信息已过期，请重新注册。");
        }

        try {
            RegisterRequestDTO registerRequestDTO = objectMapper.readValue(userDataJson, RegisterRequestDTO.class);

            User user = new User();
            user.setUsername(registerRequestDTO.getUsername());
            user.setEmail(registerRequestDTO.getEmail());
            user.setPassword(passwordEncoder.encode(registerRequestDTO.getPassword()));
            user.setRole("USER");
            user.setEnabled(true);
            String serialNumber = java.util.UUID.randomUUID().toString();
            user.setSerialNumber(serialNumber);
            user.setCreatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
            
            // The user has not uploaded a custom avatar yet, so this should be null.
            // The frontend will use the serial number to generate a Multiavatar as a fallback.
            user.setAvatarUrl(null);
            user.setBannerUrl(null);

            userMapper.insert(user);

            redisTemplate.delete(VERIFICATION_CODE_KEY_PREFIX + email);
            redisTemplate.delete(USER_REGISTRATION_DATA_KEY_PREFIX + email);

            return user;

        } catch (JsonProcessingException e) {
            throw new RuntimeException("反序列化用户注册信息时出错", e);
        }
    }

    @Override
    public void applyForDeveloperRole(String username, String message) {
        User user = findByUsername(username);
        if (user == null) {
            throw new ResourceNotFoundException("申请用户未找到。");
        }

        QueryWrapper<DeveloperApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", user.getId()).eq("status", "PENDING");
        if (developerApplicationMapper.selectCount(queryWrapper) > 0) {
            throw new DuplicateResourceException("您已有一个正在审核中的开发者申请，请勿重复提交。");
        }

        DeveloperApplication application = new DeveloperApplication();
        application.setUserId(user.getId());
        application.setStatus(DeveloperApplicationStatus.PENDING);
        application.setMessage(message);
        application.setCreatedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        
        developerApplicationMapper.insert(application);
    }

    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        int num = random.nextInt(900000) + 100000;
        return String.valueOf(num);
    }

    @Override
    public ProfileDTO getUserProfile(String username) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }
        
        // Fetch post and comment counts
        int postCount = postMapper.selectCount(new QueryWrapper<com.kitolus.community.entity.CommunityPost>().eq("author_id", user.getId())).intValue();
        int commentCount = commentMapper.selectCount(new QueryWrapper<com.kitolus.community.entity.CommunityComment>().eq("author_id", user.getId())).intValue();

        ProfileDTO profileDTO = new ProfileDTO();
        profileDTO.setId(user.getId());
        profileDTO.setUsername(user.getUsername());
        profileDTO.setEmail(user.getEmail());
        profileDTO.setAvatarUrl(user.getAvatarUrl());
        profileDTO.setAvatarVersion(user.getAvatarVersion());
        profileDTO.setBannerUrl(user.getBannerUrl());
        profileDTO.setBannerVersion(user.getBannerVersion());
        profileDTO.setRole(user.getRole());
        profileDTO.setSerialNumber(user.getSerialNumber());
        profileDTO.setCreatedAt(user.getCreatedAt());
        profileDTO.setPostCount(postCount);
        profileDTO.setCommentCount(commentCount);
        profileDTO.setAlipayAccount(user.getAlipayAccount());
        profileDTO.setWechatAccount(user.getWechatAccount());

        return profileDTO;
    }

    @Override
    public ProfileDTO getUserPublicProfile(String username) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }
        
        // Fetch post and comment counts
        int postCount = postMapper.selectCount(new QueryWrapper<com.kitolus.community.entity.CommunityPost>().eq("author_id", user.getId())).intValue();
        int commentCount = commentMapper.selectCount(new QueryWrapper<com.kitolus.community.entity.CommunityComment>().eq("author_id", user.getId())).intValue();

        ProfileDTO profileDTO = new ProfileDTO();
        profileDTO.setId(user.getId());
        profileDTO.setUsername(user.getUsername());
        // Do NOT expose email in public profiles
        profileDTO.setAvatarUrl(user.getAvatarUrl());
        profileDTO.setAvatarVersion(user.getAvatarVersion());
        profileDTO.setBannerUrl(user.getBannerUrl());
        profileDTO.setBannerVersion(user.getBannerVersion());
        profileDTO.setRole(user.getRole());
        profileDTO.setSerialNumber(user.getSerialNumber());
        profileDTO.setCreatedAt(user.getCreatedAt());
        profileDTO.setPostCount(postCount);
        profileDTO.setCommentCount(commentCount);

        return profileDTO;
    }

    @Override
    public User updateUserProfile(String currentUsername, UpdateProfileRequestDTO profileData) {
        User user = findByUsername(currentUsername);
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }

        // Update username if it's provided and different
        if (profileData.getUsername() != null && !profileData.getUsername().isEmpty() && !profileData.getUsername().equals(currentUsername)) {
            // Check if the new username is already taken
            if (userMapper.selectCount(new QueryWrapper<User>().eq("username", profileData.getUsername())) > 0) {
                throw new DuplicateResourceException("用户名已被占用");
            }
            user.setUsername(profileData.getUsername());
        }

        // Update payment accounts
        if (profileData.getAlipayAccount() != null) {
            user.setAlipayAccount(profileData.getAlipayAccount());
        }
        if (profileData.getWechatAccount() != null) {
            user.setWechatAccount(profileData.getWechatAccount());
        }

        userMapper.updateById(user);
        return user;
    }

    @Override
    public String updateUserAvatar(String username, MultipartFile file) {
        // --- 文件大小和类型校验 ---
        if (file.isEmpty()) {
            throw new InvalidDataException("无法存储空文件。");
        }
        // 5MB in bytes
        long maxFileSize = 5 * 1024 * 1024; 
        if (file.getSize() > maxFileSize) {
            throw new InvalidDataException("文件大小不能超过 5MB。");
        }
        // 可选：文件类型校验
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png") && !contentType.equals("image/webp"))) {
            throw new InvalidDataException("只支持上传 JPG, PNG 或 WebP 格式的图片。");
        }

        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }

        if (user.getSerialNumber() == null || user.getSerialNumber().isEmpty()) {
            user.setSerialNumber(UUID.randomUUID().toString());
        }
        String baseFileName = user.getSerialNumber();

        String fileName = fileStorageService.storeFile(file, baseFileName, "avatars");
        
        // Ensure the path is always stored consistently and correctly.
        String newAvatarUrl = "/storage/avatars/" + fileName;
        user.setAvatarUrl(newAvatarUrl);
        user.setAvatarVersion((user.getAvatarVersion() == null ? 0 : user.getAvatarVersion()) + 1);
        userMapper.updateById(user);
        
        return newAvatarUrl;
    }

    @Override
    public String updateUserBanner(String username, MultipartFile file) {
        if (file.isEmpty()) {
            throw new InvalidDataException("无法存储空文件。");
        }
        long maxFileSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxFileSize) {
            throw new InvalidDataException("文件大小不能超过 10MB。");
        }
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.equals("image/jpeg") && !contentType.equals("image/png") && !contentType.equals("image/webp"))) {
            throw new InvalidDataException("只支持上传 JPG, PNG 或 WebP 格式的图片。");
        }
        
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }
        
        if (user.getSerialNumber() == null || user.getSerialNumber().isEmpty()) {
            user.setSerialNumber(UUID.randomUUID().toString());
        }
        String baseFileName = user.getSerialNumber();

        String fileName = fileStorageService.storeFile(file, baseFileName, "banners");
        
        // Ensure the path is always stored consistently and correctly.
        String newBannerUrl = "/storage/banners/" + fileName;
        user.setBannerUrl(newBannerUrl);
        user.setBannerVersion((user.getBannerVersion() == null ? 0 : user.getBannerVersion()) + 1);
        userMapper.updateById(user);

        return newBannerUrl;
    }

    @Override
    public void deleteUser(String username) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("尝试删除一个不存在的用户。");
        }
        userMapper.deleteById(user.getId());
        logger.info("用户 '{}' (ID: {}) 已被成功删除。", username, user.getId());
    }

    @Override
    public void changePassword(String username, ChangePasswordRequestDTO changePasswordRequest) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到。");
        }

        // 验证当前密码
        if (!passwordEncoder.matches(changePasswordRequest.getCurrentPassword(), user.getPassword())) {
            throw new InvalidDataException("原密码错误，请重试。");
        }

        // 验证新密码
        if (passwordEncoder.matches(changePasswordRequest.getNewPassword(), user.getPassword())) {
            throw new InvalidDataException("新密码不能与原密码相同。");
        }
        if (changePasswordRequest.getNewPassword().length() < 6) {
            throw new InvalidDataException("新密码长度不能少于6个字符。");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(changePasswordRequest.getNewPassword()));
        userMapper.updateById(user);
    }

    @Override
    public DeveloperApplication findLatestDeveloperApplicationByUserId(Long userId) {
        QueryWrapper<DeveloperApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).orderByDesc("created_at").last("LIMIT 1");
        return developerApplicationMapper.selectOne(queryWrapper);
    }

    @Override
    public void handleForgotPasswordRequest(String email) {
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("email", email));
        if (user == null) {
            // To prevent user enumeration, we don't throw an error.
            // The controller will return a generic success message.
            logger.warn("Password reset requested for non-existent email: {}", email);
            return;
        }

        String token = UUID.randomUUID().toString();
        String redisKey = PASSWORD_RESET_TOKEN_KEY_PREFIX + token;
        
        // Store the user's email against the token
        redisTemplate.opsForValue().set(redisKey, email, PASSWORD_RESET_TOKEN_EXPIRATION_MINUTES, TimeUnit.MINUTES);

        String resetUrl = frontendUrl + "/reset-password?token=" + token;
        emailService.sendPasswordResetEmail(email, user.getUsername(), resetUrl);
    }

    @Override
    public void resetPassword(String token, String newPassword) {
        String redisKey = PASSWORD_RESET_TOKEN_KEY_PREFIX + token;
        String email = redisTemplate.opsForValue().get(redisKey);

        if (email == null) {
            throw new InvalidDataException("密码重置令牌无效或已过期。");
        }

        User user = userMapper.selectOne(new QueryWrapper<User>().eq("email", email));
        if (user == null) {
            // This should not happen if the token is valid, but as a safeguard.
            throw new ResourceNotFoundException("与此令牌关联的用户不存在。");
        }

        if (newPassword.length() < 6) {
            throw new InvalidDataException("新密码长度不能少于6个字符。");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updateById(user);

        // Invalidate the token after use
        redisTemplate.delete(redisKey);
    }

    @Override
    public List<Product> getPurchasedProducts(Long userId) {
        // Find all PAID orders for the user
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("status", "PAID");
        List<Order> paidOrders = orderMapper.selectList(queryWrapper);

        if (paidOrders.isEmpty()) {
            return Collections.emptyList();
        }

        // Get all unique product IDs from the paid orders
        List<Long> productIds = paidOrders.stream()
                                          .map(Order::getProductId)
                                          .distinct()
                                          .collect(Collectors.toList());
        
        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }

        // Fetch all products with these IDs
        return productMapper.selectBatchIds(productIds);
    }

    @Override
    public void updateWithdrawalSettings(String username, UpdateWithdrawalSettingsDTO settingsDTO) {
        User user = findByUsername(username);
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到");
        }

        user.setAlipayAccount(settingsDTO.getAlipayAccount());
        user.setWechatAccount(settingsDTO.getWechatAccount());

        userMapper.updateById(user);
    }

    @Override
    public List<Map<String, String>> searchUsersByUsername(String query) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("username", query)
                   .select("id", "username", "avatar_url")
                   .orderBy(true, true, "username")
                   .last("LIMIT 10");

        List<User> users = userMapper.selectList(queryWrapper);
        return users.stream()
                   .map(user -> {
                       Map<String, String> userMap = new HashMap<>();
                       userMap.put("id", user.getId().toString());
                       userMap.put("username", user.getUsername());
                       userMap.put("avatarUrl", user.getAvatarUrl());
                       return userMap;
                   })
                   .collect(Collectors.toList());
    }
}