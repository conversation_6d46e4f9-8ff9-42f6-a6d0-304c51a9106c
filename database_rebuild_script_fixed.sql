-- =====================================================
-- GTNH Website 数据库重建脚本 (修正版)
-- 修正了 serial_number 字段长度问题和其他潜在问题
-- =====================================================

-- 删除并重新创建数据库
DROP DATABASE IF EXISTS kitolus_community;
CREATE DATABASE kitolus_community CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kitolus_community;

-- 用户表 (修正 serial_number 字段长度)
CREATE TABLE user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    serial_number VARCHAR(50) UNIQUE,  -- 修正：UUID需要36个字符，增加到50以确保足够
    avatar_url VARCHAR(255),
    banner_url VARCHAR(255),
    avatar_version INT DEFAULT 0,
    banner_version INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    enabled BOOLEAN DEFAULT TRUE,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_serial_number (serial_number),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 社区话题表
CREATE TABLE community_topic (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    era VARCHAR(50),
    tier VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_era_tier (era, tier)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 社区帖子表
CREATE TABLE community_post (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id BIGINT NOT NULL,
    topic_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_pinned BOOLEAN DEFAULT FALSE,
    pinned_at TIMESTAMP NULL,
    pinned_by BIGINT NULL,
    INDEX idx_author_id (author_id),
    INDEX idx_topic_id (topic_id),
    INDEX idx_created_at (created_at),
    INDEX idx_pinned (is_pinned, pinned_at),
    FOREIGN KEY (author_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES community_topic(id) ON DELETE CASCADE,
    FOREIGN KEY (pinned_by) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 社区评论表
CREATE TABLE community_comment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL,
    author_id BIGINT NOT NULL,
    post_id BIGINT NOT NULL,
    parent_comment_id BIGINT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_author_id (author_id),
    INDEX idx_post_id (post_id),
    INDEX idx_parent_comment_id (parent_comment_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (author_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES community_post(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_comment_id) REFERENCES community_comment(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 帖子点赞表
CREATE TABLE community_post_like (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    post_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_post (user_id, post_id),
    INDEX idx_post_id (post_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES community_post(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 产品表
CREATE TABLE products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    name VARCHAR(200) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    download_url VARCHAR(255),
    author_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    status ENUM('PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'DELISTED') DEFAULT 'PENDING_APPROVAL',
    rejection_reason TEXT,
    approval_notes TEXT,
    reviewed_by BIGINT NULL,
    deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted (deleted),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    out_trade_no VARCHAR(100) UNIQUE,  -- 外部交易号
    total_fee DECIMAL(10,2) NOT NULL,  -- 总金额
    status VARCHAR(50) DEFAULT 'PENDING',  -- 订单状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    paid_at TIMESTAMP NULL,
    platform_fee DECIMAL(10,2),  -- 平台费用
    developer_revenue DECIMAL(10,2),  -- 开发者收入
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_out_trade_no (out_trade_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 消息表 (使用DATETIME类型以匹配LocalDateTime)
CREATE TABLE messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT NOT NULL,
    receiver_id BIGINT NULL,
    conversation_id VARCHAR(100),
    message_type ENUM('PRIVATE', 'SYSTEM', 'BROADCAST', 'GROUP') DEFAULT 'PRIVATE',
    title VARCHAR(200),
    content TEXT NOT NULL,
    status ENUM('SENT', 'DELIVERED', 'READ', 'FAILED', 'DELETED') DEFAULT 'SENT',
    is_read BOOLEAN DEFAULT FALSE,
    read_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    metadata TEXT,
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_message_type (message_type),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read),
    FOREIGN KEY (sender_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 通知表
CREATE TABLE notification (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    recipient_id BIGINT NOT NULL,
    sender_id BIGINT NULL,
    type ENUM('NEW_COMMENT', 'REPLY_TO_COMMENT', 'POST_LIKE', 'MENTION_IN_POST', 'MENTION_IN_COMMENT', 'DEVELOPER_APP_APPROVED', 'DEVELOPER_APP_REJECTED', 'PRODUCT_APPROVED', 'PRODUCT_REJECTED', 'PRODUCT_DELISTED') NOT NULL,
    post_id BIGINT NULL,
    comment_id BIGINT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (recipient_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES user(id) ON DELETE SET NULL,
    FOREIGN KEY (post_id) REFERENCES community_post(id) ON DELETE CASCADE,
    FOREIGN KEY (comment_id) REFERENCES community_comment(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 开发者申请表
CREATE TABLE developer_application (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by BIGINT NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 提现请求表
CREATE TABLE withdrawal_request (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_account VARCHAR(200) NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by BIGINT NULL,
    notes TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 登录历史表
CREATE TABLE login_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 产品状态历史表
CREATE TABLE product_status_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT NOT NULL,
    old_status ENUM('PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'DELISTED'),
    new_status ENUM('PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'DELISTED') NOT NULL,
    changed_by BIGINT NOT NULL,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reason TEXT,
    INDEX idx_product_id (product_id),
    INDEX idx_changed_at (changed_at),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 插入初始数据
-- =====================================================

-- 插入管理员用户 (密码: admin123)
-- BCrypt哈希生成: $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLVZqpjBWNa9CzUzPeyW
INSERT INTO user (username, password, email, role, serial_number, enabled, created_at) VALUES
('KitolusAdmin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLVZqpjBWNa9CzUzPeyW', '<EMAIL>', 'KitolusAdmin', 'admin-uuid-12345678-1234-1234-1234-123456789012', TRUE, NOW());

-- 插入社区话题数据
INSERT INTO community_topic (name, description, era, tier) VALUES
-- Pre-Steam Era
('石器时代', '最初的工具和基础设施建设', 'Pre-Steam', 'Stone Age'),
('青铜时代', '合金技术的开始', 'Pre-Steam', 'Bronze Age'),
('铁器时代', '铁制工具和武器的时代', 'Pre-Steam', 'Iron Age'),

-- Steam Era
('蒸汽时代', '蒸汽动力的工业革命', 'Steam', 'Steam Age'),
('钢铁时代', '大规模钢铁生产', 'Steam', 'Steel Age'),

-- Electric Era
('电气时代', '电力技术的兴起', 'Electric', 'Electric Age'),
('数字时代', '计算机和数字技术', 'Electric', 'Digital Age'),

-- Atomic Era
('原子时代', '核能技术的应用', 'Atomic', 'Atomic Age'),
('太空时代', '航天技术和太空探索', 'Atomic', 'Space Age'),

-- Quantum Era
('量子时代', '量子物理的应用', 'Quantum', 'Quantum Age'),
('纳米时代', '纳米技术和材料科学', 'Quantum', 'Nano Age'),

-- Fusion Era
('聚变时代', '核聚变能源技术', 'Fusion', 'Fusion Age'),
('星际时代', '星际旅行和殖民', 'Fusion', 'Interstellar Age'),

-- Transcendent Era
('超越时代', '超越物理限制的技术', 'Transcendent', 'Transcendent Age'),
('奇点时代', '技术奇点后的世界', 'Transcendent', 'Singularity Age'),

-- 通用话题
('新手指南', '适合新玩家的基础教程和指导', 'General', 'Beginner'),
('进阶技巧', '高级玩家的技术讨论', 'General', 'Advanced'),
('模组推荐', '推荐有用的模组和工具', 'General', 'Mods'),
('服务器讨论', '多人游戏和服务器相关', 'General', 'Multiplayer'),
('创意分享', '展示你的建筑和创意', 'General', 'Creative'),
('问题求助', '遇到问题时寻求帮助', 'General', 'Help'),
('更新日志', '游戏和模组的更新信息', 'General', 'Updates'),
('社区活动', '社区举办的各种活动', 'General', 'Events');

-- =====================================================
-- 脚本执行完成
-- =====================================================

-- 显示创建的表
SHOW TABLES;

-- 显示用户表结构以验证 serial_number 字段
DESCRIBE user;

-- 验证初始数据
SELECT COUNT(*) as total_topics FROM community_topic;
SELECT username, role, serial_number FROM user WHERE role = 'KitolusAdmin';

-- 完成提示
SELECT '数据库重建完成！所有表已创建，初始数据已插入。' as status;
