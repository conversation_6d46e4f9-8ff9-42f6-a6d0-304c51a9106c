package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

@Data
@TableName("product_status_history")
public class ProductStatusHistory {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("product_id")
    private Long productId;
    
    @TableField("old_status")
    private ProductStatus oldStatus;
    
    @TableField("new_status")
    private ProductStatus newStatus;
    
    @TableField("changed_by")
    private String changedBy;
    
    @TableField("changed_at")
    private Timestamp changedAt;
    
    @TableField("reason")
    private String reason;
    
    @TableField("notes")
    private String notes;
}
