'use client';

import React, { useState } from 'react';
import { SearchBox } from '@/components/SearchBox';
import { globalSearch, getSearchSuggestions } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

/**
 * 搜索功能测试页面
 */
export default function TestSearchPage() {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testGlobalSearch = async () => {
    setIsLoading(true);
    try {
      const results = await globalSearch('test');
      setTestResults({ type: 'globalSearch', data: results });
    } catch (error) {
      setTestResults({ type: 'error', data: error });
    } finally {
      setIsLoading(false);
    }
  };

  const testSearchSuggestions = async () => {
    setIsLoading(true);
    try {
      const results = await getSearchSuggestions('kit');
      setTestResults({ type: 'suggestions', data: results });
    } catch (error) {
      setTestResults({ type: 'error', data: error });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">搜索功能测试</h1>
      
      {/* 搜索组件演示 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>搜索组件演示</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">桌面端搜索框</h3>
              <SearchBox />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">移动端搜索框</h3>
              <SearchBox isMobile={true} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API测试 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>API测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button 
                onClick={testGlobalSearch} 
                disabled={isLoading}
              >
                测试全局搜索
              </Button>
              <Button 
                onClick={testSearchSuggestions} 
                disabled={isLoading}
                variant="outline"
              >
                测试搜索建议
              </Button>
            </div>

            {testResults && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">测试结果:</h4>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">搜索框功能:</h4>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                <li>点击搜索图标展开输入框</li>
                <li>输入关键词会显示实时建议</li>
                <li>按Enter或点击建议项执行搜索</li>
                <li>按ESC或点击X按钮关闭搜索框</li>
                <li>支持键盘导航（上下箭头选择建议）</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">搜索结果页面:</h4>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                <li>访问 /search?q=关键词 查看搜索结果</li>
                <li>支持用户、帖子、文章分类显示</li>
                <li>显示搜索统计和无结果提示</li>
                <li>响应式设计，移动端友好</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
