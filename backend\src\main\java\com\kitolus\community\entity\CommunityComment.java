package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("community_comment")
public class CommunityComment {
    private Long id;
    private Long postId;
    private Long authorId;
    private Long parentId;
    private String content;
    private Timestamp createdAt;
    private Timestamp updatedAt;

    @TableField(exist = false)
    private User author;

    @TableField(exist = false)
    private String parentAuthorUsername;

    @TableField(exist = false)
    private List<CommunityComment> replies;

    @TableField(exist = false)
    private Set<String> mentionedUsers;
} 