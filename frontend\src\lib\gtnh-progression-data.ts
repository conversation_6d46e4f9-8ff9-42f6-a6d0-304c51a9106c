export {};

export interface StageTopic {
  subStage: string;
  milestone: string;
  circuit?: string;
  notes: string;
}

export interface VoltageTier {
  name: string;
  topics: StageTopic[];
}

export interface MajorEra {
  name: string;
  tiers: VoltageTier[];
}

export const gtnhProgression: MajorEra[] = [
  {
    name: "初期",
    tiers: [
      {
        name: "石器",
        topics: [
          { subStage: "Java", milestone: "安装Java", notes: "你选择J8还是J17" },
          { subStage: "安装游戏", milestone: "安装好游戏", notes: "你下载的是导入包吗？" },
          { subStage: "启动游戏", milestone: "启动游戏，进入存档", notes: "你的语言文件改成简体中文了吗？" },
          { subStage: "铁器", milestone: "铁制工具", notes: "好像跳过了很多步骤！" },
        ]
      },
      {
        name: "蒸汽 ULV 8V",
        topics: [
          { subStage: "步入蒸汽", milestone: "小型燃煤锅炉", notes: "发现找矿比挖矿难" },
          { subStage: "土高炉", milestone: "砖高炉", circuit: "电路板", notes: "终于可以炼钢了" },
        ]
      },
      {
        name: "低压 LV 32V",
        topics: [
          { subStage: "步入低压", milestone: "基础蒸汽轮机", notes: "格雷必修一：电学" },
          { subStage: "电高炉", milestone: "工业高炉", notes: "用电的多方块结构测试" },
          { subStage: "铝处理", milestone: "铝锭", notes: "初次相遇：矿物处理" },
        ]
      },
      {
        name: "中压 MV 128V",
        topics: [
          { subStage: "步入中压", milestone: "MV机器外壳", notes: "向活得像人，更进一步" },
          { subStage: "制取乙烯", milestone: "聚乙烯", notes: "格雷必修二：化学" },
          { subStage: "太阳能硅", milestone: "太阳能级硅（多晶硅）锭", notes: "初次相遇：材料合成" },
          { subStage: "另类MV", milestone: "贴片晶体管", circuit: "优质电子电路板", notes: "格雷选修一：逃课" },
          { subStage: "坎塔尔合金", milestone: "坎塔尔合金线圈", notes: "初次相遇：升级线圈" },
        ]
      },
      {
        name: "高压 HV 512V",
        topics: [
          { subStage: "步入高压", milestone: "HV机器外壳", notes: "大概是一个现代人了" },
          { subStage: "无尘空间", milestone: "超净间", circuit: "处理器主机", notes: "电路板工艺：升级" },
          { subStage: "石油化工", milestone: "石油裂化机、蒸馏塔", notes: "多路线合成材料" },
          { subStage: "太空时代", milestone: "NASA工作台、T1火箭", notes: "太空起点，你知道什么是ITNT吗？" },
        ]
      },
      {
        name: "超高压 EV 2048V",
        topics: [
          { subStage: "步入超高压", milestone: "EV机器外壳", notes: "渐入佳境" },
          { subStage: "贵金属", milestone: "铂的处理", notes: "久负盛名，苦痛开端" },
          { subStage: "纳米电路板", milestone: "氡、纳米处理器主机", circuit: "纳米处理器主机", notes: "钍、镭、氡" },
          { subStage: "造访火星", milestone: "T2火箭", notes: "铀、钚" },
          { subStage: "钛铂钒合金", milestone: "钛铂钒合金线圈", notes: "TPF" },
        ]
      },
    ]
  },
  {
    name: "中期",
    tiers: [
      {
        name: "强导压 IV 8192V",
        topics: [
          { subStage: "一重天", milestone: "T3火箭", notes: "硅岩、罗斯128b" },
          { subStage: "二重天", milestone: "聚苯并咪唑", notes: "PBI" },
          { subStage: "三重天", milestone: "硅岩'粒'处理", notes: "浪费也得做" },
          { subStage: "四重天", milestone: "合金冶炼炉", notes: "准备好GTPP大乱炖" },
          { subStage: "五重天", milestone: "完全铂处理", notes: "铂、钯、铑、钌、铱、锇" },
          { subStage: "六重天", milestone: "量子处理器", circuit: "量子处理器主机", notes: "电路板工艺：量子" },
          { subStage: "七重天", milestone: "装配线", notes: "你的意思是壳子是电路板？" },
          { subStage: "八重天", milestone: "T4火箭", notes: "水星迫降抢劫" },
          { subStage: "九重天", milestone: "钐处理", notes: "其实处理流程只是一条直线" },
        ]
      },
      {
        name: "剧差压 LuV 32768V",
        topics: [
          { subStage: "硅岩之链", milestone: "完全硅岩处理", notes: "小小的Arcaea震撼" },
          { subStage: "合金之力", milestone: "硅岩线圈与硅岩合金线圈", notes: "全名是硅岩金属" },
          { subStage: "聚变之内", milestone: "核聚变反应堆控制电脑MK1", notes: "我们仍未知道那天所创造的物质的脑洞从何而来" },
          { subStage: "能量之变", milestone: "等离子发电", notes: "欢迎来到大型发电现场" },
          { subStage: "晶体之铕", milestone: "铕与晶体处理器", circuit: "晶体处理器主机", notes: "电路板工艺：晶体" },
          { subStage: "五阶之宇", milestone: "T5火箭", notes: "康康土星" },
        ]
      },
      {
        name: "零点压 ZPM 131072V",
        topics: [
          { subStage: "三元物质", milestone: "三元金属线圈", notes: "Ke" },
          { subStage: "净化水之开端", milestone: "3级净化水", notes: "你是水吗" },
          { subStage: "中子物质", milestone: "中子锭", notes: "'不再是菜鸟的证明'" },
          { subStage: "创造物质", milestone: "核聚变反应堆控制电脑MK2", notes: "铿铀，记住，铿锵有力" },
          { subStage: "涅普涅普", milestone: "T6火箭", notes: "海王星" },
        ]
      },
      {
        name: "极限压 UV 524288V",
        topics: [
          { subStage: "通流合金", milestone: "通流琥珀金线圈", notes: "化学式太长了……" },
          { subStage: "化工震撼", milestone: "强化劳伦姆机械方块", notes: "你要做这个吗" },
          { subStage: "太阳边缘", milestone: "T7火箭", notes: "制造T7火箭燃料" },
          { subStage: "未知高峰", milestone: "研究站、量子计算机", circuit: "湿件处理器主机", notes: "并不随机，但乱点也行" },
          { subStage: "生物力量", milestone: "湿件处理器", notes: "TecTech的前路不可探寻……" },
          { subStage: "等离子水", milestone: "5级净化水", notes: "是用等离子净化水" },
          { subStage: "无限攀升", milestone: "核聚变反应堆控制电脑MK3", circuit: "湿件处理器主机", notes: "电路板工艺：湿件" },
        ]
      },
    ]
  },
  {
    name: "后期",
    tiers: [
      {
        name: "极高压 UHV 2097152V",
        topics: [
          { subStage: "空间起点", milestone: "T8火箭", notes: "多挖挖矿" },
          { subStage: "龙与火焰", milestone: "觉醒龙锭线圈", notes: "从地里刨出来的，大概是什么葬龙星" },
          { subStage: "无线无尽", milestone: "中子态素压缩机", notes: "无限催化剂与无尽锭" },
          { subStage: "反常生物", milestone: "量子反常与突变活性焊料", notes: "追求概率的美妙1% * 1% ！" },
          { subStage: "生物伟力", milestone: "生物处理器", circuit: "生物处理器主机", notes: "电路板工艺：生物" },
        ]
      },
      {
        name: "超极限压 UEV 8388608V",
        topics: [
          { subStage: "有限聚变", milestone: "核聚变反应堆控制电脑MK4", notes: "前途无限且可怖" },
          { subStage: "无尽热量", milestone: "无尽线圈", circuit: "光学处理器主机", notes: "七彩铁锭" },
          { subStage: "限于光明", milestone: "光学处理器", notes: "电路板工艺：光学" },
          { subStage: "可数无限", milestone: "永恒奇点", notes: "压缩之后是压缩" },
          { subStage: "永生聚合", milestone: "海珀珍线圈", notes: "永生之龙的血、无尽之聚变物质……" },
          { subStage: "超越维度", milestone: "超维度等离子锻炉", notes: "维度残留于世间……" },
          { subStage: "打碎高维", milestone: "超立方体", notes: "创造出来，然后摧毁它" },
        ]
      },
      {
        name: "极强导压 UIV 33554432V",
        topics: [
          { subStage: "计算混沌", milestone: "Piko电路", circuit: "Piko电路", notes: "掌握时空，掌握自己" },
          { subStage: "时空有控", milestone: "时空", notes: "" },
          { subStage: "永恒塑型", milestone: "永恒线圈", notes: "" },
        ]
      },
      {
        name: "极剧差压 UMV 134217728V",
        topics: [
          { subStage: "非彼量子", milestone: "量子电路", circuit: "量子电路", notes: "" },
          { subStage: "深埋纬度", milestone: "漆黑之门", notes: "攫取无限物质" },
          { subStage: "宇宙洪荒", milestone: "鸿蒙之眼", notes: "创世后碾碎……" },
        ]
      },
      {
        name: "极上拓压 UXV 536870912V",
        topics: [
          { subStage: "晒晒太阳", milestone: "星门", notes: "这下得好好晒晒太阳了" },
        ]
      },
    ]
  },
  {
    name: "终局",
    tiers: [
      {
        name: "终压 MAX 2147483647V",
        topics: [
          { subStage: "-", milestone: "-", circuit: "-", notes: "-" }
        ]
      },
    ]
  }
]; 