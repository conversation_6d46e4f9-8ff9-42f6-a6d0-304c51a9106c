'use client';

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

const ProfilePage = () => {
    const { user, loading } = useAuth();
    const router = useRouter();

    // 当用户信息加载完成后，重定向到用户的主页
    useEffect(() => {
        if (!loading && user) {
            router.replace(`/profile/${user.username}`);
        }
    }, [loading, user, router]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Loader2 className="h-16 w-16 animate-spin text-primary" />
            </div>
        );
    }

    if (!user) {
        return (
            <div className="flex items-center justify-center min-h-screen text-white">
                <p className="text-xl">请先登录以查看您的个人资料。</p>
            </div>
        );
    }

    // 显示加载状态，等待重定向
    return (
        <div className="flex items-center justify-center min-h-screen">
            <Loader2 className="h-16 w-16 animate-spin text-primary" />
        </div>
    );
};

export default ProfilePage; 