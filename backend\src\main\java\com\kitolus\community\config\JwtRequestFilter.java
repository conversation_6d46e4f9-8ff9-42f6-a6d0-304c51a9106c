package com.kitolus.community.config;

import com.kitolus.community.entity.User;
import com.kitolus.community.service.LoginHistoryService;
import com.kitolus.community.service.UserService;
import com.kitolus.community.util.JwtTokenUtil;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.security.authentication.DisabledException;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class JwtRequestFilter extends OncePerRequestFilter {

    private final Log logger = LogFactory.getLog(this.getClass());

    private final UserDetailsService userDetailsService;
    private final JwtTokenUtil jwtTokenUtil;
    private final StringRedisTemplate redisTemplate;
    private final UserService userService;
    private final LoginHistoryService loginHistoryService;

    private static final String ACTIVE_SESSION_KEY_PREFIX = "user-session:active:";
    private static final String TOKEN_SESSION_KEY_PREFIX = "token-session:";
    private static final long SESSION_TIMEOUT_MINUTES = 30;
    private static final long TOKEN_SESSION_TIMEOUT_HOURS = 24; // 与JWT过期时间匹配

    @Autowired
    public JwtRequestFilter(
            UserDetailsService userDetailsService,
            JwtTokenUtil jwtTokenUtil,
            StringRedisTemplate redisTemplate,
            @Lazy UserService userService,
            LoginHistoryService loginHistoryService) {
        this.userDetailsService = userDetailsService;
        this.jwtTokenUtil = jwtTokenUtil;
        this.redisTemplate = redisTemplate;
        this.userService = userService;
        this.loginHistoryService = loginHistoryService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {

        final String requestUri = request.getRequestURI();

        // Allow public access to view products, but require authentication for other product-related actions.
        if ("GET".equalsIgnoreCase(request.getMethod()) && "/api/products".equals(requestUri)) {
            chain.doFilter(request, response);
            return;
        }

        // Define a list of public paths that should bypass JWT validation
        final List<String> publicPaths = Arrays.asList(
            "/api/auth/",
            "/api/kb/",
            "/api/payment/notify",
            "/error",
            "/images/",
            "/files/"
        );

        // Check if the request URI starts with any of the public paths
        boolean isPublicPath = publicPaths.stream().anyMatch(requestUri::startsWith);

        if (isPublicPath) {
            chain.doFilter(request, response);
            return;
        }

        final String requestTokenHeader = request.getHeader("Authorization");

        String username = null;
        String jwtToken = null;

        if (requestTokenHeader != null && requestTokenHeader.startsWith("Bearer ")) {
            jwtToken = requestTokenHeader.substring(7);
            try {
                username = jwtTokenUtil.getUsernameFromToken(jwtToken);
            } catch (IllegalArgumentException e) {
                logger.warn("Unable to get JWT Token");
            } catch (ExpiredJwtException e) {
                logger.warn("JWT Token has expired");
            }
        } else {
             if(requestTokenHeader != null) {
                logger.warn("JWT Token does not begin with Bearer String");
            }
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            try {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);

            if (jwtTokenUtil.validateToken(jwtToken, userDetails)) {
                UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                usernamePasswordAuthenticationToken
                        .setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);

                // --- Session Activity Tracking ---
                handleLoginHistory(username, jwtToken, request);
                }
            } catch (DisabledException e) {
                logger.warn("Authentication attempt for disabled user: " + username);
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "User account is disabled.");
                return;
            }
        }
        chain.doFilter(request, response);
    }
    
    private void handleLoginHistory(String username, String jwtToken, HttpServletRequest request) {
        String sessionKey = ACTIVE_SESSION_KEY_PREFIX + username;
        String tokenSessionKey = TOKEN_SESSION_KEY_PREFIX + jwtToken;

        // 检查基于token的会话
        Boolean hasTokenSession = redisTemplate.hasKey(tokenSessionKey);

        if (Boolean.FALSE.equals(hasTokenSession)) {
            User user = userService.findByUsername(username);
            if (user != null) {
                String ipAddress = getClientIp(request);
                String deviceFingerprint = request.getHeader("X-Device-Fingerprint");

                // 检查是否需要记录新的登录历史（基于设备指纹）
                String lastDeviceKey = "last-device:" + username;
                String lastDevice = redisTemplate.opsForValue().get(lastDeviceKey);

                if (!deviceFingerprint.equals(lastDevice)) {
                    loginHistoryService.addLoginHistory(user.getId(), ipAddress, deviceFingerprint);
                    redisTemplate.opsForValue().set(lastDeviceKey, deviceFingerprint, TOKEN_SESSION_TIMEOUT_HOURS, TimeUnit.HOURS);
                }

                // 设置基于token的会话，与JWT过期时间匹配
                redisTemplate.opsForValue().set(tokenSessionKey, username, TOKEN_SESSION_TIMEOUT_HOURS, TimeUnit.HOURS);
            }
        } else {
            // 刷新token会话的TTL
            redisTemplate.expire(tokenSessionKey, TOKEN_SESSION_TIMEOUT_HOURS, TimeUnit.HOURS);
        }

        // 保持原有的用户会话逻辑用于活跃度跟踪
        Boolean hasUserSession = redisTemplate.hasKey(sessionKey);
        if (Boolean.FALSE.equals(hasUserSession)) {
            redisTemplate.opsForValue().set(sessionKey, "active", SESSION_TIMEOUT_MINUTES, TimeUnit.MINUTES);
        } else {
            redisTemplate.expire(sessionKey, SESSION_TIMEOUT_MINUTES, TimeUnit.MINUTES);
        }
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            if (ip.contains(",")) {
                return ip.split(",")[0];
            }
            return ip;
        }

        ip = request.getHeader("X-Real-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_CLIENT_IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        String remoteAddr = request.getRemoteAddr();
        if ("0:0:0:0:0:0:0:1".equals(remoteAddr)) {
            return "127.0.0.1";
        }

        return remoteAddr;
    }
} 