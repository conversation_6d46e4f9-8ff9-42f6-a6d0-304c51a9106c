import { User } from './User';

export interface CommunityComment {
    id: number;
    postId: number;
    authorId: number;
    parentId: number | null;
    content: string;
    createdAt: string;
    updatedAt: string;
    author: User;
    children?: CommunityComment[]; // For nesting replies
    parentAuthorUsername?: string;
    mentionedUsers?: string[];
}

export interface CommentItemProps {
    comment: CommunityComment;
    onReply: (comment: { id: number; username: string }) => void;
    children?: React.ReactNode;
    depth: number;
} 