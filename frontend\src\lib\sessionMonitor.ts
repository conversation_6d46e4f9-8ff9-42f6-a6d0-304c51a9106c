/**
 * 会话监控服务 - 监控用户会话状态，处理会话冲突
 */

import apiService from '@/services/api';
import { ssoManager } from './ssoManager';

interface SessionStatus {
  authenticated: boolean;
  username?: string;
  hasActiveSession?: boolean;
  sessionTtlMinutes?: number;
}

class SessionMonitor {
  private checkInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  private checkIntervalMs = 60000; // 1分钟检查一次
  private lastCheckTime = 0;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 3;

  constructor() {
    // 监听页面可见性变化
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          this.checkSessionImmediate();
        }
      });
    }

    // 监听SSO事件
    ssoManager.on('login', () => {
      this.startMonitoring();
    });

    ssoManager.on('logout', () => {
      this.stopMonitoring();
    });
  }

  /**
   * 开始监控会话
   */
  startMonitoring() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.consecutiveFailures = 0;
    
    // 立即检查一次
    this.checkSessionImmediate();
    
    // 设置定期检查
    this.checkInterval = setInterval(() => {
      this.checkSession();
    }, this.checkIntervalMs);
  }

  /**
   * 停止监控会话
   */
  stopMonitoring() {
    this.isMonitoring = false;
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * 立即检查会话状态
   */
  private async checkSessionImmediate() {
    // 避免频繁检查
    const now = Date.now();
    if (now - this.lastCheckTime < 5000) { // 5秒内不重复检查
      return;
    }

    await this.checkSession();
  }

  /**
   * 检查会话状态
   */
  private async checkSession() {
    if (!this.isMonitoring) {
      return;
    }

    this.lastCheckTime = Date.now();

    try {
      const token = localStorage.getItem('jwt_token');
      if (!token) {
        this.handleSessionLost('No token found');
        return;
      }

      // 检查会话状态
      const response = await apiService.get<SessionStatus>('/api/session/status');
      const sessionStatus = response.data;

      if (!sessionStatus.authenticated) {
        this.handleSessionLost('Not authenticated');
        return;
      }

      // 检查会话是否即将过期
      if (sessionStatus.sessionTtlMinutes !== undefined && sessionStatus.sessionTtlMinutes < 5) {
        // 会话即将在5分钟内过期，尝试刷新
        await this.refreshSession();
      }

      // 重置失败计数
      this.consecutiveFailures = 0;

    } catch (error: any) {
      this.consecutiveFailures++;
      
      if (error?.response?.status === 401) {
        // 认证失败，会话已失效
        this.handleSessionLost('Authentication failed');
      } else if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        // 连续失败次数过多，可能是网络问题或会话问题
        console.warn('Session check failed multiple times:', error);
        this.handleSessionLost('Multiple check failures');
      }
    }
  }

  /**
   * 刷新会话
   */
  private async refreshSession() {
    try {
      await apiService.post('/api/session/refresh');
      console.log('Session refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh session:', error);
      if (error?.response?.status === 401) {
        this.handleSessionLost('Failed to refresh session');
      }
    }
  }

  /**
   * 处理会话丢失
   */
  private handleSessionLost(reason: string) {
    console.warn('Session lost:', reason);
    
    this.stopMonitoring();
    
    // 广播会话冲突事件
    ssoManager.broadcastSessionConflict();
    
    // 清理本地状态
    localStorage.removeItem('jwt_token');
    delete apiService.defaults.headers.common['Authorization'];
    
    // 触发认证错误事件
    window.dispatchEvent(new CustomEvent('auth-error', { 
      detail: { reason } 
    }));
  }

  /**
   * 验证token有效性
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      const response = await apiService.post('/api/session/validate-token', {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      return response.data.valid === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取当前监控状态
   */
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      consecutiveFailures: this.consecutiveFailures,
      lastCheckTime: this.lastCheckTime
    };
  }
}

// 创建全局实例
export const sessionMonitor = new SessionMonitor();

// 自动启动监控（如果已登录）
if (typeof window !== 'undefined') {
  const token = localStorage.getItem('jwt_token');
  if (token) {
    sessionMonitor.startMonitoring();
  }
}
