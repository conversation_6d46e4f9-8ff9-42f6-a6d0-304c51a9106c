package com.kitolus.community.service;

import com.kitolus.community.dto.ProfileDTO;
import com.kitolus.community.dto.RegisterRequestDTO;
import com.kitolus.community.dto.UpdateProfileRequestDTO;
import com.kitolus.community.dto.ChangePasswordRequestDTO;
import com.kitolus.community.dto.ResetPasswordRequestDTO;
import com.kitolus.community.dto.UpdateWithdrawalSettingsDTO;
import com.kitolus.community.entity.User;
import com.kitolus.community.entity.DeveloperApplication;
import com.kitolus.community.entity.Product;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface UserService {
    Map<String, Object> requestRegistration(RegisterRequestDTO registerRequestDTO);

    User completeRegistration(String email, String code);

    User findByUsername(String username);

    User findById(Long userId);

    ProfileDTO getUserProfile(String username);

    ProfileDTO getUserPublicProfile(String username);

    User updateUserProfile(String username, UpdateProfileRequestDTO profileData);

    void changePassword(String username, ChangePasswordRequestDTO changePasswordRequest);

    String updateUserAvatar(String username, MultipartFile file);

    String updateUserBanner(String username, MultipartFile file);

    void deleteUser(String username);

    void applyForDeveloperRole(String username, String message);

    DeveloperApplication findLatestDeveloperApplicationByUserId(Long userId);

    void handleForgotPasswordRequest(String email);

    void resetPassword(String token, String newPassword);

    List<Product> getPurchasedProducts(Long userId);

    List<Map<String, String>> searchUsersByUsername(String query);

    void updateWithdrawalSettings(String username, UpdateWithdrawalSettingsDTO settingsDTO);
} 