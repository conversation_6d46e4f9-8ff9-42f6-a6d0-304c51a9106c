package com.kitolus.community.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class RegisterRequestDTO {
    @NotEmpty
    @Pattern(regexp = "^\\S+$", message = "用户名不能包含空格")
    private String username;

    @NotEmpty(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$", 
             message = "密码必须至少8位，且包含大写字母、小写字母和数字")
    private String password;

    @NotEmpty(message = "邮箱不能为空")
    @Email
    private String email;
} 