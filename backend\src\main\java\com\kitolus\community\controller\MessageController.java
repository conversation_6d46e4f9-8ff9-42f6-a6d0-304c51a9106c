package com.kitolus.community.controller;

import com.kitolus.community.entity.Message;
import com.kitolus.community.entity.User;
import com.kitolus.community.util.JwtTokenUtil;
import com.kitolus.community.service.MessageService;
import com.kitolus.community.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import jakarta.servlet.http.HttpServletResponse;
import com.kitolus.community.entity.Message.MessageType;

/**
 * 消息API控制器
 * 提供消息相关的REST API
 */
@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = {"https://kitolus.top", "http://localhost:3000"})
public class MessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 验证JWT token并获取用户名
     */
    private String validateTokenAndGetUsername(String authHeader) {
        try {
            String token = authHeader.replace("Bearer ", "");
            return jwtTokenUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 发送私信
     */
    @PostMapping("/private")
    public ResponseEntity<?> sendPrivateMessage(
            @RequestBody Map<String, Object> request,
            @RequestHeader("Authorization") String authHeader) {
        
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }
            User sender = userService.findByUsername(username);

            Long receiverId = Long.valueOf(request.get("receiverId").toString());
            String title = (String) request.get("title");
            String content = (String) request.get("content");

            Message message = messageService.sendPrivateMessage(sender.getId(), receiverId, title, content);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "messageId", message.getId(),
                "timestamp", LocalDateTime.now().toString()
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取用户消息列表
     */
    @GetMapping("/list")
    public ResponseEntity<?> getUserMessages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader("Authorization") String authHeader) {
        
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);

            Page<Message> messages = messageService.getUserMessages(user.getId(), page, size);

            return ResponseEntity.ok(Map.of(
                "messages", messages.getRecords(),
                "totalElements", messages.getTotal(),
                "totalPages", messages.getPages(),
                "currentPage", page,
                "hasNext", messages.hasNext()
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取未读消息
     */
    @GetMapping("/unread")
    public ResponseEntity<?> getUnreadMessages(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            
            List<Message> unreadMessages = messageService.getUnreadMessages(user.getId());
            Long unreadCount = messageService.getUnreadMessageCount(user.getId());
            
            return ResponseEntity.ok(Map.of(
                "messages", unreadMessages,
                "count", unreadCount
            ));
            
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread/count")
    public ResponseEntity<?> getUnreadMessageCount(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }

            Long count = messageService.getUnreadMessageCount(user.getId());
            return ResponseEntity.ok(Map.of("count", count != null ? count : 0));

        } catch (Exception e) {
            e.printStackTrace(); // 打印详细错误信息
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get unread count: " + e.getMessage()));
        }
    }

    /**
     * 获取私信列表
     */
    @GetMapping("/private")
    public ResponseEntity<?> getPrivateMessages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader("Authorization") String authHeader) {

        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }

            Page<Message> messages = messageService.getPrivateMessages(user.getId(), page, size);

            return ResponseEntity.ok(Map.of(
                "messages", messages != null ? messages.getRecords() : new java.util.ArrayList<>(),
                "totalElements", messages != null ? messages.getTotal() : 0,
                "totalPages", messages != null ? messages.getPages() : 0,
                "currentPage", page,
                "hasNext", messages != null ? messages.hasNext() : false
            ));

        } catch (Exception e) {
            e.printStackTrace(); // 打印详细错误信息
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get private messages: " + e.getMessage()));
        }
    }

    /**
     * 获取系统消息列表
     */
    @GetMapping("/system")
    public ResponseEntity<?> getSystemMessages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader("Authorization") String authHeader) {

        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            Page<Message> messages = messageService.getSystemMessages(user.getId(), page, size);

            return ResponseEntity.ok(Map.of(
                "messages", messages.getRecords(),
                "totalElements", messages.getTotal(),
                "totalPages", messages.getPages(),
                "currentPage", page,
                "hasNext", messages.hasNext()
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/{messageId}/read")
    public ResponseEntity<?> markMessageAsRead(
            @PathVariable Long messageId,
            @RequestHeader("Authorization") String authHeader) {

        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            boolean success = messageService.markMessageAsRead(messageId, user.getId());

            return ResponseEntity.ok(Map.of(
                "success", success,
                "messageId", messageId
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<?> deleteMessage(
            @PathVariable Long messageId,
            @RequestHeader("Authorization") String authHeader) {

        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            boolean success = messageService.deleteMessage(messageId, user.getId());

            return ResponseEntity.ok(Map.of(
                "success", success,
                "messageId", messageId
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 批量删除与特定用户的所有对话消息
     */
    @DeleteMapping("/conversation/{otherUserId}")
    public ResponseEntity<?> deleteConversation(
            @PathVariable Long otherUserId,
            @RequestHeader("Authorization") String authHeader) {

        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            int deletedCount = messageService.deleteConversation(user.getId(), otherUserId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "会话删除成功",
                "deletedCount", deletedCount
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    // SSE消息流端点已移动到MessageStreamController中，避免重复映射

    /**
     * 获取联系人列表
     */
    @GetMapping("/contacts")
    public ResponseEntity<?> getContacts(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);

            // 暂时返回空的联系人列表
            // TODO: 实现真正的联系人逻辑
            return ResponseEntity.ok(Map.of(
                "contacts", new Object[0],
                "total", 0
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 轮询消息端点
     */
    @GetMapping("/poll")
    public ResponseEntity<?> pollMessages(
            @RequestParam String token,
            @RequestParam(defaultValue = "0") Long since) {

        try {
            String username = validateTokenAndGetUsername("Bearer " + token);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);

            // 获取指定时间后的新消息
            List<Message> newMessages = messageService.getMessagesSince(user.getId(),
                since > 0 ? java.time.Instant.ofEpochMilli(since).atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
                          : java.time.LocalDateTime.now().minusMinutes(5));

            return ResponseEntity.ok(Map.of(
                "messages", newMessages,
                "timestamp", System.currentTimeMillis(),
                "hasMore", false
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 屏蔽用户
     */
    @PostMapping("/block-user/{userId}")
    public ResponseEntity<?> blockUser(
            @PathVariable Long userId,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            // TODO: 实现用户屏蔽逻辑
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户已屏蔽"
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 取消屏蔽用户
     */
    @DeleteMapping("/block-user/{userId}")
    public ResponseEntity<?> unblockUser(
            @PathVariable Long userId,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            // TODO: 实现取消屏蔽逻辑
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "已取消屏蔽用户"
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取屏蔽用户列表
     */
    @GetMapping("/blocked-users")
    public ResponseEntity<?> getBlockedUsers(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            // TODO: 实现获取屏蔽用户列表逻辑
            return ResponseEntity.ok(Map.of(
                "users", new Object[0]
            ));

        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取对话列表（按用户分组的私信）
     */
    @GetMapping("/conversations")
    public ResponseEntity<?> getConversations(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }

            List<Map<String, Object>> conversations = messageService.getConversations(user.getId());

            // 如果没有对话，返回空列表而不是错误
            if (conversations == null) {
                conversations = new ArrayList<>();
            }

            return ResponseEntity.ok(Map.of("conversations", conversations));

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get conversations: " + e.getMessage()));
        }
    }

    /**
     * 创建测试对话数据（仅用于调试）
     */
    @PostMapping("/test-conversation")
    public ResponseEntity<?> createTestConversation(@RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }

            // 简化测试：查找testuser用户，如果不存在就创建自己给自己发消息
            User testUser = userService.findByUsername("testuser");
            if (testUser == null) {
                // 如果没有testuser，就创建一个自己给自己的消息用于测试
                messageService.sendPrivateMessage(
                    user.getId(),
                    user.getId(),
                    "测试消息",
                    "这是一条测试消息，用于验证对话列表功能。发送者和接收者都是自己。"
                );

                return ResponseEntity.ok(Map.of(
                    "message", "测试对话已创建（自己给自己）",
                    "from", user.getUsername(),
                    "to", user.getUsername()
                ));
            } else {
                // 创建测试消息
                messageService.sendPrivateMessage(
                    testUser.getId(),
                    user.getId(),
                    "测试消息",
                    "这是一条测试消息，用于验证对话列表功能。"
                );

                return ResponseEntity.ok(Map.of(
                    "message", "测试对话已创建",
                    "from", testUser.getUsername(),
                    "to", user.getUsername()
                ));
            }

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of("error", "Failed to create test conversation: " + e.getMessage()));
        }
    }

    /**
     * 获取与特定用户的对话消息
     */
    @GetMapping("/conversation/{otherUserId}")
    public ResponseEntity<?> getConversationMessages(
            @PathVariable Long otherUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String username = validateTokenAndGetUsername(authHeader);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "Invalid token"));
            }

            User user = userService.findByUsername(username);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }

            Page<Message> messages = messageService.getConversationMessages(user.getId(), otherUserId, page, size);

            return ResponseEntity.ok(Map.of(
                "messages", messages != null ? messages.getRecords() : new java.util.ArrayList<>(),
                "totalElements", messages != null ? messages.getTotal() : 0,
                "totalPages", messages != null ? messages.getPages() : 0,
                "currentPage", page,
                "hasNext", messages != null ? messages.hasNext() : false
            ));

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of("error", "Failed to get conversation messages: " + e.getMessage()));
        }
    }

}
