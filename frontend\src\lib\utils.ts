import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import multiavatar from '@multiavatar/multiavatar/esm'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Resolves the correct avatar source. It can be a URL to an uploaded image
 * or raw SVG code generated on the client-side.
 * 1. Custom uploaded avatar (if avatarUrl is a path).
 * 2. Generated Multiavatar SVG code (if avatarUrl is a seed/serial number).
 * @param avatarUrl The avatar data from the user object.
 * @param serialNumber The user's serial number, used as a seed for Multiavatar.
 * @returns A fully qualified image URL string, or raw SVG code string.
 */
export function resolveAvatarUrl(avatarUrl: string | null | undefined, serialNumber: string | null | undefined): string {
  // Goal: return a root-relative path like "/storage/avatars/..." or a multiavatar svg string.
  if (avatarUrl) {
    // Case 1: The URL is already in the correct, modern format (e.g., /storage/avatars/...).
    if (avatarUrl.startsWith('/storage/')) {
      return avatarUrl;
    }
    
    // Case 2: The URL is from an older part of the code, missing the leading slash (e.g., storage/avatars/...).
    if (avatarUrl.startsWith('storage/')) {
      return `/${avatarUrl}`;
    }

    // Case 3: The URL is from the oldest format, just the sub-path (e.g., "avatars/...").
    const isUploadedAvatar = /\.(webp|png|jpg|jpeg|gif)$/i.test(avatarUrl);
    if (isUploadedAvatar) {
      // It's a path like "avatars/..." or "banners/...". We need to prepend "/storage/".
      return `/storage/${avatarUrl}`;
    }
  }

  // Fallback case: No valid avatarUrl, generate a multiavatar.
  const seed = serialNumber || 'default-fallback-seed';
  return multiavatar(seed);
} 