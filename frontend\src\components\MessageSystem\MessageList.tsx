'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  MoreVertical,
  Trash2,
  Reply,
  Forward,
  Flag,
  Circle,
  CheckCircle2,
  Bell,
  Mail,
  AlertTriangle,
  Info
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAuth } from '@/contexts/AuthContext';
import { Message, MessageType, MessageStatus, MessagePriority } from '@/types/Message';
import { getMessages, markMessageAsRead, deleteMessage } from '@/services/messageApi';
import { cn } from '@/lib/utils';

interface MessageListProps {
  type?: MessageType;
  searchQuery: string;
}

export const MessageList: React.FC<MessageListProps> = ({
  type,
  searchQuery
}) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // 获取消息列表
  const fetchMessages = useCallback(async (pageNum = 1, reset = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);

      const response = await getMessages({
        type,
        page: pageNum,
        limit: 20,
        search: searchQuery || undefined
      });
      
      if (reset || pageNum === 1) {
        setMessages(response.messages);
      } else {
        setMessages(prev => [...prev, ...response.messages]);
      }
      
      setHasMore(response.hasMore);
      setPage(pageNum);
    } catch (error) {
      // Handle error silently
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [type, searchQuery]);

  useEffect(() => {
    fetchMessages(1, true);
  }, [fetchMessages]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchMessages(page + 1);
    }
  }, [fetchMessages, page, loadingMore, hasMore]);

  // 标记为已读
  const handleMarkAsRead = async (messageId: number) => {
    try {
      await markMessageAsRead(messageId);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: MessageStatus.READ, readAt: new Date().toISOString() }
          : msg
      ));
    } catch (error) {
      // Handle error silently
    }
  };

  // 删除消息
  const handleDeleteMessage = async (messageId: number) => {
    try {
      await deleteMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (error) {
      // Handle error silently
    }
  };

  // 获取消息类型图标
  const getMessageTypeIcon = (messageType: MessageType, priority: MessagePriority) => {
    const iconClass = "w-4 h-4";
    
    switch (messageType) {
      case MessageType.PRIVATE:
        return <Mail className={cn(iconClass, "text-blue-500")} />;
      case MessageType.SYSTEM:
        return <Info className={cn(iconClass, "text-gray-500")} />;
      case MessageType.ANNOUNCEMENT:
        return <Bell className={cn(iconClass, "text-yellow-500")} />;
      case MessageType.COMMUNITY_REPLY:
        return <Reply className={cn(iconClass, "text-green-500")} />;
      case MessageType.COMMUNITY_LIKE:
        return <CheckCircle2 className={cn(iconClass, "text-pink-500")} />;
      case MessageType.COMMUNITY_FOLLOW:
        return <Circle className={cn(iconClass, "text-purple-500")} />;
      default:
        return <Mail className={cn(iconClass, "text-gray-500")} />;
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: MessagePriority) => {
    switch (priority) {
      case MessagePriority.URGENT:
        return "border-l-red-500";
      case MessagePriority.HIGH:
        return "border-l-orange-500";
      case MessagePriority.NORMAL:
        return "border-l-blue-500";
      case MessagePriority.LOW:
        return "border-l-gray-500";
      default:
        return "border-l-gray-500";
    }
  };

  if (loading) {
    return (
      <div className="p-4 space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-start space-x-3">
            <Skeleton className="w-8 h-8 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <ScrollArea className="h-full">
      <div className="p-2">
        {messages.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Mail className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">
              {searchQuery ? '没有找到匹配的消息' : '暂无消息'}
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  "group relative p-3 rounded-lg border-l-2 transition-colors cursor-pointer",
                  getPriorityColor(message.priority),
                  message.status === MessageStatus.UNREAD 
                    ? "bg-primary/5 hover:bg-primary/10" 
                    : "bg-muted/30 hover:bg-muted/50"
                )}
                onClick={() => {
                  if (message.status === MessageStatus.UNREAD) {
                    handleMarkAsRead(message.id);
                  }
                }}
              >
                {/* 消息头部 */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getMessageTypeIcon(message.type, message.priority)}
                    <h4 className="font-medium text-sm truncate">
                      {message.title}
                    </h4>
                    {message.status === MessageStatus.UNREAD && (
                      <Circle className="w-2 h-2 fill-primary text-primary" />
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(
                        new Date(message.createdAt),
                        { addSuffix: true, locale: zhCN }
                      )}
                    </span>
                    
                    {/* 操作菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {message.status === MessageStatus.READ && (
                          <DropdownMenuItem>
                            <Mail className="w-4 h-4 mr-2" />
                            标记为未读
                          </DropdownMenuItem>
                        )}
                        {message.type === MessageType.PRIVATE && (
                          <>
                            <DropdownMenuItem>
                              <Reply className="w-4 h-4 mr-2" />
                              回复
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Forward className="w-4 h-4 mr-2" />
                              转发
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuItem>
                          <Flag className="w-4 h-4 mr-2" />
                          举报
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteMessage(message.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* 发送者信息 */}
                {message.sender && (
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={message.sender.fullAvatarUrl || undefined} />
                      <AvatarFallback className="text-xs">
                        {message.sender.username?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-muted-foreground">
                      {message.sender.username}
                    </span>
                  </div>
                )}

                {/* 消息内容 */}
                <div className="text-sm text-foreground/90 line-clamp-3">
                  {message.content}
                </div>

                {/* 优先级标识 */}
                {message.priority !== MessagePriority.NORMAL && (
                  <Badge 
                    variant={message.priority === MessagePriority.URGENT ? "destructive" : "secondary"}
                    className="absolute top-2 right-2 text-xs"
                  >
                    {message.priority === MessagePriority.URGENT && '紧急'}
                    {message.priority === MessagePriority.HIGH && '重要'}
                    {message.priority === MessagePriority.LOW && '低优先级'}
                  </Badge>
                )}
              </motion.div>
            ))}

            {/* 加载更多 */}
            {hasMore && (
              <div className="p-4 text-center">
                <Button
                  variant="ghost"
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="text-sm"
                >
                  {loadingMore ? '加载中...' : '加载更多'}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </ScrollArea>
  );
};
