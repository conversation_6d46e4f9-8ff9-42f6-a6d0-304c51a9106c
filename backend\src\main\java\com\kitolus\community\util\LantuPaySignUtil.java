package com.kitolus.community.util;

import org.apache.commons.codec.digest.DigestUtils;
import java.util.Map;
import java.util.TreeMap;

public class LantuPaySignUtil {

    /**
     * Generates a signature for LantuPay API requests.
     *
     * @param params     The request parameters (excluding 'sign').
     * @param partnerKey The secret key provided by LantuPay.
     * @return The uppercase MD5 signature string.
     */
    public static String createSign(Map<String, String> params, String partnerKey) {
        // Remove sign if it exists, just in case
        params.remove("sign");

        // Use TreeMap to sort parameters by key
        Map<String, String> sortedParams = new TreeMap<>(params);

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> param : sortedParams.entrySet()) {
            String key = param.getKey();
            String value = param.getValue();
            // Ignore blank values
            if (value != null && !value.trim().isEmpty()) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(key).append("=").append(value);
            }
        }

        sb.append("&key=").append(partner<PERSON>ey);

        return DigestUtils.md5Hex(sb.toString()).toUpperCase();
    }
} 