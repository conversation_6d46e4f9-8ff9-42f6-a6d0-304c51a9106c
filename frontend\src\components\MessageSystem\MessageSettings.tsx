'use client';

import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  Mail, 
  Shield, 
  Trash2, 
  Download,
  Upload,
  Settings,
  Users,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAuth } from '@/contexts/AuthContext';
import { getBlockedUsers, unblockUser, markAllMessagesAsRead } from '@/services/messageApi';
import { toast } from 'sonner';

interface MessageSettingsProps {}

export const MessageSettings: React.FC<MessageSettingsProps> = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState({
    enableNotifications: true,
    enablePrivateMessages: true,
    enableSystemNotifications: true,
    enableCommunityNotifications: true,
    enableEmailNotifications: false,
    autoMarkAsRead: false,
    showOnlineStatus: true,
    allowMessagesFromStrangers: true
  });
  const [blockedUsers, setBlockedUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取屏蔽用户列表
  const fetchBlockedUsers = async () => {
    try {
      const response = await getBlockedUsers();
      setBlockedUsers(response.users);
    } catch (error) {
      // Handle error silently
    }
  };

  useEffect(() => {
    fetchBlockedUsers();
  }, []);

  // 更新设置
  const updateSetting = (key: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    // TODO: 保存到后端
    toast.success('设置已更新');
  };

  // 取消屏蔽用户
  const handleUnblockUser = async (userId: number) => {
    try {
      await unblockUser(userId);
      setBlockedUsers(prev => prev.filter(user => user.id !== userId));
      toast.success('已取消屏蔽该用户');
    } catch (error) {
      toast.error('操作失败，请稍后重试');
    }
  };

  // 标记所有消息为已读
  const handleMarkAllAsRead = async () => {
    setLoading(true);
    try {
      await markAllMessagesAsRead();
      toast.success('所有消息已标记为已读');
    } catch (error) {
      toast.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 清空所有消息
  const handleClearAllMessages = async () => {
    setLoading(true);
    try {
      // TODO: 实现清空所有消息的API
      toast.success('所有消息已清空');
    } catch (error) {
      toast.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 导出消息数据
  const handleExportMessages = async () => {
    try {
      // TODO: 实现导出消息数据的功能
      toast.success('消息数据导出成功');
    } catch (error) {
      toast.error('导出失败，请稍后重试');
    }
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          消息设置
        </DialogTitle>
        <DialogDescription>
          管理你的消息通知、隐私设置和屏蔽列表
        </DialogDescription>
      </DialogHeader>

      <ScrollArea className="h-full">
        <div className="p-4 space-y-6">
          {/* 通知设置 */}
          <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">通知设置</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-notifications">启用消息通知</Label>
              <Switch
                id="enable-notifications"
                checked={settings.enableNotifications}
                onCheckedChange={(checked) => updateSetting('enableNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-private">私信通知</Label>
              <Switch
                id="enable-private"
                checked={settings.enablePrivateMessages}
                onCheckedChange={(checked) => updateSetting('enablePrivateMessages', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-system">系统通知</Label>
              <Switch
                id="enable-system"
                checked={settings.enableSystemNotifications}
                onCheckedChange={(checked) => updateSetting('enableSystemNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-community">社区互动通知</Label>
              <Switch
                id="enable-community"
                checked={settings.enableCommunityNotifications}
                onCheckedChange={(checked) => updateSetting('enableCommunityNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-email">邮件通知</Label>
              <Switch
                id="enable-email"
                checked={settings.enableEmailNotifications}
                onCheckedChange={(checked) => updateSetting('enableEmailNotifications', checked)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* 隐私设置 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">隐私设置</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="show-online">显示在线状态</Label>
              <Switch
                id="show-online"
                checked={settings.showOnlineStatus}
                onCheckedChange={(checked) => updateSetting('showOnlineStatus', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="allow-strangers">允许陌生人发送消息</Label>
              <Switch
                id="allow-strangers"
                checked={settings.allowMessagesFromStrangers}
                onCheckedChange={(checked) => updateSetting('allowMessagesFromStrangers', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="auto-read">自动标记为已读</Label>
              <Switch
                id="auto-read"
                checked={settings.autoMarkAsRead}
                onCheckedChange={(checked) => updateSetting('autoMarkAsRead', checked)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* 屏蔽用户管理 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">屏蔽用户管理</h3>
            {blockedUsers.length > 0 && (
              <Badge variant="secondary">{blockedUsers.length}</Badge>
            )}
          </div>
          
          {blockedUsers.length === 0 ? (
            <p className="text-sm text-muted-foreground">暂无屏蔽用户</p>
          ) : (
            <div className="space-y-2">
              {blockedUsers.map((blockedUser) => (
                <div key={blockedUser.id} className="flex items-center justify-between p-2 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={blockedUser.fullAvatarUrl || undefined} />
                      <AvatarFallback className="text-xs">
                        {blockedUser.username?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{blockedUser.username}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUnblockUser(blockedUser.id)}
                  >
                    取消屏蔽
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        <Separator />

        {/* 消息管理 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">消息管理</h3>
          </div>
          
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleMarkAllAsRead}
              disabled={loading}
            >
              <Mail className="w-4 h-4 mr-2" />
              标记所有消息为已读
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleExportMessages}
            >
              <Download className="w-4 h-4 mr-2" />
              导出消息数据
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  清空所有消息
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认清空所有消息？</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作将永久删除您的所有消息记录，包括私信、通知等。此操作无法撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleClearAllMessages}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    确认清空
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <Separator />

        {/* 帮助信息 */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">使用说明</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• 私信：与其他用户进行一对一交流</p>
            <p>• 系统通知：接收系统更新和维护信息</p>
            <p>• 社区通知：接收帖子回复、点赞等互动通知</p>
            <p>• 屏蔽用户：被屏蔽的用户无法向您发送消息</p>
          </div>
        </div>
      </div>
    </ScrollArea>
    </>
  );
};
