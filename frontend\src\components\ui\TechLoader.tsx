import React, { useState, useEffect } from 'react';

const loadingTips = [
  "蒸汽先行！蒸汽机比风力靠谱",
  "粉碎矿石：双倍矿粉的秘密",
  "青铜时代：你的第一台机器雏形",
  "电路板：绝缘线缆保平安",
  "高压电=爆炸！注意电压等级",
  "焦炉优先：木炭才是早期能源",
  "蒸汽锅炉：安全阀必须安装",
  "锤子妙用：矿物处理三件套",
  "洗矿厂！稀有副产等你发现",
  "石油：地下钻机开动前需勘探",
  "合成蓝钢？坩埚温度是关键",
  "超频加速？先升级机器外壳",
  "EU能源网络：分层变压",
  "核电风险：铅板防辐射",
  "工业高炉：耐火砖不能省",
  "离心机：分离稀土必备",
  "纳米剑：耗电如流水",
  "AE2自动化的终点：分子装配",
  "物流管道：别忘染色标记",
  "钛合金：电解机最后一步",
  "电路组装机：硅板需净化",
  "真空冷冻机：液氮制备中…",
  "Gregtech配方：查NEI更高效",
  "量子套：百万伏特护体",
  "空间塔挑战：戴森球起步",
  "第一夜：燧石工具救命稻草",
  "饥饿值？试试营养学模组",
  "防怪墙：3格高+火把环绕",
  "甘蔗田：早期纸张来源",
  "挖矿秘诀：鱼骨式矿道",
  "橡胶树：带斑点的树干",
  "锡罐保鲜：食物不腐烂",
  "粘性树脂：树液提取器必备",
  "无限水：两桶造井小技巧",
  "下界探险：先备铂金盔甲",
  "萤石粉：照明兼魔法材料",
  "刷怪塔：水流+掉落伤害",
  "毒抗药水：抗凋灵骷髅",
  "床很重要！跳过血月夜",
  "青金石矿：兼作染料与电路",
  "洞穴探险：带足火把与梯",
  "跌落保护？羊毛变降落伞",
  "战利品袋：优先开稀有级",
  "村民交易：绿宝石换钻石",
  "防岩浆：随身水桶妙用",
  "飞行背包：先充能后起飞",
  "血月降临！躲进地下掩体",
  "抗火药水：下界熔岩海必备",
  "养蜜蜂：蜂箱增产指南",
  "附魔台：书架摆五面",
  "神秘时代：从魔导手册开始",
  "注魔祭坛：节点请勿枯竭",
  "血魔法：献祭刀别误伤自己",
  "法杖核心：火球速清怪群",
  "奥术工作台：要素别放反",
  "傀儡帮手：自动化农场首选",
  "精灵门：符文矩阵精确定位",
  "腐化之地！速用纯净盐",
  "龙之研究：孵化器控温指南",
  "源质：用罐子小心搬运",
  "邪术：召唤恶魔前存档",
  "符文盾：抵挡格雷科技爆炸",
  "植物魔法！火花连锁传输",
  "灵宝工具：自动修复符文",
  "咒波同步：护目镜勿忘戴",
  "进度卡关？查任务手册！",
  "物流优化：管道分类过滤",
  "矿物处理：先洗后离心",
  "基地选址：平坦+近水源",
  "废物利用：垃圾处理机启动",
  "连锁挖矿：镐子附魔首选",
  "铁路系统：运输超重货物",
  "自动化：从熔炉阵列起步",
  "保护罩：能源护盾防爆墙",
  "堆肥桶：早期肥料来源",
  "经验转存：附魔书储藏法",
  "背包升级：合成扩容组件",
  "坐标记录：F3截图备忘",
  "雨林探险：带抗毒药水",
  "矿脉定位：扫描仪的声波",
  "效率公式：EU/MJ换算表",
  "多方块结构：先预览后造",
  "虚空世界：防跌落必穿靴",
  "工具损毁：维修台急救",
  "跨维度：虫洞生成指南",
  "终极目标：星门通往银河",
  "成就党：全任务树达成",
  "建筑党：微区块加载器",
  "GTNH信条：耐心是美德",
  "旅程永续：更新日志必读",
  "格雷科技，小子！它会在高压下硬化。",
  "正在为主电路通电... 希望这次别炸。",
  "你的肝还好吗？",
  "警告：检测到微小的电压不匹配。准备好收拾残局了吗？",
  "又是一个不眠之夜，只为那闪亮的钛。",
  "今天你的基地爆炸了吗？",
  "正在编译NEI配方... 请准备好咖啡。",
  "AE2网络过载？试试更多的P2P通道。",
  "一个机器方块？太天真了，你需要一个3x3x4的多方块结构。",
  "欢迎来到格雷乌斯的地狱厨房。",
  "四连并行处理矿物？不，你需要三十二连！"
];

const TechLoader = () => {
  const [currentTipIndex, setCurrentTipIndex] = useState(0);
  const [fade, setFade] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  useEffect(() => {
    if (hasMounted) {
      // Select a random tip on initial client-side render
      setCurrentTipIndex(Math.floor(Math.random() * loadingTips.length));
      setFade(true); // Fade in the first tip

      const interval = setInterval(() => {
        setFade(false);
        setTimeout(() => {
          setCurrentTipIndex(prevIndex => {
            let nextIndex;
            do {
              nextIndex = Math.floor(Math.random() * loadingTips.length);
            } while (nextIndex === prevIndex);
            return nextIndex;
          });
          setFade(true);
        }, 500); // Corresponds to the fade-out duration
      }, 4000); // Time each tip is displayed

      return () => clearInterval(interval);
    }
  }, [hasMounted]);

  return (
    <div className="flex flex-col items-center justify-center space-y-6">
      <h1 className="text-2xl font-bold text-primary/90 animate-pulse">
        正在加载 GREGTECH NEW HORIZONS...
      </h1>
      <div className="flex items-end justify-center h-12 space-x-1.5">
        <div
          className="w-3 h-8 bg-primary/90 animate-wave"
          style={{ animationDelay: '-0.4s' }}
        ></div>
        <div
          className="w-3 h-12 bg-primary/90 animate-wave"
          style={{ animationDelay: '-0.2s' }}
        ></div>
        <div className="w-3 h-12 bg-primary/90 animate-wave"></div>
        <div
          className="w-3 h-8 bg-primary/90 animate-wave"
          style={{ animationDelay: '0.2s' }}
        ></div>
        <div
          className="w-3 h-6 bg-primary/90 animate-wave"
          style={{ animationDelay: '0.4s' }}
        ></div>
      </div>
      <p
        className={`text-lg text-center text-primary/80 transition-opacity duration-500 min-h-[28px] ${
          fade ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {loadingTips[currentTipIndex]}
      </p>
    </div>
  );
};

export default TechLoader;
