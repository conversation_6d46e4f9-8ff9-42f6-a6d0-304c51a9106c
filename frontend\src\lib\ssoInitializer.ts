/**
 * SSO初始化器 - 确保SSO管理器在客户端正确初始化
 */

import { ssoManager } from './ssoManager';

let isInitialized = false;

export function initializeSSO() {
  if (typeof window === 'undefined') {
    console.log('🔧 服务端环境，跳过SSO初始化');
    return false;
  }

  if (isInitialized) {
    console.log('🔧 SSO已初始化，跳过重复初始化');
    return true;
  }

  console.log('🔧 强制初始化SSO管理器...');

  try {
    // 检查ssoManager是否正确初始化
    const manager = ssoManager as any;
    
    console.log('🔍 SSO管理器状态检查:', {
      hasManager: !!manager,
      hasChannel: !!manager?.channel,
      hasListeners: !!manager?.listeners,
      currentToken: manager?.currentToken?.substring(0, 20) + '...' || 'null'
    });

    // 如果没有BroadcastChannel，手动初始化
    if (!manager?.channel && 'BroadcastChannel' in window) {
      console.log('🔧 手动初始化BroadcastChannel...');
      manager.channel = new BroadcastChannel('sso-channel');
      manager.channel.onmessage = manager.handleMessage?.bind(manager);
      console.log('✅ BroadcastChannel手动初始化成功');
    }

    // 如果没有事件监听器，手动初始化
    if (!manager?.listeners) {
      console.log('🔧 手动初始化事件监听器...');
      manager.listeners = new Map();
      console.log('✅ 事件监听器手动初始化成功');
    }

    // 如果没有localStorage监听器，手动添加
    if (typeof window !== 'undefined') {
      console.log('🔧 确保localStorage事件监听器...');
      // 移除可能存在的旧监听器
      window.removeEventListener('storage', manager.handleStorageChange);
      // 添加新监听器
      window.addEventListener('storage', manager.handleStorageChange?.bind(manager));
      console.log('✅ localStorage事件监听器已确保');
    }

    // 测试SSO功能
    console.log('🧪 测试SSO功能...');
    const testEvent = {
      type: 'test',
      timestamp: Date.now()
    };

    if (manager?.channel) {
      try {
        manager.channel.postMessage(testEvent);
        console.log('✅ BroadcastChannel测试成功');
      } catch (error) {
        console.error('❌ BroadcastChannel测试失败:', error);
      }
    }

    // 测试localStorage事件
    try {
      localStorage.setItem('sso-test', JSON.stringify(testEvent));
      localStorage.removeItem('sso-test');
      console.log('✅ localStorage事件测试成功');
    } catch (error) {
      console.error('❌ localStorage事件测试失败:', error);
    }

    isInitialized = true;
    console.log('✅ SSO管理器强制初始化完成');
    return true;

  } catch (error) {
    console.error('❌ SSO管理器强制初始化失败:', error);
    return false;
  }
}

// 自动初始化（仅在客户端）
if (typeof window !== 'undefined') {
  // 延迟初始化，确保所有模块都已加载
  setTimeout(() => {
    initializeSSO();
  }, 100);
}

export { ssoManager };
