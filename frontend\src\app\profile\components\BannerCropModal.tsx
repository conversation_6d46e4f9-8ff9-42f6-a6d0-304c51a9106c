import { useState, useRef } from 'react';
import ReactCrop, { type Crop, centerCrop, makeAspectCrop } from 'react-image-crop';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import 'react-image-crop/dist/ReactCrop.css';
import { Loader2 } from 'lucide-react';

interface BannerCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  image: string;
  onSave: (croppedImage: Blob) => void;
  isSaving?: boolean;
}

// Function to generate a centered crop with a specific aspect ratio
function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight
    ),
    mediaWidth,
    mediaHeight
  );
}

export const BannerCropModal = ({ isOpen, onClose, image, onSave, isSaving }: BannerCropModalProps) => {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<Crop>();
  const imgRef = useRef<HTMLImageElement>(null);

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const aspect = 3 / 1; // 3:1 aspect ratio for banners
    setCrop(centerAspectCrop(width, height, aspect));
  };

  const handleSave = () => {
    if (completedCrop && imgRef.current) {
        const canvas = document.createElement('canvas');
        const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
        const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
        const pixelRatio = window.devicePixelRatio;

        canvas.width = Math.floor(completedCrop.width * scaleX * pixelRatio);
        canvas.height = Math.floor(completedCrop.height * scaleY * pixelRatio);

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            throw new Error('No 2d context');
        }

        ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);
        ctx.imageSmoothingQuality = 'high';

        ctx.drawImage(
            imgRef.current,
            completedCrop.x * scaleX,
            completedCrop.y * scaleY,
            completedCrop.width * scaleX,
            completedCrop.height * scaleY,
            0,
            0,
            completedCrop.width * scaleX,
            completedCrop.height * scaleY
        );

        canvas.toBlob((blob) => {
            if (blob) {
                onSave(blob);
                onClose();
            }
        }, 'image/png');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-black/50 backdrop-blur-md border-white/20 text-white sm:max-w-[80vw]">
        <DialogHeader>
          <DialogTitle>裁剪您的横幅</DialogTitle>
        </DialogHeader>
        <div className="flex justify-center items-center my-4">
            {image && (
                 <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={3 / 1}
                    minHeight={100}
                  >
                    <img
                        ref={imgRef}
                        src={image}
                        onLoad={onImageLoad}
                        alt="Banner to crop"
                        style={{ maxHeight: '70vh' }}
                    />
                </ReactCrop>
            )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} className="bg-transparent hover:bg-white/10 border-white/20" disabled={isSaving}>取消</Button>
          <Button onClick={handleSave} className="bg-primary hover:bg-primary/90" disabled={isSaving}>
            {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isSaving ? '正在处理...' : '保存并上传'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 