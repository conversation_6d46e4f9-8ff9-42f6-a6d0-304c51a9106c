/**
 * 现代化消息状态管理 - 使用Zustand
 * 优势：更轻量、类型安全、无样板代码
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Message, Conversation, MessageStats, MessageType, MessageStatus } from '@/types/Message';
import { smartConnectionManager, ConnectionStatus, ConnectionType } from '@/lib/smartConnectionManager';

interface MessageState {
  // 状态
  isOpen: boolean;
  activeTab: string;
  selectedConversationId: string | null;
  unreadStats: MessageStats | null;
  
  // 实时连接状态
  connectionStatus: ConnectionStatus;
  connectionType: ConnectionType;
  lastSeen: Record<number, string>; // 用户最后在线时间
  typingUsers: Record<string, number[]>; // 正在输入的用户
  
  // 优化的消息缓存
  messageCache: Map<string, Message[]>;
  conversationCache: Map<string, Conversation>;
  
  // Actions
  setOpen: (open: boolean) => void;
  setActiveTab: (tab: string) => void;
  setSelectedConversation: (id: string | null) => void;
  updateUnreadStats: (stats: MessageStats) => void;
  startNewConversation: (userId: number) => void;
  
  // 实时更新
  addMessage: (message: Message) => void;
  updateMessage: (messageId: number, updates: Partial<Message>) => void;
  removeMessage: (messageId: number) => void;
  
  // 会话管理
  updateConversation: (conversation: Conversation) => void;
  markConversationAsRead: (conversationId: string) => void;
  
  // 实时状态
  setConnectionStatus: (status: ConnectionStatus) => void;
  setConnectionType: (type: ConnectionType) => void;
  updateUserLastSeen: (userId: number, timestamp: string) => void;
  setTypingUsers: (conversationId: string, userIds: number[]) => void;

  // 连接管理
  initializeConnection: (token: string, userId?: number) => Promise<void>;
  reconnectConnection: () => Promise<void>;
  disconnectConnection: () => void;
  
  // 缓存管理
  getCachedMessages: (conversationId: string) => Message[] | undefined;
  setCachedMessages: (conversationId: string, messages: Message[]) => void;
  clearCache: () => void;
}

export const useMessageStore = create<MessageState>()(
  subscribeWithSelector(
    immer(
      persist(
        (set, get) => ({
          // 初始状态
          isOpen: false,
          activeTab: 'conversations',
          selectedConversationId: null,
          unreadStats: null,
          connectionStatus: 'disconnected',
          connectionType: 'mock',
          lastSeen: {},
          typingUsers: {},
          messageCache: new Map(),
          conversationCache: new Map(),

          // Actions
          setOpen: (open) => set({ isOpen: open }),
          
          setActiveTab: (tab) => set({ activeTab: tab }),
          
          setSelectedConversation: (id) => set({ selectedConversationId: id }),

          updateUnreadStats: (stats) => set({ unreadStats: stats }),

          startNewConversation: (userId) => set((state) => {
            const conversationId = `conv_${userId}`;
            state.selectedConversationId = conversationId;
            state.isOpen = true;
            state.activeTab = 'conversations';
          }),

          // 实时消息更新
          addMessage: (message) => set((state) => {
            const conversationId = (message as any).conversationId || 'system';
            const cached = state.messageCache.get(conversationId) || [];
            
            // 避免重复消息
            if (!cached.find(m => m.id === message.id)) {
              cached.unshift(message);
              state.messageCache.set(conversationId, cached);
            }
            
            // 更新未读统计
            if (state.unreadStats) {
              state.unreadStats.totalUnread += 1;
              switch (message.type) {
                case MessageType.PRIVATE:
                  state.unreadStats.privateUnread += 1;
                  break;
                case MessageType.SYSTEM:
                  state.unreadStats.systemUnread += 1;
                  break;
                // ... 其他类型
              }
            }
          }),

          updateMessage: (messageId, updates) => set((state) => {
            state.messageCache.forEach((messages, conversationId) => {
              const index = messages.findIndex(m => m.id === messageId);
              if (index !== -1) {
                // 创建新的消息对象而不是直接修改
                messages[index] = { ...messages[index], ...updates };
              }
            });
          }),

          removeMessage: (messageId) => set((state) => {
            state.messageCache.forEach((messages, conversationId) => {
              const index = messages.findIndex(m => m.id === messageId);
              if (index !== -1) {
                messages.splice(index, 1);
              }
            });
          }),

          // 会话管理
          updateConversation: (conversation) => set((state) => {
            state.conversationCache.set(conversation.id, conversation);
          }),

          markConversationAsRead: (conversationId) => set((state) => {
            const conversation = state.conversationCache.get(conversationId);
            if (conversation) {
              const unreadCount = conversation.unreadCount || 0;

              // 创建新的conversation对象而不是直接修改
              const updatedConversation = {
                ...conversation,
                unreadCount: 0
              };

              // 更新缓存中的conversation
              state.conversationCache.set(conversationId, updatedConversation);

              // 更新总体统计
              if (state.unreadStats && unreadCount > 0) {
                state.unreadStats.totalUnread = Math.max(0, state.unreadStats.totalUnread - unreadCount);
                state.unreadStats.privateUnread = Math.max(0, state.unreadStats.privateUnread - unreadCount);
              }
            }
          }),

          // 实时状态管理
          setConnectionStatus: (status) => set({ connectionStatus: status }),

          setConnectionType: (type) => set({ connectionType: type }),

          updateUserLastSeen: (userId, timestamp) => set((state) => {
            state.lastSeen[userId] = timestamp;
          }),

          setTypingUsers: (conversationId, userIds) => set((state) => {
            state.typingUsers[conversationId] = userIds;
          }),

          // 连接管理
          initializeConnection: async (token, userId) => {
            try {
              // 清除之前的监听器，避免重复
              smartConnectionManager.removeAllListeners('statusChanged');

              // 设置事件监听器
              smartConnectionManager.on('statusChanged', (status) => {
                console.log('MessageStore: Status changed to:', status, smartConnectionManager.connectionType);
                set({
                  connectionStatus: status,
                  connectionType: smartConnectionManager.connectionType
                });
              });

              await smartConnectionManager.connect(token, userId);

              // 立即更新连接状态
              set({
                connectionStatus: smartConnectionManager.connectionStatus,
                connectionType: smartConnectionManager.connectionType
              });

            } catch (error) {
              console.error('Failed to initialize connection:', error);
              set({ connectionStatus: 'error' });
            }
          },

          reconnectConnection: async () => {
            try {
              await smartConnectionManager.reconnect();
            } catch (error) {
              console.error('Failed to reconnect:', error);
            }
          },

          disconnectConnection: () => {
            smartConnectionManager.disconnect();
          },

          // 缓存管理
          getCachedMessages: (conversationId) => {
            return get().messageCache.get(conversationId);
          },
          
          setCachedMessages: (conversationId, messages) => set((state) => {
            state.messageCache.set(conversationId, messages);
          }),
          
          clearCache: () => set((state) => {
            state.messageCache.clear();
            state.conversationCache.clear();
          }),
        }),
        {
          name: 'message-store',
          storage: createJSONStorage(() => localStorage),
          partialize: (state) => ({
            // 只持久化必要的状态
            lastSeen: state.lastSeen,
            activeTab: state.activeTab,
          }),
        }
      )
    )
  )
);

// 初始化连接状态监听器
smartConnectionManager.on('statusChanged', (status: ConnectionStatus) => {
  useMessageStore.getState().setConnectionStatus(status);
});

smartConnectionManager.on('connected', () => {
  const info = smartConnectionManager.getConnectionInfo();
  useMessageStore.getState().setConnectionType(info.type);
});

smartConnectionManager.on('message', (message: any) => {
  const store = useMessageStore.getState();

  switch (message.type) {
    case 'NEW_MESSAGE':
      store.addMessage(message.data);
      break;
    case 'MESSAGE_READ':
      if (message.data.messageId) {
        store.updateMessage(message.data.messageId, { status: MessageStatus.READ });
      }
      break;
    case 'USER_ONLINE_STATUS':
      if (message.data.userId && message.data.timestamp) {
        store.updateUserLastSeen(message.data.userId, message.data.timestamp);
      }
      break;
    case 'TYPING_STATUS':
      if (message.data.conversationId && message.data.userIds) {
        store.setTypingUsers(message.data.conversationId, message.data.userIds);
      }
      break;
  }
});

// 选择器 - 优化性能，避免不必要的重渲染
export const useUnreadCount = () => useMessageStore(state => state.unreadStats?.totalUnread || 0);
export const useConnectionStatus = () => useMessageStore(state => state.connectionStatus);
export const useConnectionType = () => useMessageStore(state => state.connectionType);
export const useTypingUsers = (conversationId: string) =>
  useMessageStore(state => state.typingUsers[conversationId] || []);

// 获取连接状态的文本描述
export const useConnectionStatusText = () => {
  const status = useConnectionStatus();
  const type = useConnectionType();

  const statusMap = {
    'connecting': '连接中',
    'connected': '已连接',
    'disconnected': '未连接',
    'error': '连接错误'
  };

  const typeMap = {
    'websocket': 'WebSocket',
    'sse': 'SSE',
    'polling': '轮询',
    'mock': '模拟'
  };

  return `${statusMap[status] || status} (${typeMap[type] || type})`;
};
