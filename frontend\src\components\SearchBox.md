# 动态搜索功能实现文档

## 功能概述

在Header组件中实现了一个完整的动态搜索功能，支持用户、帖子、文章的全局搜索，具有实时搜索建议和响应式设计。

## 核心组件

### 1. SearchBox 组件 (`/components/SearchBox.tsx`)

**主要功能：**
- 动态展开/收起的搜索输入框
- 实时搜索建议下拉框
- 键盘导航支持
- 移动端适配
- 无障碍访问支持

**Props：**
- `className?: string` - 自定义样式类
- `isMobile?: boolean` - 是否为移动端模式

**状态管理：**
- `isExpanded` - 搜索框展开状态
- `query` - 搜索查询字符串
- `suggestions` - 搜索建议列表
- `selectedIndex` - 键盘选中的建议项索引
- `showSuggestions` - 是否显示建议下拉框

### 2. 搜索结果页面 (`/app/search/page.tsx`)

**主要功能：**
- 全局搜索结果展示
- 分类标签页（全部、用户、帖子、文章）
- 搜索统计信息
- 无结果友好提示
- 响应式布局

### 3. 类型定义 (`/types/GlobalSearchResult.ts`)

```typescript
interface GlobalSearchResult {
  users: User[];
  posts: CommunityPost[];
  articles: KnowledgeBaseArticle[];
  totalResults: number;
  query: string;
}

interface SearchSuggestion {
  type: 'user' | 'post' | 'article';
  id: string | number;
  title: string;
  subtitle?: string;
  avatarUrl?: string;
}
```

## API 接口

### 1. 全局搜索 API

```typescript
export const globalSearch = async (query: string): Promise<GlobalSearchResult>
```

**端点：** `GET /api/search/global?query={query}`

**功能：** 执行全局搜索，返回用户、帖子、文章的综合结果

### 2. 搜索建议 API

```typescript
export const getSearchSuggestions = async (query: string): Promise<SearchSuggestion[]>
```

**端点：** `GET /api/search/suggestions?query={query}`

**功能：** 获取实时搜索建议，用于下拉提示

### 3. 分类搜索 API

- `searchUsers(query)` - 搜索用户
- `searchCommunityPosts(query)` - 搜索社区帖子
- `searchKb(query)` - 搜索知识库文章

## 用户交互流程

### 1. 搜索框展开
1. 用户点击搜索图标
2. 搜索框平滑展开动画
3. 自动聚焦到输入框

### 2. 实时建议
1. 用户输入查询内容
2. 300ms防抖后发送建议请求
3. 显示下拉建议列表
4. 支持键盘上下导航

### 3. 执行搜索
1. 用户按Enter或点击建议项
2. 跳转到搜索结果页面
3. 显示分类搜索结果

### 4. 搜索框收起
1. 用户按ESC或点击关闭按钮
2. 清空搜索状态
3. 平滑收起动画

## 键盘快捷键

- `Enter` - 执行搜索或选择当前建议项
- `Escape` - 关闭搜索框
- `ArrowDown` - 向下选择建议项
- `ArrowUp` - 向上选择建议项

## 响应式设计

### 桌面端
- 搜索框宽度：256px (w-64)
- 完整的建议下拉框
- 鼠标悬停效果

### 移动端
- 搜索框宽度：192px (w-48)
- 触摸友好的按钮尺寸
- 优化的建议项间距

## 无障碍访问

### ARIA 属性
- `aria-label` - 按钮和输入框标签
- `aria-expanded` - 建议框展开状态
- `aria-autocomplete="list"` - 自动完成类型
- `role="combobox"` - 组合框角色
- `role="listbox"` - 建议列表角色
- `role="option"` - 建议项角色
- `aria-selected` - 选中状态

### 键盘导航
- Tab键焦点管理
- 方向键导航建议
- Enter键确认选择
- Escape键退出

## 性能优化

### 1. 防抖处理
使用300ms防抖避免频繁API请求

### 2. 懒加载
建议列表仅在有内容时渲染

### 3. 虚拟化
建议列表限制最大高度，支持滚动

### 4. 缓存策略
可扩展添加搜索结果缓存

## 样式设计

### 主题一致性
- 使用CSS变量保持主题一致
- 支持深色/浅色模式
- 与Header背景效果协调

### 动画效果
- Framer Motion提供平滑动画
- 展开/收起过渡效果
- 建议框淡入淡出

### 视觉层次
- 毛玻璃背景效果
- 阴影和边框层次
- 清晰的视觉反馈

## 扩展功能

### 1. 搜索历史
可添加本地存储的搜索历史记录

### 2. 高级搜索
支持筛选条件和排序选项

### 3. 搜索分析
记录搜索行为用于优化

### 4. 语音搜索
集成Web Speech API

## 测试页面

访问 `/test-search` 查看搜索功能演示和API测试。

## 部署注意事项

1. 确保后端API端点已实现
2. 配置适当的搜索索引
3. 设置合理的搜索限流
4. 监控搜索性能指标
