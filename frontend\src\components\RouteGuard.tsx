'use client';

import { useAuth } from '@/contexts/AuthContext';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState, useRef } from 'react';
import TechLoader from './ui/TechLoader';
import { toast } from 'sonner';

const PRIVATE_ROUTES = ['/profile', '/dashboard', '/showcase'];
const PUBLIC_ONLY_ROUTES = ['/login', '/register'];

const backgroundImages = [
    '/images/background/Loading_1.webp',
    '/images/background/Loading_2.webp',
    '/images/background/Loading_3.webp',
    '/images/background/Loading_4.webp',
];

// Custom hook to get the previous value of a prop or state
function usePrevious(value: boolean) {
  const ref = useRef<boolean>();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

const loadingMessages = [
    { text: "正在校准格雷科技机器...", author: "一位严谨的工程师" },
    { text: "正在为主电路通电... 祈祷别出岔子。", author: "担惊受怕的电路板" },
    { text: "正在预热工业高炉，请准备好耐火砖。", author: "《多方块结构建筑学》" },
    { text: "正在扫描矿脉... 这次会是中子素吗？", author: "矿脉探测仪" },
    { text: "正在检查电压等级，一块电路板的生死存亡。", author: "高压电用户手册" },
    { text: "正在为AE2网络分配频道，但愿别冲突。", author: "P2P通道工程师" },
    { text: "正在冷却真空冷冻机，马上就能处理钨钢了。", author: "《高级材料学》" },
    { text: "正在合成第一块纳米电路板，感觉自己像个神。", author: "一位未来的神" },
    { text: "正在配置流体管道，液体可不会自己走路。", author: "流体力学专家" },
    { text: "正在搭建多方块结构，图纸看了一遍又一遍。", author: "建筑师的自我修养" },
    { text: "正在平衡离心机，一点震动都可能前功尽弃。", author: "精密仪器操作员" },
    { text: "正在为聚变反应堆注入燃料，成败在此一举。", author: "星际能源公司" },
    { text: "正在计算EU/t消耗，电费是个大问题。", author: "电力网络管理员" },
    { text: "正在给扳手附魔，希望它能挺过下次拆家。", author: "一位资深扳手使用者" },
    { text: "正在清理背包，又是99%的石头和泥土。", author: "仓鼠症晚期患者" },
    { text: "正在研究神秘时代... 要素有点不够用。", author: "奥术工作台" },
    { text: "正在献祭... 为了更强大的血魔法。", author: "血魔法祭坛的低语" },
    { text: "正在与盖亚战斗，植物魔法的终极考验。", author: "一位植物魔法师" },
    { text: "正在孵化龙蛋，温度计的指针不能晃。", author: "龙之研究员" },
    { text: "正在规划铁路网络，让矿车跑遍世界。", author: "铁道工程师" },
    { text: "正在躲避血月，怪物们今晚异常兴奋。", author: "一位生存专家" },
    { text: "正在下界寻找石英，电路板的必需品。", author: "地狱探险家" },
    { text: "正在准备远征暮色森林，带上最好的装备。", author: "暮色森林传送门" },
    { text: "正在优化矿物处理线，从双倍到五倍的跨越。", author: "矿物学教授" },
    { text: "正在给量子套装充电，能量就是安全感。", author: "高能物理学家" },
    { text: "正在用分子装配机搓暗物质，宇宙在我手中。", author: "AE2自动化大师" },
    { text: "正在绘制星图，目标是星辰大海。", author: "高级火箭技术员" },
    { text: "正在调试戴森球，太阳能是无穷的。", author: "戴森球项目组" },
    { text: "正在检查服务器TPS，愿服主与你同在。", author: "一位忧心忡忡的服主" },
    { text: "正在备份存档... 这是最重要的一个步骤。", author: "一位有远见的玩家" },
    { text: "格雷科技，小子！它会在高压下硬化。", author: "阿姆斯特朗议员" },
    { text: "警告：检测到微小的电压不匹配。准备好收拾残局了吗？", author: "电压警报系统" },
    { text: "你的肝还好吗？", author: "一位来自东方的神秘医生" },
    { text: "一个机器方块？太天真了，你需要一个3x3x4的多方块结构。", author: "GregoriusT" },
    { text: "欢迎来到格雷乌斯的地狱厨房。", author: "戈登·拉姆齐（大概）" },
    { text: "四连并行处理矿物？不，你需要三十二连！", author: "效率至上主义者" },
    { text: "今天你的基地爆炸了吗？", author: "每日安全问候" },
    { text: "正在编译NEI配方... 请准备好咖啡。", author: "NEI (Not Enough Items)" },
    { text: "AE2网络过载？试试更多的P2P通道。", author: "《AE2网络故障排除》" },
    { text: "挖矿五分钟，整理背包两小时。", author: "GTNH玩家的日常" },
    { text: "扳手不是万能的，但没有扳手是万万不能的。", author: "工具人法则第一条" },
    { text: "不要和LV机器谈效率，它还只是个孩子。", author: "一位宽容的工程师" },
    { text: "每一个GTNH玩家上辈子都是电路工程师。", author: "宇宙真理" },
    { text: "从燧石到星门，鬼知道我经历了什么。", author: "一位匿名的星系探险家" },
    { text: "这根电线应该接哪里来着...？", author: "一个健忘的玩家" },
    { text: "GTNH教会我最重要的事：耐心，以及多做备份。", author: "一本泛黄的日记" },
    { text: "我只是想要个蛋糕，为什么需要一个化工厂？", author: "一个饥饿的甜点师" },
    { text: "别问，问就是并行。", author: "GTNH社区箴言" },
    { text: "正在思考人生... 以及下一个该自动化的物品。", author: "哲学家" },
    { text: "确认一下，你真的关好高压电的开关了吗？", author: "安全生产监督员" },
    { text: '在GTNH里，最动听的声音是机器完成工作的"叮"。', author: "工业交响乐" },
    { text: "又是一个不眠之夜，只为那闪亮的钛。", author: "钛合金爱好者" },
    { text: "你看到我的橡胶树苗了吗？我种了一整个生态群系。", author: "橡胶大亨" },
    { text: "电量焦虑症是GTNH玩家的常见病。", author: "《格雷科技心理学》" },
    { text: "相信我，这个多方块结构绝对是对的...大概。", author: "一个不太自信的建筑师" },
    { text: "当NEI都救不了你时，说明你玩到了GTNH的精髓。", author: "一位得道高人" },
    { text: "正在压缩100万个圆石...", author: "物质压缩机" },
    { text: "正在寻找最后一颗钻石...", author: "一个执着的矿工" },
    { text: "正在试图理解聚变反应堆的蓝图...", author: "未来的核物理学家" },
    { text: "正在给核弹井输入目标坐标...开个玩笑。", author: "一位爱好和平的玩家" },
    { text: "正在用记事本记录复杂的合成链...", author: "人类智慧的体现" },
    { text: "正在计算制作一个UHV机器需要多少铁...", author: "Excel表格" },
    { text: "正在更新任务书...新的折磨已上线。", author: "任务书编写者" },
    { text: "正在为自动化产线添加最后一个伺服电机...", author: "自动化工程师" },
    { text: "正在为你的电脑默哀...它为GTNH付出了太多。", author: "显卡" },
    { text: "正在检查电缆的损耗...每一EU都弥足珍贵。", author: "节能减排办公室" },
    { text: "正在为你的第一台蒸汽机寻找燃料...", author: "工业革命的开端" },
    { text: "正在用锤子和板子制作齿轮...工业的黎明。", author: "一位初级工匠" },
    { text: "正在躲避从天而降的触手...腐化真讨厌。", author: "净化盐生产商" },
    { text: "正在用纯净节点净化土地...为了家园。", author: "大地守护者" },
    { text: "正在设置物流管道的优先级...别让它堵了。", author: "交通管理部门" },
    { text: "正在研究如何自动化种植...不然要饿死了。", author: "农业科学家" },
    { text: "正在酿造能抵抗凋零的药水...", author: "炼金术士" },
    { text: "正在给镐子附上时运III...虽然格雷不吃这一套。", author: "一个天真的附魔师" },
    { text: "正在比较不同发电机之间的EU/t...", author: "能源分析师" },
    { text: "正在努力把基地从木头升级到混凝土...", author: "建筑材料供应商" },
    { text: "正在遥望星空，思考着如何飞向月球...", author: "天文学家" },
    { text: "正在把所有的箱子升级为铁箱子...", author: "仓储管理员" },
    { text: "正在试图在没有地图的情况下回家...", author: "一个迷路的孩子" },
    { text: "正在和成群的猪人打交道...", author: "地狱外交官" },
    { text: "正在用末影珍珠进行战略性转移...", author: "空间跳跃技术员" },
    { text: "正在初始化世界生成器...塑造一个充满挑战的世界。", author: "创世神" },
    { text: "正在构建维度...从主世界到遥远的星系。", author: "维度旅行者" },
    { text: "正在应用物理法则...电压、重力、以及爆炸。", author: "牛顿" },
    { text: "正在模拟生态系统...从第一棵橡胶树开始。", author: "生物学家" },
    { text: "正在启动化学反应引擎...元素周期表的力量。", author: "门捷列夫" },
    { text: "正在建立知识体系...你的任务书即将更新。", author: "图书管理员" },
    { text: "正在唤醒远古的魔法...从第一根法杖开始。", author: "梅林" },
    { text: "正在编织时空...等待你的探索。", author: "时间管理者" },
    { text: "正在点亮第一颗恒星...为你的旅程指引方向。", author: "领航员" },
    { text: "正在雕琢每一个方块...即使它最终会被炸掉。", author: "一位艺术家" },
    { text: "客户端正在响应... 但愿吧。", author: "网络延迟" },
    { text: "正在同步光影与现实...哪个才是现实？", author: "一位哲学家" },
    { text: "渲染距离已设为最大，祝你的显卡好运。", author: "GPU" },
    { text: "正在加载声音文件...爆炸声已就位。", author: "音效师" },
    { text: "正在连接至服务器...请输入那该死的IP地址。", author: "多人游戏菜单" },
    { text: "请记住，钻石在Y=12层...但这在GTNH里没用。", author: "过时的挖矿指南" },
    { text: "不要向下挖...除非你带了水桶和防火药水。", author: "生存法则" },
];

/**
 * This inner component is rendered only after the auth state has been loaded.
 * It can safely use client-side hooks like usePathname and useRouter.
 */
function ProtectedContent({ children }: { children: React.ReactNode }) {
    const router = useRouter();
    const pathname = usePathname();
    const { isAuthenticated, justLoggedIn, resetJustLoggedIn } = useAuth();

    useEffect(() => {
        const isPrivateRoute = PRIVATE_ROUTES.some(route => pathname.startsWith(route));
        const isPublicOnlyRoute = PUBLIC_ONLY_ROUTES.some(route => pathname.startsWith(route));

        if (isAuthenticated) {
            // User is authenticated
            if (isPublicOnlyRoute) {
                // And is on a page like /login or /register
                if (justLoggedIn) {
                    // This is the initial redirect after a fresh login.
                    // We just redirect and wait for the next render on the new page to reset the flag.
                    router.push('/');
                } else {
                    // This is an already-logged-in user trying to access /login.
                    // Toast and redirect.
                    toast.info('您已登录，无需重复操作。');
                    router.push('/');
                }
            } else {
                // User is on a normal, authenticated page.
                // If the `justLoggedIn` flag is still true, it means we just arrived from login.
                // Now is the safe time to reset it.
                if (justLoggedIn) {
                    resetJustLoggedIn();
                }
            }
        } else {
            // User is not authenticated
            if (isPrivateRoute) {
                if (!pathname.startsWith('/api')) {
                    router.push('/login');
                }
            }
        }
    }, [isAuthenticated, justLoggedIn, pathname, router, resetJustLoggedIn]);
    
    const isPrivateRoute = PRIVATE_ROUTES.some(route => pathname.startsWith(route));
    // While the redirect is happening, or for a private route accessed without auth,
    // we can show nothing or a loader. Returning null is cleaner than a loader flash.
    if (!isAuthenticated && isPrivateRoute) {
        return null;
    }

    return <>{children}</>;
}

/**
 * This is the main guard component. Its only job is to check the initial
 * loading state of the authentication. It does NOT use any client-side
 * router hooks, making it safe for SSR.
 */
export default function RouteGuard({ children }: { children: React.ReactNode }) {
    const { loading: authLoading, justLoggedIn } = useAuth();
    const [messageIndex, setMessageIndex] = useState(0);
    const [bgImage, setBgImage] = useState('');
    const [isReadyForAnimation, setIsReadyForAnimation] = useState(false);
    const [minLoadingTimePassed, setMinLoadingTimePassed] = useState(false);

    // Effect to set up content for the loading screen and min display time
    useEffect(() => {
        const randomBg = backgroundImages[Math.floor(Math.random() * backgroundImages.length)];
        setBgImage(randomBg);
        setMessageIndex(Math.floor(Math.random() * loadingMessages.length));
        
        // A tiny delay to allow the component to mount before starting the fade-in animation
        const animationTimer = setTimeout(() => setIsReadyForAnimation(true), 50);

        // Set a timer for the minimum loading duration
        const minTimeTimer = setTimeout(() => setMinLoadingTimePassed(true), 3000);

        return () => {
            clearTimeout(animationTimer);
            clearTimeout(minTimeTimer);
        };
    }, []); // Runs once on mount

    // If the user has just logged in, we don't want to show the long loading screen again.
    // We can bypass the minimum loading time check.
    const showLoadingScreen = (authLoading || !minLoadingTimePassed) && !justLoggedIn;

    if (showLoadingScreen) {
        return (
            <div 
                className={`fixed inset-0 flex items-center justify-center z-[9999] transition-opacity duration-500 ${isReadyForAnimation ? 'opacity-100' : 'opacity-0'}`}
                style={{ 
                    backgroundImage: `url('${bgImage}')`,
                    backgroundSize: 'cover',
                }}
            >
                <div className="absolute inset-0 bg-black/50"></div>
                <div className="text-center z-10">
                    <TechLoader />
                    <div className={`mt-6 text-lg font-medium text-white tracking-wider transition-opacity duration-500 ${isReadyForAnimation ? 'opacity-100' : 'opacity-0'}`}>
                        <div className="inline-block text-center">
                            <p>“{loadingMessages[messageIndex].text}”</p>
                            <p className="mt-2 text-sm text-gray-400 text-right">— {loadingMessages[messageIndex].author}</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
    
    // Once authentication state is loaded, render the ProtectedContent
    // which can then safely perform routing checks.
    return <ProtectedContent>{children}</ProtectedContent>;
}
