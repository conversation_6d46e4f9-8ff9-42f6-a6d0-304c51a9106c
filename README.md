# Kitolus Community - 全栈社区平台 🚀

这是一个为特定玩家社区（如 GTNH）打造的、功能完善的现代化全栈网站平台。项目采用前后端分离架构，集成了用户系统、内容管理、全文搜索和支付等多种功能，旨在提供一个高性能、高可用的社区解决方案。

---

### 📚 目录

- [🌟 项目总览](#-项目总览)
  - [✨ 核心功能](#-核心功能)
- [🛠️ 技术栈](#️-技术栈)
- [🏛️ 系统设计与实现](#️-系统设计与实现)
  - [1. 系统总体架构](#1-系统总体架构)
  - [2. 核心流程可视化](#2-核心流程可视化)
  - [3. 核心功能深度解析](#3-核心功能深度解析)
  - [4. 项目结构与模块深度解析](#4-项目结构与模块深度解析)
  - [5. API 参考 (核心)](#5-api-参考-核心)
- [🚀 部署与运维](#-部署与运维)
  - [1. 环境准备](#1-环境准备)
  - [2. 本地运行](#2-本地运行)
  - [3. 生产部署](#3-生产部署)

---

## 🌟 项目总览

### ✨ 核心功能

- **精密的用户认证系统**: 基于 JWT 的无状态认证，支持注册、登录、邮件验证和安全的密码修改。
- **功能丰富的个人中心**: 用户可以修改个人信息、上传并裁剪头像/横幅，并管理账户安全设置。
- **高性能知识库 (KB)**:
    - 由 **Apache Lucene** 驱动的毫秒级**全文检索**，支持精准、高效的中英文混合搜索。
- **动态项目展示页**: 通过静态 JSON 文件作为轻量级 CMS，动态渲染项目列表，易于更新和维护。
- **产品与支付集成**: 展示商品信息并集成第三方支付网关 (蓝兔支付)，实现完整的交易闭环。
- **现代化动态前端**: 基于 Next.js 和 `shadcn/ui` 构建，提供丝滑的 UI/UX 和优秀的首屏加载性能 (SSR)。
- **健壮的 RESTful API**: 后端提供结构清晰、安全可靠的 API 接口。

---

## 🛠️ 技术栈

| 分层 | 技术 | 版本/组件 | 在项目中的作用与选型考量 |
| :--- | :--- | :--- | :--- |
| **前端** | **Next.js** | 14+ (App Router) | 利用其服务端渲染 (SSR) 和静态站点生成 (SSG) 的能力，优化 SEO 和首屏加载速度。App Router 提供了更现代化的路由和布局管理方案。 |
| | **React & TypeScript** | 18+ | 构建可复用、类型安全的 UI 组件，是现代 Web 开发的基石。 |
| | **Tailwind CSS** | 3+ | 提供原子化的 CSS 类，极大地提高了 UI 开发效率和样式一致性。 |
| | **shadcn/ui** | - | 基于 Radix UI 和 Tailwind CSS 的组件库，美观、易用且高度可定制，是项目中所有核心UI（如按钮、对话框、表单）的基础。 |
| | **Framer Motion** | - | 用于实现流畅、富有表现力的页面过渡和交互动画，提升用户体验。 |
| | **axios** | - | 一个可靠的 HTTP 客户端，用于在前端 (`/services/api.ts`) 统一管理所有对后端 API 的请求。 |
| **后端** | **Java** | 17 | 长期稳定支持 (LTS) 版本，拥有成熟的生态和高性能的虚拟机 (JVM)。 |
| | **Spring Boot** | 3.3.0 | 业内最主流的 Java 开发框架，通过自动配置和起步依赖 (Starters) 极大地简化了后端应用的开发和部署。 |
| | **Spring Security** | - | 提供了全面的安全服务，本项目中主要用于密码加密 (`BCryptPasswordEncoder`) 和配置 JWT 认证流程。 |
| | **MyBatis Plus** | 3.5.x | 在流行 ORM 框架 MyBatis 的基础上进行了增强，提供了强大的 CRUD 功能和条件构造器，简化了数据库操作，同时保留了编写原生 SQL 的灵活性。 |
| | **JJWT (JSON Web Token)** | 0.11.5 | 用于生成和验证 JWT，是实现无状态认证流程的核心库。 |
| | **Apache Lucene** | 8.11.3 | 一个高性能、功能丰富的全文搜索引擎库。项目使用它为知识库文章建立索引，并利用 `SmartChineseAnalyzer` 实现了强大的中英文分词搜索能力。 |
| **数据库** | **MySQL** | 8.0+ | 成熟、可靠的关系型数据库，用于持久化存储用户信息、订单、产品等核心业务数据。 |
| | **Redis** | - | 高性能的内存数据库。在本项目中可能用于：1. 缓存热点数据（如用户信息、知识库文章）以减轻数据库压力。2. 存储 JWT 的黑名单或会话信息以增强安全性。 |

---

## 🏛️ 系统设计与实现

本章节深入探讨了系统的架构、核心流程、功能实现细节、模块组织和API定义，旨在为开发者提供一个全面的技术参考。

### 1. 系统总体架构

下图展示了本项目的前后端分离架构以及各个技术组件之间的逻辑关系。

```mermaid
graph TD
    subgraph "用户端"
        A["用户浏览器"]
    end

    subgraph "前端 (Next.js)"
        B["Next.js 应用"]
        C["UI (shadcn/ui & Tailwind)"]
        D["状态管理 (AuthContext)"]
        E["API请求 (axios)"]
        B --> C
        B --> D
        B --> E
    end

    subgraph "后端 (Spring Boot)"
        F["Nginx 反向代理"]
        G["Spring Boot 应用"]
        H["Controllers (API接口)"]
        I["Services (业务逻辑)"]
        J["Mappers (数据访问)"]
        K["Security (安全控制 & JWT)"]
        G --> H --> I --> J
        G --> K
    end

    subgraph "数据与服务"
        L["MySQL 数据库"]
        M["Redis 缓存"]
        N["文件系统 (头像/文章)"]
        O["邮件服务 (SMTP)"]
        P["支付网关 (LantuPay)"]
        Q["搜索引擎 (Lucene)"]
    end

    A <--> F
    F -- "/api/*" --> G
    F -- "/" --> B
    E --> G

    J --> L
    I -- "缓存" --> M
    I -- "文件" --> N
    I -- "邮件" --> O
    I -- "支付" --> P
    I -- "搜索" --> Q
    K -- "JWT/用户信息" --> M
    K -- "验证" --> J
```

---

### 2. 核心流程可视化

本章节通过图表展示项目核心功能的端到端逻辑流和模块交互关系。

#### 1. 后端：请求生命周期

下图展示了一个典型的 API 请求在 Spring Boot 后端内部的完整处理流程。

```mermaid
graph TD
    subgraph "外部请求"
        A[HTTP Request]
    end

    subgraph "Spring Boot 应用内部"
        B(网络层 / Tomcat)
        C{Spring Security 过滤器链}
        D[JwtRequestFilter]
        E[DispatcherServlet]
        F[Controller]
        G[Service]
        H[Mapper]
        I[Database]

        A --> B --> C
        C -- "受保护路由" --> D
        C -- "公共路由" --> E
        D -- "JWT 验证通过" --> E
        E --> F
        F --> G
        G --> H
        H --> I
    end
    
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#f9f,stroke:#333,stroke-width:2px
```

#### 2. 前端：组件化架构

下图展示了 Next.js 前端应用如何通过组件、Context 和服务层协同工作，最终渲染出一个完整的页面。

```mermaid
graph TD
    subgraph "用户访问"
        A[URL: /profile]
    end

    subgraph "Next.js App Router"
        B["app/layout.tsx<br/>(根布局, 挂载全局Provider)"]
        C["app/profile/page.tsx<br/>(页面级组件)"]
    end

    subgraph "共享逻辑与组件"
        D["业务组件<br/>(e.g., AccountSettings)"]
        E["原子UI组件<br/>(e.g., Button, Input)"]
        F["全局状态<br/>(AuthContext)"]
        G["API服务<br/>(api.ts)"]
    end

    A --> B --> C
    C --> D
    D --> E
    B -- "包裹应用" --> F
    D -- "useAuth() 读取" --> F
    D -- "apiService() 调用" --> G
    
    style F fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
```

#### 3. 核心流程：用户登录认证

```mermaid
sequenceDiagram
    actor U as User
    participant FE as 前端 (Login Page)
    participant BE as 后端 (AuthController)

    U->>FE: 1. 填写用户名和密码, 点击登录
    FE->>BE: 2. POST /api/auth/login
    note over BE: 内部：校验密码, 查询数据库
    alt 密码正确
        note over BE: 内部：生成JWT
        BE-->>FE: 3. 响应 200 OK, 返回JWT
        note over FE: 内部：存储JWT, 跳转页面
        FE-->>U: 4. 导航至个人中心
    else 密码错误
        BE-->>FE: 3b. 响应 401 Unauthorized
        FE-->>U: 4b. 显示错误提示
    end
```

#### 4. 核心流程：知识库搜索

```mermaid
sequenceDiagram
    participant U as User
    participant FE as 前端 (KB Page)
    participant BE as 后端 (KB Service)
    
    U->>FE: 1. 在搜索框输入 "Spring Boot"
    FE->>BE: 2. GET /api/kb/search?q=Spring+Boot
    note over BE: 内部：调用 Apache Lucene 执行全文检索
    BE-->>FE: 3. 返回搜索结果的 JSON 数组
    FE-->>U: 4. 渲染搜索结果列表
```

#### 5. 核心流程：更新个人资料

```mermaid
sequenceDiagram
    participant U as 用户 (浏览器)
    participant FE as 前端 (Next.js UI)
    participant BE as 后端 (Spring Boot)

    U->>FE: 1. 修改个人信息, 点击保存
    note over FE: 内部：调用 apiService.put()
    FE->>BE: 2. 发送 PUT /api/user/profile 请求 (含JWT)
    note over BE: 内部：验证JWT, 调用Service, 更新数据库
    BE-->>FE: 3. 返回 200 OK 响应
    note over FE: 内部：显示 "更新成功" Toast
    FE-->>U: 4. UI状态同步更新
```

---

### 3. 核心功能深度解析

本章节将项目文档的粒度提升至关键函数和组件内部，通过图文结合的方式，揭示核心功能的底层实现细节。

#### 1. 核心功能：文件上传、裁剪与 WebP 优化

此流程涉及前端的用户交互、图像处理，以及后端的文件存储和格式转换，是典型的全栈功能。

```mermaid
sequenceDiagram
    participant U as User
    participant FC as 前端组件 (Profile)
    participant CM as 裁剪弹窗 (Crop Modal)
    participant API as api.ts (axios)
    participant BC as 后端 Controller
    participant FS as FileStorageService
    participant DB as 数据库

    U->>+FC: 1. 点击 "上传头像"
    FC->>+CM: 2. 打开弹窗, 传入原始图片文件
    U->>+CM: 3. 调整裁剪框
    CM-->>-FC: 4. 回传裁剪后的图片 (Blob 对象)
    FC->>+API: 5. 创建 FormData, 调用 api.post('/user/avatar')
    API->>+BC: 6. PUT /api/user/avatar (multipart/form-data)
    BC->>+FS: 7. 调用 storeAvatar(file)
    note over FS: 生成UUID文件名<br/>使用 ImageIO 和 webp-imageio 库<br/>将图片编码为 WebP 格式
    FS-->>-BC: 8. 返回优化后的文件存储路径
    BC->>+DB: 9. 更新用户表中的 avatarUrl 字段
    DB-->>-BC: 10. 确认更新成功
    BC-->>-API: 11. 响应 200 OK
    API-->>-FC: 12. 请求成功
    FC-->>U: 13. UI 更新，显示新头像
```

**代码实现细节**:
- **前端 `AvatarCropModal.tsx`**:
    - 利用 `react-image-crop` 之类的库，在客户端完成可视化的裁剪操作。
    - 裁剪完成的回调函数 (`onCropComplete`) 是关键，它能将 `<canvas>` 元素上的图像数据转换为一个 `Blob` 对象。这个过程通常通过 `canvas.toBlob()` 实现。
    - 这个 `Blob` 对象被视为一个标准文件，可以被添加到 `FormData` 中，准备上传。
- **后端 `FileStorageService.java`**:
    - `storeAvatar` 方法接收一个 `MultipartFile` 对象。
    - **WebP 转换核心**: 它不直接保存原始文件。而是通过 `ImageIO.read(file.getInputStream())` 将上传的文件读入一个 `BufferedImage` 对象。
    - 接着，它使用 `ImageIO.write(image, "webp", outputStream)`，借助 `webp-imageio` 依赖的能力，将这个 `BufferedImage` 以 WebP 格式写入到服务器的磁盘文件中。
    - 这个优化步骤能将图片文件大小平均减少 **50%-80%**，极大地提升了前端的加载性能和用户体验。

---

#### 2. 核心机制：前端认证状态恢复

这是一个"看不见但至关重要"的流程。它确保了用户在刷新页面或关闭浏览器重开后，依然能保持登录状态，而不是每次都需要重新登录。

```mermaid
sequenceDiagram
    participant App as App 启动
    participant AC as AuthContext
    participant LS as localStorage
    participant API as api.ts (axios)
    participant BE as 后端 API

    App->>+AC: 1. 应用挂载, AuthProvider 加载
    AC->>LS: 2. useEffect 触发, 读取 JWT
    alt Token 存在
        LS-->>AC: 3. 返回 JWT
        AC->>+API: 4. 调用 api.get('/user/me')
        note over API: 请求拦截器自动附加JWT
        API->>+BE: 5. 发送带认证头的请求
        BE-->>-API: 6. 验证JWT, 返回用户完整信息
        API-->>AC: 7. 成功回调, 返回用户数据
        AC->>AC: 8. 更新状态: setUser(data), setLoading(false)
    else Token 不存在
        LS-->>AC: 3b. 返回 null
        AC->>AC: 4b. 更新状态: setLoading(false), user 为 null
    end
    deactivate AC
    note over AC: 整个应用的UI根据最新的认证状态重新渲染
```

**代码实现细节**:
- **前端 `AuthContext.tsx`**:
    - 在组件内部，一个 `useEffect` hook 被设置为空依赖数组 `[]`，这意味着它只在组件**首次挂载**时执行一次。
    - 在这个 `useEffect` 中，程序首先尝试从 `localStorage.getItem('token')` 获取 JWT。
    - **如果 Token 存在**:
        - 为了验证这个 Token 是否仍然有效（可能已在服务器端过期），它会调用 `apiService.get('/api/user/me')`。
        - 这个 API 端点是受保护的，后端会验证 Token 的有效性。如果有效，则返回该 Token 对应的用户信息。
        - 收到用户信息后，`AuthContext` 将用户信息存入 `user` state，并将 `loading` state 设为 `false`。
    - **如果 Token 不存在或验证失败**:
        - `AuthContext` 会直接将 `loading` state 设为 `false`，而 `user` state 保持为 `null`。
- **UI 渲染逻辑**:
    - 在 `loading` 为 `true` 期间，页面可以显示一个全局的加载动画，避免页面闪烁。
    - 当 `loading` 变为 `false` 后，应用中的任何组件都可以通过 `useAuth()` hook 来安全地访问 `user` 对象，并根据 `user` 是否为 `null` 来决定显示"登录/注册"按钮还是用户的个人信息。 

---

#### 3. 核心功能：动态数据驱动的项目展示 (Showcase) 页面

此页面展示了如何使用静态 JSON 文件作为轻量级 CMS (内容管理系统) 来动态渲染一个内容丰富的列表，是构建作品集、项目列表等页面的绝佳实践。

```mermaid
graph TD
    subgraph "数据层 (Data Layer)"
        A["/frontend/src/app/showcase/projects.json<br/>(静态项目数据)"]
    end

    subgraph "前端渲染流程 (Frontend Rendering)"
        B["Showcase Page<br/>(app/showcase/page.tsx)"]
        C["ShowcaseClient.tsx<br/>(客户端组件)"]
        D["ProjectCard Component<br/>(循环渲染)"]
        E["shadcn/ui Components<br/>(Card, Badge, Button)"]
    end

    subgraph "用户交互 (User Interaction)"
        F[用户浏览器]
    end

    A -- "import/fetch" --> C
    B -- "渲染" --> C
    C -- "map over projects" --> D
    D -- "组合UI" --> E
    E -- "HTML" --> F

    style A fill:#ccf,stroke:#333,stroke-width:2px
```

**代码实现细节**:
- **`projects.json`**:
    - 这是一个简单的 JSON 数组，每个对象代表一个要展示的项目。
    - **职责**: 充当一个无需数据库的、版本控制友好的 "内容源"。修改、添加或删除项目只需要编辑这个文件，无需重新部署后端或数据库迁移。
    - **结构**: 每个项目对象包含 `title`, `description`, `imageUrl`, `tags` (如 `["Next.js", "Spring Boot"]`), 以及 `projectUrl` 等字段。
- **`ShowcaseClient.tsx`**:
    - **职责**: 这是展示页面的核心**客户端组件**，负责获取数据并管理UI状态。
    - **数据获取**: 组件在 `useEffect` hook 中通过 `import` 或 `fetch` API 直接加载 `projects.json` 文件中的数据，并将其存储在 React state 中 (`useState<Project[]>([])`)。
    - **UI 渲染**: 它使用 `.map()` 方法遍历项目数据 state，为每个项目渲染一个 `ProjectCard` 组件，并将项目数据作为 `props` 传递下去。
    - **交互性 (可选)**: 可以在此组件中添加筛选逻辑。例如，根据标签 (`tags`) 过滤项目，并通过 `useState` 管理当前选中的筛选条件，动态地重新渲染列表。
- **`ProjectCard.tsx` (假想的业务组件)**:
    - **职责**: 这是一个可复用的"业务组件"，负责展示**单个**项目的信息。
    - **构成**: 它由多个 `shadcn/ui` 原子组件构成：
        - `Card`:作为整体容器。
        - `img`: 显示项目图片 `imageUrl`。
        - `CardHeader`, `CardTitle`, `CardDescription`: 显示标题和描述。
        - `Badge`: 循环渲染 `tags` 数组，为每个技术栈显示一个徽章。
        - `Button`: 提供一个链接到 `projectUrl` 的 "查看详情" 按钮。

---

#### 4. 核心机制：前端路由守卫 (`RouteGuard.tsx`)

为了保护某些页面（如 `/profile`）不被未登录的用户访问，项目采用了一个路由守卫组件。这是通过组合 Next.js 的布局系统和 React Context 实现的一种优雅的权限控制方案。

```mermaid
graph TD
    subgraph "用户访问"
        A[URL: /profile]
    end

    subgraph "Next.js 渲染流程"
        B["Root Layout<br/>(加载 AuthProvider)"]
        C["Profile Layout/Page<br/>(被 RouteGuard 包裹)"]
        D{"RouteGuard Component"}
        E["页面内容<br/>(ProfileOverview, etc.)"]
        F["全局加载动画 / 重定向"]
    end

    subgraph "全局状态"
        G["AuthContext<br/>(user, loading)"]
    end

    A --> B
    B -- "渲染子路由" --> C
    C -- "渲染" --> D
    
    D -- "useAuth()" --> G
    
    D -- "if (loading)" --> F
    D -- "if (!user && !loading)" --> F
    D -- "if (user && !loading)" --> E
```

**代码实现细节**:
- **包裹受保护的组件**: 在需要保护的页面的 `layout.tsx` 或 `page.tsx` 中，使用 `<RouteGuard>` 组件包裹实际的页面内容。
- **`RouteGuard.tsx` 内部逻辑**:
    1.  **获取认证状态**: 组件通过 `useAuth()` hook 从 `AuthContext` 中获取 `user` 和 `loading` 两个状态。
    2.  **处理加载状态**: 当 `loading` 为 `true` 时，`AuthContext` 仍在验证本地的 JWT。在此期间，`RouteGuard` 会显示一个全屏的加载指示器 (e.g., Skeleton screen)，防止页面内容在认证状态未知时闪烁。
    3.  **处理未登录状态**: 当 `loading` 变为 `false` 且 `user` 对象为 `null` 时，表示用户未登录。`RouteGuard` 会使用 Next.js 的 `useRouter` hook 将用户重定向到登录页面 (`/login`)。
    4.  **处理已登录状态**: 当 `loading` 为 `false` 且 `user` 对象存在时，表示用户已认证。`RouteGuard` 会直接渲染其 `children`，即实际的页面内容。

---

### 4. 项目结构与模块深度解析

本章节提供基于文本的代码结构拆解，与上一章节的可视化图表互为补充。

#### 后端 (Backend) - `kitolus-community`

后端是基于 Spring Boot 构建的 RESTful API 服务，遵循经典的三层架构模式。

```
backend/src/main/java/com/kitolus/community/
├── config/             # 1. 应用配置层
├── controller/         # 2. 接口与视图层 (API Entry)
├── dto/                # 3. 数据传输对象 (API Contracts)
├── entity/             # 4. 数据实体层 (Database Mapping)
├── exception/          # 5. 全局异常处理
├── mapper/             # 6. 数据访问层 (Data Access Layer)
├── service/            # 7. 业务逻辑层 (Business Logic)
└── util/               # 8. 工具类
```

1.  **`config` (配置层)**:
    -   `SecurityConfig.java`: **核心安全配置**。定义了整个应用的 sécurité 策略。它配置了哪些 API 端点是公开的（如 `/api/auth/**`），哪些是需要认证的。最重要的是，它将 `JwtRequestFilter` 注册到了 Spring Security 的过滤器链中，确保了所有受保护的请求都会经过 JWT 验证。
    -   `WebConfig.java`: **Web 相关配置**。主要用于配置静态资源的映射。例如，它将 URL 路径 `/avatars/**` 映射到服务器文件系统的 `storage/avatars/` 目录，这样前端就可以通过 URL 直接访问用户上传的头像。
    -   `AppConfig.java`: 提供全局共享的 Bean，例如 `BCryptPasswordEncoder`，供 `UserService` 在加密和验证密码时注入使用。

2.  **`controller` (接口层)**:
    -   **职责**: 作为 API 的入口，直接接收来自前端的 HTTP 请求。
    -   **逻辑**: Controller 负责解析请求参数（如 `@RequestBody`, `@PathVariable`），然后调用 `service` 层相应的方法来处理业务逻辑。它**不包含任何业务逻辑**，只做请求的转发和响应的封装。
    -   **示例**: `UserController.java` 中的 `updateProfile` 方法接收一个包含用户信息的 `UpdateProfileRequestDTO`，然后立即调用 `userService.updateUserProfile(dto)`。

3.  **`dto` (数据传输对象)**:
    -   **职责**: 定义 API 的"数据契约"。
    -   **逻辑**: DTO 是纯粹的数据载体，用于在 Controller 和前端之间传输数据。使用 DTO 的好处是可以将领域模型 (`entity`) 与 API 接口解耦，避免暴露数据库结构，并且可以根据需要定制传输的数据字段。
    -   **示例**: `RegisterRequestDTO.java` 只包含注册所需的字段，而 `User.java` (Entity) 可能包含密码、创建时间等不应暴露给注册接口的敏感信息。

4.  **`entity` (数据实体层)**:
    -   **职责**: 映射到数据库中的表结构。
    -   **逻辑**: 每个 Entity 类（如 `User.java`, `Product.java`）都与一张数据库表对应。MyBatis Plus 会根据这些 Entity 的定义来自动执行 CRUD 操作。

5.  **`exception` (异常处理层)**:
    -   **职责**: 捕获并处理整个应用的异常，提供统一、友好的错误响应。
    -   **逻辑**: `GlobalExceptionHandler.java` 使用 `@RestControllerAdvice` 注解，可以捕获所有 Controller 抛出的异常。例如，当 `service` 层抛出一个 `ResourceNotFoundException` 时，这里会捕获它，并向前端返回一个包含清晰错误信息的、HTTP 状态码为 404 的 JSON 响应。

6.  **`mapper` (数据访问层)**:
    -   **职责**: 直接与数据库进行交互。
    -   **逻辑**: 这是一个继承了 `BaseMapper<T>` 的接口（如 `UserMapper.java`）。MyBatis Plus 会在运行时为这些接口自动生成实现，提供一套功能强大的数据操作方法（如 `selectById`, `updateById`, `selectList` 等）。复杂的 SQL 查询可以通过 XML 文件或注解来编写。

7.  **`service` (业务逻辑层)**:
    -   **职责**: **项目的核心**，所有复杂的业务逻辑都在这一层实现。
    -   **逻辑**: Service 层会根据业务需求，协调调用一个或多个 `mapper` 接口来完成数据读写，也可能调用其他 `service` 或 `util` 类。它采用接口（如 `UserService`）和实现（如 `UserServiceImpl`）分离的设计模式，有利于解耦和测试。
    -   **示例**: `UserServiceImpl.java` 中的 `changePassword` 方法，需要先调用 `userMapper` 查询用户，然后使用 `PasswordEncoder` 验证旧密码，最后再调用 `userMapper` 更新新密码，这是一个典型的业务流程。

8.  **`util` (工具类)**:
    -   **职责**: 提供与业务无直接关系，但被广泛复用的功能。
    -   **示例**: `JwtTokenUtil.java` 封装了所有与 JWT 生成、解析和验证相关的静态方法，供 `AuthService` 和 `JwtRequestFilter` 调用。

#### 前端 (Frontend) - `Next.js App`

前端是基于 Next.js App Router 构建的单页应用 (SPA)，负责所有用户界面的渲染和交互。

```
frontend/src/
├── app/                # 1. 路由与页面核心
├── components/         # 2. 可复用UI组件
├── contexts/           # 3. 全局状态管理
├── lib/                # 4. 通用工具函数
├── services/           # 5. API服务层
└── types/              # 6. TypeScript类型定义
```

1.  **`app` (路由与页面)**:
    -   **职责**: Next.js 14 的核心，基于文件系统的路由。
    -   **逻辑**: 每个文件夹代表一个 URL 段。文件夹内的 `page.tsx` 文件就是该路由对应的页面组件。`layout.tsx` 定义了该路由及其子路由共享的布局。
    -   **示例**:
        -   `/app/profile/page.tsx`: 渲染"个人资料"页面的主组件。
        -   `/app/kb/[id]/page.tsx`: 一个动态路由，用于显示特定 ID 的知识库文章。
        -   `/app/layout.tsx`: **根布局**，定义了整个应用的 HTML 框架，并引入了 `AppProviders`。

2.  **`components` (UI组件)**:
    -   **职责**: 存放所有可复用的 React 组件。
    -   **逻辑**:
        -   `ui/`: 存放从 `shadcn/ui` 生成的基础组件（如 `Button`, `Input`, `Card`）。它们是构成界面的原子。
        -   根目录下的组件: **业务组件**，由基础组件组合而成，并包含特定的业务逻辑。
    -   **示例**: `ProductCard.tsx` 是一个业务组件，它使用 `Card` 和 `Button` 等基础组件来展示一个产品的具体信息，并处理"加入购物车"等交互。`DangerZone.tsx` 则封装了删除账户的所有UI和逻辑。

3.  **`contexts` (全局状态管理)**:
    -   **职责**: 使用 React Context API 在整个应用中共享和管理全局状态。
    -   **逻辑**: `AuthContext.tsx` 是最重要的 Context。它提供了一个 `user` 对象、`token`、`loading` 状态，以及 `login`, `logout` 等方法。应用的根布局 `layout.tsx` 使用 `AuthProvider` 包裹所有页面，因此任何子组件都可以通过 `useAuth()` hook 访问到当前的认证状态和用户信息。

4.  **`lib` (通用工具)**:
    -   **职责**: 存放不与特定组件或页面绑定的通用辅助函数。
    -   **示例**: `utils.ts` 中的 `cn` 函数，用于合并和条件化 Tailwind CSS 类名，在构建动态样式时非常有用。

5.  **`services` (API服务层)**:
    -   **职责**: **封装所有与后端 API 的通信**。
    -   **逻辑**: `api.ts` 文件是核心。它创建并导出一个 `axios` 实例。这个实例预配置了后端的 `baseURL` (`NEXT_PUBLIC_API_URL`)。更重要的是，它设置了一个**请求拦截器** (interceptor)，这个拦截器会在每个请求发送前，从 `localStorage` 中读取 JWT，并将其添加到 `Authorization` 请求头中。这使得组件在调用 API 时无需关心认证细节。

6.  **`types` (类型定义)**:
    -   **职责**: 存放共享的 TypeScript 类型定义。
    -   **逻辑**: 为从后端 API 获取的数据定义类型（如 `KnowledgeBaseArticle.ts`），可以确保在整个前端应用中数据类型的一致性，并获得强大的编辑器智能提示和类型检查。

---

### 5. API 参考 (核心)

| 路径 | 方法 | 描述 | 是否需要认证 |
| :--- | :--- | :--- | :--- |
| `/api/auth/register` | `POST` | 用户注册 | 否 |
| `/api/auth/login` | `POST` | 用户登录，返回 JWT | 否 |
| `/api/auth/verify` | `POST` | 验证邮箱验证码 | 否 |
| `/api/user/me` | `GET` | 获取当前登录用户的详细信息 | 是 |
| `/api/user/profile` | `PUT` | 更新当前用户的个人资料 | 是 |
| `/api/user/change-password` | `POST` | 修改密码 | 是 |
| `/api/user/avatar` | `POST` | 上传/更新用户头像 | 是 |
| `/api/kb/all` | `GET` | 获取所有知识库文章列表 | 否 |
| `/api/kb/{id}` | `GET` | 获取单篇知识库文章详情 | 否 |
| `/api/kb/search` | `GET` | 根据关键词搜索知识库文章 | 否 |
| `/api/products` | `GET` | 获取所有产品列表 | 否 |
| `/api/payment/create` | `POST` | 创建支付订单 | 是 |

---

## 🚀 部署与运维

### 1. 环境准备

在开始之前，请确保你的开发环境中安装了以下软件：
- [Java 17](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html) 或更高版本
- [Apache Maven](https://maven.apache.org/)
- [Node.js v18.x](https://nodejs.org/) 或更高版本
- [MySQL 8.0](https://dev.mysql.com/downloads/mysql/) 或更高版本
- [Redis](https://redis.io/download/)

### 2. 本地运行

#### 后端 (Backend)

1.  **配置 `application.yml`**:
    进入 `backend/src/main/resources/` 目录，打开 `application.yml` 文件。根据你的本地环境，修改以下配置：
    - `spring.datasource`: 你的 MySQL 数据库 URL、用户名和密码。
    - `spring.redis`: 你的 Redis 服务器地址、端口和密码。
    - `spring.mail`: 你的 SMTP 邮件服务配置（用于发送注册验证码）。
    - `jwt.secret`: 修改为一个自定义的、更安全的密钥。

2.  **启动后端服务**:
    在你的 IDE (如 IntelliJ IDEA) 中直接运行 `CommunityApplication.java` 文件，或者在 `backend` 目录下执行 Maven 命令：
    ```bash
    mvn spring-boot:run
    ```
    后端服务默认会运行在 `http://localhost:8080`。

#### 前端 (Frontend)

1.  **安装依赖**:
    进入 `frontend` 目录，执行以下命令安装所有依赖包：
    ```bash
    npm install
    ```

2.  **配置环境变量**:
    在 `frontend` 目录下，创建一个名为 `.env.local` 的文件，并添加以下内容。这个地址必须指向你的后端服务。
    ```
    NEXT_PUBLIC_API_URL=http://localhost:8080
    ```

3.  **启动前端开发服务器**:
    在 `frontend` 目录下，执行：
    ```bash
    npm run dev
    ```
    前端服务会运行在 `http://localhost:3000`。

现在，你可以通过浏览器访问 `http://localhost:3000` 来使用这个应用了。

### 3. 生产部署

#### 构建应用

- **构建前端**: 在 `frontend` 目录下执行 `npm run build`。这会生成一个优化的、可用于生产的 `.next` 目录。
- **构建后端**: 在 `backend` 目录下执行 `mvn clean package`。这会生成一个可执行的 `.jar` 文件在 `backend/target/` 目录下。

#### 部署架构与反向代理

在生产环境中，为了实现高性能和高可用，推荐使用 Nginx 作为反向代理，并使用进程管理器 (如 PM2, systemd) 来守护应用。

```mermaid
graph TD
    U["/用户浏览器/"] -- "https://yourdomain.com" --> N[Nginx<br/>(Port 80/443)]
    
    subgraph "服务器 (Your Server)"
        N -- "location /api/ { ... }<br/>(API 请求)" --> BE[Spring Boot App<br/>(localhost:8080)]
        N -- "location / { ... }<br/>(页面访问)" --> FE[Next.js App<br/>(localhost:3000)]

        subgraph "前端进程 (PM2)"
            FE
        end

        subgraph "后端进程 (systemd/java)"
            BE
        end

        DB[(MySQL Database)]
        RD[(Redis)]
    end

    FE <--> BE
    BE <--> DB
    BE <--> RD

    style N fill:#9f9,stroke:#333,stroke-width:2px
```

#### 服务器配置

1.  将后端生成的 `.jar` 文件和前端生成的 `.next` 目录及相关文件 (`public`, `package.json`, `next.config.mjs`) 上传到服务器。
2.  **重要**: 应用会在 `.jar` 文件所在的目录旁边寻找并创建以下文件夹用于持久化存储：
    - `kb_articles/`: 用于存放上传的知识库 Markdown 文件。
    - `storage/avatars/`: 用于存放上传的用户头像。
    请确保运行 `.jar` 文件的用户对该目录有**读写权限**。
3.  **运行应用**:
    - **后端**: 使用 `systemd` 或 `supervisor` 管理 `java -jar your-app.jar` 进程。
    - **前端**: 使用 `pm2` 管理 `npm start` 进程 (`pm2 start npm --name "frontend" -- run start`)。
4.  **配置 Nginx**:
    在 `/etc/nginx/sites-available/` 创建一个配置文件，并软链接到 `/etc/nginx/sites-enabled/`。

    **Nginx 核心配置示例 (`yourdomain.conf`)**:
    ```nginx
    server {
        listen 80;
        server_name yourdomain.com;

        # 根路径，代理到 Next.js 前端应用
        location / {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # API 路径，代理到 Spring Boot 后端应用
        location /api/ {
            proxy_pass http://localhost:8080/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 可选：代理静态文件（如头像），减轻后端压力
        location /avatars/ {
            alias /path/to/your/app/storage/avatars/;
            expires 30d;
        }
    }
    ```
    *这个配置将所有 `/api/` 开头的请求转发给后端，所有其他请求由前端处理，并直接高效地提供静态资源。别忘了使用 Certbot 配置 SSL/TLS 来启用 HTTPS。*
