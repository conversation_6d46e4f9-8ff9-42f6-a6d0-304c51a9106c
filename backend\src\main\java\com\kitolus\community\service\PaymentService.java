package com.kitolus.community.service;

import com.kitolus.community.dto.PaymentRequestDTO;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

public interface PaymentService {
    Map<String, Object> createOrder(Long productId, Long userId) throws Exception;
    
    Map<String, String> createPayment(PaymentRequestDTO paymentRequest, HttpServletRequest request) throws Exception;
    
    boolean handlePaymentNotification(Map<String, String> notificationData);

    String getOrderStatus(String orderId) throws Exception;
} 