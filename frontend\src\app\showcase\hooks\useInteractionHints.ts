import { useState, useEffect, useCallback } from 'react';

interface UseInteractionHintsOptions {
  storageKey?: string;
  autoHideDelay?: number;
  initialDelay?: number;
}

export const useInteractionHints = (options: UseInteractionHintsOptions = {}) => {
  const {
    storageKey = 'showcase-interaction-hints',
    autoHideDelay = 3000,
    initialDelay = 2000
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  // 检查是否已经显示过提示
  const checkHasSeenHints = useCallback(() => {
    try {
      return localStorage.getItem(storageKey) === 'true';
    } catch {
      return false;
    }
  }, [storageKey]);

  // 标记已看过提示
  const markHintsAsSeen = useCallback(() => {
    try {
      localStorage.setItem(storageKey, 'true');
    } catch {
      // 忽略localStorage错误
    }
  }, [storageKey]);

  // 显示提示
  const showHints = useCallback(() => {
    setIsVisible(true);
    setHasInteracted(false);
  }, []);

  // 隐藏提示
  const hideHints = useCallback(() => {
    setIsVisible(false);
    markHintsAsSeen();
  }, [markHintsAsSeen]);

  // 重置提示状态（开发时使用）
  const resetHints = useCallback(() => {
    try {
      localStorage.removeItem(storageKey);
      setIsVisible(true);
      setHasInteracted(false);
    } catch {
      // 忽略localStorage错误
    }
  }, [storageKey]);

  // 处理用户交互
  const handleUserInteraction = useCallback(() => {
    if (!hasInteracted && isVisible) {
      setHasInteracted(true);
      // 用户交互后延迟隐藏
      setTimeout(() => {
        hideHints();
      }, autoHideDelay);
    }
  }, [hasInteracted, isVisible, autoHideDelay, hideHints]);

  // 初始化提示显示
  useEffect(() => {
    if (!checkHasSeenHints()) {
      const timer = setTimeout(() => {
        showHints();
      }, initialDelay);

      return () => clearTimeout(timer);
    }
  }, [checkHasSeenHints, showHints, initialDelay]);

  // 监听用户交互事件
  useEffect(() => {
    if (!isVisible) return;

    const events = ['mousedown', 'touchstart', 'wheel', 'keydown'];
    
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [isVisible, handleUserInteraction]);

  return {
    isVisible,
    hasInteracted,
    showHints,
    hideHints,
    resetHints,
    handleUserInteraction
  };
};
