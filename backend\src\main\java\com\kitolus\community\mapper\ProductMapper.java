package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.Product;
import com.kitolus.community.dto.ProductDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    @Select("SELECT * FROM products WHERE id = #{id}")
    Product findByIdIncludeDeleted(@Param("id") Long id);

    @Select("SELECT * FROM products WHERE user_id = #{userId} AND deleted = false")
    List<Product> findByUserId(@Param("userId") Long userId);

    @Select("SELECT COUNT(*) FROM products WHERE user_id = #{userId} AND deleted = false")
    long countByUserId(@Param("userId") Long userId);

    @Select("SELECT " +
            "p.id, p.name, p.price, p.description, p.image_url, p.download_url, p.created_at, p.updated_at, p.reviewed_at, p.status, " +
            "p.rejection_reason, p.approval_notes, p.reviewed_by, " +
            "u.id as authorId, u.username as authorName, u.avatar_url as authorAvatarUrl " +
            "FROM products p " +
            "JOIN user u ON p.user_id = u.id " +
            "WHERE p.status = 'APPROVED' AND p.deleted = false " +
            "ORDER BY p.created_at DESC")
    List<ProductDTO> selectApprovedProductsWithAuthor();

    @Select("SELECT DISTINCT p.* FROM products p " +
            "JOIN orders o ON p.id = o.product_id " +
            "WHERE o.user_id = #{userId} AND o.status = 'PAID'")
    List<Product> findPurchasedByUserId(@Param("userId") Long userId);

    @Delete("DELETE FROM products WHERE id = #{id}")
    int physicalDeleteById(@Param("id") Long id);

    @Select("SELECT * FROM products WHERE user_id = #{userId}")
    List<Product> selectAllByUserIdIncludeDeleted(@Param("userId") Long userId);

    @Delete("DELETE FROM products WHERE user_id = #{userId}")
    int physicalDeleteByUserId(@Param("userId") Long userId);
} 