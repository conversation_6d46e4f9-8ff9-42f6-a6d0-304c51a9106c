'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Error logged internally for debugging
  }, [error]);

  return (
    <div className="container mx-auto px-4 md:px-6 py-32 md:py-40 text-center">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-bold text-destructive mb-4">糟糕！出错了</h1>
        <p className="text-muted-foreground mb-8">
          加载知识库时遇到了一个预期之外的问题。
        </p>
        <div className="bg-destructive-foreground/10 p-4 rounded-md mb-8">
          <p className="text-sm text-destructive font-mono">{error.message}</p>
        </div>
        <Button
          onClick={
            // Attempt to recover by trying to re-render the segment
            () => reset()
          }
        >
          再试一次
        </Button>
      </div>
    </div>
  );
}
