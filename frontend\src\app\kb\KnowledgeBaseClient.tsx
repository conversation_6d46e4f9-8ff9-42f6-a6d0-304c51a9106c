'use client';

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Search, BookCopy, Loader2, ServerCrash, BookOpen, RefreshCw, Upload } from 'lucide-react';
import { searchKb, getRandomArticles, getKbStats } from '@/services/api';
import debounce from 'lodash/debounce';
import useSWR from 'swr';
import { KnowledgeBaseArticle } from '@/types/KnowledgeBaseArticle';
import { SearchResult } from '@/types/SearchResult';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

export default function KnowledgeBaseClient() {
    const { user } = useAuth(); // Get user from AuthContext
    const searchParams = useSearchParams();
    const initialQuery = searchParams.get('q') || '';

    // State for featured articles
    const [articles, setArticles] = useState<KnowledgeBaseArticle[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // State for search functionality
    const [searchTerm, setSearchTerm] = useState(initialQuery);
    const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
    const [isSearching, setIsSearching] = useState(false);
    const [searchError, setSearchError] = useState<string | null>(null);
    const isMounted = useRef(false); // To prevent initial search trigger on mount for the same query

    // State for file upload
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);

    // Fetch KB Stats
    const { data: stats, error: statsError } = useSWR('kb-stats', getKbStats);

    const fetchFeaturedArticles = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const articlesData = await getRandomArticles(4);
            setArticles(articlesData);
        } catch (err) {
            setError('无法加载推荐文章，请稍后再试。');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // The core search logic, debounced
    const debouncedSearch = useCallback(
        debounce(async (query: string) => {
            // Update URL only when the search is actually performed
            const params = new URLSearchParams(window.location.search);
            if (query) {
                params.set('q', query);
            } else {
                params.delete('q');
            }
            // Use replaceState to avoid page reloads and re-renders from router
            window.history.replaceState({}, '', `${window.location.pathname}?${params.toString()}`);

            if (!query) {
                setSearchResults(null);
                setIsSearching(false);
                return;
            }
            setIsSearching(true);
            setSearchError(null);
            try {
                const results = await searchKb(query);
                setSearchResults(results);
            } catch (err) {
                setSearchError('搜索失败，请稍后重试。');
                setSearchResults(null);
            } finally {
                setIsSearching(false);
            }
        }, 300), // 300ms debounce delay
        []
    );

    // Effect to run search when searchTerm changes
    useEffect(() => {
        // This check prevents running a search with the initial URL query on mount,
        // as it's already handled by the initial state. We only want to run it on user changes.
        if (isMounted.current) {
            debouncedSearch(searchTerm);
        } else {
            isMounted.current = true;
            // If there's an initial query, perform a search right away
            if(searchTerm) {
                debouncedSearch(searchTerm);
            }
        }
    }, [searchTerm, debouncedSearch]);


    // Initial data fetch for featured articles
    useEffect(() => {
        fetchFeaturedArticles();
    }, [fetchFeaturedArticles]);


    // This is the CORRECT way to handle input changes for controlled components
    // especially with IME support. It only updates the state.
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    // --- File Upload Logic ---
    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];
            if (file.type === 'text/markdown' || file.name.endsWith('.md')) {
                handleFileUpload(file);
            } else {
                setUploadError("请选择一个 Markdown 文件 (.md)");
            }
        }
    };

    const handleFileUpload = async (file: File) => {
        setIsUploading(true);
        setUploadError(null);
        const formData = new FormData();
        formData.append('file', file);
        try {
            const response = await apiService.post('/api/kb/upload', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            await fetchFeaturedArticles(); // Refresh list on success
        } catch (error: any) {
            console.error('An error occurred during upload:', error);
            setUploadError(`上传失败: ${error.response?.data?.message || error.message}`);
        } finally {
            setIsUploading(false);
            // Reset file input
            const fileInput = document.getElementById('file-upload') as HTMLInputElement;
            if (fileInput) fileInput.value = '';
        }
    };
    
    // --- Render Logic ---
    const renderArticleList = (articles: KnowledgeBaseArticle[], title: string) => {
        if (!articles || articles.length === 0) {
            return null;
        }
        return (
            <div className="mt-8">
                <h3 className="text-2xl font-semibold text-foreground border-b pb-3 mb-6">{title}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {articles.map((article) => (
                        <Link href={`/kb/${article.id}`} key={article.id}>
                            <Card className="bg-background/50 border-border h-full hover:shadow-lg hover:border-primary transition-all duration-300 transform hover:-translate-y-1">
                                <CardHeader>
                                    <CardTitle className="flex items-start gap-3">
                                        <BookCopy className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                                        <span>{article.title}</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground text-sm line-clamp-3">
                                        {article.summary}
                                    </p>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>
        );
    };

    const hasSearchResults = searchResults && (searchResults.titleMatches.length > 0 || searchResults.contentMatches.length > 0);

    return (
        <div className="container mx-auto px-4 md:px-6 py-32 relative">
            {user && (user.role === 'ADMIN' || user.role === 'EDITOR') && (
            <div className="absolute top-16 right-10 md:right-16">
                <Button variant="outline" onClick={() => document.getElementById('file-upload')?.click()} disabled={isUploading}>
                    {isUploading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : <Upload className="mr-2 h-5 w-5" />}
                    {isUploading ? '上传中...' : '上传知识文档'}
                </Button>
                <input
                    type="file"
                    id="file-upload"
                    aria-label="File upload"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                    accept=".md,text/markdown"
                />
            </div>
            )}

            <div className="max-w-3xl mx-auto text-center mb-16">
                <div className="flex items-center justify-center gap-4 mb-4">
                    <h1 className="text-4xl md:text-5xl font-bold text-foreground">知识库</h1>
                    {stats && (
                        <Badge variant="secondary" className="text-lg">
                            共 {stats.totalArticles} 篇
                        </Badge>
                    )}
                </div>
                <p className="text-muted-foreground">搜索社区分享的所有指南、教程和技术蓝图。</p>
                <div className="mt-8 flex gap-2">
                    <div className="relative flex-grow">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                        <Input
                            type="search"
                            placeholder="在所有文章中即时搜索..."
                            className="w-full pl-10"
                            value={searchTerm}
                            onChange={handleSearchChange}
                        />
                    </div>
                     <Button variant="outline" onClick={fetchFeaturedArticles} disabled={isLoading || isSearching}>
                        <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                </div>
            </div>

            {(uploadError || error) && (
                <div className="text-center text-red-500 my-4">
                    <p>{uploadError || error}</p>
                </div>
            )}

            {isSearching && (
                <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                    <p className="text-muted-foreground mt-4">搜索中...</p>
                </div>
            )}
            
            {!isSearching && searchTerm && !searchError && !hasSearchResults && (
                 <div className="text-center">
                    <p className="text-muted-foreground">未找到与 "{searchTerm}" 相关的文章。</p>
                </div>
            )}

            {hasSearchResults && (
                <div className="mt-8">
                    {renderArticleList(searchResults.titleMatches, '标题匹配的结果')}
                    {renderArticleList(searchResults.contentMatches, '内容匹配的结果')}
                </div>
            )}

            {!searchTerm && !isSearching && !error && (
                 <>
                    {isLoading ? (
                         <div className="text-center">
                            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                            <p className="text-muted-foreground mt-4">加载中...</p>
                        </div>
                    ) : (
                       renderArticleList(articles, '精选文章')
                    )}
                </>
            )}

             {error && !isLoading && (
                <div className="text-center text-red-500 flex flex-col items-center">
                    <ServerCrash className="h-12 w-12 mb-4" />
                    <p>{error}</p>
                </div>
            )}

            {/* Link to all articles page */}
            {!searchTerm && (
                 <div className="text-center mt-16">
                    <Link href="/kb/all">
                        <Button variant="outline">
                            <BookOpen className="mr-2 h-5 w-5" /> 浏览所有卡片
                        </Button>
                    </Link>
                </div>
            )}
        </div>
    );
} 