/**
 * 现代化虚拟滚动消息列表
 * 优势：处理大量消息、流畅滚动、手势支持、无障碍访问
 */

import React, { useMemo, useCallback, useRef, useEffect } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useGesture } from '@use-gesture/react';
import { motion, useSpring, useMotionValue } from 'framer-motion';
import { Message, MessageType, MessageStatus } from '@/types/Message';
import { MessageReactions } from './MessageReactions';

interface VirtualMessageListProps {
  messages: Message[];
  onLoadMore: () => void;
  onMessageAction: (action: string, messageId: number) => void;
  hasMore: boolean;
  loading: boolean;
  currentUserId: number;
}

export const VirtualMessageList: React.FC<VirtualMessageListProps> = ({
  messages,
  onLoadMore,
  onMessageAction,
  hasMore,
  loading,
  currentUserId,
}) => {
  const listRef = useRef<HTMLDivElement>(null);
  const [selectedMessageId, setSelectedMessageId] = React.useState<number | null>(null);
  const [showReactions, setShowReactions] = React.useState<number | null>(null);

  // 虚拟滚动配置
  const rowVirtualizer = useVirtualizer({
    count: messages.length,
    getScrollElement: () => listRef.current,
    estimateSize: useCallback(() => 80, []), // 估算每项高度
    overscan: 5, // 预渲染项目数
  });

  // 手势处理
  const y = useMotionValue(0);
  const pullToRefreshThreshold = 100;
  
  const bind = useGesture({
    onDrag: ({ down, movement: [, my], velocity: [, vy], direction: [, dy] }) => {
      // 下拉刷新逻辑
      if (dy > 0 && listRef.current?.scrollTop === 0) {
        y.set(down ? Math.max(0, my) : 0);
        
        if (!down && my > pullToRefreshThreshold) {
          onLoadMore();
        }
      }
    },
    onWheel: ({ delta: [, dy] }) => {
      // 滚轮滚动到顶部时加载更多
      if (dy < 0 && listRef.current?.scrollTop === 0 && hasMore) {
        onLoadMore();
      }
    },
  });

  // 消息项渲染器 - 适配 @tanstack/react-virtual
  const MessageRow = React.memo(({ index }: { index: number }) => {
    const message = messages[index];
    if (!message) return null;

    const isOwn = message.senderId === currentUserId;
    const showAvatar = !isOwn && (index === 0 || messages[index - 1]?.senderId !== message.senderId);

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        className="px-4 py-2"
      >
        <MessageItem
          message={message}
          isOwn={isOwn}
          showAvatar={showAvatar}
          onAction={onMessageAction}
          onLongPress={() => handleMessageLongPress(message)}
          onDoubleClick={() => handleMessageDoubleClick(message)}
        />

        {/* 消息反应 */}
        {showReactions === message.id && (
          <MessageReactions
            messageId={message.id}
            onReact={(emoji) => handleReaction(message.id, emoji)}
            onClose={() => setShowReactions(null)}
          />
        )}
      </motion.div>
    );
  });

  // 长按消息处理
  const handleMessageLongPress = useCallback(async (message: Message) => {
    setSelectedMessageId(message.id);

    // 显示上下文菜单
    setShowReactions(message.id);
  }, []);

  // 双击消息处理
  const handleMessageDoubleClick = useCallback((message: Message) => {
    if (message.senderId !== currentUserId) {
      // 快速回复
      onMessageAction('quick_reply', message.id);
    } else {
      // 编辑消息
      onMessageAction('edit', message.id);
    }
  }, [currentUserId, onMessageAction]);

  // 消息反应处理
  const handleReaction = useCallback((messageId: number, emoji: string) => {
    onMessageAction('react', messageId);
    setShowReactions(null);
  }, [onMessageAction]);



  // 键盘导航支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setSelectedMessageId(null);
        setShowReactions(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // 自动滚动到底部（新消息）
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.senderId === currentUserId) {
        // 自己发送的消息，滚动到底部
        rowVirtualizer.scrollToIndex(messages.length - 1, { align: 'end' });
      }
    }
  }, [messages.length, currentUserId]);

  return (
    <div className="flex-1 relative overflow-hidden">
      {/* 下拉刷新指示器 */}
      <motion.div
        style={{ y }}
        className="absolute top-0 left-0 right-0 z-10 flex justify-center py-2"
      >
        <motion.div
          animate={{ rotate: y.get() > pullToRefreshThreshold ? 180 : 0 }}
          className="w-6 h-6 text-muted-foreground"
        >
          ↓
        </motion.div>
      </motion.div>

      {/* 虚拟滚动列表 */}
      <div
        ref={listRef}
        className="h-full overflow-auto"
        {...bind()}
        role="log"
        aria-label="消息列表"
        aria-live="polite"
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map((virtualRow) => (
            <div
              key={virtualRow.index}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              <MessageRow index={virtualRow.index} />
            </div>
          ))}
        </div>

        {/* 加载更多指示器 */}
        {loading && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        )}
      </div>



      {/* 无障碍访问支持 */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {messages.length > 0 && `共 ${messages.length} 条消息`}
      </div>
    </div>
  );
};

// 消息项组件
const MessageItem: React.FC<{
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
  onAction: (action: string, messageId: number, data?: any) => void;
  onLongPress: () => void;
  onDoubleClick: () => void;
}> = ({ message, isOwn, showAvatar, onAction, onLongPress, onDoubleClick }) => {
  const longPressTimer = useRef<NodeJS.Timeout>();

  const handleMouseDown = () => {
    longPressTimer.current = setTimeout(onLongPress, 500);
  };

  const handleMouseUp = () => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }
  };

  const handleTouchStart = () => {
    longPressTimer.current = setTimeout(onLongPress, 500);
  };

  const handleTouchEnd = () => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }
  };

  return (
    <div
      className={`flex ${isOwn ? 'justify-end' : 'justify-start'} group`}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onDoubleClick={onDoubleClick}
    >
      {/* 头像 */}
      {!isOwn && showAvatar && (
        <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
          {message.sender?.username?.charAt(0).toUpperCase() || 'U'}
        </div>
      )}

      {/* 消息气泡 */}
      <div
        className={`max-w-[70%] rounded-lg px-3 py-2 ${
          isOwn
            ? 'bg-primary text-primary-foreground'
            : 'bg-muted text-muted-foreground'
        }`}
      >
        <div className="text-sm">{message.content}</div>
        <div className="text-xs opacity-70 mt-1">
          {new Date(message.createdAt).toLocaleTimeString()}
        </div>
      </div>

      {/* 消息状态指示器 */}
      {isOwn && (
        <div className="ml-2 flex items-end">
          <div className="w-4 h-4 text-xs opacity-50">
            {message.status === MessageStatus.READ && '✓✓'}
            {message.status === MessageStatus.UNREAD && '✓'}
          </div>
        </div>
      )}
    </div>
  );
};
