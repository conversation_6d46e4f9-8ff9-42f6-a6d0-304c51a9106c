package com.kitolus.community.service;

import com.kitolus.community.entity.Message;
import com.kitolus.community.entity.User;
import com.kitolus.community.mapper.MessageMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 消息服务
 * 处理消息的CRUD操作和业务逻辑
 */
@Service
@Transactional
public class MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageService.class);

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private WebSocketMessageService webSocketMessageService;

    /**
     * 发送私信
     */
    public Message sendPrivateMessage(Long senderId, Long receiverId, String title, String content) {
        try {
            // 简化验证，暂时跳过用户存在性检查
            // TODO: 添加用户存在性验证

            // 创建消息
            Message message = new Message(senderId, receiverId, Message.MessageType.PRIVATE, content);
            message.setTitle(title);
            
            // 保存到数据库
            messageMapper.insert(message);

            // 🚀 实时推送给接收者（WebSocket推送）
            try {
                boolean pushSuccess = webSocketMessageService.pushNewMessage(message);
                if (pushSuccess) {
                    logger.info("消息实时推送成功 - 发送者: {}, 接收者: {}", senderId, receiverId);
                } else {
                    logger.debug("消息实时推送失败，接收者可能不在线 - 发送者: {}, 接收者: {}", senderId, receiverId);
                }
            } catch (Exception e) {
                logger.error("消息实时推送异常", e);
                // 推送失败不影响消息发送，继续执行
            }

            logger.info("Private message sent from user {} to user {}", senderId, receiverId);
            return message;
            
        } catch (Exception e) {
            logger.error("Error sending private message", e);
            throw new RuntimeException("Failed to send message", e);
        }
    }

    /**
     * 发送系统消息
     */
    public Message sendSystemMessage(Long receiverId, String title, String content) {
        try {
            Message message = new Message();
            message.setSenderId(0L); // 系统消息的发送者ID为0
            message.setReceiverId(receiverId);
            message.setMessageType(Message.MessageType.SYSTEM);
            message.setTitle(title);
            message.setContent(content);
            
            messageMapper.insert(message);
            
            // TODO: 实时推送（暂时禁用）
            
            logger.info("System message sent to user {}", receiverId);
            return message;
            
        } catch (Exception e) {
            logger.error("Error sending system message", e);
            throw new RuntimeException("Failed to send system message", e);
        }
    }

    /**
     * 发送广播消息
     */
    public Message sendBroadcastMessage(String title, String content) {
        try {
            Message message = new Message();
            message.setSenderId(0L); // 系统发送
            message.setReceiverId(null); // 广播消息无特定接收者
            message.setMessageType(Message.MessageType.BROADCAST);
            message.setTitle(title);
            message.setContent(content);
            
            messageMapper.insert(message);
            
            // TODO: 广播给所有在线用户（暂时禁用）
            
            logger.info("Broadcast message sent: {}", title);
            return message;
            
        } catch (Exception e) {
            logger.error("Error sending broadcast message", e);
            throw new RuntimeException("Failed to send broadcast message", e);
        }
    }

    // 注意：通知功能已移至NotificationService，请使用NotificationService.createNotificationXXX方法

    /**
     * 获取用户消息列表
     */
    @Transactional(readOnly = true)
    public Page<Message> getUserMessages(Long userId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page + 1, size); // MyBatis Plus页码从1开始
        return messageMapper.findUserMessages(pageRequest, userId);
    }

    /**
     * 获取用户未读消息
     */
    @Transactional(readOnly = true)
    public List<Message> getUnreadMessages(Long userId) {
        return messageMapper.findUnreadMessages(userId);
    }

    /**
     * 获取未读消息数量
     */
    @Transactional(readOnly = true)
    public Long getUnreadMessageCount(Long userId) {
        return messageMapper.countUnreadMessages(userId);
    }

    /**
     * 获取两个用户之间的对话
     */
    @Transactional(readOnly = true)
    public List<Message> getConversation(Long user1Id, Long user2Id) {
        return messageMapper.findConversationBetweenUsers(user1Id, user2Id);
    }

    /**
     * 标记消息为已读
     */
    public boolean markMessageAsRead(Long messageId, Long userId) {
        try {
            Message message = messageMapper.selectById(messageId);
            if (message != null && message.getReceiverId().equals(userId) && !message.getIsRead()) {
                message.markAsRead();
                messageMapper.updateById(message);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("Error marking message as read", e);
            return false;
        }
    }

    /**
     * 标记对话为已读
     */
    public int markConversationAsRead(Long userId, Long senderId) {
        try {
            return messageMapper.markConversationAsRead(userId, senderId);
        } catch (Exception e) {
            logger.error("Error marking conversation as read", e);
            return 0;
        }
    }

    /**
     * 获取用户联系人列表
     */
    @Transactional(readOnly = true)
    public List<Object[]> getUserContacts(Long userId) {
        return messageMapper.findUserContacts(userId);
    }

    /**
     * 获取指定时间后的新消息（用于轮询）
     */
    @Transactional(readOnly = true)
    public List<Message> getMessagesSince(Long userId, LocalDateTime since) {
        return messageMapper.findMessagesSince(userId, since);
    }

    /**
     * 获取用户私信列表
     */
    @Transactional(readOnly = true)
    public Page<Message> getPrivateMessages(Long userId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page + 1, size);
        return messageMapper.findPrivateMessages(pageRequest, userId);
    }

    /**
     * 获取用户系统消息列表
     */
    @Transactional(readOnly = true)
    public Page<Message> getSystemMessages(Long userId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page + 1, size);
        return messageMapper.findSystemMessages(pageRequest, userId);
    }

    /**
     * 根据ID获取消息
     */
    @Transactional(readOnly = true)
    public Message getMessageById(Long messageId) {
        return messageMapper.selectById(messageId);
    }

    /**
     * 删除消息
     */
    public boolean deleteMessage(Long messageId, Long userId) {
        try {
            Message message = messageMapper.selectById(messageId);
            if (message != null) {
                // 只有发送者或接收者可以删除消息
                if (message.getSenderId().equals(userId) ||
                    (message.getReceiverId() != null && message.getReceiverId().equals(userId))) {

                    // 软删除：更新状态为DELETED
                    message.setStatus(Message.MessageStatus.DELETED);
                    messageMapper.updateById(message);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("Error deleting message", e);
            return false;
        }
    }

    /**
     * 标记消息为已读
     */
    @Transactional
    public void markAsRead(Long messageId) {
        try {
            Message message = messageMapper.selectById(messageId);
            if (message != null) {
                message.setIsRead(true);
                message.setReadAt(java.time.LocalDateTime.now());
                messageMapper.updateById(message);
                logger.info("Message marked as read: {}", messageId);
            }
        } catch (Exception e) {
            logger.error("Error marking message as read", e);
        }
    }

    /**
     * 获取用户的对话列表（按对话伙伴分组）
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getConversations(Long userId) {
        return messageMapper.findConversations(userId);
    }

    /**
     * 获取与特定用户的对话消息
     */
    @Transactional(readOnly = true)
    public Page<Message> getConversationMessages(Long userId, Long otherUserId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page + 1, size);
        return messageMapper.findConversationMessages(pageRequest, userId, otherUserId);
    }

    /**
     * 批量删除与特定用户的所有对话消息
     */
    @Transactional
    public int deleteConversation(Long userId, Long otherUserId) {
        try {
            // 删除双方的所有对话消息
            int deletedCount = messageMapper.deleteConversationMessages(userId, otherUserId);
            logger.info("用户 {} 删除了与用户 {} 的 {} 条对话消息", userId, otherUserId, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            logger.error("删除对话消息失败: userId={}, otherUserId={}", userId, otherUserId, e);
            throw new RuntimeException("删除对话消息失败", e);
        }
    }
}
