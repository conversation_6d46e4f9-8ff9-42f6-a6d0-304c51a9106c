import { User } from './User';
import { CommunityTopic } from './CommunityTopic';
import { CommunityComment } from './CommunityComment';

export interface CommunityPost {
  id: number;
  title: string;
  content: string;
  authorId: number;
  topicId: number;
  likes: number;
  comments: CommunityComment[];
  createdAt: string;
  updatedAt?: string;
  author: User;
  topic: CommunityTopic;
  likedByCurrentUser: boolean;
  mentionedUsers?: string[];
  isPinned?: boolean;
  pinnedAt?: string;
  pinnedBy?: number;
  _count: {
    comments: number;
    likes: number;
  };
}