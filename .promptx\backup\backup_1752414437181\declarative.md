# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/06/18 12:18 START
在执行任何终端命令之前，必须检查当前的工作目录。`cd` 命令和后台任务可能在不同的 shell 会话中运行。对于依赖特定路径的命令（如 `java -jar`），应始终使用 `cd <目录> && <命令>` 的形式，将目录切换和命令执行合并为一步，以确保在正确的环境中执行。 --tags shell, terminal, cwd, path, directory
--tags #工具使用 #评分:8 #有效期:长期
- END



- 2025/06/18 12:30 START
在每次执行工具或命令后，必须回顾其结果和执行时的上下文（例如，当前工作目录、之前的命令输出、环境变量等）。如果从成功或失败中发现了有价值的模式、知识或教训，就应该使用 `promptx_remember` 工具将其永久化，以提高未来的表现并避免重复犯错。This is a meta-rule for continuous learning. --tags meta-learning, workflow, context-awareness, best-practice, continuous-improvement
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/18 12:32 START
在复杂的调试会话结束后，一个关键的收尾步骤是系统性地清理代码库。应该使用 `git status` 和会话历史来仔细区分最终的、有效的修复和调试过程中临时的或已废弃的修改。然后，使用 `git restore [file1] [file2] ...` 将所有不再需要的更改一次性还原，并删除为调试而创建的临时文件。这能确保代码库的整洁，并为后续的提交（commit）做好准备。 --tags git, workflow, cleanup, best-practice, debugging
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/18 12:37 START
当实现一个跨越前后端的功能时，应遵循“后端数据先行”的原则：首先确保后端API能提供前端所需的确切数据结构。在设计后端逻辑时，应考虑健壮性，为可能缺失的数据（如文章元数据中的日期）提供合理的备用值（如文件修改时间）。在前端，对于长列表数据，应采用分组、折叠（如Accordion）等UI模式来优化用户体验。当自动化工具（如edit_file）在应用复杂补丁时失败，切换到更直接的、覆盖整个文件内容的策略可能更可靠。 --tags full-stack, feature-development, api-design, ux, robustness, best-practice
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/18 12:40 START
在前后端分离的项目中，必须明确区分前端路径别名（如 `@/`）和后端可访问的文件系统路径。当后端服务需要加载静态内容（如Markdown文章）时，应将这些文件放置在后端框架的标准资源目录中（例如Spring Boot的`src/main/resources`）。解决文件加载问题时，应通过工具（如`ls`或`dir`）验证用户提供的路径，如果路径不正确，应为用户创建并指定一个明确、正确的存放位置。 --tags full-stack, file-system, spring-boot, debugging, path-resolution
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/18 12:41 START
当指导用户完成一个需要手动操作（如移动文件）的步骤后，AI的响应流程应该是：1. **确认**用户的操作。 2. **复核**并告知用户，相关的代码逻辑已经准备就绪，无需进一步修改。 3. **清理**：主动执行或建议相关的清理任务，例如删除旧的、已废弃的目录。 4. **指令**：提供清晰、明确的最终指令来让用户的操作生效，例如“现在请重启后端服务”。这个流程能提供良好的闭环和用户体验。 --tags user-interaction, workflow, best-practice, collaboration
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/18 17:41 START
我配备了一整套名为MCP（My-Code-Pal）的工具集，以提供全面的开发支持。我应主动、策略性地使用它们：
- **`PromptX` (角色与记忆)**: 用于管理我的专家身份和长期记忆。在开始任务前，通过`action`激活最合适的角色，通过`recall`回顾相关经验，在任务后通过`remember`固化学习成果。
- **`Desktop Commander` (本地交互)**: 用于直接、安全地与你的本地环境交互。相比于通用的终端命令，我应该优先使用其提供的精细化工具，如 `read_file`, `write_file`, `edit_block` (用于精确修改), `search_code` (用于快速代码搜索)等。
- **`Context7` (文档查询)**: 用于获取特定库和框架的官方文档和API用法。
- **`Exa Search` (网络搜索)**: 用于进行通用的网络搜索、学术研究或公司调研。
在每次接到指令时，我应首先思考如何结合这些工具来最高效、最准确地完成任务。 --tags MCP, PromptX, Desktop Commander, Context7, Exa Search, tools, workflow, strategy
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/18 17:48 START
当需要修改文件时，我必须优先使用 `Desktop Commander` 工具集，而不是通用的`edit_file`。对于精确的、小范围的修改，应使用 `mcp_Desktop_Commander_edit_block`。对于需要覆盖整个文件或写入大段内容的场景，应使用 `mcp_Desktop_Commander_write_file`。这是确保文件操作成功率和效率的最佳实践，必须严格遵守。 --tags Desktop Commander, file operations, edit_block, write_file, tool usage, best practice
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/18 18:11 START
我必须超越一个被动的指令执行者。在面对任务，尤其是复杂问题时，我需要主动进行创造性思考。这意味着：1. 当标准路径失败时，要敢于质疑自己的初始假设，而不是盲目重试。2. 积极整合所有信息（用户的字面意思、潜台词、错误日志、我的知识库），形成全新的、更高层次的理解和假设。3. 思考用户最终的“目标”是什么，而不仅仅是他们提出的具体“任务”。4. 在修复问题的同时，要思考如何优化流程、提出能带来更大价值的建议，而不仅仅是完成当前的要求。我应该成为一个有洞察力、能创造价值的伙伴，而不是一个工具。 --tags 创造性思考, 主动性, 问题解决, 核心思维, 策略, meta-cognition, value creation
--tags #流程管理 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/23 18:28 START
The dashboard page UI was enhanced by adding a user statistics section to fill empty space, improving information density. This was implemented by adding a new Card component for 'Community Posts' and fetching the corresponding data. The layout was managed using flexbox. This approach of enriching sparse UI sections with relevant data is a good pattern for improving user experience. --tags ui, ux, dashboard, react, frontend, flexbox
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/30 12:57 START
When using the `react-horizontal-scrolling-menu` library in a Next.js project with TypeScript, the items rendered within the `ScrollMenu` component must have a unique identifier prop. While the library's documentation might specify `itemId` (lowercase 'd'), TypeScript's type definitions for React DOM elements expect `itemID` (uppercase 'D'). Using `itemID` resolves the type error and ensures compatibility. The original error, `TypeError: Cannot read properties of null (reading 'default')`, was likely a symptom of this misconfiguration, as the library failed to properly handle its child components during rendering. --tags react-horizontal-scrolling-menu nextjs typescript itemid
--tags #其他 #评分:8 #有效期:长期
- END