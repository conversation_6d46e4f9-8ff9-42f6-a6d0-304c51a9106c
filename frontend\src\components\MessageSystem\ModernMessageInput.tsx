'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Smile,
  Mic,
  MicOff,
  Type,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useSendMessage } from '@/hooks/useMessageQueries';
import { MessageType, MessagePriority } from '@/types/Message';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ModernMessageInputProps {
  conversationId?: string;
  receiverId?: number;
  onMessageSent?: () => void;
  placeholder?: string;
  className?: string;
}

export const ModernMessageInput: React.FC<ModernMessageInputProps> = ({
  conversationId,
  receiverId,
  onMessageSent,
  placeholder = "输入消息...",
  className
}) => {
  const [content, setContent] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const sendMessageMutation = useSendMessage();

  // 自动调整文本框高度
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);



  // 输入处理
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setContent(value);
    
    // 输入状态指示
    setIsTyping(true);
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1000);

  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!content.trim()) return;

    try {
      await sendMessageMutation.mutateAsync({
        type: MessageType.PRIVATE,
        receiverId,
        title: '私信',
        content: content.trim(),
        priority: MessagePriority.NORMAL,
      });

      setContent('');
      onMessageSent?.();

      // 聚焦回输入框
      textareaRef.current?.focus();
    } catch (error) {
      toast.error('消息发送失败，请重试');
    }
  };

  // 键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    

  };

  // 语音录制（模拟）
  const toggleRecording = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      toast.info('开始录音...');
    } else {
      toast.info('录音结束');
      // 这里应该处理语音转文字
    }
  };



  return (
    <div className={cn("relative", className)}>

      {/* 主输入区域 */}
      <div className="flex items-end gap-2 p-3 bg-background border border-border rounded-lg">
        {/* 附件按钮 */}
        <Button
          variant="ghost"
          size="icon"
          className="w-8 h-8 shrink-0"
        >
          <Paperclip className="w-4 h-4" />
        </Button>

        {/* 文本输入 */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="min-h-[40px] max-h-32 resize-none border-0 focus-visible:ring-0 p-0"
            rows={1}
          />
          
          {/* 输入状态指示器 */}
          {isTyping && (
            <div className="absolute bottom-1 right-1">
              <Type className="w-3 h-3 text-muted-foreground animate-pulse" />
            </div>
          )}
        </div>

        {/* 表情按钮 */}
        <Button
          variant="ghost"
          size="icon"
          className="w-8 h-8 shrink-0"
        >
          <Smile className="w-4 h-4" />
        </Button>

        {/* 语音按钮 */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleRecording}
          className={cn(
            "w-8 h-8 shrink-0",
            isRecording && "text-red-500 bg-red-500/10"
          )}
        >
          {isRecording ? (
            <MicOff className="w-4 h-4" />
          ) : (
            <Mic className="w-4 h-4" />
          )}
        </Button>

        {/* 发送按钮 */}
        <Button
          onClick={handleSendMessage}
          disabled={!content.trim() || sendMessageMutation.isPending}
          size="icon"
          className="w-8 h-8 shrink-0"
        >
          {sendMessageMutation.isPending ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* 字数统计 */}
      <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-2">
          {isTyping && (
            <Badge variant="secondary" className="text-xs">
              正在输入...
            </Badge>
          )}
        </div>
        <span>{content.length}/1000</span>
      </div>
    </div>
  );
};
