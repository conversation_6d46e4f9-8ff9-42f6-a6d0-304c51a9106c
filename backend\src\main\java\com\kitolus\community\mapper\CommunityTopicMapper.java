package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.CommunityTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CommunityTopicMapper extends BaseMapper<CommunityTopic> {

    @Select("SELECT * FROM community_topic WHERE name = #{name} LIMIT 1")
    CommunityTopic findByName(@Param("name") String name);
} 