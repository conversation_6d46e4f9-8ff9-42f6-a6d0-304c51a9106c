{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "rimraf ./.next && next dev --turbo -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@auth/prisma-adapter": "^2.9.1", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^5.1.1", "@multiavatar/multiavatar": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.19.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/lodash": "^4.17.17", "@types/loglevel": "^1.5.4", "@types/qrcode.react": "^3.0.0", "@types/recharts": "^2.0.1", "@use-gesture/react": "^10.3.1", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "graphql": "^16.11.0", "graphql-ws": "^6.0.6", "jimp": "^1.6.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "loglevel": "^1.9.2", "lucide-react": "^0.395.0", "music-metadata": "^11.2.3", "next": "14.2.4", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "postprocessing": "^6.37.4", "prisma": "^6.9.0", "qrcode.react": "^4.2.0", "react": "^18", "react-confetti": "^6.4.0", "react-dom": "^18", "react-hook-form": "^7.58.1", "react-horizontal-scrolling-menu": "^8.2.0", "react-image-crop": "^11.0.10", "react-is": "^19.1.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "recharts": "^3.0.2", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.0", "sonner": "^2.0.5", "swiper": "^11.2.8", "swr": "^2.3.3", "tailwind-merge": "^2.3.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "use-sound": "^5.0.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/classnames": "^2.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/three": "^0.159.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.4", "glob": "^11.0.3", "postcss": "^8.4.38", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}