package com.kitolus.community.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriter;
import javax.imageio.IIOImage;
import javax.imageio.ImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.awt.Color;
import java.awt.Graphics2D;

@Service
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);
    private final Path fileStorageLocation;
    
    // Now uses the unified storage-base-path
    public FileStorageService(@Value("${file-storage.storage-base-path}") String basePath) {
        this.fileStorageLocation = Paths.get(basePath).toAbsolutePath().normalize();
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("无法创建用于存储上传文件的根目录。", ex);
        }
    }

    // Original method, now defaults to the "avatars" subdirectory
    public String storeFile(MultipartFile file, String baseFileName) {
        return storeFile(file, baseFileName, "avatars");
    }

    // Overloaded method to handle subdirectories (e.g., "avatars", "banners", "products")
    public String storeFile(MultipartFile file, String baseFileName, String subDirectory) {
        try {
            Path targetDirectory = this.fileStorageLocation.resolve(subDirectory);
            Files.createDirectories(targetDirectory);

            // Get original file extension
            String originalFileName = file.getOriginalFilename();
            String extension = "";
            if (originalFileName != null && originalFileName.contains(".")) {
                extension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }

            // The new filename will be the base name with the original extension.
            String newFileName;
            Path targetLocation;

            logger.info("准备将文件写入到: {}", this.fileStorageLocation.resolve(subDirectory).toAbsolutePath().toString());

            // Delete old files with the same base name in the specific subdirectory
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(targetDirectory, baseFileName + ".*")) {
                for (Path entry : stream) {
                    Files.delete(entry);
                    logger.info("已删除旧文件: {}", entry.toString());
                }
            }

            // Convert to a compressed JPEG instead of WebP
            newFileName = baseFileName + ".jpg";
            targetLocation = targetDirectory.resolve(newFileName);
            
            logger.info("准备将图片压缩为JPEG并写入到: {}", targetLocation.toAbsolutePath().toString());

            try {
                byte[] fileBytes = file.getBytes();
                BufferedImage image = ImageIO.read(new ByteArrayInputStream(fileBytes));

                if (image == null) {
                    throw new IOException("无法将上传的文件解码为图片。可能文件已损坏或格式不受支持。");
                }
                
                BufferedImage finalImage = image;
                // Handle transparency for formats like PNG -> JPEG conversion to avoid black backgrounds
                if (image.getTransparency() != BufferedImage.OPAQUE) {
                    // Create a new image with a white background
                    BufferedImage newImageWithWhiteBg = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = newImageWithWhiteBg.createGraphics();
                    g.setColor(Color.WHITE);
                    g.fillRect(0, 0, image.getWidth(), image.getHeight());
                    g.drawImage(image, 0, 0, null);
                    g.dispose();
                    finalImage = newImageWithWhiteBg;
                }

                ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
                ImageWriteParam writeParam = writer.getDefaultWriteParam();
                
                writeParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                writeParam.setCompressionQuality(0.85f); // Adjust quality here (0.0 to 1.0)

                try (ImageOutputStream outputStream = ImageIO.createImageOutputStream(Files.newOutputStream(targetLocation))) {
                    writer.setOutput(outputStream);
                    writer.write(null, new IIOImage(finalImage, null, null), writeParam);
                } finally {
                    writer.dispose();
                }
                
                logger.info("文件已成功压缩为JPEG并写入: {}", targetLocation.toAbsolutePath().toString());

            } catch (Exception e) {
                logger.error("在将图片压缩为JPEG时发生错误。", e);
                throw new RuntimeException("无法压缩并存储文件: " + e.getMessage(), e);
            }

            return newFileName;

        } catch (IOException ex) {
            logger.error("存储文件时出错。", ex);
            throw new RuntimeException("无法存储文件。请再试一次！", ex);
        }
    }

    /**
     * Stores a product image with a unique UUID-based name.
     *
     * @param file The image file to store.
     * @return The new, unique filename (e.g., "a8f6a3b4-8b6c-4f7d-8a2a-5e4c9f1d0a1b.jpg").
     */
    public String storeProductImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传的文件不能为空。");
        }

        // Generate a unique filename using UUID
        String baseFileName = java.util.UUID.randomUUID().toString();

        // Pass it to the generic storeFile method, specifying the "products" subdirectory
        return storeFile(file, baseFileName, "products");
    }

    /**
     * @deprecated This method uses the original filename which can cause issues. Use the UUID-based storeProductImage(MultipartFile file) instead.
     */
    @Deprecated
    public String storeProductImage(MultipartFile file, String baseFileName) {
        // Use the original filename as the base, storage service will handle conflicts if necessary
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new RuntimeException("无法获取原始文件名。");
        }
        // Remove extension from original filename to use it as baseFileName
        String nameWithoutExtension = originalFilename.contains(".") ? originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;
        return storeFile(file, nameWithoutExtension, "products");
    }

    public void deleteFile(String fileName, String subDirectory) throws IOException {
        if (fileName == null || fileName.isBlank()) {
            logger.warn("尝试删除一个空文件名。");
            return;
        }

        try {
            Path targetDirectory = this.fileStorageLocation.resolve(subDirectory);
            Path filePath = targetDirectory.resolve(fileName).normalize();

            // Security check to prevent path traversal
            if (!filePath.startsWith(targetDirectory)) {
                throw new IOException("无法删除位于存储目录之外的文件: " + fileName);
            }

            if (Files.exists(filePath)) {
                Files.delete(filePath);
                logger.info("文件已成功删除: {}", filePath);
            } else {
                logger.warn("尝试删除一个不存在的文件: {}", filePath);
            }
        } catch (IOException ex) {
            logger.error("删除文件 {} 时出错", fileName, ex);
            throw ex; // Re-throw the exception to be handled by the service layer
        }
    }
} 