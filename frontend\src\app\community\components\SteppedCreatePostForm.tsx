'use client';

import React, { useState, useRef, useMemo } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ArrowRight, Check, Edit3, Eye, FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { HierarchicalPartitionSelector } from './HierarchicalPartitionSelector';
import { getPartitionDisplayInfo } from '@/lib/partition-mapping';
import { MajorEra } from '@/lib/gtnh-progression-data';
import { generatePostPartitionOptions } from '@/lib/community-sections-data';
import { cn } from '@/lib/utils';

// Markdown组件配置
const markdownComponents = {
  h1: ({ children }: any) => (
    <h1 className="text-3xl font-bold mb-6 text-zinc-100 border-b border-zinc-700 pb-2">
      {children}
    </h1>
  ),
  h2: ({ children }: any) => (
    <h2 className="text-2xl font-semibold mb-4 text-zinc-200 border-l-4 border-zinc-600 pl-4 bg-zinc-800/30 py-2 rounded-r-lg">
      {children}
    </h2>
  ),
  h3: ({ children }: any) => (
    <h3 className="text-xl font-medium mb-3 text-zinc-300 flex items-center gap-2">
      <span className="w-2 h-2 bg-zinc-500 rounded-full"></span>
      {children}
    </h3>
  ),
  p: ({ children }: any) => <p className="mb-4 text-zinc-300 leading-relaxed text-base">{children}</p>,
  ul: ({ children }: any) => <ul className="list-none mb-4 text-zinc-300 space-y-2">{children}</ul>,
  ol: ({ children }: any) => <ol className="list-decimal list-inside mb-4 text-zinc-300 space-y-2">{children}</ol>,
  li: ({ children }: any) => (
    <li className="flex items-start gap-3">
      <span className="w-1.5 h-1.5 bg-zinc-500 rounded-full mt-2 flex-shrink-0"></span>
      <span>{children}</span>
    </li>
  ),
  code: ({ children }: any) => (
    <code className="bg-zinc-800 px-2 py-1 rounded-md text-sm text-zinc-300 border border-zinc-700">
      {children}
    </code>
  ),
  pre: ({ children }: any) => (
    <pre className="bg-zinc-800 p-4 rounded-xl mb-4 overflow-x-auto text-sm text-zinc-300 border border-zinc-700">
      {children}
    </pre>
  ),
  blockquote: ({ children }: any) => (
    <blockquote className="border-l-4 border-zinc-600 pl-6 py-2 mb-4 text-zinc-400 italic bg-zinc-800/30 rounded-r-xl">
      {children}
    </blockquote>
  ),
  strong: ({ children }: any) => <strong className="font-semibold text-zinc-200">{children}</strong>,
  em: ({ children }: any) => <em className="italic text-zinc-400">{children}</em>,
};

interface SteppedCreatePostFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { title: string; content: string; partition: string }) => void;
  partitions: MajorEra[];
}

// 步骤定义
const STEPS = [
  { id: 1, title: '选择分区', description: '选择最适合的讨论分区', icon: FileText },
  { id: 2, title: '编辑内容', description: '编写帖子标题和内容', icon: Edit3 },
  { id: 3, title: '预览确认', description: '预览并确认发布', icon: Eye }
];

export const SteppedCreatePostForm: React.FC<SteppedCreatePostFormProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  partitions 
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [partition, setPartition] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 重置表单
  const resetForm = () => {
    setCurrentStep(1);
    setTitle('');
    setContent('');
    setPartition('');
  };

  // 关闭处理
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 下一步
  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  // 上一步
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 提交处理
  const handleSubmit = () => {
    if (title && content && partition) {
      onSubmit({ title, content, partition });
      resetForm();
    }
  };

  // 验证当前步骤是否可以继续
  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return !!partition;
      case 2:
        return !!title && !!content;
      case 3:
        return !!title && !!content && !!partition;
      default:
        return false;
    }
  };

  // 获取分区显示信息
  const partitionInfo = partition ? getPartitionDisplayInfo(partition) : null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-7xl h-[85vh] bg-zinc-950/95 backdrop-blur-2xl border border-zinc-800 text-zinc-100 flex flex-col shadow-2xl p-4">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-zinc-900/50 via-zinc-800/30 to-zinc-900/50 rounded-lg pointer-events-none" />

        <DialogHeader className="flex-shrink-0 relative z-10 mb-1">
          <DialogTitle className="text-base font-bold text-zinc-100 mb-1">
            创建新帖子
          </DialogTitle>
          <DialogDescription className="text-zinc-400 text-xs">
            {STEPS[currentStep - 1].description}
          </DialogDescription>
        </DialogHeader>

        {/* 步骤指示器 */}
        <div className="flex-shrink-0 mb-1 relative z-10">
          <div className="bg-zinc-900/50 backdrop-blur-md rounded-xl p-1.5 border border-zinc-800">
            <div className="flex items-center justify-between mb-2">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id;
                const isCompleted = currentStep > step.id;
                const IconComponent = step.icon;

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={cn(
                      "flex items-center justify-center w-7 h-7 rounded-full border-2 transition-all duration-300",
                      isCompleted
                        ? "bg-zinc-700 border-zinc-600 text-zinc-100" :
                      isActive
                        ? "bg-zinc-800 border-zinc-600 text-zinc-100" :
                        "border-zinc-700 text-zinc-500 bg-zinc-900/50"
                    )}>
                      {isCompleted ? <Check size={14} /> : <IconComponent size={14} />}
                    </div>
                    <div className="ml-1.5">
                      <div className={cn(
                        "text-xs font-semibold transition-colors",
                        isActive ? "text-zinc-200" : isCompleted ? "text-zinc-300" : "text-zinc-500"
                      )}>
                        {step.title}
                      </div>
                      <div className="text-xs text-zinc-600">{step.description}</div>
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className={cn(
                        "flex-1 h-1 mx-2 rounded-full transition-all duration-300",
                        isCompleted
                          ? "bg-zinc-600"
                          : "bg-zinc-800"
                      )} />
                    )}
                  </div>
                );
              })}
            </div>
            <div className="relative">
              <div className="h-1 bg-zinc-800 rounded-full overflow-hidden">
                <div
                  className="h-full bg-zinc-600 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${(currentStep / STEPS.length) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 步骤内容 */}
        <div className="flex-grow min-h-0 overflow-hidden relative z-10">
          {currentStep === 1 && (
            <div className="h-full bg-zinc-900/50 backdrop-blur-md rounded-2xl border border-zinc-800 shadow-xl">
              <div className="p-2 border-b border-zinc-800">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-zinc-800 rounded-lg">
                    <FileText size={16} className="text-zinc-400" />
                  </div>
                  <h3 className="text-sm font-semibold text-zinc-100">选择讨论分区</h3>
                </div>
                {partitionInfo && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-zinc-400">已选择:</span>
                    <Badge className="bg-zinc-800 text-zinc-300 border-zinc-700">
                      {partitionInfo.shortDisplayName}
                    </Badge>
                  </div>
                )}
              </div>
              <div className="p-2 h-full overflow-y-auto">
                <HierarchicalPartitionSelector
                  value={partition}
                  onChange={setPartition}
                  placeholder="选择最适合的分区"
                />
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="h-full flex flex-col gap-2">
              {/* 标题输入 */}
              <div className="flex-shrink-0 bg-zinc-900/50 backdrop-blur-md rounded-2xl p-2 border border-zinc-800">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-zinc-800 rounded-lg">
                    <Edit3 size={16} className="text-zinc-400" />
                  </div>
                  <label className="text-sm font-semibold text-zinc-100">
                    帖子标题 *
                  </label>
                </div>
                <Input
                  type="text"
                  placeholder="输入一个吸引人的标题..."
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="text-sm bg-zinc-800/50 backdrop-blur-sm border-zinc-700 h-9 placeholder:text-zinc-500 focus:border-zinc-600 focus:bg-zinc-800 transition-all duration-200"
                  maxLength={200}
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-zinc-500">让标题简洁而有吸引力</span>
                  <span className="text-xs text-zinc-400">{title.length} / 200</span>
                </div>
              </div>

              {/* 内容编辑和预览 - 左右布局 */}
              <div className="flex-grow min-h-0 flex gap-2">
                {/* 左侧：内容编辑 */}
                <div className="flex-1 bg-zinc-900/50 backdrop-blur-md rounded-2xl border border-zinc-800 flex flex-col">
                  <div className="p-2 border-b border-zinc-800">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-zinc-800 rounded-lg">
                        <FileText size={16} className="text-zinc-400" />
                      </div>
                      <label className="text-sm font-semibold text-zinc-100">
                        帖子内容 *
                        <span className="text-sm text-zinc-400 font-normal ml-2">(支持 Markdown 格式)</span>
                      </label>
                    </div>
                  </div>
                  <div className="flex-grow min-h-0 flex flex-col">
                    <Textarea
                      ref={textareaRef}
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="在这里输入你的帖子内容... 支持 Markdown 格式。&#10;&#10;你可以使用：&#10;# 标题&#10;**粗体** *斜体*&#10;- 列表项&#10;```代码块```&#10;> 引用"
                      className="flex-grow w-full bg-transparent text-sm resize-none p-2 focus:outline-none border-none placeholder:text-zinc-500 leading-relaxed"
                      maxLength={5000}
                    />
                    <div className="flex justify-between items-center p-2 border-t border-zinc-800 bg-zinc-900/30">
                      <span className="text-xs text-zinc-500">支持 Markdown 语法</span>
                      <span className="text-sm text-zinc-400">{content.length} / 5000</span>
                    </div>
                  </div>
                </div>

                {/* 右侧：实时预览 */}
                <div className="flex-1 bg-zinc-900/50 backdrop-blur-md rounded-2xl border border-zinc-800 flex flex-col">
                  <div className="p-2 border-b border-zinc-800">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-zinc-800 rounded-lg">
                        <Eye size={16} className="text-zinc-400" />
                      </div>
                      <h3 className="text-sm font-semibold text-zinc-100">实时预览</h3>
                    </div>
                  </div>
                  <div className="flex-grow min-h-0 p-2 overflow-y-auto">
                    <div className="prose prose-invert prose-sm max-w-none">
                      {title && (
                        <h1 className="text-base font-bold mb-2 text-zinc-100">
                          {title}
                        </h1>
                      )}
                      <div className="text-zinc-300 leading-relaxed">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          components={markdownComponents}
                        >
                          {content || "开始输入内容以查看预览..."}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="h-full flex gap-2">
              {/* 左侧：帖子信息摘要 */}
              <div className="w-72 flex-shrink-0 bg-zinc-900/50 backdrop-blur-md rounded-2xl border border-zinc-800 flex flex-col">
                <div className="p-2 border-b border-zinc-800">
                  <div className="flex items-center gap-2">
                    <div className="p-1 bg-zinc-800 rounded-lg">
                      <Check size={16} className="text-zinc-400" />
                    </div>
                    <h3 className="text-sm font-semibold text-zinc-100">帖子信息</h3>
                  </div>
                </div>
                <div className="p-2 space-y-1.5">
                  <div className="bg-zinc-800/50 rounded-xl p-1.5 border border-zinc-700">
                    <span className="text-sm text-zinc-400 block mb-1">分区</span>
                    <Badge className="bg-zinc-700 text-zinc-300 border-zinc-600">
                      {partitionInfo?.shortDisplayName || partition}
                    </Badge>
                  </div>
                  <div className="bg-zinc-800/50 rounded-xl p-1.5 border border-zinc-700">
                    <span className="text-sm text-zinc-400 block mb-1">标题</span>
                    <span className="text-zinc-200 font-medium">{title}</span>
                  </div>
                  <div className="bg-zinc-800/50 rounded-xl p-1.5 border border-zinc-700">
                    <span className="text-sm text-zinc-400 block mb-1">内容长度</span>
                    <span className="text-zinc-200 font-medium">{content.length} 字符</span>
                  </div>
                  <div className="bg-zinc-800/50 rounded-xl p-1.5 border border-zinc-700">
                    <span className="text-sm text-zinc-400 block mb-1">创建时间</span>
                    <span className="text-zinc-200 font-medium">{new Date().toLocaleString('zh-CN')}</span>
                  </div>
                </div>
              </div>

              {/* 右侧：预览内容 */}
              <div className="flex-1 bg-zinc-900/50 backdrop-blur-md rounded-2xl border border-zinc-800 flex flex-col">
                <div className="p-2 border-b border-zinc-800">
                  <div className="flex items-center gap-2">
                    <div className="p-1 bg-zinc-800 rounded-lg">
                      <Eye size={16} className="text-zinc-400" />
                    </div>
                    <h3 className="text-sm font-semibold text-zinc-100">最终预览</h3>
                  </div>
                </div>
                <div className="flex-grow min-h-0 p-2 overflow-y-auto">
                  <div className="bg-zinc-800/30 rounded-xl p-2 border border-zinc-700">
                    <div className="prose prose-invert prose-sm max-w-none">
                      <h1 className="text-base font-bold mb-2 text-zinc-100">
                        {title}
                      </h1>
                      <div className="text-zinc-300 leading-relaxed">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          components={markdownComponents}
                        >
                          {content || "内容为空"}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex-shrink-0 relative z-10">
          <div className="bg-zinc-900/50 backdrop-blur-md rounded-xl p-1.5 border border-zinc-800 mt-0.5">
            <div className="flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-zinc-400 hover:text-zinc-200 hover:bg-zinc-800 transition-all duration-200"
              >
                取消
              </Button>

              <div className="flex gap-2">
                {currentStep > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevious}
                    className="border-zinc-700 text-zinc-300 hover:bg-zinc-800 hover:border-zinc-600 transition-all duration-200"
                  >
                    <ArrowLeft size={14} className="mr-1" />
                    上一步
                  </Button>
                )}

                {currentStep < STEPS.length ? (
                  <Button
                    size="sm"
                    onClick={handleNext}
                    disabled={!canProceed()}
                    className="bg-zinc-700 hover:bg-zinc-600 text-white disabled:opacity-50 transition-all duration-200"
                  >
                    下一步
                    <ArrowRight size={14} className="ml-1" />
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    onClick={handleSubmit}
                    disabled={!canProceed()}
                    className="bg-zinc-700 hover:bg-zinc-600 text-white disabled:opacity-50 transition-all duration-200"
                  >
                    <Check size={14} className="mr-1" />
                    发布帖子
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
