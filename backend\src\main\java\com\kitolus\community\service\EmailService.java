package com.kitolus.community.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.mail.javamail.MimeMessageHelper;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Autowired(required = false)
    private JavaMailSender mailSender;

    @Value("${spring.mail.host:}")
    private String host;

    @Value("${spring.mail.username}")
    private String fromEmail;

    public void sendVerificationCode(String to, String code) {
        String verificationSubject = "欢迎加入Kitolus社区 - 请验证您的账户";
        String htmlContent = buildEmailTemplate(
            "账户验证",
            "<p style='margin:0 0 16px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>欢迎加入 Kitolus 社区！</p>" +
            "<p style='margin:0 0 16px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>我们非常高兴您能选择这个属于《格雷科技：新视野》爱好者的大家庭。在这里，您可以分享自动化成果，交流游玩心得，或者向技术大佬们请教难题。</p>" +
            "<p style='margin:0 0 24px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>为了激活您的账户，请在注册页面输入以下一次性验证码：</p>" +
            "<div style='background-color: #111827; border: 1px solid #374151; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;'>" +
            "  <p style='font-size: 36px; font-weight: bold; letter-spacing: 10px; color: #f9fafb; margin: 0;'>" + code + "</p>" +
            "</div>" +
            "<p style='margin:0 0 16px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>此验证码将在 <strong>1 分钟</strong>内失效。为保障安全，请勿与他人分享。</p>" +
            "<p style='margin:0; font-size: 16px; line-height: 1.6; color: #d1d5db;'>我们期待着您为社区带来的独特光彩！</p>"
        );
        sendHtmlEmail(to, verificationSubject, htmlContent);
    }

    public void sendPasswordResetEmail(String to, String username, String resetUrl) {
        String resetSubject = "Kitolus社区 - 重置您的密码";
        String htmlContent = buildEmailTemplate(
            "密码重置请求",
            "<p style='margin:0 0 16px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>您好，" + username + "！</p>" +
            "<p style='margin:0 0 24px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>我们收到了一个重置您账户密码的请求。我们理解保护账户安全的重要性，请点击下方按钮来创建您的新密码。</p>" +
            "<div style='text-align: center; margin: 25px 0;'>" +
            "<a href='" + resetUrl + "' target='_blank' style='" +
            "display: inline-block; background-color: #374151; color: #f9fafb !important; font-size: 16px; font-weight: 500; " +
            "text-decoration: none; padding: 15px 30px; border-radius: 10px; " +
            "transition: background-color 0.2s; box-shadow: 0 4px 6px rgba(0,0,0,0.2);'>" +
            "设置新密码" +
            "</a></div>" +
            "<p style='margin:0 0 16px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>为了您的账户安全，此重置链接将在 <strong>15 分钟</strong>内失效。</p>" +
            "<p style='margin:0 0 24px; font-size: 16px; line-height: 1.6; color: #d1d5db;'>如果您没有请求重置密码，请立即忽略并删除此邮件。您的账户目前仍然是安全的。</p>" +
            "<p style='font-size: 12px; color: #9ca3af; margin-top: 20px; padding: 10px; background-color: #111827; border-radius: 8px;'>小提示：设置一个包含大小写字母、数字和符号的强密码，能更好地保护您的账户安全。</p>" +
            "<p style='font-size: 12px; color: #9ca3af; margin-top: 20px;'>如果按钮无效，请将以下链接复制并粘贴到您的浏览器地址栏中：<br><a href='" + resetUrl + "' target='_blank' style='word-break: break-all; color: #60a5fa !important; text-decoration: underline;'>" + resetUrl + "</a></p>"
        );
        sendHtmlEmail(to, resetSubject, htmlContent);
    }

    private String buildEmailTemplate(String title, String content) {
        String year = java.time.Year.now().toString();
        return "<!DOCTYPE html>" +
            "<html lang='zh-CN'>" +
            "<head>" +
            "  <meta charset='UTF-8'>" +
            "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
            "  <title>" + title + "</title>" +
            "</head>" +
            "<body style='margin: 0; padding: 0; background-color: #111111; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif; color: #d1d5db;'>" +
            "  <table width='100%' border='0' cellspacing='0' cellpadding='0' style='background-color: #111111;'>" +
            "    <tr><td align='center' style='padding: 20px;'>" +
            "      <table width='100%' style='max-width: 600px; margin: 0 auto;' border='0' cellspacing='0' cellpadding='0'>" +
            // Header
            "        <tr><td style='padding: 20px 0; text-align: center;'>" +
            "          <h1 style='display: inline-block; background-color: #1a1a1a; border: 1px solid #333333; padding: 12px 24px; border-radius: 12px; font-size: 24px; font-weight: bold; color: #e5e7eb; margin: 0;'>Kitolus Community</h1>" +
            "          <p style='font-size: 14px; color: #9ca3af; margin: 15px 0 0;'>格雷科技：新视野 玩家社区</p>" +
            "        </td></tr>" +
            // Content
            "        <tr><td style='background-color: #1f2937; border-radius: 16px; padding: 40px;'>" +
            "          <h2 style='font-size: 22px; font-weight: 600; color: #f9fafb; margin-top: 0; margin-bottom: 24px;'>" + title + "</h2>" +
            content +
            "        </td></tr>" +
            // Footer
            "        <tr><td style='padding: 30px 20px; text-align: center; font-size: 12px; color: #6b7280;'>" +
            "          <p style='margin: 0;'>这是一个自动发送的邮件，请勿直接回复。</p>" +
            "          <p style='margin: 10px 0 0;'>&copy; " + year + " Kitolus Community. All Rights Reserved.</p>" +
            "        </td></tr>" +
            "      </table>" +
            "    </td></tr>" +
            "  </table>" +
            "</body>" +
            "</html>";
    }

    private void sendHtmlEmail(String to, String subject, String htmlBody) {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlBody, true);
            mailSender.send(mimeMessage);
        } catch (MessagingException e) {
            // In a real application, you'd want more robust error handling,
            // perhaps queuing the email to be retried.
            throw new RuntimeException("发送邮件失败", e);
        }
    }
}
