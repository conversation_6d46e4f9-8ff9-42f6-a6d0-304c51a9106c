package com.kitolus.community;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan(basePackages = "com.kitolus.community.mapper", sqlSessionFactoryRef = "sqlSessionFactory")
@EnableScheduling
public class KitolusCommunityApplication {

    public static void main(String[] args) {
        SpringApplication.run(KitolusCommunityApplication.class, args);
    }

} 