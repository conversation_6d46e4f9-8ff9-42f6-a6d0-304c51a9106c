package com.kitolus.community.controller;

import com.kitolus.community.dto.ApprovalNotesDTO;
import com.kitolus.community.dto.DeveloperApplicationDTO;
import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.dto.AdminDashboardStatsDTO;
import com.kitolus.community.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.kitolus.community.entity.Product;
import com.kitolus.community.dto.RejectReasonDTO;
import com.kitolus.community.dto.ReviewWithdrawalRequestDTO;
import com.kitolus.community.dto.UserAdminViewDTO;
import com.kitolus.community.dto.WithdrawalRequestDetailsDTO;
import com.kitolus.community.entity.WithdrawalRequest;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.service.EarningsService;
import com.kitolus.community.service.ProductService;
import com.kitolus.community.service.ProductStatusHistoryService;
import com.kitolus.community.entity.ProductStatusHistory;
import jakarta.validation.Valid;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasAuthority('KitolusAdmin')")
public class AdminController {

    @Autowired
    private AdminService adminService;
    
    @Autowired
    private ProductService productService;

    @Autowired
    private EarningsService earningsService;

    @Autowired
    private ProductStatusHistoryService statusHistoryService;

    @GetMapping("/dashboard-stats")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<AdminDashboardStatsDTO> getDashboardStats() {
        AdminDashboardStatsDTO stats = adminService.getAdminDashboardStats();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/developer-applications")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<List<DeveloperApplicationDTO>> getDeveloperApplications(@RequestParam(required = false) String status) {
        List<DeveloperApplicationDTO> applications = adminService.getDeveloperApplicationsByStatus(status);
        return ResponseEntity.ok(applications);
    }

    @PostMapping("/developer-applications/{id}/approve")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Void> approveDeveloperApplication(@PathVariable Long id) {
        adminService.approveDeveloperApplication(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/developer-applications/{id}/reject")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Void> rejectDeveloperApplication(@PathVariable Long id, @RequestBody RejectReasonDTO reason) {
        adminService.rejectDeveloperApplication(id, reason.getReason());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/products/pending")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<List<Product>> getProductsForApproval() {
        List<Product> products = adminService.getProductsForApproval();
        return ResponseEntity.ok(products);
    }

    @GetMapping("/products/{id}")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Product> getProductDetails(@PathVariable Long id) {
        Product product = productService.getProductById(id);
        if (product == null) {
            throw new ResourceNotFoundException("Product not found with id: " + id);
        }
        return ResponseEntity.ok(product);
    }

    @PostMapping("/products/{id}/approve")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Product> approveProduct(@PathVariable Long id, @RequestBody(required = false) ApprovalNotesDTO approvalNotes) {
        String notes = (approvalNotes != null) ? approvalNotes.getNotes() : "";
        Product product = adminService.approveProduct(id, notes);
        return ResponseEntity.ok(product);
    }

    @PostMapping("/products/{id}/reject")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Product> rejectProduct(@PathVariable Long id, @RequestBody RejectReasonDTO rejectReasonDTO) {
        return ResponseEntity.ok(adminService.rejectProduct(id, rejectReasonDTO.getReason()));
    }

    @GetMapping("/products/all")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<List<ProductDTO>> getAllProducts() {
        return ResponseEntity.ok(adminService.getAllProducts());
    }

    @GetMapping("/earnings-summary")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<EarningsSummaryDTO> getEarningsSummary() {
        return ResponseEntity.ok(adminService.getEarningsSummary());
    }

    @GetMapping("/users")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<List<UserAdminViewDTO>> getAllUsers() {
        return ResponseEntity.ok(adminService.getAllUsers());
    }

    @PostMapping("/users/{id}/role")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<?> updateUserRole(@PathVariable Long id, @RequestBody Map<String, String> payload) {
        String role = payload.get("newRole");
        adminService.updateUserRole(id, role);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/users/{userId}/toggle-status")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Void> toggleUserStatus(@PathVariable Long userId) {
        adminService.toggleUserStatus(userId);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/users/{userId}")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Void> deleteUser(@PathVariable Long userId) {
        adminService.deleteUser(userId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/products/{id}/delist")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<Void> delistProduct(@PathVariable Long id, @RequestBody RejectReasonDTO rejectReasonDTO) {
        adminService.delistProduct(id, rejectReasonDTO.getReason());
        return ResponseEntity.ok().build();
    }

    // Withdrawal Management
    @GetMapping("/withdrawal-requests")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<List<WithdrawalRequestDetailsDTO>> getAllWithdrawalRequests() {
        List<WithdrawalRequestDetailsDTO> requests = earningsService.getAllWithdrawalRequests();
        return ResponseEntity.ok(requests);
    }

    @PutMapping("/withdrawal-requests/{id}/review")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<WithdrawalRequest> reviewWithdrawalRequest(@PathVariable Long id, @Valid @RequestBody ReviewWithdrawalRequestDTO reviewDTO) {
        String adminUsername = SecurityContextHolder.getContext().getAuthentication().getName();
        WithdrawalRequest updatedRequest = earningsService.reviewWithdrawalRequest(id, reviewDTO, adminUsername);
        return ResponseEntity.ok(updatedRequest);
    }

    @GetMapping("/products/{id}/status-history")
    @PreAuthorize("hasAuthority('KitolusAdmin')")
    public ResponseEntity<?> getProductStatusHistory(@PathVariable Long id) {
        try {
            List<ProductStatusHistory> history = statusHistoryService.getProductStatusHistory(id);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", "获取产品状态历史失败: " + e.getMessage()));
        }
    }
}