package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import java.sql.Timestamp;

@Data
@TableName("developer_applications")
public class DeveloperApplication {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("status")
    private DeveloperApplicationStatus status;
    
    // A brief message from the user on why they want to be a developer
    @TableField("message")
    private String message;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("reviewed_at")
    private Timestamp reviewedAt;

    @TableField("reviewed_by")
    private Long reviewedBy; // Admin user ID

    @TableField("rejection_reason")
    private String rejectionReason;
} 