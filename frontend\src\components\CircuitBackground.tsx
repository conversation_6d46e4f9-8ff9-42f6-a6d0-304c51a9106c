'use client';

import React, { useRef, useEffect } from 'react';

// Axial-coordinate implementation for hexagonal grids
type Hex = { q: number; r: number };

const CircuitBackground: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationFrameId = useRef<number>();
    const isMounted = useRef<boolean>(false);

    useEffect(() => {
        if (!isVisible) {
            return;
        }

        isMounted.current = true;
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let width = window.innerWidth;
        let height = window.innerHeight;
        canvas.width = width;
        canvas.height = height;

        const HEX_RADIUS = 30;
        const NODE_COUNT = 30;
        const NODE_SPEED = 0.2;
        const MAX_LINK_DISTANCE_HEX = 7; // distance in hex units

        const HEX_WIDTH = Math.sqrt(3) * HEX_RADIUS;
        const HEX_HEIGHT = 2 * HEX_RADIUS;
        
        // --- Hex Grid Helper Functions ---
        const hexToPixel = (hex: Hex): { x: number; y: number } => {
            const x = HEX_RADIUS * (Math.sqrt(3) * hex.q + (Math.sqrt(3) / 2) * hex.r);
            const y = HEX_RADIUS * ((3 / 2) * hex.r);
            return { x, y };
        };

        const pixelToHex = (x: number, y: number): Hex => {
            const q = (Math.sqrt(3) / 3 * x - 1 / 3 * y) / HEX_RADIUS;
            const r = (2 / 3 * y) / HEX_RADIUS;
            return hexRound({ q, r });
        };
        
        const hexRound = (frac: Hex): Hex => {
            const q = Math.round(frac.q);
            const r = Math.round(frac.r);
            const s = Math.round(-frac.q - frac.r);
            
            const q_diff = Math.abs(q - frac.q);
            const r_diff = Math.abs(r - frac.r);
            const s_diff = Math.abs(s - (-frac.q - frac.r));
            
            if (q_diff > r_diff && q_diff > s_diff) {
                return { q: -r - s, r };
            } else if (r_diff > s_diff) {
                return { q, r: -q - s };
            }
            return { q, r };
        };

        const hexDistance = (a: Hex, b: Hex): number => {
            const dq = Math.abs(a.q - b.q);
            const dr = Math.abs(a.r - b.r);
            const ds = Math.abs((-a.q - a.r) - (-b.q - b.r));
            return Math.max(dq, dr, ds);
        };
        
        const DIRECTIONS: Hex[] = [ {q: 1, r: 0}, {q: 1, r: -1}, {q: 0, r: -1}, {q: -1, r: 0}, {q: -1, r: 1}, {q: 0, r: 1} ];

        class Node {
            x: number;
            y: number;
            vx: number;
            vy: number;
            radius: number;
            currentHex: Hex;

            constructor() {
                this.x = Math.random() * width;
                this.y = Math.random() * height;
                this.vx = (Math.random() - 0.5) * NODE_SPEED;
                this.vy = (Math.random() - 0.5) * NODE_SPEED;
                this.radius = Math.random() * 2 + 1;
                this.currentHex = pixelToHex(this.x, this.y);
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;

                if (this.x < -HEX_WIDTH || this.x > width + HEX_WIDTH) this.vx *= -1;
                if (this.y < -HEX_HEIGHT || this.y > height + HEX_HEIGHT) this.vy *= -1;
                
                this.currentHex = pixelToHex(this.x, this.y);
            }

            draw(context: CanvasRenderingContext2D) {
                const center = hexToPixel(this.currentHex);
                context.beginPath();
                context.arc(center.x, center.y, this.radius, 0, Math.PI * 2);
                context.fillStyle = 'rgba(200, 225, 255, 0.9)';
                context.fill();
            }
        }

        const nodes = Array.from({ length: NODE_COUNT }, () => new Node());

        const drawHexGrid = (context: CanvasRenderingContext2D) => {
            context.strokeStyle = 'rgba(255, 255, 255, 0.05)';
            context.lineWidth = 1;

            const startQ = pixelToHex(0, 0).q - 2;
            const endQ = pixelToHex(width, height).q + 2;
            const startR = pixelToHex(0, height).r - 2;
            const endR = pixelToHex(width, 0).r + 2;

            for (let r = startR; r <= endR; r++) {
                 for (let q = startQ; q <= endQ; q++) {
                    const center = hexToPixel({ q, r });
                    context.beginPath();
                    for (let i = 0; i < 6; i++) {
                        const angle = Math.PI / 180 * (60 * i - 30);
                        const x = center.x + HEX_RADIUS * Math.cos(angle);
                        const y = center.y + HEX_RADIUS * Math.sin(angle);
                        if (i === 0) context.moveTo(x, y);
                        else context.lineTo(x, y);
                    }
                    context.closePath();
                    context.stroke();
                }
            }
        };
        
        const findPath = (start: Hex, end: Hex): Hex[] => {
            const path: Hex[] = [start];
            let current = start;
            for(let i=0; i < 15; i++) { // Safety break
                if (current.q === end.q && current.r === end.r) break;
                
                const validMoves = DIRECTIONS.filter(dir => {
                    const nextHex = { q: current.q + dir.q, r: current.r + dir.r };
                    return hexDistance(nextHex, end) < hexDistance(current, end);
                });

                if (validMoves.length === 0) break;
                const move = validMoves[Math.floor(Math.random() * validMoves.length)];
                current = { q: current.q + move.q, r: current.r + move.r };
                path.push(current);
            }
            return path;
        };

        const drawLinks = (context: CanvasRenderingContext2D) => {
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    const nodeA = nodes[i];
                    const nodeB = nodes[j];
                    const dist = hexDistance(nodeA.currentHex, nodeB.currentHex);

                    if (dist < MAX_LINK_DISTANCE_HEX) {
                        const opacity = 1 - (dist / MAX_LINK_DISTANCE_HEX);
                        context.strokeStyle = `rgba(200, 225, 255, ${opacity * 0.4})`;
                        context.lineWidth = 1;
                        
                        const path = findPath(nodeA.currentHex, nodeB.currentHex);
                        const pixelPath = path.map(hex => hexToPixel(hex));
                        
                        context.beginPath();
                        context.moveTo(pixelPath[0].x, pixelPath[0].y);
                        for(let k = 1; k < pixelPath.length; k++) {
                            context.lineTo(pixelPath[k].x, pixelPath[k].y);
                        }
                        context.stroke();
                    }
                }
            }
        };

        const animate = () => {
            if (!ctx) return;
            
            // 1. Clear with a radial gradient to create a "nebula" effect
            const bgGradient = ctx.createRadialGradient(width / 2, height / 2, 0, width / 2, height / 2, Math.max(width, height) / 2);
            bgGradient.addColorStop(0, '#0a142a'); // Brighter core
            bgGradient.addColorStop(1, '#00050a'); // Original dark edge
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, width, height);
            
            drawHexGrid(ctx);

            nodes.forEach(node => {
                node.update();
                node.draw(ctx);
            });
            drawLinks(ctx);

            // 2. Apply a vignette effect on top
            const vignetteGradient = ctx.createRadialGradient(width / 2, height / 2, width / 3, width / 2, height / 2, Math.max(width, height) / 1.5);
            vignetteGradient.addColorStop(0, 'rgba(0,0,0,0)');
            vignetteGradient.addColorStop(1, 'rgba(0,0,0,0.6)');
            ctx.fillStyle = vignetteGradient;
            ctx.fillRect(0, 0, width, height);

            if (isMounted.current) {
                animationFrameId.current = requestAnimationFrame(animate);
            }
        };

        const handleResize = () => {
            width = window.innerWidth;
            height = window.innerHeight;
            if(canvas){
                canvas.width = width;
                canvas.height = height;
            }
        };
        
        window.addEventListener('resize', handleResize);
        animate();

        return () => {
            isMounted.current = false;
            if (animationFrameId.current) {
                cancelAnimationFrame(animationFrameId.current);
            }
            window.removeEventListener('resize', handleResize);
        };
    }, [isVisible]);

    if (!isVisible) {
        return null;
    }

    return <canvas ref={canvasRef} style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', zIndex: -1, background: '#00050a' }} />;
};

export default CircuitBackground; 