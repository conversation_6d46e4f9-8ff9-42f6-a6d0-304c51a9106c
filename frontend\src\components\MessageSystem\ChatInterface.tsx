'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  Send,
  MoreVertical,
  ArrowLeft,
  Phone,
  Video,
  Info
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { getConversations, getConversationMessages, sendPrivateMessage } from '@/services/messageApi';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface Conversation {
  other_user_id: number;
  other_user_name: string;
  other_user_avatar: string;
  last_message_time: string;
  last_message_content: string;
  unread_count: number;
}

interface ChatMessage {
  id: number;
  senderId: number;
  receiverId: number;
  content: string;
  createdAt: string;
  sender_name?: string;
  sender_avatar?: string;
  receiver_name?: string;
  receiver_avatar?: string;
}

export const ChatInterface: React.FC = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载对话列表
  const loadConversations = async () => {
    try {
      setLoading(true);
      const response = await getConversations();
      setConversations(response.conversations || []);
    } catch (error) {
      console.error('Failed to load conversations:', error);
      toast.error('加载对话列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载对话消息
  const loadMessages = async (otherUserId: number) => {
    try {
      const response = await getConversationMessages(otherUserId);
      setMessages(response.messages || []);
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error('Failed to load messages:', error);
      toast.error('加载消息失败');
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || sending) return;

    try {
      setSending(true);
      await sendPrivateMessage(selectedConversation.other_user_id, newMessage.trim());
      setNewMessage('');
      
      // 重新加载消息
      await loadMessages(selectedConversation.other_user_id);
      
      // 重新加载对话列表以更新最后消息
      await loadConversations();
      
      toast.success('消息发送成功');
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('消息发送失败');
    } finally {
      setSending(false);
    }
  };

  // 选择对话
  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    loadMessages(conversation.other_user_id);
  };

  // 初始加载
  useEffect(() => {
    loadConversations();
  }, []);

  // 消息滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-background">
      {/* 对话列表 */}
      <div className="w-80 border-r border-border flex flex-col">
        <div className="p-4 border-b border-border">
          <h3 className="font-semibold text-lg">私信</h3>
        </div>
        
        <ScrollArea className="flex-1">
          <div className="p-2">
            {conversations.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">暂无对话</p>
              </div>
            ) : (
              conversations.map((conversation) => (
                <motion.div
                  key={conversation.other_user_id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  whileHover={{ scale: 1.01 }}
                  transition={{ duration: 0.2 }}
                  className={cn(
                    "flex items-center p-4 rounded-xl cursor-pointer transition-all duration-200 mb-2",
                    "border border-transparent backdrop-blur-sm",
                    selectedConversation?.other_user_id === conversation.other_user_id
                      ? "bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20 shadow-sm"
                      : "hover:bg-gradient-to-r hover:from-muted/30 hover:to-muted/10 hover:shadow-sm"
                  )}
                  onClick={() => handleSelectConversation(conversation)}
                >
                  <Avatar className="h-12 w-12 mr-4 ring-2 ring-background shadow-sm">
                    <AvatarImage src={conversation.other_user_avatar} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold">
                      {conversation.other_user_name?.[0]?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-sm truncate">{conversation.other_user_name}</h4>
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(conversation.last_message_time), { 
                          addSuffix: true, 
                          locale: zhCN 
                        })}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-muted-foreground truncate">
                        {conversation.last_message_content || '暂无消息'}
                      </p>
                      {conversation.unread_count > 0 && (
                        <span className="bg-primary text-primary-foreground text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* 聊天区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {selectedConversation ? (
          <>
            {/* 聊天头部 */}
            <div className="p-6 border-b border-border/20 flex items-center justify-between bg-muted/5 overflow-hidden">
              <div className="flex items-center">
                <Avatar className="h-12 w-12 mr-4 ring-2 ring-background shadow-sm">
                  <AvatarImage src={selectedConversation.other_user_avatar} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary font-semibold">
                    {selectedConversation.other_user_name?.[0]?.toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <h4 className="font-semibold text-lg truncate">{selectedConversation.other_user_name}</h4>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                    <p className="text-sm text-muted-foreground truncate">在线</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="h-10 w-10 rounded-xl hover:bg-muted/50">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="h-10 w-10 rounded-xl hover:bg-muted/50">
                  <Video className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="h-10 w-10 rounded-xl hover:bg-muted/50">
                  <Info className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 消息区域 */}
            <ScrollArea className="flex-1 p-6">
              <div className="space-y-6">
                {messages.map((message, index) => {
                  const isCurrentUser = message.senderId === user?.id;
                  const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;
                  
                  return (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={cn(
                        "flex items-start gap-3",
                        isCurrentUser ? "justify-end flex-row-reverse" : "justify-start"
                      )}
                    >
                      {/* 头像显示 - 始终显示 */}
                      <div className="flex-shrink-0">
                        {showAvatar ? (
                          <Avatar className="h-8 w-8 ring-2 ring-background/50">
                            <AvatarImage
                              src={isCurrentUser ? (user?.avatarUrl || undefined) : message.sender_avatar}
                            />
                            <AvatarFallback className="bg-gradient-to-br from-primary/15 to-primary/5 text-primary font-semibold text-xs">
                              {isCurrentUser
                                ? user?.username?.[0]?.toUpperCase()
                                : message.sender_name?.[0]?.toUpperCase()
                              }
                            </AvatarFallback>
                          </Avatar>
                        ) : (
                          <div className="w-8 h-8" />
                        )}
                      </div>

                      {/* 消息气泡 */}
                      <div className={cn(
                        "max-w-[75%] rounded-2xl px-4 py-3 shadow-sm relative",
                        isCurrentUser
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted/80 border border-border/30 backdrop-blur-sm"
                      )}>
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        <p className={cn(
                          "text-xs mt-2 opacity-70",
                          isCurrentUser ? "text-primary-foreground/70" : "text-muted-foreground"
                        )}>
                          {formatDistanceToNow(new Date(message.createdAt), {
                            addSuffix: true,
                            locale: zhCN
                          })}
                        </p>
                      </div>
                    </motion.div>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* 消息输入区域 */}
            <div className="p-6 border-t border-border/20 bg-muted/5">
              <div className="flex items-center space-x-4">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="输入消息..."
                  className="flex-1 h-12 text-sm rounded-xl border-border/30 bg-background focus:ring-2 focus:ring-primary/20"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  disabled={sending}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() || sending}
                  size="lg"
                  className="h-12 w-12 rounded-xl shadow-sm"
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-32 h-32 rounded-3xl bg-muted/20 flex items-center justify-center mb-8 mx-auto">
                <Send className="w-16 h-16 text-muted-foreground/30" />
              </div>
              <h3 className="font-bold text-2xl mb-4">开始对话</h3>
              <p className="text-muted-foreground max-w-[320px] leading-relaxed">
                选择左侧的对话或点击右上角的邮件图标开始新的聊天，与其他用户交流想法
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
