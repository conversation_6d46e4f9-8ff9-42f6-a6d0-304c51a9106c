'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types/Product';
import Image from 'next/image';
import { ChevronDown, ShoppingCart, CheckCircle, ImageOff } from 'lucide-react';

interface ExpandableProductCardProps {
    product: Product;
    isOwned: boolean;
    onPurchaseClick: (product: Product) => void;
}

const ExpandableProductCard = ({ product, isOwned, onPurchaseClick }: ExpandableProductCardProps) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const cardVariants = {
        collapsed: {
            height: "200px",
            transition: { type: 'spring', stiffness: 300, damping: 30 }
        },
        expanded: {
            height: "450px",
            transition: { type: 'spring', stiffness: 300, damping: 30 }
        }
    };

    return (
        <motion.div
            layout
            variants={cardVariants}
            initial="collapsed"
            animate={isExpanded ? "expanded" : "collapsed"}
            className="relative rounded-2xl overflow-hidden shadow-lg bg-zinc-900 border border-zinc-800 cursor-pointer"
            onClick={() => setIsExpanded(!isExpanded)}
        >
            <AnimatePresence>
                {/* Background Image */}
                <motion.div layout="position" className="absolute inset-0">
                    {product.imageUrl ? (
                        <Image
                            src={product.imageUrl}
                            alt={product.name}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                    ) : (
                         <div className="w-full h-full bg-zinc-800 flex items-center justify-center">
                            <ImageOff className="w-16 h-16 text-zinc-600" />
                        </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                </motion.div>

                {/* Collapsed Content */}
                {!isExpanded && (
                     <motion.div
                        layout="position"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="absolute bottom-0 left-0 right-0 p-4 text-white z-10"
                    >
                        <h3 className="text-xl font-bold truncate">{product.name}</h3>
                    </motion.div>
                )}

                {/* Expanded Content */}
                {isExpanded && (
                    <motion.div
                        layout="position"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
                        exit={{ opacity: 0 }}
                        className="absolute bottom-0 left-0 right-0 p-6 text-white z-10 flex flex-col justify-end h-full"
                        onClick={(e) => e.stopPropagation()} // Prevent card from collapsing when clicking content
                    >
                        <div className="flex-grow" /> {/* Spacer */}
                        <h3 className="text-2xl font-bold mb-2">{product.name}</h3>
                        <p className="text-sm text-zinc-300 line-clamp-3 mb-4">{product.description}</p>
                        <div className="flex justify-between items-center">
                            <p className={`text-2xl font-bold ${isOwned ? 'text-green-400' : 'text-amber-400'}`}>
                                {isOwned ? '已拥有' : `¥${product.price.toFixed(2)}`}
                            </p>
                            <button
                                onClick={() => onPurchaseClick(product)}
                                disabled={isOwned}
                                className="flex items-center gap-2 px-4 py-2 rounded-lg bg-amber-500/80 hover:bg-amber-500 disabled:bg-green-600/80 disabled:cursor-not-allowed transition-colors"
                            >
                                {isOwned ? <CheckCircle size={20} /> : <ShoppingCart size={20} />}
                                {isOwned ? '已拥有' : '购买'}
                            </button>
                        </div>
                    </motion.div>
                )}

                {/* Arrow Indicator */}
                 <motion.div layout className="absolute bottom-4 right-4 z-20 text-white/70">
                    <motion.div animate={{ rotate: isExpanded ? 180 : 0 }}>
                        <ChevronDown size={24} />
                    </motion.div>
                </motion.div>

            </AnimatePresence>
        </motion.div>
    );
};

export default ExpandableProductCard; 