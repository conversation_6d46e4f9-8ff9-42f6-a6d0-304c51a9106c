package com.kitolus.community.controller;

import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.service.ProductService;
import com.kitolus.community.service.UserService;
import com.kitolus.community.dto.CreateProductRequestDTO;
import com.kitolus.community.dto.UpdateProductRequestDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private ProductService productService;

    @Autowired
    private UserService userService;
    
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || "anonymousUser".equals(authentication.getPrincipal())) {
            return null;
        }
        String currentPrincipalName = authentication.getName();
        return userService.findByUsername(currentPrincipalName);
    }

    @PostMapping(value = "/upload", consumes = {"multipart/form-data"})
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<?> createProductWithUpload(
            @RequestPart("name") String name,
            @RequestPart("price") String priceStr,
            @RequestPart("description") String description,
            @RequestPart("downloadUrl") String downloadUrl,
            @RequestPart("image") MultipartFile imageFile) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("message", "用户未认证"));
        }

        try {
            BigDecimal price = new BigDecimal(priceStr);
            Product newProduct = productService.createProduct(
                name,
                price,
                description,
                downloadUrl,
                imageFile,
                currentUser
            );
            return ResponseEntity.ok(newProduct);
        } catch (NumberFormatException e) {
            return ResponseEntity.badRequest().body(Map.of("message", "无效的价格格式: " + e.getMessage()));
        } catch (IOException e) {
            return ResponseEntity.badRequest().body(Map.of("message", "文件上传失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", "创建商品失败: " + e.getMessage()));
        }
    }

    @GetMapping
    public ResponseEntity<List<ProductDTO>> getProducts() {
        List<ProductDTO> products = productService.getApprovedProductsWithAuthor();
        return ResponseEntity.ok(products);
    }

    @GetMapping("/my-products")
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<List<ProductDTO>> getMyProducts() {
        User currentUser = getCurrentUser();
        List<ProductDTO> products = productService.findByUserId(currentUser.getId());
        return ResponseEntity.ok(products);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<?> updateProduct(@PathVariable Long id, @RequestBody UpdateProductRequestDTO request) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("message", "用户未认证"));
        }

        try {
            Product updatedProduct = productService.updateProduct(id, request, currentUser);
            return ResponseEntity.ok(updatedProduct);
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(404).body(Map.of("message", e.getMessage()));
        } catch (AccessDeniedException e) {
            return ResponseEntity.status(403).body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", "更新产品失败: " + e.getMessage()));
        }
    }

    @GetMapping("/purchased")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<?> getPurchasedProducts() {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            // This case should ideally not be hit if @PreAuthorize is working correctly
            return ResponseEntity.status(401).body(Map.of("message", "用户未认证"));
        }
        List<Product> purchasedProducts = productService.findPurchasedByUserId(currentUser.getId());
        return ResponseEntity.ok(purchasedProducts);
    }

    @DeleteMapping("/by-developer/{productId}")
    @PreAuthorize("hasAnyAuthority('DEVELOPER', 'KitolusAdmin')")
    public ResponseEntity<Void> deleteProductByDeveloper(@PathVariable Long productId) {
        try {
            logger.info("收到删除产品请求，产品ID: {}", productId);
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            logger.info("当前认证用户: {}", authentication.getName());
            User currentUser = userService.findByUsername(authentication.getName());
            logger.info("找到用户信息: {}, 角色: {}", currentUser.getUsername(), currentUser.getRole());
            productService.deleteProductAsDeveloper(productId, currentUser);
            logger.info("产品删除成功，产品ID: {}", productId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            logger.error("删除产品失败，产品ID: {}", productId, e);
            throw e; // 重新抛出异常，让全局异常处理器处理
        }
    }
} 