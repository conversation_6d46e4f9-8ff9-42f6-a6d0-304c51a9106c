package com.kitolus.community.service;

import com.kitolus.community.dto.EarningsSummaryDTO;
import com.kitolus.community.dto.MonthlyRevenueDTO;
import com.kitolus.community.dto.DailyRevenueDTO;
import com.kitolus.community.dto.OrderDTO;
import com.kitolus.community.dto.ProductDTO;
import com.kitolus.community.dto.ProductSalesStatsDTO;
import com.kitolus.community.dto.CreateWithdrawalRequestDTO;
import com.kitolus.community.dto.ReviewWithdrawalRequestDTO;
import com.kitolus.community.dto.WithdrawalRequestDetailsDTO;
import com.kitolus.community.entity.Order;
import com.kitolus.community.entity.Product;
import com.kitolus.community.entity.User;
import com.kitolus.community.entity.WithdrawalChannel;
import com.kitolus.community.entity.WithdrawalRequest;
import com.kitolus.community.entity.WithdrawalStatus;
import com.kitolus.community.exception.InvalidDataException;
import com.kitolus.community.exception.ResourceNotFoundException;
import com.kitolus.community.mapper.OrderMapper;
import com.kitolus.community.mapper.ProductMapper;
import com.kitolus.community.mapper.UserMapper;
import com.kitolus.community.mapper.WithdrawalRequestMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EarningsServiceImpl implements EarningsService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private WithdrawalRequestMapper withdrawalRequestMapper;

    private static final String PAID_STATUS = "PAID";
    private static final String DEVELOPER_ROLE = "DEVELOPER";

    @Override
    public EarningsSummaryDTO getGlobalEarningsSummary() {
        BigDecimal totalRevenueDecimal = orderMapper.sumTotalFeeByStatus(PAID_STATUS);
        double totalRevenue = totalRevenueDecimal != null ? totalRevenueDecimal.doubleValue() : 0.0;
        
        BigDecimal communityRevenueDecimal = orderMapper.sumPlatformFeeByStatus(PAID_STATUS);
        double communityRevenue = communityRevenueDecimal != null ? communityRevenueDecimal.doubleValue() : 0.0;

        long totalSales = orderMapper.countByStatus(PAID_STATUS);
        System.out.println("DEBUG: Total sales count: " + totalSales);

        List<MonthlyRevenueDTO> monthlyRevenue = orderMapper.findMonthlyRevenue(PAID_STATUS);
        System.out.println("DEBUG: Monthly revenue entries: " + monthlyRevenue.size());

        List<Map<String, Object>> topProducts = orderMapper.findTopSellingProductCounts(PAID_STATUS);
        System.out.println("DEBUG: Top products raw data: " + topProducts);

        ProductDTO topSellingProduct = getTopSellingProductFromStats(topProducts);
        List<ProductDTO> topSellingProducts = getTopSellingProductsFromStats(topProducts, 5);
        System.out.println("DEBUG: Final top selling product: " + (topSellingProduct != null ? topSellingProduct.getName() : "null"));
        System.out.println("DEBUG: Top selling products count: " + topSellingProducts.size());
        double averageOrderValue = (totalSales > 0) ? totalRevenue / totalSales : 0.0;
        long developerCount = userMapper.countByRole(DEVELOPER_ROLE);
        long productCount = productMapper.selectCount(null);
        List<Order> recentRawOrders = orderMapper.findRecentPaidOrders(10);
        List<OrderDTO> recentOrders = convertToOrderDTOs(recentRawOrders);
        List<DailyRevenueDTO> dailyRevenue = orderMapper.findDailyRevenue(PAID_STATUS);

        EarningsSummaryDTO summary = new EarningsSummaryDTO(totalRevenue, communityRevenue, totalSales, monthlyRevenue, dailyRevenue, topSellingProduct, averageOrderValue, developerCount, productCount, recentOrders);
        summary.setTopSellingProducts(topSellingProducts);
        return summary;
    }

    @Override
    public EarningsSummaryDTO getDeveloperEarningsSummary(Long developerId) {
        BigDecimal developerRevenueDecimal = orderMapper.sumDeveloperRevenueByStatusAndDeveloper(PAID_STATUS, developerId);
        double developerRevenue = developerRevenueDecimal != null ? developerRevenueDecimal.doubleValue() : 0.0;
        BigDecimal availableBalance = getAvailableBalance(developerId);

        // Note: For a developer's view, "totalRevenue" now means their actual take-home pay.
        long totalSales = orderMapper.countByStatusAndDeveloper(PAID_STATUS, developerId);
        List<MonthlyRevenueDTO> monthlyRevenue = orderMapper.findMonthlyRevenueByDeveloper(PAID_STATUS, developerId);
        List<Map<String, Object>> topProducts = orderMapper.findTopSellingProductCountsByDeveloper(PAID_STATUS, developerId);
        ProductDTO topSellingProduct = getTopSellingProductFromStats(topProducts);
        
        // We need the original total fee to calculate the average order value correctly.
        BigDecimal totalFeeForAvg = orderMapper.sumTotalFeeByStatusAndDeveloper(PAID_STATUS, developerId);
        double totalFeeForAvgDouble = totalFeeForAvg != null ? totalFeeForAvg.doubleValue() : 0.0;
        double averageOrderValue = (totalSales > 0) ? totalFeeForAvgDouble / totalSales : 0.0;
        
        long productCount = productMapper.countByUserId(developerId);
        List<Order> recentRawOrders = orderMapper.findRecentPaidOrdersByDeveloper(developerId, 5);
        List<OrderDTO> recentOrders = convertToOrderDTOs(recentRawOrders);
        List<DailyRevenueDTO> dailyRevenue = orderMapper.findDailyRevenueByDeveloper(PAID_STATUS, developerId);

        // For developer view, developerCount is not relevant in this context, so setting to 0.
        // The totalRevenue parameter for this constructor is now the developer's net revenue.
        return new EarningsSummaryDTO(developerRevenue, availableBalance, totalSales, monthlyRevenue, dailyRevenue, topSellingProduct, averageOrderValue, 0, productCount, recentOrders);
    }
    
    @Override
    public WithdrawalRequest createWithdrawalRequest(CreateWithdrawalRequestDTO requestDTO, String username) {
        User user = userMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到。");
        }
        if (!"DEVELOPER".equals(user.getRole())) {
            throw new InvalidDataException("只有开发者可以申请提现。");
        }

        String account = getWithdrawalAccount(user, requestDTO.getChannel());
        if (account == null || account.isEmpty()) {
            throw new InvalidDataException("您尚未绑定此提现渠道的账号，请先在个人资料中设置。");
        }

        BigDecimal availableBalance = getAvailableBalance(user.getId());

        if (requestDTO.getAmount().compareTo(availableBalance) > 0) {
            throw new InvalidDataException("提现金额超过可提现余额。您的可提现余额为：" + availableBalance + "元。");
        }
        if (requestDTO.getAmount().compareTo(BigDecimal.ONE) < 0) {
            throw new InvalidDataException("提现金额必须大于1元。");
        }

        WithdrawalRequest withdrawalRequest = new WithdrawalRequest();
        withdrawalRequest.setUserId(user.getId());
        withdrawalRequest.setAmount(requestDTO.getAmount());
        withdrawalRequest.setChannel(requestDTO.getChannel());
        withdrawalRequest.setAccountInfo(account);
        withdrawalRequest.setStatus(WithdrawalStatus.PENDING);
        withdrawalRequest.setNotes(requestDTO.getNotes());
        withdrawalRequest.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        withdrawalRequest.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        withdrawalRequestMapper.insert(withdrawalRequest);
        return withdrawalRequest;
    }

    @Override
    public List<WithdrawalRequestDetailsDTO> getWithdrawalRequestsForUser(String username) {
        User user = userMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>().eq("username", username));
        if (user == null) {
            throw new ResourceNotFoundException("用户未找到。");
        }
        return withdrawalRequestMapper.findByUserIdWithUserDetails(user.getId());
    }

    @Override
    public List<WithdrawalRequestDetailsDTO> getAllWithdrawalRequests() {
        return withdrawalRequestMapper.findAllWithUserDetails();
    }

    @Override
    public WithdrawalRequest reviewWithdrawalRequest(Long requestId, ReviewWithdrawalRequestDTO reviewDTO, String adminUsername) {
        WithdrawalRequest request = withdrawalRequestMapper.selectById(requestId);
        if (request == null) {
            throw new ResourceNotFoundException("提现申请未找到。");
        }
        if (request.getStatus() != WithdrawalStatus.PENDING) {
            throw new InvalidDataException("该申请已被处理，请勿重复操作。");
        }

        User admin = userMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>().eq("username", adminUsername));
        if (admin == null) {
            throw new ResourceNotFoundException("审核员用户未找到。");
        }

        request.setReviewedBy(admin.getId());
        request.setReviewedAt(new Timestamp(System.currentTimeMillis()));
        request.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        request.setStatus(reviewDTO.getStatus());

        if (reviewDTO.getStatus() == WithdrawalStatus.REJECTED) {
            if (reviewDTO.getNotes() == null || reviewDTO.getNotes().trim().isEmpty()) {
                throw new InvalidDataException("拒绝提现申请必须提供原因。");
            }
            request.setNotes(reviewDTO.getNotes());
        }

        withdrawalRequestMapper.updateById(request);
        // Here you might want to send a notification to the user.
        return request;
    }

    @Override
    public List<OrderDTO> getDeveloperTransactionHistory(String username) {
        User user = userMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<User>().eq("username", username));
        if (user == null || !"DEVELOPER".equals(user.getRole())) {
            throw new ResourceNotFoundException("开发者用户未找到。");
        }
        List<Order> rawOrders = orderMapper.findAllPaidOrdersByDeveloper(user.getId());
        return convertToOrderDTOs(rawOrders);
    }

    private BigDecimal getAvailableBalance(Long developerId) {
        BigDecimal totalRevenue = orderMapper.sumDeveloperRevenueByStatusAndDeveloper(PAID_STATUS, developerId);
        if (totalRevenue == null) {
            totalRevenue = BigDecimal.ZERO;
        }
        BigDecimal paidOut = withdrawalRequestMapper.sumApprovedWithdrawalsByUserId(developerId);
        return totalRevenue.subtract(paidOut);
    }

    private String getWithdrawalAccount(User user, WithdrawalChannel channel) {
        if (channel == WithdrawalChannel.ALIPAY) {
            return user.getAlipayAccount();
        } else if (channel == WithdrawalChannel.WECHAT) {
            return user.getWechatAccount();
        }
        return null;
    }
    
    private ProductDTO getTopSellingProductFromStats(List<Map<String, Object>> topProducts) {
        System.out.println("DEBUG: getTopSellingProductFromStats called with topProducts: " + topProducts);

        if (topProducts == null || topProducts.isEmpty()) {
            System.out.println("DEBUG: No top products found - topProducts is null or empty");
            return null;
        }

        Map<String, Object> topProductData = topProducts.get(0);
        System.out.println("DEBUG: Top product data: " + topProductData);

        Long topProductId = ((Number) topProductData.get("product_id")).longValue();
        System.out.println("DEBUG: Top product ID: " + topProductId);

        // Use the same query as getApprovedProductsWithAuthor to get complete product info including author avatar
        List<ProductDTO> products = productMapper.selectApprovedProductsWithAuthor();
        System.out.println("DEBUG: Found " + products.size() + " approved products");

        ProductDTO dto = products.stream()
            .filter(p -> p.getId().equals(topProductId))
            .findFirst()
            .orElse(null);

        System.out.println("DEBUG: Final ProductDTO: " + (dto != null ? dto.getName() : "null"));
        return dto;
    }

    private List<ProductDTO> getTopSellingProductsFromStats(List<Map<String, Object>> topProducts, int limit) {
        System.out.println("DEBUG: getTopSellingProductsFromStats called with " + (topProducts != null ? topProducts.size() : 0) + " products, limit: " + limit);

        if (topProducts == null || topProducts.isEmpty()) {
            System.out.println("DEBUG: No top products found - returning empty list");
            return new ArrayList<>();
        }

        // Get all approved products once
        List<ProductDTO> allProducts = productMapper.selectApprovedProductsWithAuthor();
        System.out.println("DEBUG: Found " + allProducts.size() + " approved products");

        List<ProductDTO> result = new ArrayList<>();
        int count = Math.min(topProducts.size(), limit);

        for (int i = 0; i < count; i++) {
            Map<String, Object> topProductData = topProducts.get(i);
            Long productId = ((Number) topProductData.get("product_id")).longValue();
            Long salesCount = ((Number) topProductData.get("sales_count")).longValue();

            ProductDTO dto = allProducts.stream()
                .filter(p -> p.getId().equals(productId))
                .findFirst()
                .orElse(null);

            if (dto != null) {
                // 可以在这里添加销量信息到DTO中
                System.out.println("DEBUG: Adding product: " + dto.getName() + " with " + salesCount + " sales");
                result.add(dto);
            }
        }

        System.out.println("DEBUG: Returning " + result.size() + " top selling products");
        return result;
    }
    
    private List<OrderDTO> convertToOrderDTOs(List<Order> orders) {
        return orders.stream().map(order -> {
            OrderDTO dto = new OrderDTO();
            dto.setId(order.getId());
            dto.setTotalFee(order.getTotalFee());
            dto.setPaidAt(order.getPaidAt());

            Product product = productMapper.findByIdIncludeDeleted(order.getProductId());
            ProductDTO productDTO = new ProductDTO();

            if (product != null) {
                productDTO.setName(product.getName() + (product.isDeleted() ? " [已下架]" : ""));
            } else {
                productDTO.setName("[商品信息不存在]");
            }
                dto.setProduct(productDTO);

            User user = userMapper.selectById(order.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProductSalesStatsDTO> getDeveloperProductSalesStats(Long developerId) {
        return orderMapper.findProductSalesStatsByDeveloper(PAID_STATUS, developerId);
    }
}