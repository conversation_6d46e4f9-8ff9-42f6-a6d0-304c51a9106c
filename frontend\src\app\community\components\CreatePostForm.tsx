'use client';

import { useState, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Bold, Italic, Code, Link, List, Quote, Image as ImageIcon, X, Send, Pilcrow, Type } from 'lucide-react';
import { motion } from 'framer-motion';
import { CommunityTopic } from '@/types/CommunityTopic';
import { MajorEra } from '@/lib/gtnh-progression-data';
import { generatePostPartitionOptions, PostPartitionOption } from '@/lib/community-sections-data';
import { HierarchicalPartitionSelector } from './HierarchicalPartitionSelector';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';

interface CreatePostFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (post: { title: string; content: string; partition: string }) => void;
  partitions: MajorEra[];
}

interface CustomCodeProps extends React.HTMLAttributes<HTMLElement> {
    inline?: boolean;
    node?: any;
}

export const CreatePostForm: React.FC<CreatePostFormProps> = ({ isOpen, onClose, onSubmit, partitions }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [partition, setPartition] = useState('');
  const [livePreview, setLivePreview] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const partitionOptions = useMemo<ComboboxOption[]>(() => {
    // 合并传统的阶段分区和新的社区分区
    const legacyOptions = partitions.flatMap(era =>
      era.tiers.flatMap(tier =>
        tier.topics.map(topic => ({
          value: `${era.name}|${tier.name}|${topic.subStage}`,
          label: `[阶段] ${tier.name} - ${topic.subStage}: ${topic.milestone}`
        }))
      )
    );

    const newOptions = generatePostPartitionOptions().map(option => ({
      value: option.value,
      label: option.label
    }));

    return [...legacyOptions, ...newOptions];
  }, [partitions]);

  const handleSubmit = () => {
    if (title && content && partition) {
      onSubmit({ title, content, partition });
      // Reset form
      setTitle('');
      setContent('');
      setPartition('');
    }
  };

  const handleEditorAction = (action: 'bold' | 'italic' | 'code' | 'quote' | 'link' | 'list' | 'image') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    let newContent = '';
    let newCursorPos = 0;

    switch (action) {
      case 'bold':
        newContent = `${content.substring(0, start)}**${selectedText}**${content.substring(end)}`;
        newCursorPos = end + 4;
        break;
      case 'italic':
        newContent = `${content.substring(0, start)}*${selectedText}*${content.substring(end)}`;
        newCursorPos = end + 2;
        break;
      case 'code':
        newContent = `${content.substring(0, start)}\`\`\`\n${selectedText}\n\`\`\`${content.substring(end)}`;
        newCursorPos = end + 8;
        break;
      case 'quote':
        newContent = `${content.substring(0, start)}> ${selectedText}${content.substring(end)}`;
        newCursorPos = end + 2;
        break;
      case 'list':
        newContent = `${content.substring(0, start)}- ${selectedText}${content.substring(end)}`;
        newCursorPos = end + 2;
        break;
      case 'link':
        newContent = `${content.substring(0, start)}[${selectedText}](${''})${content.substring(end)}`;
        newCursorPos = start + 1;
        break;
      case 'image':
        newContent = `${content.substring(0, start)}![${selectedText}](${''})${content.substring(end)}`;
        newCursorPos = start + 2;
        break;
      default:
        return;
    }
    
    setContent(newContent);
    setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };
  
  const EditorToolbar: React.FC = () => (
    <div className="flex items-center gap-1 p-2 rounded-t-md bg-zinc-800/50 border-b border-zinc-700">
      {([
        { action: 'bold', icon: Bold }, { action: 'italic', icon: Italic },
        { action: 'quote', icon: Quote }, { action: 'code', icon: Code },
        { action: 'link', icon: Link }, { action: 'list', icon: List },
        { action: 'image', icon: ImageIcon }
      ] as const).map(({ action, icon: Icon }) => (
        <Button key={action} variant="ghost" size="sm" onMouseDown={(e) => {e.preventDefault(); handleEditorAction(action);}} className="text-zinc-400 hover:text-white hover:bg-zinc-700">
          <Icon className="h-4 w-4" />
        </Button>
      ))}
    </div>
  );

  const markdownComponents = {
    code({node, inline, className, children, ...props}: CustomCodeProps) {
        const match = /language-(\w+)/.exec(className || '');
        return !inline && match ? (
        <div className="bg-zinc-800/50 my-2 rounded-md overflow-hidden">
            <div className="px-4 py-1 bg-zinc-700/50 text-xs text-zinc-400">{match[1]}</div>
            <pre className="p-4 text-sm overflow-x-auto"><code {...props}>{children}</code></pre>
        </div>
        ) : (
        <code className="bg-zinc-700 text-zinc-300 px-1 py-0.5 rounded-sm" {...props}>
            {children}
        </code>
        );
    }
  }

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl h-[95vh] bg-zinc-950/90 backdrop-blur-lg border-zinc-800 text-zinc-200 flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-zinc-50 to-zinc-400">创建新帖子</DialogTitle>
          <DialogDescription className="text-zinc-500">
            分享你的想法、项目或问题。请选择最合适的分区。
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-4 flex-grow min-h-0 overflow-hidden">
          {/* Title Input */}
          <div className="flex-shrink-0">
            <Input
              type="text"
              placeholder="标题：铸造一个传奇的开端"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-lg bg-zinc-900 border-zinc-700 h-12 placeholder:text-zinc-600 focus:border-zinc-500"
            />
          </div>

          {/* Partition Selector - Collapsible */}
          <div className="flex-shrink-0 border border-zinc-700 rounded-md bg-zinc-900">
            <div className="p-3 border-b border-zinc-700">
              <h3 className="text-sm font-medium text-zinc-300">选择分区</h3>
              {partition && (
                <p className="text-xs text-zinc-500 mt-1">
                  已选择: {partitionOptions.find(opt => opt.value === partition)?.label || partition}
                </p>
              )}
            </div>
            <div className="max-h-64 overflow-y-auto p-4">
              <HierarchicalPartitionSelector
                value={partition}
                onChange={setPartition}
                placeholder="选择分区"
              />
            </div>
          </div>

          {/* Editor and Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 flex-grow min-h-0">
            {/* Editor Column */}
            <div className="flex flex-col min-h-0">
              <div className="flex flex-col flex-grow rounded-md border border-zinc-800 bg-zinc-900">
                <EditorToolbar />
                <Textarea
                  ref={textareaRef}
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="在这里输入你的帖子内容... 支持 Markdown 格式。"
                  className="flex-grow w-full bg-transparent text-base resize-none p-4 focus:outline-none min-h-[300px]"
                  maxLength={5000}
                />
                <div className="text-right text-sm text-zinc-500 p-2 border-t border-zinc-800">
                  {content.length} / 5000
                </div>
              </div>
            </div>

            {/* Preview Column */}
            <div className="flex flex-col min-h-0">
              <div className="flex flex-col flex-grow rounded-md border border-zinc-800 bg-zinc-900/50 overflow-hidden">
                <div className="p-3 border-b border-zinc-700">
                  <h3 className="text-sm font-medium text-zinc-300">预览</h3>
                </div>
                <div className="flex-grow overflow-y-auto p-4">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    className="prose prose-invert prose-sm max-w-none"
                    components={markdownComponents}
                  >
                    {content || "在左侧输入内容以查看预览..."}
                  </ReactMarkdown>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button onClick={onClose} variant="outline" className="border-zinc-700 text-zinc-300 hover:bg-zinc-800 hover:text-white">取消</Button>
          <Button onClick={handleSubmit} className="bg-gradient-to-br from-zinc-500 to-zinc-700 text-white hover:from-zinc-400 hover:to-zinc-600">
            <Send className="w-4 h-4 mr-2" />
            发布
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 