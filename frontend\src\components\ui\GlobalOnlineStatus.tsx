/**
 * 全局在线状态组件
 * 可以在任何地方使用，显示用户的在线状态
 */

import React from 'react';
import { useOnlineStatus } from '@/contexts/GlobalWebSocketContext';
import { OnlineStatusIndicator } from './OnlineStatusIndicator';
import { cn } from '@/lib/utils';

interface GlobalOnlineStatusProps {
  userId: number;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

/**
 * 全局在线状态指示器
 * 自动从全局WebSocket上下文获取在线状态
 */
export const GlobalOnlineStatus: React.FC<GlobalOnlineStatusProps> = ({
  userId,
  size = 'md',
  showText = false,
  className
}) => {
  const { isUserOnline } = useOnlineStatus();
  
  return (
    <OnlineStatusIndicator
      isOnline={isUserOnline(userId)}
      size={size}
      showText={showText}
      className={className}
    />
  );
};

interface UserWithOnlineStatusProps {
  userId: number;
  userName: string;
  avatarUrl?: string;
  showOnlineText?: boolean;
  className?: string;
  avatarSize?: 'sm' | 'md' | 'lg';
}

/**
 * 带在线状态的用户组件
 */
export const UserWithOnlineStatus: React.FC<UserWithOnlineStatusProps> = ({
  userId,
  userName,
  avatarUrl,
  showOnlineText = false,
  className,
  avatarSize = 'md'
}) => {
  const { isUserOnline } = useOnlineStatus();
  
  const avatarSizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  return (
    <div className={cn('flex items-center gap-3', className)}>
      {/* 头像和在线状态 */}
      <div className="relative">
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt={userName}
            className={cn('rounded-full object-cover', avatarSizeClasses[avatarSize])}
          />
        ) : (
          <div className={cn(
            'rounded-full bg-gray-300 flex items-center justify-center',
            avatarSizeClasses[avatarSize]
          )}>
            <span className="text-gray-600 font-medium">
              {userName.charAt(0).toUpperCase()}
            </span>
          </div>
        )}
        
        {/* 在线状态指示器 */}
        <div className="absolute -bottom-1 -right-1">
          <GlobalOnlineStatus userId={userId} size="sm" />
        </div>
      </div>

      {/* 用户信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-gray-900 truncate">{userName}</h3>
          {showOnlineText && (
            <GlobalOnlineStatus 
              userId={userId} 
              size="sm" 
              showText 
            />
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * 在线用户计数器
 */
export const OnlineUserCount: React.FC<{ className?: string }> = ({ className }) => {
  const { onlineUsers } = useOnlineStatus();
  
  return (
    <div className={cn('flex items-center gap-2 text-sm text-gray-600', className)}>
      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
      <span>{onlineUsers.size} 人在线</span>
    </div>
  );
};
