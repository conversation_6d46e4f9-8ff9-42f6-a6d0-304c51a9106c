import os
import subprocess
import paramiko
import glob
import logging
from datetime import datetime

# Enable paramiko logging
# This will create a file named 'paramiko.log' in the current directory.
# You might want to remove this or comment it out after debugging.
paramiko.util.log_to_file("paramiko.log")

# --- 配置 ---

# 服务器信息
SERVER_IP = "*************"
SERVER_USER = "root"
# 推荐使用SSH密钥进行认证，请将your_key_path替换为您的私钥路径
# 如果使用密码，请将use_key设置为False并填写密码
SSH_KEY_PATH = "C:/Users/<USER>/.ssh/id_rsa"  # 修改为您的私钥路径
# 如果您的私钥是加密的，请在此处填写私钥的密码。
# 如果您使用密码认证（USE_KEY_FOR_AUTH = False），请在此处填写服务器密码。
SSH_PASSWORD = "dsbz233S"
USE_KEY_FOR_AUTH = True  # 设置为True使用密钥，设置为False使用密码

# 本地项目路径
PROJECT_ROOT = r"E:\GTNH Projects\GTNH Website"
BACKEND_DIR = os.path.join(PROJECT_ROOT, "backend")
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")

# 远程服务器路径
REMOTE_BACKEND_DIR = "/home/<USER>/app/backend"
REMOTE_FRONTEND_DIR = "/home/<USER>/app/frontend"

# 服务名称
BACKEND_SERVICE_NAME = "gtnh-backend.service"  # 您在服务器上配置的systemd服务名
FRONTEND_PM2_NAME = "gtnh-frontend"  # 您想在pm2中使用的应用名


# --- 函数定义 ---

def get_timestamp():
    """返回格式化的当前时间戳"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def run_local_command(command, working_dir, extra_env=None):
    """在本地执行命令"""
    print(f"[{get_timestamp()}] 在目录 {working_dir} 中执行: {command}")

    # 获取当前环境变量并更新，这是正确的注入方式
    env = os.environ.copy()
    if extra_env:
        env.update(extra_env)

    try:
        # 在 Popen 调用中传入更新后的 env
        process = subprocess.Popen(command, cwd=working_dir, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                   env=env)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"[{get_timestamp()}] 命令执行失败! 返回码: {process.returncode}")
            print("--- STDOUT ---")
            print(stdout.decode('gbk', errors='ignore'))
            print("--- STDERR ---")
            print(stderr.decode('gbk', errors='ignore'))
            exit(1)
        # print(stdout.decode('gbk', errors='ignore'))
        # print("命令执行成功!")
    except Exception as e:
        print(f"[{get_timestamp()}] 执行本地命令时出错: {e}")
        exit(1)


def create_ssh_client(server, user, password, key_path, use_key=True):
    """创建并返回一个SSH客户端，并设置连接超时"""
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        if use_key:
            print(f"[{get_timestamp()}] 使用密钥 {key_path} 连接到 {user}@{server}...")
            private_key = paramiko.RSAKey.from_private_key_file(key_path, password=password)
            # 添加 15 秒连接超时
            client.connect(server, username=user, pkey=private_key, timeout=15)
        else:
            print(f"[{get_timestamp()}] 使用密码连接到 {user}@{server}...")
            # 添加 15 秒连接超时
            client.connect(server, username=user, password=password, timeout=15)
        print(f"[{get_timestamp()}] SSH连接成功!")
        return client
    except Exception as e:
        print(f"[{get_timestamp()}] SSH连接失败: {e}")
        exit(1)


def upload_file_sftp(ssh_client, local_path, remote_path):
    """使用SFTP上传文件"""
    try:
        sftp = ssh_client.open_sftp()
        print(f"[{get_timestamp()}] 正在上传: {os.path.basename(local_path)}...")
        sftp.put(local_path, remote_path)
        sftp.close()
        print(f"[{get_timestamp()}] ✅  {os.path.basename(local_path)} 上传成功!")
    except Exception as e:
        print(f"[{get_timestamp()}] \n上传失败: {e}")
        exit(1)


def execute_remote_command(ssh_client, command, quiet=False, timeout=None):
    """
    在远程服务器上执行命令
    :param ssh_client: paramiko SSH 客户端
    :param command: 要执行的命令
    :param quiet: 如果为True，将不打印stdout/stderr，只返回成功或失败。
    :param timeout: (新增) 命令执行的超时时间（秒）。
    :return: 命令是否成功执行 (exit status 0)
    """
    print(f"[{get_timestamp()}] 在服务器上执行: {command}")
    try:
        # 将timeout参数传递给exec_command
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
        exit_status = stdout.channel.recv_exit_status()

        if exit_status == 0:
            if not quiet:
                print("--- STDOUT ---")
                # 使用 .decode() 替代 .decode('utf-8')，让其使用默认编码
                print(stdout.read().decode())
                print("远程命令执行成功!")
            return True
        else:
            if not quiet:
                print(f"[{get_timestamp()}] 远程命令执行失败! 返回码: {exit_status}")
                print("--- STDOUT ---")
                print(stdout.read().decode())
                print("--- STDERR ---")
                print(stderr.read().decode())
            return False
    except Exception as e:  # 捕捉包括超时在内的所有异常
        if not quiet:
            print(f"[{get_timestamp()}] 远程命令执行期间出错 (很可能是超时): {e}")
        return False


def clean_local_storage(project_root):
    """在构建前，清理本地开发环境中上传的图片缓存，防止混淆。"""
    print(f"\n--- [{get_timestamp()}] 0. 清理本地storage目录 ---")
    storage_base = os.path.join(project_root, "backend", "storage")
    dirs_to_clean = [os.path.join(storage_base, "avatars"), os.path.join(storage_base, "banners")]

    for directory in dirs_to_clean:
        if not os.path.isdir(directory):
            print(f"[{get_timestamp()}] 目录 {os.path.basename(directory)} 不存在，跳过。")
            continue

        print(f"[{get_timestamp()}] 正在清理目录: {directory}")
        # 使用 os.listdir 比 glob 更直接
        filenames = os.listdir(directory)
        if not filenames:
            print("  -> 目录为空，无需清理。")
            continue

        deleted_count = 0
        for filename in filenames:
            file_path = os.path.join(directory, filename)
            try:
                # 确保我们只删除文件和符号链接，不删除子目录
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                    deleted_count += 1
            except Exception as e:
                print(f"[{get_timestamp()}]  -> 删除 {file_path} 时出错: {e}")

        if deleted_count > 0:
            print(f"[{get_timestamp()}]  -> 成功删除 {deleted_count} 个文件。")

    print(f"[{get_timestamp()}] ✅ 本地storage目录清理完毕!")


# --- 主逻辑 ---

def main():
    # 0. 清理本地缓存
    clean_local_storage(PROJECT_ROOT)

    # 1. 打包后端
    print(f"\n--- [{get_timestamp()}] 1. 开始打包后端 ---")
    backend_pom_path = os.path.join(BACKEND_DIR, "pom.xml")
    run_local_command(f'mvn clean package -f "{backend_pom_path}" -DskipTests', BACKEND_DIR)
    print(f"[{get_timestamp()}] ✅ 后端打包完成!")

    # 2. 打包前端
    print(f"\n--- [{get_timestamp()}] 2. 开始构建前端 ---")
    # 在构建时注入生产环境的API URL。
    # Nginx会处理HTTPS和反向代理
    frontend_build_env = {
        'NEXT_PUBLIC_API_URL': 'https://kitolus.top'
    }
    run_local_command("npm run build", FRONTEND_DIR, extra_env=frontend_build_env)
    print(f"[{get_timestamp()}] ✅ 前端构建完成!")

    # 3. 压缩前端文件 (使用 tar 工具)
    print(f"\n--- [{get_timestamp()}] 3. 开始压缩前端文件 ---")
    frontend_archive_name = "frontend.tar.gz"
    local_frontend_archive_path = os.path.join(FRONTEND_DIR, frontend_archive_name)

    # 删除旧的压缩包
    if os.path.exists(local_frontend_archive_path):
        os.remove(local_frontend_archive_path)
        print(f"[{get_timestamp()}] 已删除旧的压缩文件: {local_frontend_archive_path}")

    # 需要打包的文件夹和文件
    files_to_archive = [".next", "public", "package.json", "package-lock.json", "next.config.mjs",
                        "ecosystem.config.js"]

    # 检查文件是否存在，并构建要压缩的文件列表字符串
    items_to_archive_str = []
    for item in files_to_archive:
        if os.path.exists(os.path.join(FRONTEND_DIR, item)):
            items_to_archive_str.append(f'"{item}"')
        else:
            print(f"[{get_timestamp()}] 警告: 在 {FRONTEND_DIR} 中找不到 '{item}'，将跳过压缩。")

    if not items_to_archive_str:
        print(f"[{get_timestamp()}] 错误：没有找到任何可以压缩的前端文件。")
        exit(1)

    # 构建 tar 命令行指令
    # Windows 10/11 内置了 tar.exe
    # c: 创建
    # z: 使用 gzip 压缩
    # v: 显示过程
    # f: 指定存档文件名
    command = f'tar -czvf "{frontend_archive_name}" --exclude="public/music" {" ".join(items_to_archive_str)}'

    # 在前端项目目录执行压缩命令
    run_local_command(command, FRONTEND_DIR)
    print(f"[{get_timestamp()}] ✅ 前端文件压缩成功!")

    # 连接服务器
    ssh = create_ssh_client(SERVER_IP, SERVER_USER, SSH_PASSWORD, SSH_KEY_PATH, USE_KEY_FOR_AUTH)

    # 4. 部署后端
    print(f"\n--- [{get_timestamp()}] 4. 开始部署后端 ---")

    # 找到最新的JAR文件
    target_dir = os.path.join(BACKEND_DIR, "target")
    list_of_jars = glob.glob(os.path.join(target_dir, '*.jar'))
    if not list_of_jars:
        print(f"[{get_timestamp()}] 在 {target_dir} 找不到JAR文件")
        exit(1)
    # 排除-sources.jar 和 -javadoc.jar
    non_source_jars = [f for f in list_of_jars if '-sources.jar' not in f and '-javadoc.jar' not in f]
    if not non_source_jars:
        print(f"[{get_timestamp()}] 在 {target_dir} 找不到可执行的JAR文件")
        exit(1)

    latest_jar = max(non_source_jars, key=os.path.getctime)
    jar_filename = os.path.basename(latest_jar)
    print(f"[{get_timestamp()}] 找到最新的JAR文件: {latest_jar}")

    # 创建远程目录并上传JAR
    execute_remote_command(ssh, f"mkdir -p {REMOTE_BACKEND_DIR}")
    upload_file_sftp(ssh, latest_jar, os.path.join(REMOTE_BACKEND_DIR, jar_filename).replace("\\", "/"))

    # --- 修改后的健壮的重启逻辑 ---
    print(f"\n--> [{get_timestamp()}] 正在尝试正常停止服务 (10秒超时)...")
    stop_successful = execute_remote_command(ssh, f"sudo systemctl stop {BACKEND_SERVICE_NAME}", timeout=10)

    if not stop_successful:
        print(f"--> [{get_timestamp()}] 服务未能正常关闭，将被强制终止。")
        # -9 代表 SIGKILL 信号
        # -f 代表匹配完整命令行
        kill_command = f"sudo pkill -9 -f {jar_filename}"
        execute_remote_command(ssh, kill_command)
        print(f"--> [{get_timestamp()}] 等待2秒确保进程已被终止...")
        execute_remote_command(ssh, "sleep 2")

    print(f"--> [{get_timestamp()}] 正在启动新的后端服务...")
    execute_remote_command(ssh, f"sudo systemctl start {BACKEND_SERVICE_NAME}")

    print(f"--> [{get_timestamp()}] 等待2秒让服务初始化...")
    execute_remote_command(ssh, "sleep 2")
    print(f"--> [{get_timestamp()}] 检查服务最终状态:")
    execute_remote_command(ssh, f"sudo systemctl status {BACKEND_SERVICE_NAME}")
    print(f"[{get_timestamp()}] ✅ 后端部署完成!")
    # --- 重启逻辑结束 ---


    # 5. 部署前端
    print(f"\n--- [{get_timestamp()}] 5. 开始部署前端 ---")
    remote_frontend_archive_path = os.path.join(REMOTE_FRONTEND_DIR, frontend_archive_name).replace("\\", "/")

    # 创建远程目录并上传压缩包
    execute_remote_command(ssh, f"mkdir -p {REMOTE_FRONTEND_DIR}")
    upload_file_sftp(ssh, local_frontend_archive_path, remote_frontend_archive_path)

    # 解压、安装依赖
    deploy_commands = f"""
    cd {REMOTE_FRONTEND_DIR} && \
    echo "正在解压 {frontend_archive_name}..." && \
    tar -xzvf {frontend_archive_name} && \
    echo "正在删除旧的压缩包..." && \
    rm {frontend_archive_name} && \
    echo "关键：正在将所有文件所有权更改为 'kitolus' 用户..." && \
    chown -R kitolus:kitolus . && \
    echo "正在以 'kitolus' 用户身份安装npm依赖..." && \
    sudo -u kitolus npm install --production
    """
    execute_remote_command(ssh, deploy_commands)

    # 检查前端服务状态并决定是启动还是重启
    print(f"\n--- [{get_timestamp()}] 检查并启动/重启前端服务: {FRONTEND_PM2_NAME} ---")
    check_command = f"sudo -u kitolus pm2 describe {FRONTEND_PM2_NAME}"

    # 使用 quiet=True 来执行检查，这样在进程不存在时不会打印错误
    process_exists = execute_remote_command(ssh, check_command, quiet=True)

    if process_exists:
        print(f"[{get_timestamp()}] 进程 '{FRONTEND_PM2_NAME}' 已存在, 正在重启...")
        # 重启命令也需要在 kitolus 用户下，并且在正确的目录中执行以加载正确的环境
        restart_command = f"sudo -u kitolus /bin/bash -c 'cd {REMOTE_FRONTEND_DIR} && pm2 restart {FRONTEND_PM2_NAME}'"
        execute_remote_command(ssh, restart_command)
    else:
        print(f"[{get_timestamp()}] 进程 '{FRONTEND_PM2_NAME}' 不存在, 正在启动...")
        # 启动命令需要在 kitolus 用户下，并且在正确的目录中
        start_command = f"sudo -u kitolus /bin/bash -c 'cd {REMOTE_FRONTEND_DIR} && pm2 start npm --name {FRONTEND_PM2_NAME} -- run start'"
        execute_remote_command(ssh, start_command)
    
    print(f"[{get_timestamp()}] ✅ 前端部署完成!")

    # 关闭SSH连接
    ssh.close()
    print(f"\n[{get_timestamp()}] 部署完成!")


if __name__ == "__main__":
    main()
