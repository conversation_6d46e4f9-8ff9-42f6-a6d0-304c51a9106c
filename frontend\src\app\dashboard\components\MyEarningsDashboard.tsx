'use client';

import { useEffect, useState } from 'react';
import {
    getMyEarningsSummary,
    getMyWithdrawalRequests,
    createWithdrawalRequest,
    getDeveloperTransactions,
    getMyProductSalesStats,
} from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { DollarSign, Wallet, Landmark, Pencil, Loader2, ServerCrash, Package } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { EarningsSummaryDTO } from '@/types/EarningsSummary';
import { WithdrawalRequest, WithdrawalStatus, WithdrawalChannel } from '@/types/WithdrawalRequest';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getMyProfile } from '@/services/api';
import { ProfileDTO } from '@/types/ProfileDTO';
import { OrderDTO } from '@/types/Order';
import { ProductSalesStats } from '@/types/ProductSalesStats';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import ProductSalesStatsTable from '@/components/ProductSalesStatsTable';

// Helper function to safely format dates
const formatDate = (dateString: string | Date | undefined | null) => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '无效日期';
    }
};

const transactionColumns: ColumnDef<OrderDTO>[] = [
    {
        accessorKey: "id",
        header: "订单号",
    },
    {
        accessorKey: "product.name",
        header: "产品名称",
        cell: ({ row }) => row.original.product.name,
    },
    {
        accessorKey: "username",
        header: "购买用户",
    },
    {
        accessorKey: "totalFee",
        header: "金额",
        cell: ({ row }) => `¥${row.original.totalFee.toFixed(2)}`,
    },
    {
        accessorKey: "paidAt",
        header: "支付时间",
        cell: ({ row }) => formatDate(row.original.paidAt),
    },
];

const MyEarningsDashboard = () => {
    const { user: authUser } = useAuth();
    const [profile, setProfile] = useState<ProfileDTO | null>(null);
    const [summary, setSummary] = useState<EarningsSummaryDTO | null>(null);
    const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalRequest[]>([]);
    const [transactions, setTransactions] = useState<OrderDTO[]>([]);
    const [productSalesStats, setProductSalesStats] = useState<ProductSalesStats[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isWithdrawalModalOpen, setIsWithdrawalModalOpen] = useState(false);

    useEffect(() => {
        const fetchDeveloperData = async () => {
            if (!authUser) return;
            try {
                setLoading(true);
                const [profileData, summaryData, historyData, transactionsData, salesStatsData] = await Promise.all([
                    getMyProfile(),
                    getMyEarningsSummary(),
                    getMyWithdrawalRequests(),
                    getDeveloperTransactions(),
                    getMyProductSalesStats(),
                ]);
                setProfile(profileData);
                setSummary(summaryData);
                setWithdrawalHistory(historyData);
                setTransactions(transactionsData);
                setProductSalesStats(salesStatsData);
            } catch (err) {
                setError('无法加载您的收益数据，请稍后重试。');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchDeveloperData();
    }, [authUser]);

    const formatCurrency = (value: number | undefined) => `¥${(value || 0).toFixed(2)}`;
    
    const statusTextMap: { [key in WithdrawalStatus]: string } = {
        [WithdrawalStatus.PENDING]: '审核中',
        [WithdrawalStatus.APPROVED]: '已批准',
        [WithdrawalStatus.REJECTED]: '已拒绝',
    };
    
    const WithdrawalModal = () => {
        const [amount, setAmount] = useState('');
        const [selectedChannel, setSelectedChannel] = useState<WithdrawalChannel>(WithdrawalChannel.ALIPAY);
        const [isSubmitting, setIsSubmitting] = useState(false);
        
        const isAlipayConfigured = profile?.alipayAccount && profile.alipayAccount.length > 0;
        // For now, wechat is always disabled as per requirements.
        const canSubmit = isAlipayConfigured; 

        const handleSubmit = async () => {
            if (!canSubmit) {
                toast.error("请先在“提现设置”中绑定有效的提现渠道。");
                return;
            }
            if (parseFloat(amount) <= 0 || !amount) {
                toast.error("请输入有效的提现金额。");
                return;
            }
            if (summary?.availableBalance && parseFloat(amount) > summary.availableBalance) {
                toast.error("提现金额不能超过可提现余额。");
                return;
            }
            setIsSubmitting(true);
            try {
                const newRequest = await createWithdrawalRequest({
                    amount: parseFloat(amount),
                    channel: selectedChannel,
                    notes: '' // Optional notes
                });
                setWithdrawalHistory(prev => [newRequest, ...prev]);
                toast.success('提现申请已提交！');
                const summaryData = await getMyEarningsSummary();
                setSummary(summaryData);
                setIsWithdrawalModalOpen(false);
            } catch (err: any) {
                toast.error(err.response?.data?.message || '提现申请失败。');
            } finally {
                setIsSubmitting(false);
            }
        };

        return (
            <Dialog open={isWithdrawalModalOpen} onOpenChange={setIsWithdrawalModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>申请提现</DialogTitle>
                        <DialogDescription>
                            您的提现申请将发送给管理员审核，通常会在1-3个工作日内处理。
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <label className="text-sm font-medium">可提现余额</label>
                            <p className="text-2xl font-bold">{formatCurrency(summary?.availableBalance)}</p>
                        </div>
                        <div>
                            <label htmlFor="amount" className="text-sm font-medium">提现金额 (元)</label>
                            <Input
                                id="amount"
                                type="number"
                                placeholder="0.00"
                                value={amount}
                                onChange={(e) => setAmount(e.target.value)}
                                min="0.01"
                                step="0.01"
                            />
                        </div>
                        <div>
                            <label htmlFor="channel" className="text-sm font-medium">提现渠道</label>
                            <Select 
                                value={selectedChannel} 
                                onValueChange={(value) => setSelectedChannel(value as WithdrawalChannel)}
                            >
                                <SelectTrigger id="channel">
                                    <SelectValue placeholder="选择一个渠道" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value={WithdrawalChannel.ALIPAY} disabled={!isAlipayConfigured}>
                                        支付宝 ({isAlipayConfigured ? profile.alipayAccount : '未绑定'})
                                    </SelectItem>
                                    <SelectItem value={WithdrawalChannel.WECHAT} disabled>
                                        微信 (暂不支持)
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            {!canSubmit && (
                                <p className="text-xs text-red-500 mt-2">
                                    您需要先在“开发者中心”的“提现设置”中绑定您的支付宝账号。
                                </p>
                            )}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsWithdrawalModalOpen(false)}>取消</Button>
                        <Button onClick={handleSubmit} disabled={isSubmitting || !canSubmit}>
                            {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            确认提交
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        );
    };

    const WithdrawalHistoryTable = () => (
        <Card>
            <CardHeader>
                <CardTitle>提现历史</CardTitle>
                <CardDescription>您最近的提现申请记录。</CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>申请日期</TableHead>
                            <TableHead>金额</TableHead>
                            <TableHead>渠道</TableHead>
                            <TableHead>状态</TableHead>
                            <TableHead>处理日期</TableHead>
                            <TableHead>备注</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {withdrawalHistory.length > 0 ? withdrawalHistory.map(req => (
                            <TableRow key={req.id}>
                                <TableCell>{formatDate(req.createdAt)}</TableCell>
                                <TableCell>{formatCurrency(req.amount)}</TableCell>
                                <TableCell>{req.channel}</TableCell>
                                <TableCell>
                                    <Badge variant={
                                        req.status === WithdrawalStatus.APPROVED ? 'success'
                                        : req.status === WithdrawalStatus.REJECTED ? 'destructive'
                                        : 'secondary'
                                    }>{statusTextMap[req.status]}</Badge>
                                </TableCell>
                                <TableCell>{formatDate(req.reviewedAt)}</TableCell>
                                <TableCell>{req.notes || '-'}</TableCell>
                            </TableRow>
                        )) : (
                            <TableRow>
                                <TableCell colSpan={6} className="text-center">没有历史记录。</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );

    if (loading) {
        return (
            <div className="space-y-6">
                 <Skeleton className="h-8 w-1/3" />
                 <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {Array.from({ length: 3 }).map((_, i) => <Skeleton key={i} className="h-28" />)}
                 </div>
                 <Skeleton className="h-96" />
                 <Skeleton className="h-96" />
            </div>
        )
    }

    if (error) {
         return (
            <Alert variant="destructive">
                <ServerCrash className="h-4 w-4" />
                <AlertTitle>加载错误</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

  return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-3xl font-bold tracking-tight text-white">我的收益</h2>
                <Button onClick={() => setIsWithdrawalModalOpen(true)}>
                    <Wallet className="mr-2 h-4 w-4" /> 申请提现
                </Button>
            </div>
            {/* Summary Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-muted-foreground">
                            <DollarSign className="h-5 w-5" /> 总收入
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-3xl font-bold">{formatCurrency(summary?.developerRevenue)}</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-muted-foreground">
                            <Landmark className="h-5 w-5" /> 可提现余额
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-3xl font-bold">{formatCurrency(summary?.availableBalance)}</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-muted-foreground">
                             <Package className="h-5 w-5" /> 总销量
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-3xl font-bold">{summary?.totalSales || 0}</p>
                    </CardContent>
                </Card>
            </div>
            
            <WithdrawalModal />
            
            {/* Product Sales Statistics */}
            <ProductSalesStatsTable stats={productSalesStats} loading={loading} />

            {/* Transaction History Table */}
            <Card>
                <CardHeader>
                    <CardTitle>交易记录</CardTitle>
                    <CardDescription>您所有成功的订单列表。</CardDescription>
                </CardHeader>
                <CardContent>
                    <DataTable columns={transactionColumns} data={transactions} noResultsMessage="没有找到任何成功的交易记录。" />
                </CardContent>
            </Card>

            {/* Withdrawal History Table */}
            <WithdrawalHistoryTable />
        </div>
    );
};

export default MyEarningsDashboard; 