/**
 * 图片压缩工具函数
 * 支持头像、Banner等不同类型图片的压缩处理
 */

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  outputFormat?: 'jpeg' | 'webp' | 'png';
  maxSizeKB?: number;
}

export interface CompressionPresets {
  avatar: CompressionOptions;
  banner: CompressionOptions;
  product: CompressionOptions;
  general: CompressionOptions;
}

// 预设的压缩配置
export const COMPRESSION_PRESETS: CompressionPresets = {
  avatar: {
    maxWidth: 512,
    maxHeight: 512,
    quality: 0.85,
    outputFormat: 'jpeg',
    maxSizeKB: 200
  },
  banner: {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8,
    outputFormat: 'jpeg',
    maxSizeKB: 800
  },
  product: {
    maxWidth: 1200,
    maxHeight: 1200,
    quality: 0.85,
    outputFormat: 'jpeg',
    maxSizeKB: 500
  },
  general: {
    maxWidth: 1920,
    maxHeight: 1920,
    quality: 0.8,
    outputFormat: 'jpeg',
    maxSizeKB: 1000
  }
};

/**
 * 压缩图片文件
 * @param file 原始图片文件
 * @param options 压缩选项
 * @returns Promise<Blob> 压缩后的图片Blob
 */
export async function compressImage(
  file: File | Blob,
  options: CompressionOptions = COMPRESSION_PRESETS.general
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('无法创建Canvas上下文'));
      return;
    }

    const handleImageLoad = () => {
      try {
        // 计算压缩后的尺寸
        const { width: newWidth, height: newHeight } = calculateDimensions(
          img.width,
          img.height,
          options.maxWidth || 1920,
          options.maxHeight || 1920
        );

        // 设置Canvas尺寸
        canvas.width = newWidth;
        canvas.height = newHeight;

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // 转换为Blob
        const outputFormat = options.outputFormat || 'jpeg';
        const quality = options.quality || 0.8;
        const mimeType = `image/${outputFormat}`;

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('图片压缩失败'));
              return;
            }

            // 检查文件大小是否符合要求
            const maxSizeBytes = (options.maxSizeKB || 1000) * 1024;
            if (blob.size <= maxSizeBytes) {
              resolve(blob);
            } else {
              // 如果文件仍然太大，降低质量重新压缩
              const newQuality = Math.max(0.1, quality * 0.8);
              compressImage(file, { ...options, quality: newQuality })
                .then(resolve)
                .catch(reject);
            }
          },
          mimeType,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    // 加载图片
    if (file instanceof File) {
      img.onload = handleImageLoad;
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsDataURL(file);
    } else {
      // Blob类型
      const url = URL.createObjectURL(file);
      img.onload = () => {
        URL.revokeObjectURL(url);
        handleImageLoad();
      };
      img.src = url;
    }
  });
}

/**
 * 计算保持宽高比的新尺寸
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let { width, height } = { width: originalWidth, height: originalHeight };

  // 如果图片尺寸已经小于最大限制，直接返回原尺寸
  if (width <= maxWidth && height <= maxHeight) {
    return { width, height };
  }

  // 计算缩放比例
  const widthRatio = maxWidth / width;
  const heightRatio = maxHeight / height;
  const ratio = Math.min(widthRatio, heightRatio);

  return {
    width: Math.round(width * ratio),
    height: Math.round(height * ratio)
  };
}

/**
 * 使用预设配置压缩图片
 * @param file 原始图片文件
 * @param preset 预设类型
 * @returns Promise<Blob> 压缩后的图片Blob
 */
export async function compressImageWithPreset(
  file: File | Blob,
  preset: keyof CompressionPresets
): Promise<Blob> {
  return compressImage(file, COMPRESSION_PRESETS[preset]);
}

/**
 * 验证图片文件类型
 * @param file 文件对象
 * @param allowedTypes 允许的MIME类型数组
 * @returns boolean
 */
export function validateImageType(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp']
): boolean {
  return allowedTypes.includes(file.type);
}

/**
 * 验证图片文件大小
 * @param file 文件对象
 * @param maxSizeMB 最大文件大小（MB）
 * @returns boolean
 */
export function validateImageSize(file: File, maxSizeMB: number = 10): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * 获取图片尺寸信息
 * @param file 图片文件
 * @returns Promise<{width: number, height: number}>
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({ width: img.width, height: img.height });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法读取图片尺寸'));
    };

    img.src = url;
  });
}

/**
 * 将Blob转换为File对象
 * @param blob Blob对象
 * @param fileName 文件名
 * @param mimeType MIME类型
 * @returns File对象
 */
export function blobToFile(blob: Blob, fileName: string, mimeType?: string): File {
  return new File([blob], fileName, { 
    type: mimeType || blob.type,
    lastModified: Date.now()
  });
}
