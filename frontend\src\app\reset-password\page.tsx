'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import { motion, AnimatePresence } from 'framer-motion';
import apiService from '@/services/api';
import { Loader2, KeyRound, Lock, CheckCircle2, XCircle } from 'lucide-react';

const passwordValidations = {
  minLength: (p: string) => p.length >= 8,
  hasUpper: (p: string) => /[A-Z]/.test(p),
  hasLower: (p: string) => /[a-z]/.test(p),
  hasNumber: (p: string) => /\d/.test(p),
};

const ResetPasswordPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordValidationState, setPasswordValidationState] = useState({
    minLength: false,
    hasUpper: false,
    hasLower: false,
    hasNumber: false,
  });

  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!token) {
      setError('无效的重置链接或链接已过期。');
    }
  }, [token]);
  
  useEffect(() => {
    setPasswordValidationState({
      minLength: passwordValidations.minLength(password),
      hasUpper: passwordValidations.hasUpper(password),
      hasLower: passwordValidations.hasLower(password),
      hasNumber: passwordValidations.hasNumber(password),
    });
  }, [password]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isPasswordValid = Object.values(passwordValidationState).every(Boolean);
    if (!isPasswordValid) {
      setError("密码不满足所有安全要求，请检查。");
      return;
    }

    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }
    
    if (!token) {
      setError('重置令牌丢失，请重新通过忘记密码邮件中的链接访问。');
      return;
    }

    setError('');
    setSuccess('');
    setIsSubmitting(true);

    try {
      const response = await apiService.post('/api/users/reset-password', {
        token,
        newPassword: password,
      }, {
        authRequired: false,
      });

      setSuccess(response.data.message || '密码重置成功！正在跳转至登录页面...');
      setTimeout(() => {
        router.push('/login');
      }, 3000);

    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.response?.data?.error || '密码重置失败，请稍后重试';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const ValidationIndicator = ({ isValid, text }: { isValid: boolean, text: string }) => (
    <div className={`flex items-center transition-colors duration-300 ${isValid ? 'text-green-400' : 'text-gray-500'}`}>
      {isValid ? <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0" /> : <XCircle className="h-4 w-4 mr-2 flex-shrink-0" />}
      <span>{text}</span>
    </div>
  );


  return (
    <main className="min-h-screen relative">
      <div className="fixed inset-0 bg-[url('/images/background/background.webp')] bg-cover bg-bottom opacity-50"></div>
      <div className="fixed inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60"></div>
      <div className="relative">
        <Header />
        <div className="min-h-[calc(100vh-64px)] flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <div className="bg-[#1a1a1a]/90 backdrop-blur-md rounded-2xl p-8 shadow-2xl border border-[#2a2a2a]">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-100">设置新密码</h1>
                <p className="text-gray-400 mt-2">
                  请输入您的新密码，请确保密码足够安全。
                </p>
              </div>

              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-3 mb-4 text-sm text-red-400 bg-red-900/20 rounded-lg border border-red-800/50"
                    role="alert"
                  >
                    {error}
                  </motion.div>
                )}
                {success && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="p-3 mb-4 text-sm text-green-400 bg-green-900/20 rounded-lg border border-green-800/50"
                    role="alert"
                  >
                    {success}
                  </motion.div>
                )}
              </AnimatePresence>
              
              {!success && (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium text-gray-300"
                    >
                      新密码
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <KeyRound className="h-5 w-5 text-gray-500" />
                      </div>
                      <input
                        id="password"
                        type="password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={isSubmitting}
                        className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                        placeholder="请输入新密码"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm mt-2 px-1">
                    <ValidationIndicator isValid={passwordValidationState.minLength} text="至少8个字符" />
                    <ValidationIndicator isValid={passwordValidationState.hasUpper} text="一个大写字母" />
                    <ValidationIndicator isValid={passwordValidationState.hasLower} text="一个小写字母" />
                    <ValidationIndicator isValid={passwordValidationState.hasNumber} text="一个数字" />
                  </div>

                  <div className="space-y-2">
                    <label
                      htmlFor="confirmPassword"
                      className="block text-sm font-medium text-gray-300"
                    >
                      确认新密码
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-500" />
                      </div>
                      <input
                        id="confirmPassword"
                        type="password"
                        required
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={isSubmitting}
                        className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                        placeholder="请再次输入新密码"
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting || !token}
                    className="w-full flex justify-center py-3 px-4 border border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-200 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 focus:ring-offset-gray-900 disabled:bg-gray-700/50 disabled:cursor-not-allowed transition-colors duration-300"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        处理中...
                      </>
                    ) : (
                      '确认重置密码'
                    )}
                  </button>
                </form>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </main>
  );
};

export default ResetPasswordPage; 