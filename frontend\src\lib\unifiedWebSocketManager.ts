/**
 * 统一的WebSocket管理器 - 兼容版本
 * 基于SessionManager，保持与现有代码的兼容性
 */

import { sessionManager } from './sessionManager';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface WebSocketEventHandlers {
  [eventType: string]: ((data: any) => void)[];
}

class UnifiedWebSocketManager {
  private ws: WebSocket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isDestroyed = false;
  private shouldReconnect = true;
  private eventHandlers: WebSocketEventHandlers = {};

  // 单例模式
  private static instance: UnifiedWebSocketManager | null = null;

  private constructor() {
    if (typeof window !== 'undefined') {
      // 监听会话失效
      sessionManager.onSessionInvalidated(() => {
        console.log('🔌 会话失效，断开WebSocket连接');
        this.shouldReconnect = false;
        this.disconnect();
      });

      // 定期检查连接状态
      setInterval(() => {
        if (sessionManager.isSessionValid() && this.token && !this.isConnected && this.shouldReconnect) {
          console.log('🔍 定期检查：会话有效但WebSocket未连接，尝试重连');
          this.connect();
        }
      }, 10000);

      // 监听页面可见性变化
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && this.shouldReconnect && this.token) {
          setTimeout(() => {
            if (sessionManager.isSessionValid() && this.shouldReconnect && this.token) {
              console.log('🔌 页面可见性变化，检查WebSocket连接');
              this.connect();
            }
          }, 1000);
        }
      });
    }
  }

  static getInstance(): UnifiedWebSocketManager {
    if (!UnifiedWebSocketManager.instance) {
      UnifiedWebSocketManager.instance = new UnifiedWebSocketManager();
    }
    return UnifiedWebSocketManager.instance;
  }

  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }

  /**
   * 初始化连接
   */
  initialize(token: string) {
    console.log('🌐 设置统一WebSocket管理器...');

    // 防止重复初始化
    if (this.token === token && this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('ℹ️ WebSocket已连接且token相同，跳过重复初始化');
      return;
    }

    this.token = token;
    this.shouldReconnect = true;

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('🔄 检测到新token，断开现有连接');
      this.disconnect();
    }

    if (this.isConnecting) {
      console.log('⚠️ WebSocket正在连接中，等待完成');
      return;
    }

    // 增加延迟，避免竞争条件
    setTimeout(() => {
      if (sessionManager.isSessionValid()) {
        console.log('🔌 会话有效，建立WebSocket连接');
        this.connect();
      } else {
        console.log('🔌 会话无效，等待有效会话');
      }
    }, 500);
  }

  /**
   * 建立WebSocket连接
   */
  async connect(): Promise<void> {
    if (this.isConnecting || this.isDestroyed || !this.token || !this.shouldReconnect) {
      return;
    }

    if (!sessionManager.isSessionValid()) {
      console.log('⚠️ 会话无效，跳过WebSocket连接');
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('ℹ️ WebSocket已连接，跳过重复连接');
      return;
    }

    this.isConnecting = true;

    try {
      const wsUrl = `wss://kitolus.top/ws/messages?token=${this.token}`;
      console.log('🚀 统一WebSocket连接:', wsUrl.substring(0, 80) + '...');

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        console.log('✅ 统一WebSocket连接成功');
        this.emit('connected');
        this.startHeartbeat();

        setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('📤 连接成功后请求在线状态');
            this.requestOnlineStatus();
          }
        }, 500);
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 收到WebSocket消息:', message);
          this.emit(message.type, message);
        } catch (error) {
          console.error('❌ 解析WebSocket消息失败:', error);
        }
      };

      this.ws.onclose = (event) => {
        this.isConnecting = false;
        console.log('🔌 统一WebSocket连接关闭:', event.code, event.reason);
        this.emit('disconnected', { code: event.code, reason: event.reason });
        this.stopHeartbeat();

        if (this.shouldReconnect && !this.isDestroyed) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        this.isConnecting = false;
        console.error('❌ 统一WebSocket连接错误:', error);
        this.emit('error', error);
      };

    } catch (error) {
      this.isConnecting = false;
      console.error('❌ 创建WebSocket连接失败:', error);
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      console.log('🔌 统一WebSocket主动断开');
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.stopHeartbeat();
  }

  /**
   * 发送消息
   */
  send(message: any): boolean {
    if (this.isConnected && this.ws) {
      const messageStr = JSON.stringify(message);
      console.log('📤 发送WebSocket消息:', message);
      this.ws.send(messageStr);
      return true;
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送消息');
      return false;
    }
  }

  /**
   * 请求在线状态
   */
  requestOnlineStatus() {
    this.send({ type: 'REQUEST_ONLINE_STATUS', timestamp: Date.now() });
  }

  /**
   * 开始输入状态
   */
  startTyping(conversationId: string) {
    this.send({
      type: 'START_TYPING',
      conversationId,
      timestamp: Date.now()
    });
  }

  /**
   * 停止输入状态
   */
  stopTyping() {
    this.send({
      type: 'STOP_TYPING',
      timestamp: Date.now()
    });
  }

  /**
   * 更新token
   */
  updateToken(token: string | null) {
    if (token && token !== this.token) {
      console.log('🔄 更新WebSocket token');
      this.token = token;
      this.shouldReconnect = true;
      
      if (sessionManager.isSessionValid()) {
        this.disconnect();
        setTimeout(() => this.connect(), 1000);
      }
    } else if (!token) {
      console.log('🔄 Token被清除，断开WebSocket连接');
      this.shouldReconnect = false;
      this.disconnect();
    }
  }

  /**
   * 添加事件监听器
   */
  on(eventType: string, handler: (data: any) => void) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(eventType: string, handler: (data: any) => void) {
    if (this.eventHandlers[eventType]) {
      const index = this.eventHandlers[eventType].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[eventType].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(eventType: string, data?: any) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('❌ WebSocket事件处理器错误:', error);
        }
      });
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'PING', timestamp: Date.now() });
      }
    }, 30000);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ 达到最大重连次数，停止重连');
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    setTimeout(() => {
      if (this.shouldReconnect && !this.isDestroyed) {
        this.connect();
      }
    }, delay);
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('🌐 清理统一WebSocket连接');
    this.isDestroyed = true;
    this.shouldReconnect = false;
    this.disconnect();
    this.eventHandlers = {};
  }
}

// 导出单例实例
export const unifiedWebSocketManager = UnifiedWebSocketManager.getInstance();
