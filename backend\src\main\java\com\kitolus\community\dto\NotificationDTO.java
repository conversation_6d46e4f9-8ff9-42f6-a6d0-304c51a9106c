package com.kitolus.community.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kitolus.community.entity.NotificationType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
public class NotificationDTO {
    private Long id;
    private NotificationType type;
    private Long postId;
    private String postTitle;
    private Long commentId;
    private String contentPreview;
    private String parentCommentPreview;
    private String senderUsername;
    private String senderAvatarUrl;
    private String senderSerialNumber;
    private Integer senderAvatarVersion;
    private Timestamp createdAt;

    @JsonProperty("isRead")
    private boolean isRead;
} 