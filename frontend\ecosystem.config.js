export default {
  apps: [
    {
      name: 'gtnh-frontend',
      cwd: '/home/<USER>/app/frontend',
      script: 'npm',
      args: 'run start',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        // AI-NOTE: The base URL should NOT include /api.
        // Axios requests in the code already include the /api prefix,
        // so setting this to 'https://kitolus.top/api' will result in a '/api/api' duplication.
        NEXT_PUBLIC_API_URL: 'https://kitolus.top',
      },
    },
  ],
}; 