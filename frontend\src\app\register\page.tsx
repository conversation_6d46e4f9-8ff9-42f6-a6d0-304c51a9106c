'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, User, Mail, Lock, KeyRound, CheckCircle2, XCircle } from 'lucide-react';
import { getPatchedCountdown } from '@/lib/verificationPatch';

// Password validation criteria
const passwordValidations = {
  minLength: (p: string) => p.length >= 8,
  hasUpper: (p: string) => /[A-Z]/.test(p),
  hasLower: (p: string) => /[a-z]/.test(p),
  hasNumber: (p: string) => /\d/.test(p),
};

const RegisterPage = () => {
  const [stage, setStage] = useState('register'); // 'register' or 'verify'
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [code, setCode] = useState('');
  const [verificationSentMessage, setVerificationSentMessage] = useState('');
  
  const [passwordValidationState, setPasswordValidationState] = useState({
    minLength: false,
    hasUpper: false,
    hasLower: false,
    hasNumber: false,
  });
  
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const router = useRouter();
  const { login } = useAuth();

  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Effect for real-time password validation
  useEffect(() => {
    setPasswordValidationState({
      minLength: passwordValidations.minLength(password),
      hasUpper: passwordValidations.hasUpper(password),
      hasLower: passwordValidations.hasLower(password),
      hasNumber: passwordValidations.hasNumber(password),
    });
  }, [password]);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleRegistrationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isPasswordValid = Object.values(passwordValidationState).every(Boolean);
    if (!isPasswordValid) {
      setError("密码不满足所有安全要求，请检查。");
      return;
    }

    // 前端校验：检查用户名是否包含空格
    if (/\s/.test(username)) {
      setError("用户名不能包含空格");
      return;
    }
    if (password !== confirmPassword) {
      setError("两次输入的密码不一致，请重新输入");
      return;
    }
    setError('');
    setSuccessMessage('');
    setIsSubmitting(true);

    try {
      const response = await apiService.post('/api/auth/register', { username, email, password }, {
        authRequired: false
      });
      setVerificationSentMessage(`验证码已发送至 ${email}。如果没收到，请检查您的垃圾邮件箱。`);
      const { displaySeconds } = getPatchedCountdown();
      setCountdown(displaySeconds);
      setStage('verify');
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.response?.data?.error || '注册请求失败，请稍后重试';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (countdown > 0) return;
    
    setError('');
    try {
      const response = await apiService.post('/api/auth/register', { username, email, password }, {
        authRequired: false
      });
      const { displaySeconds } = getPatchedCountdown();
      setCountdown(displaySeconds);
      setVerificationSentMessage(`新的验证码已发送至 ${email}。如果没收到，请检查您的垃圾邮件箱。`);
    } catch (err: any) {
      if (err.response) {
        const errorMessage = err.response.data?.message || err.response.data?.error;
        setError(errorMessage || '重新发送验证码失败，请稍后重试');
      } else {
        setError('重新发送验证码请求失败，请稍后重试');
      }
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    try {
      const response = await apiService.post('/api/auth/verify', {
        email: email,
        code: verificationCode.trim()
      }, {
        authRequired: false
      });
      const { token } = response.data;
      login(token);
      router.push('/?registered=true');
    } catch (err: any) {
      if (err.response) {
        const errorMessage = err.response.data?.message || err.response.data?.error;
        if (err.response.status === 400) {
          setError('验证码无效或已过期');
        } else {
          setError(errorMessage || '验证失败，请稍后重试');
        }
      } else if (err.request) {
        setError('无法连接到服务器，请检查网络连接');
      } else {
        setError('验证请求失败，请稍后重试');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const ValidationIndicator = ({ isValid, text }: { isValid: boolean, text: string }) => (
    <div className={`flex items-center transition-colors duration-300 ${isValid ? 'text-green-400' : 'text-gray-500'}`}>
      {isValid ? <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0" /> : <XCircle className="h-4 w-4 mr-2 flex-shrink-0" />}
      <span>{text}</span>
    </div>
  );

  return (
    <main className="min-h-screen relative">
      <div className="fixed inset-0 bg-[url('/images/background/background.webp')] bg-cover bg-bottom opacity-50"></div>
      <div className="fixed inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60"></div>
      <div className="relative">
      <Header />
        <div className="min-h-[calc(100vh-64px)] relative">
          <div className="relative min-h-[calc(100vh-64px)] flex items-center justify-center p-4 pt-24">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full max-w-md"
            >
              <div className="bg-[#1a1a1a]/90 backdrop-blur-md rounded-2xl p-8 shadow-2xl border border-[#2a2a2a]">
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-center mb-8"
                >
                  <h1 className="text-3xl font-bold text-gray-100">
                    GTNH 社区
                  </h1>
                  <p className="text-gray-400 mt-2">
                    {stage === 'register' ? '创建您的账号' : '验证您的邮箱'}
                  </p>
                </motion.div>

                <AnimatePresence>
                  {verificationSentMessage && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="p-3 mb-4 text-sm text-green-300 bg-green-900/30 rounded-lg border border-green-700/50"
                      role="alert"
                    >
                      {verificationSentMessage}
                    </motion.div>
                  )}
                  {successMessage && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="p-3 mb-4 text-sm text-gray-300 bg-[#2a2a2a]/50 rounded-lg border border-[#3a3a3a]"
                      role="alert"
                    >
                      {successMessage}
                    </motion.div>
                  )}
                  {error && (
        <motion.div
                      initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="p-3 mb-4 text-sm text-gray-300 bg-[#2a2a2a]/50 rounded-lg border border-[#3a3a3a]"
                      role="alert"
        >
                      {error}
                    </motion.div>
                  )}
                </AnimatePresence>

          {stage === 'register' ? (
              <form onSubmit={handleRegistrationSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                        用户名
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-5 w-5 text-gray-500" />
                        </div>
                        <input
                          id="username"
                          type="text"
                          required
                          value={username}
                          onChange={(e) => setUsername(e.target.value.replace(/\s/g, ''))}
                          disabled={isSubmitting}
                          className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                          placeholder="请输入不含空格的用户名"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                        邮箱
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-5 w-5 text-gray-500" />
                        </div>
                        <input
                          id="email"
                          type="email"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          disabled={isSubmitting}
                          className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                          placeholder="请输入邮箱"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                        密码
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-5 w-5 text-gray-500" />
                        </div>
                        <input
                          id="password"
                          type="password"
                          required
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          disabled={isSubmitting}
                          className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                          placeholder="请输入密码"
                        />
                      </div>
                      {/* Password strength indicator */}
                      <AnimatePresence>
                        {password.length > 0 && (
                          <motion.div 
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs mt-2"
                          >
                            <ValidationIndicator isValid={passwordValidationState.minLength} text="至少8个字符" />
                            <ValidationIndicator isValid={passwordValidationState.hasUpper} text="包含大写字母" />
                            <ValidationIndicator isValid={passwordValidationState.hasLower} text="包含小写字母" />
                            <ValidationIndicator isValid={passwordValidationState.hasNumber} text="包含数字" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                        确认密码
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-5 w-5 text-gray-500" />
            </div>
                        <input
                          id="confirmPassword"
                          type="password"
                          required
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          disabled={isSubmitting}
                          className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                          placeholder="请再次输入密码"
                        />
            </div>
                </div>

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    type="submit" 
                    disabled={isSubmitting}
                      className="w-full flex justify-center items-center py-2 px-4 border border-[#3a3a3a] rounded-lg shadow-sm text-sm font-medium text-gray-100 bg-[#2a2a2a] hover:bg-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3a3a3a] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                      {isSubmitting ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        '注册'
                      )}
                    </motion.button>

                    <div className="text-center">
                      <p className="text-sm text-gray-400">
                        已有账号？{' '}
                        <motion.a
                          whileHover={{ scale: 1.05 }}
                          href="/login"
                          className="font-medium text-gray-300 hover:text-gray-200 transition-colors duration-200"
                        >
                          立即登录
                        </motion.a>
                      </p>
                </div>
              </form>
          ) : (
              <form onSubmit={handleVerificationSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-300">
                        验证码
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <KeyRound className="h-5 w-5 text-gray-500" />
                        </div>
                        <input
                          id="verificationCode"
                          name="verificationCode"
                          type="text"
                          required
                          value={verificationCode}
                          onChange={(e) => setVerificationCode(e.target.value)}
                          disabled={isSubmitting}
                          className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                          placeholder="请输入验证码"
                        />
                      </div>
            </div>

                    <div className="flex items-center justify-between">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        type="button"
                        onClick={handleResendCode}
                        disabled={isSubmitting || countdown > 0}
                        className="text-sm text-gray-400 hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                      >
                        {countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送验证码'}
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    type="submit" 
                    disabled={isSubmitting}
                        className="flex justify-center items-center py-2 px-4 border border-[#3a3a3a] rounded-lg shadow-sm text-sm font-medium text-gray-100 bg-[#2a2a2a] hover:bg-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3a3a3a] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                        {isSubmitting ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          '验证'
                        )}
                      </motion.button>
            </div>
          </form>
          )}
              </div>
        </motion.div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default RegisterPage; 