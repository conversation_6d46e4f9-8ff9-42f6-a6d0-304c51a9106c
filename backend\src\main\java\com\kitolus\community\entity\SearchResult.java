package com.kitolus.community.entity;

import java.util.List;

public class SearchResult {
    private List<KnowledgeBaseArticle> titleMatches;
    private List<KnowledgeBaseArticle> contentMatches;

    public SearchResult(List<KnowledgeBaseArticle> titleMatches, List<KnowledgeBaseArticle> contentMatches) {
        this.titleMatches = titleMatches;
        this.contentMatches = contentMatches;
    }

    // Getters and Setters
    public List<KnowledgeBaseArticle> getTitleMatches() {
        return titleMatches;
    }

    public void setTitleMatches(List<KnowledgeBaseArticle> titleMatches) {
        this.titleMatches = titleMatches;
    }

    public List<KnowledgeBaseArticle> getContentMatches() {
        return contentMatches;
    }

    public void setContentMatches(List<KnowledgeBaseArticle> contentMatches) {
        this.contentMatches = contentMatches;
    }
}
