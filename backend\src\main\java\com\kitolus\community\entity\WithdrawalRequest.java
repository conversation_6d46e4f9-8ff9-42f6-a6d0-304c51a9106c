package com.kitolus.community.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@TableName("withdrawal_requests")
public class WithdrawalRequest {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    private BigDecimal amount;

    @TableField(exist = false)
    private String userUsername;

    private WithdrawalChannel channel;

    @TableField("account_info")
    private String accountInfo;

    private WithdrawalStatus status;

    private String notes;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_at")
    private Timestamp updatedAt;

    @TableField("reviewed_by")
    private Long reviewedBy;

    @TableField(exist = false)
    private String reviewerUsername;

    @TableField("reviewed_at")
    private Timestamp reviewedAt;
} 