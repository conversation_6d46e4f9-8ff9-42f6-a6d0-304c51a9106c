package com.kitolus.community.service;

import com.kitolus.community.entity.LoginHistory;
import com.kitolus.community.mapper.LoginHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LoginHistoryServiceImpl implements LoginHistoryService {

    private final LoginHistoryMapper loginHistoryMapper;

    @Autowired
    public LoginHistoryServiceImpl(LoginHistoryMapper loginHistoryMapper) {
        this.loginHistoryMapper = loginHistoryMapper;
    }

    @Override
    public void addLoginHistory(Long userId, String ipAddress, String deviceFingerprint) {
        if (userId == null) {
            return;
        }
        LoginHistory history = new LoginHistory(userId, ipAddress, deviceFingerprint);
        loginHistoryMapper.insert(history);
    }
}
