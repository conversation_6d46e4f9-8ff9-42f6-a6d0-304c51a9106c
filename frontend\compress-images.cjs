const fs = require('fs');
const path = require('path');
const { glob } = require('glob');
const sharp = require('sharp');

(async () => {
    const inputDirs = process.argv.slice(2);

    if (inputDirs.length === 0) {
        console.error('Error: Please provide at least one input directory.');
        console.log('Usage: node compress-images.cjs <dir1> <dir2> ...');
        process.exit(1);
    }

    console.log('Starting image optimization (PNG/JPG -> WebP) using sharp...');

    for (const inputDir of inputDirs) {
        const absoluteInputDir = path.resolve(inputDir);
        if (!fs.existsSync(absoluteInputDir)) {
            console.warn(`Warning: Directory not found, skipping: ${absoluteInputDir}`);
            continue;
        }

        console.log(`\nProcessing directory: ${absoluteInputDir}`);

        try {
            const filesToProcess = await glob(`${absoluteInputDir.replace(/\\/g, '/')}/*.{jpg,png,jpeg}`);
            let counter = 1;
            
            if (filesToProcess.length === 0) {
                console.log('No JPG, PNG, or JPEG images found to compress.');
                continue;
            }
            
            console.log('--- Compression & Replacement Report ---');
            for (const filePath of filesToProcess) {
                const originalPath = path.resolve(filePath);
                const originalSize = fs.statSync(originalPath).size;
                const outputFileName = `Loading_${counter}.webp`;
                counter++;
                const outputPath = path.join(path.dirname(originalPath), outputFileName);

                await sharp(originalPath)
                    .webp({ quality: 80, effort: 6 })
                    .toFile(outputPath);
                
                const compressedSize = fs.statSync(outputPath).size;
                const reduction = ((originalSize - compressedSize) / originalSize) * 100;

                console.log(
                    `Converted: ${path.basename(originalPath)} -> ${path.basename(outputPath)} | ${(originalSize / 1024).toFixed(2)} KB -> ${(compressedSize / 1024).toFixed(2)} KB (${reduction.toFixed(1)}% reduction)`
                );

                fs.unlinkSync(originalPath);
                console.log(`   -> Deleted original: ${path.basename(originalPath)}`);
            }
            console.log('------------------------------------');
            console.log(`Optimization complete for: ${absoluteInputDir}`);

        } catch (error) {
            console.error(`An error occurred during compression for directory ${absoluteInputDir}:`, error);
        }
    }

})().catch(error => {
    console.error("A critical error occurred:", error);
}); 