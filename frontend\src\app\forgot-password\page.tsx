'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Loader2, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import Header from '@/components/Header';
import Link from 'next/link';
import apiService from '@/services/api';

const ForgotPasswordPage = () => {
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setMessage('');

        if (!email) {
            toast.error("请输入您的电子邮箱地址。");
            setIsLoading(false);
            return;
        }

        try {
            // This endpoint needs to be created on the backend
            const response = await apiService.post('/api/user/forgot-password', { email });
            setMessage(`如果邮箱 ${email} 已注册，一封邮件将发送至此地址。如果没收到，请检查您的垃圾邮件箱。`);
            toast.success('请求已发送！');
        } catch (error: any) {
            // To prevent user enumeration, we show a generic success message even if the email doesn't exist.
            // The backend should handle this by not throwing an error for non-existent emails.
            setMessage(`如果邮箱 ${email} 已注册，一封邮件将发送至此地址。如果没收到，请检查您的垃圾邮件箱。`);
            console.error('Forgot password error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <main className="min-h-screen relative">
            <div className="fixed inset-0 bg-[url('/images/background/background.webp')] bg-cover bg-bottom opacity-50"></div>
            <div className="fixed inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60"></div>
            <div className="relative h-full w-full">
                <Header />
                <div className="h-[calc(100vh-64px)] relative w-full flex items-center justify-center p-4">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                        className="w-full max-w-[420px]"
                    >
                        <div className="bg-[#1a1a1a]/90 backdrop-blur-md rounded-2xl p-10 shadow-2xl border border-[#2a2a2a] w-full">
                            <motion.div
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2 }}
                                className="text-center mb-8"
                            >
                                <h1 className="text-3xl font-bold text-gray-100">
                                    重置您的密码
                                </h1>
                                <p className="text-gray-400 mt-2">
                                    请输入您的注册邮箱地址，我们将向您发送重置链接。
                                </p>
                            </motion.div>
                            
                            {message ? (
                                <div className="p-4 mb-6 text-center text-gray-300 bg-[#2a2a2a]/50 rounded-lg border border-[#3a3a3a]">
                                    <p>{message}</p>
                                    <Link href="/login" className="mt-4 inline-block text-sm font-medium text-gray-300 hover:text-gray-100 transition-colors duration-200">
                                        &larr; 返回登录
                                    </Link>
                                </div>
                            ) : (
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="space-y-2">
                                        <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                                            电子邮箱
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <Mail className="h-5 w-5 text-gray-500" />
                                            </div>
                                            <input
                                                id="email"
                                                name="email"
                                                type="email"
                                                autoComplete="email"
                                                required
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                disabled={isLoading}
                                                className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                                                placeholder="<EMAIL>"
                                            />
                                        </div>
                                    </div>
    
                                    <motion.button
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                        type="submit"
                                        disabled={isLoading}
                                        className="w-full flex justify-center items-center py-2 px-4 border border-[#3a3a3a] rounded-lg shadow-sm text-sm font-medium text-gray-100 bg-[#2a2a2a] hover:bg-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3a3a3a] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                                    >
                                        {isLoading ? <Loader2 className="h-5 w-5 animate-spin" /> : '发送重置链接'}
                                    </motion.button>
                                </form>
                            )}

                            {!message && (
                               <div className="text-center mt-6">
                                    <Link href="/login" className="flex items-center justify-center text-sm text-gray-400 hover:text-gray-200 transition-colors duration-200">
                                        <ArrowLeft className="w-4 h-4 mr-2"/>
                                        返回登录
                                    </Link>
                                </div>
                            )}
                        </div>
                    </motion.div>
                </div>
            </div>
        </main>
    );
};

export default ForgotPasswordPage; 