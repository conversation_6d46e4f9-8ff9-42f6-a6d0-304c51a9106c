/**
 * 分区映射工具 - 解决中英文显示不一致问题
 * 将数据库中的英文分区信息映射为中文显示
 */

import { communitySections } from './community-sections-data';
import { gtnhProgression } from './gtnh-progression-data';

// 分区信息接口
export interface PartitionInfo {
  sectionName: string;
  categoryName: string;
  subcategoryName: string;
  fullDisplayName: string;
  shortDisplayName: string;
}

// 创建分区映射表
const createPartitionMappings = () => {
  const mappings = new Map<string, PartitionInfo>();

  // 1. 处理传统的阶段分区 (era|tier|topic格式)
  gtnhProgression.forEach(era => {
    era.tiers.forEach(tier => {
      tier.topics.forEach(topic => {
        const key = `${era.name}|${tier.name}|${topic.subStage}`;
        mappings.set(key, {
          sectionName: '阶段分区',
          categoryName: era.name,
          subcategoryName: `[${tier.name}] ${topic.subStage}`,
          fullDisplayName: `阶段分区 > ${era.name} > [${tier.name}] ${topic.subStage}`,
          shortDisplayName: `${era.name} - ${topic.subStage}`
        });

        // 同时支持英文格式的映射（如果数据库中存储的是英文）
        const englishKey = `${era.name}|${tier.name}|${topic.subStage}`;
        if (!mappings.has(englishKey)) {
          mappings.set(englishKey, {
            sectionName: '阶段分区',
            categoryName: era.name,
            subcategoryName: `[${tier.name}] ${topic.subStage}`,
            fullDisplayName: `阶段分区 > ${era.name} > [${tier.name}] ${topic.subStage}`,
            shortDisplayName: `${era.name} - ${topic.subStage}`
          });
        }
      });
    });
  });

  // 2. 处理新的社区分区 (section|category|subcategory格式)
  communitySections.forEach(section => {
    section.categories.forEach(category => {
      category.subcategories.forEach(subcategory => {
        const key = `${section.id}|${category.id}|${subcategory.id}`;
        mappings.set(key, {
          sectionName: section.name,
          categoryName: category.name,
          subcategoryName: subcategory.name,
          fullDisplayName: `${section.name} > ${category.name} > ${subcategory.name}`,
          shortDisplayName: `${category.name} - ${subcategory.name}`
        });
      });
    });
  });

  return mappings;
};

// 全局分区映射表
const partitionMappings = createPartitionMappings();

/**
 * 根据分区值获取中文显示信息
 * @param partitionValue 分区值，格式可能是 era|tier|topic 或 section|category|subcategory
 * @returns 分区显示信息
 */
export const getPartitionDisplayInfo = (partitionValue: string): PartitionInfo | null => {
  if (!partitionValue) return null;

  // 直接从映射表查找
  const info = partitionMappings.get(partitionValue);
  if (info) return info;

  // 如果没有找到，尝试解析并创建默认显示
  const parts = partitionValue.split('|');
  if (parts.length === 3) {
    const [part1, part2, part3] = parts;
    
    // 尝试匹配阶段分区格式
    const eraMatch = gtnhProgression.find(era => era.name === part1);
    if (eraMatch) {
      const tierMatch = eraMatch.tiers.find(tier => tier.name === part2);
      if (tierMatch) {
        const topicMatch = tierMatch.topics.find(topic => topic.subStage === part3);
        if (topicMatch) {
          return {
            sectionName: '阶段分区',
            categoryName: part1,
            subcategoryName: `[${part2}] ${part3}`,
            fullDisplayName: `阶段分区 > ${part1} > [${part2}] ${part3}`,
            shortDisplayName: `${part1} - ${part3}`
          };
        }
      }
    }

    // 尝试匹配新分区格式
    const section = communitySections.find(s => s.id === part1);
    if (section) {
      const category = section.categories.find(c => c.id === part2);
      if (category) {
        const subcategory = category.subcategories.find(sc => sc.id === part3);
        if (subcategory) {
          return {
            sectionName: section.name,
            categoryName: category.name,
            subcategoryName: subcategory.name,
            fullDisplayName: `${section.name} > ${category.name} > ${subcategory.name}`,
            shortDisplayName: `${category.name} - ${subcategory.name}`
          };
        }
      }
    }
  }

  // 如果都没有匹配，返回原始值作为显示
  return {
    sectionName: '未知分区',
    categoryName: '未知类别',
    subcategoryName: partitionValue,
    fullDisplayName: partitionValue,
    shortDisplayName: partitionValue
  };
};

/**
 * 从帖子的topic信息获取分区显示信息
 * @param topic 帖子的topic对象，包含era, tier, name等字段
 * @returns 分区显示信息
 */
export const getPartitionDisplayInfoFromTopic = (topic: {
  era?: string;
  tier?: string;
  name?: string;
}): PartitionInfo | null => {
  if (!topic || !topic.era || !topic.tier || !topic.name) return null;

  // 构造分区值并查找
  const partitionValue = `${topic.era}|${topic.tier}|${topic.name}`;
  return getPartitionDisplayInfo(partitionValue);
};

/**
 * 获取所有可用的分区选项（用于下拉选择等）
 * @returns 所有分区选项的数组
 */
export const getAllPartitionOptions = (): Array<{
  value: string;
  label: string;
  info: PartitionInfo;
}> => {
  const options: Array<{
    value: string;
    label: string;
    info: PartitionInfo;
  }> = [];

  partitionMappings.forEach((info, value) => {
    options.push({
      value,
      label: info.fullDisplayName,
      info
    });
  });

  return options.sort((a, b) => a.label.localeCompare(b.label));
};

/**
 * 检查分区值是否有效
 * @param partitionValue 分区值
 * @returns 是否有效
 */
export const isValidPartition = (partitionValue: string): boolean => {
  return partitionMappings.has(partitionValue) || getPartitionDisplayInfo(partitionValue) !== null;
};

/**
 * 获取分区的简短显示名称（用于标签等紧凑显示）
 * @param partitionValue 分区值
 * @returns 简短显示名称
 */
export const getPartitionShortName = (partitionValue: string): string => {
  const info = getPartitionDisplayInfo(partitionValue);
  return info ? info.shortDisplayName : partitionValue;
};

/**
 * 获取分区的完整显示名称（用于详细显示）
 * @param partitionValue 分区值
 * @returns 完整显示名称
 */
export const getPartitionFullName = (partitionValue: string): string => {
  const info = getPartitionDisplayInfo(partitionValue);
  return info ? info.fullDisplayName : partitionValue;
};
