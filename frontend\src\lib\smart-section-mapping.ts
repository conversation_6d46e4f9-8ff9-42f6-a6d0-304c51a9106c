/**
 * 智能分区映射工具
 * 根据帖子内容和标题智能地将帖子分配到不同的社区分区
 */

import { CommunityPost } from '@/types/CommunityPost';
import { communitySections } from './community-sections-data';

// 关键词映射表
const sectionKeywords = {
  // 阶段分区关键词
  progression: [
    '阶段', '进度', '发展', '升级', '时代', '压级', '电路', '机器',
    '石器', '蒸汽', '低压', '中压', '高压', '极高压', '绝缘压', '润滑压', '零点压', '终极压',
    'LV', 'MV', 'HV', 'EV', 'IV', 'LuV', 'ZPM', 'UV', 'UHV', 'UEV', 'UIV', 'UMV', 'UXV',
    '初期', '中期', '后期', '入门', '基础', '制作', '合成'
  ],
  
  // 技术讨论区关键词
  technical: [
    '自动化', '红石', '管道', '物流', '传输', '存储', '分拣', '计算机', '编程', '脚本',
    '优化', '效率', '设计', '电路', '逻辑', '控制', '系统', '网络', '数据', '算法',
    'AE2', 'ME', 'OpenComputers', 'ComputerCraft', 'BuildCraft', 'Logistics Pipes'
  ],
  
  // 资源分享区关键词
  resources: [
    '资源', '材料', '矿物', '配方', '数据', '表格', '统计', '计算器', '工具', '软件',
    '下载', '分享', '收集', '整理', '汇总', '列表', '清单', '指南', '手册', '文档',
    '攻略', '教程', '说明', '介绍', '解释'
  ],
  
  // 新手指导区关键词
  guidance: [
    '新手', '入门', '指导', '帮助', '教学', '学习', '问题', '疑问', '求助', '解答',
    '基础', '开始', '第一', '初学', '不会', '怎么', '如何', '什么', '为什么',
    '建议', '推荐', '技巧', '经验', '心得', '总结'
  ],
  
  // 作品展示区关键词
  showcase: [
    '展示', '作品', '建筑', '设计', '创作', '项目', '成果', '完成', '建造', '制作',
    '分享', '欣赏', '美观', '漂亮', '酷炫', '震撼', '巨型', '大型', '复杂',
    '截图', '图片', '视频', '演示', '参观', '游览'
  ],
  
  // 多人合作区关键词
  collaboration: [
    '多人', '合作', '团队', '组队', '招募', '寻找', '一起', '共同', '协作', '配合',
    '服务器', '联机', '在线', '公会', '群组', '社团', '伙伴', '朋友', '队友',
    '分工', '协调', '规划', '管理', '组织', '活动', '比赛', '挑战'
  ]
};

// 特殊关键词权重（某些关键词更能代表特定分区）
const keywordWeights: { [section: string]: { [keyword: string]: number } } = {
  progression: {
    '阶段': 3, '进度': 3, '时代': 3, '压级': 3,
    '石器': 2, '蒸汽': 2, '低压': 2, '中压': 2, '高压': 2,
    'LV': 2, 'MV': 2, 'HV': 2, 'EV': 2, 'IV': 2
  },
  technical: {
    '自动化': 3, '红石': 3, '管道': 3, '物流': 3,
    'AE2': 3, 'ME': 3, 'OpenComputers': 3, 'ComputerCraft': 3
  },
  resources: {
    '资源': 3, '配方': 3, '数据': 3, '下载': 3, '攻略': 3, '教程': 3
  },
  guidance: {
    '新手': 3, '入门': 3, '求助': 3, '帮助': 3, '问题': 3
  },
  showcase: {
    '展示': 3, '作品': 3, '建筑': 3, '截图': 3, '视频': 3
  },
  collaboration: {
    '多人': 3, '合作': 3, '团队': 3, '招募': 3, '服务器': 3
  }
};

/**
 * 根据帖子内容智能判断其所属分区
 * @param post 帖子对象
 * @returns 分区ID数组，按匹配度排序
 */
export const getPostSections = (post: CommunityPost): string[] => {
  const text = `${post.title} ${post.content}`.toLowerCase();
  const sectionScores: { [key: string]: number } = {};

  // 初始化分数
  Object.keys(sectionKeywords).forEach(section => {
    sectionScores[section] = 0;
  });

  // 计算每个分区的匹配分数
  Object.entries(sectionKeywords).forEach(([section, keywords]) => {
    keywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      const matches = (text.match(new RegExp(keywordLower, 'g')) || []).length;

      if (matches > 0) {
        const weight = keywordWeights[section]?.[keyword] || 1;
        sectionScores[section] += matches * weight;
      }
    });
  });

  // 特殊处理：如果帖子有明确的topic信息，归类到阶段分区
  if (post.topic?.era && post.topic?.tier && post.topic?.name) {
    sectionScores.progression += 3; // 给阶段分区加分，但不要过高
  }

  // 为了确保每个分区都能显示内容，给所有分区一个基础分数
  // 这样即使没有关键词匹配，帖子也可能被分配到其他分区
  const hasAnyKeywordMatch = Object.values(sectionScores).some(score => score > 0);
  if (!hasAnyKeywordMatch) {
    // 如果没有任何关键词匹配，根据帖子的基本特征进行分配
    if (post.topic?.era && post.topic?.tier && post.topic?.name) {
      sectionScores.progression = 1;
    }
    // 给其他分区也分配基础分数，让帖子可以出现在多个分区中
    sectionScores.technical = 0.5;
    sectionScores.resources = 0.3;
    sectionScores.guidance = 0.3;
    sectionScores.showcase = 0.3;
    sectionScores.collaboration = 0.3;
  }

  // 按分数排序并返回所有有分数的分区
  return Object.entries(sectionScores)
    .filter(([_, score]) => score > 0)
    .sort(([, a], [, b]) => b - a)
    .map(([section]) => section);
};

/**
 * 检查帖子是否属于指定分区
 * @param post 帖子对象
 * @param sectionId 分区ID
 * @returns 是否属于该分区
 */
export const isPostInSection = (post: CommunityPost, sectionId: string): boolean => {
  // 对于阶段分区，直接检查topic信息
  if (sectionId === 'progression') {
    return !!(post.topic?.era && post.topic?.tier && post.topic?.name);
  }

  // 对于其他分区，采用宽松匹配策略
  // 如果帖子有完整的topic信息，就让它出现在所有分区中
  // 这样确保用户点击任何分区都能看到内容
  if (post.topic?.era && post.topic?.tier && post.topic?.name) {
    const text = `${post.title} ${post.content}`.toLowerCase();
    const keywords = sectionKeywords[sectionId as keyof typeof sectionKeywords];

    if (!keywords) {
      return false;
    }

    // 首先检查是否有关键词匹配（优先级高）
    const hasKeywordMatch = keywords.some(keyword =>
      text.includes(keyword.toLowerCase())
    );

    if (hasKeywordMatch) {
      return true;
    }

    // 如果没有关键词匹配，使用宽松策略：
    // 让所有有完整topic信息的帖子都能出现在非阶段分区中
    // 这样确保每个分区都有内容显示
    switch (sectionId) {
      case 'technical':
        // 技术讨论区：显示所有帖子，因为GTNH本身就是技术性很强的整合包
        return true;
      case 'resources':
        // 资源分享区：显示所有帖子，因为每个帖子都可能包含有用信息
        return true;
      case 'guidance':
        // 新手指导区：显示所有帖子，因为任何阶段的经验都对新手有帮助
        return true;
      case 'showcase':
        // 作品展示区：显示所有帖子，因为每个阶段都可能有展示内容
        return true;
      case 'collaboration':
        // 多人合作区：显示所有帖子，因为任何内容都可能涉及合作
        return true;
      default:
        return false;
    }
  }

  // 如果帖子没有完整的topic信息，只有在有关键词匹配时才显示
  const text = `${post.title} ${post.content}`.toLowerCase();
  const keywords = sectionKeywords[sectionId as keyof typeof sectionKeywords];

  if (!keywords) {
    return false;
  }

  return keywords.some(keyword =>
    text.includes(keyword.toLowerCase())
  );
};

/**
 * 检查帖子是否属于指定类别
 * @param post 帖子对象
 * @param sectionId 分区ID
 * @param categoryId 类别ID
 * @returns 是否属于该类别
 */
export const isPostInCategory = (post: CommunityPost, sectionId: string, categoryId: string): boolean => {
  // 对于阶段分区，根据era匹配
  if (sectionId === 'progression') {
    return post.topic?.era === categoryId || 
           post.topic?.era === categoryId.replace('-', ' '); // 处理ID格式差异
  }
  
  // 对于其他分区，先检查是否属于该分区，然后进行更细致的分类
  if (!isPostInSection(post, sectionId)) {
    return false;
  }
  
  // 这里可以根据需要添加更细致的类别匹配逻辑
  // 目前简单返回true，表示属于该分区的帖子都显示在各个类别中
  return true;
};

/**
 * 检查帖子是否属于指定子类别
 * @param post 帖子对象
 * @param sectionId 分区ID
 * @param categoryId 类别ID
 * @param subcategoryId 子类别ID
 * @returns 是否属于该子类别
 */
export const isPostInSubcategory = (post: CommunityPost, sectionId: string, categoryId: string, subcategoryId: string): boolean => {
  // 对于阶段分区，使用精确匹配
  if (sectionId === 'progression') {
    if (subcategoryId.includes('|')) {
      const parts = subcategoryId.split('|');
      if (parts.length === 3) {
        const [era, tier, topic] = parts;
        return post.topic?.era === era &&
               post.topic?.tier === tier &&
               post.topic?.name === topic;
      }
    }
  }
  
  // 对于其他分区，先检查是否属于该类别
  if (!isPostInCategory(post, sectionId, categoryId)) {
    return false;
  }
  
  // 这里可以根据需要添加更细致的子类别匹配逻辑
  return true;
};

/**
 * 获取帖子的主要分区（最匹配的分区）
 * @param post 帖子对象
 * @returns 主要分区ID
 */
export const getPostPrimarySection = (post: CommunityPost): string => {
  const sections = getPostSections(post);
  return sections.length > 0 ? sections[0] : 'progression'; // 默认归类到阶段分区
};

/**
 * 获取分区的统计信息
 * @param posts 帖子数组
 * @returns 各分区的帖子数量统计
 */
export const getSectionStats = (posts: CommunityPost[]): { [sectionId: string]: number } => {
  const stats: { [sectionId: string]: number } = {};
  
  // 初始化统计
  communitySections.forEach(section => {
    stats[section.id] = 0;
  });
  
  // 统计每个帖子
  posts.forEach(post => {
    const primarySection = getPostPrimarySection(post);
    stats[primarySection] = (stats[primarySection] || 0) + 1;
  });
  
  return stats;
};
