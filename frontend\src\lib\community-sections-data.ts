export interface CommunitySubcategory {
  id: string;
  name: string;
  description: string;
  tags?: string[];
}

export interface CommunityCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  subcategories: CommunitySubcategory[];
}

export interface CommunitySection {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'progression' | 'technical' | 'resources' | 'guidance' | 'showcase' | 'trading';
  color: string; // 主题色
  categories: CommunityCategory[];
}

// 将现有的GTNH进度数据转换为新格式
import { gtnhProgression } from './gtnh-progression-data';

// 阶段分区 - 基于现有的GTNH进度数据
const progressionSection: CommunitySection = {
  id: 'progression',
  name: '阶段分区',
  description: 'GTNH游戏进度阶段讨论',
  icon: 'Zap',
  type: 'progression',
  color: 'from-blue-500 to-cyan-500',
  categories: gtnhProgression.map(era => ({
    id: era.name.toLowerCase().replace(/\s+/g, '-'),
    name: era.name,
    description: `${era.name}阶段相关讨论`,
    icon: era.name === '初期' ? 'Pickaxe' : era.name === '中期' ? 'Cpu' : era.name === '后期' ? 'Atom' : 'Crown',
    subcategories: era.tiers.flatMap(tier => 
      tier.topics.map(topic => ({
        id: `${era.name}|${tier.name}|${topic.subStage}`,
        name: `[${tier.name}] ${topic.subStage}`,
        description: topic.milestone,
        tags: topic.circuit ? [topic.circuit] : []
      }))
    )
  }))
};

// 技术讨论区
const technicalSection: CommunitySection = {
  id: 'technical',
  name: '技术讨论区',
  description: '深入探讨GTNH的各种技术和机制',
  icon: 'Settings',
  type: 'technical',
  color: 'from-green-500 to-emerald-500',
  categories: [
    {
      id: 'automation',
      name: '自动化系统',
      description: '自动化设计与优化',
      icon: 'Bot',
      subcategories: [
        { id: 'ae2-storage', name: 'AE2存储系统', description: 'Applied Energistics 2 存储网络设计' },
        { id: 'logistics', name: '物流管道', description: '物品传输和分拣系统' },
        { id: 'auto-crafting', name: '自动合成', description: '自动化生产线设计' },
        { id: 'fluid-automation', name: '流体自动化', description: '液体和气体的自动化处理' }
      ]
    },
    {
      id: 'gt-machines',
      name: 'GT机器',
      description: 'GregTech机器相关讨论',
      icon: 'Cog',
      subcategories: [
        { id: 'multiblock', name: '多方块结构', description: '大型机器的建造和配置' },
        { id: 'circuit-config', name: '电路配置', description: '编程电路的使用技巧' },
        { id: 'efficiency', name: '效率优化', description: '机器效率提升方法' },
        { id: 'overclocking', name: '超频技术', description: '机器超频的原理和应用' }
      ]
    },
    {
      id: 'magic-mods',
      name: '魔法模组',
      description: '魔法类模组技术讨论',
      icon: 'Sparkles',
      subcategories: [
        { id: 'thaumcraft', name: '神秘时代', description: 'Thaumcraft 研究和应用' },
        { id: 'botania', name: '植物魔法', description: 'Botania 花卉魔法系统' },
        { id: 'blood-magic', name: '血魔法', description: 'Blood Magic 仪式和祭坛' },
        { id: 'witchery', name: '巫术学', description: 'Witchery 巫术和仪式' }
      ]
    }
  ]
};

// 资源分享区
const resourcesSection: CommunitySection = {
  id: 'resources',
  name: '资源分享区',
  description: '分享有用的资源和工具',
  icon: 'FolderOpen',
  type: 'resources',
  color: 'from-purple-500 to-violet-500',
  categories: [
    {
      id: 'blueprints',
      name: '蓝图分享',
      description: '建筑和机器设计蓝图',
      icon: 'FileText',
      subcategories: [
        { id: 'factory-layouts', name: '工厂布局', description: '高效的工厂设计方案' },
        { id: 'building-designs', name: '建筑设计', description: '美观实用的建筑蓝图' },
        { id: 'machine-setups', name: '机器配置', description: '特定机器的最优配置' }
      ]
    },
    {
      id: 'saves-downloads',
      name: '存档下载',
      description: '游戏存档分享',
      icon: 'Download',
      subcategories: [
        { id: 'complete-worlds', name: '完整世界', description: '完整的游戏世界存档' },
        { id: 'stage-saves', name: '阶段存档', description: '特定进度阶段的存档' },
        { id: 'test-worlds', name: '测试世界', description: '用于测试的专用存档' }
      ]
    },
    {
      id: 'tools-software',
      name: '工具软件',
      description: '实用工具和软件',
      icon: 'Wrench',
      subcategories: [
        { id: 'calculators', name: '计算器', description: '各种游戏计算工具' },
        { id: 'planners', name: '规划工具', description: '工厂和基地规划软件' },
        { id: 'analyzers', name: '分析工具', description: '数据分析和优化工具' }
      ]
    }
  ]
};

// 新手指导区
const guidanceSection: CommunitySection = {
  id: 'guidance',
  name: '新手指导区',
  description: '新手入门和进阶指导',
  icon: 'BookOpen',
  type: 'guidance',
  color: 'from-orange-500 to-amber-500',
  categories: [
    {
      id: 'tutorials',
      name: '入门教程',
      description: '基础概念和初期发展指导',
      icon: 'GraduationCap',
      subcategories: [
        { id: 'basic-concepts', name: '基础概念', description: 'GTNH核心概念解释' },
        { id: 'early-game', name: '初期发展', description: '游戏开始阶段的发展路线' },
        { id: 'resource-gathering', name: '资源收集', description: '高效的资源获取方法' },
        { id: 'first-machines', name: '初期机器', description: '第一批机器的制作和使用' }
      ]
    },
    {
      id: 'advanced-guides',
      name: '进阶指南',
      description: '中后期规划和高级技巧',
      icon: 'Target',
      subcategories: [
        { id: 'mid-game-planning', name: '中期规划', description: '中期发展策略和目标' },
        { id: 'late-game-prep', name: '后期准备', description: '为后期内容做准备' },
        { id: 'advanced-techniques', name: '高级技巧', description: '进阶玩家的技巧分享' },
        { id: 'optimization', name: '优化策略', description: '效率和性能优化方法' }
      ]
    },
    {
      id: 'faq-troubleshooting',
      name: '常见问题',
      description: 'FAQ和故障排除',
      icon: 'HelpCircle',
      subcategories: [
        { id: 'common-issues', name: '常见问题', description: '新手常遇到的问题解答' },
        { id: 'troubleshooting', name: '故障排除', description: '技术问题的解决方案' },
        { id: 'performance-issues', name: '性能问题', description: '游戏性能优化建议' },
        { id: 'mod-conflicts', name: '模组冲突', description: '模组兼容性问题解决' }
      ]
    }
  ]
};

// 展示区
const showcaseSection: CommunitySection = {
  id: 'showcase',
  name: '展示区',
  description: '展示你的创作和成就',
  icon: 'Trophy',
  type: 'showcase',
  color: 'from-yellow-500 to-orange-500',
  categories: [
    {
      id: 'factory-showcase',
      name: '工厂展示',
      description: '展示你的自动化工厂',
      icon: 'Factory',
      subcategories: [
        { id: 'production-lines', name: '生产线', description: '高效的自动化生产线展示' },
        { id: 'mega-factories', name: '大型工厂', description: '规模庞大的工厂建设' },
        { id: 'creative-designs', name: '创意设计', description: '独特的工厂设计理念' },
        { id: 'compact-builds', name: '紧凑建造', description: '空间利用率极高的设计' }
      ]
    },
    {
      id: 'achievements',
      name: '成就分享',
      description: '里程碑达成和特殊成就',
      icon: 'Award',
      subcategories: [
        { id: 'milestones', name: '里程碑', description: '重要进度节点的达成' },
        { id: 'speed-runs', name: '速通记录', description: '快速通关的记录分享' },
        { id: 'challenges', name: '挑战完成', description: '特殊挑战的完成展示' },
        { id: 'first-time', name: '首次体验', description: '第一次达成某个目标的感受' }
      ]
    },
    {
      id: 'creative-builds',
      name: '创意建筑',
      description: '装饰性建筑和艺术作品',
      icon: 'Palette',
      subcategories: [
        { id: 'decorative', name: '装饰建筑', description: '美观的装饰性建筑' },
        { id: 'pixel-art', name: '像素艺术', description: '用方块创作的艺术作品' },
        { id: 'themed-builds', name: '主题建筑', description: '特定主题的建筑设计' },
        { id: 'landscapes', name: '景观设计', description: '地形改造和景观美化' }
      ]
    }
  ]
};

// 多人合作区
const collaborationSection: CommunitySection = {
  id: 'collaboration',
  name: '多人合作区',
  description: '团队协作和多人游戏交流',
  icon: 'Users',
  type: 'trading', // 保持原有的type以维持兼容性
  color: 'from-indigo-500 to-purple-500',
  categories: [
    {
      id: 'technical-collaboration',
      name: '技术合作',
      description: '共同项目和知识分享',
      icon: 'Cog',
      subcategories: [
        { id: 'joint-projects', name: '共同项目', description: '多人合作的大型项目' },
        { id: 'knowledge-share', name: '知识分享', description: '技术经验的交流分享' },
        { id: 'mentorship', name: '导师指导', description: '经验玩家指导新手' },
        { id: 'research-groups', name: '研究小组', description: '特定技术的研究团队' },
        { id: 'code-review', name: '代码审查', description: '脚本和自动化代码的互相审查' }
      ]
    },
    {
      id: 'server-recruitment',
      name: '服务器招募',
      description: '多人游戏和团队组建',
      icon: 'Server',
      subcategories: [
        { id: 'server-ads', name: '服务器宣传', description: '服务器招募和介绍' },
        { id: 'team-building', name: '团队组建', description: '寻找游戏伙伴和团队成员' },
        { id: 'events', name: '活动组织', description: '社区活动和比赛组织' },
        { id: 'guilds', name: '公会招募', description: '游戏公会的招募信息' },
        { id: 'competitive', name: '竞技比赛', description: '速通比赛和技术挑战' }
      ]
    },
    {
      id: 'multiplayer-coordination',
      name: '多人协调',
      description: '多人游戏中的协调和规划',
      icon: 'Users',
      subcategories: [
        { id: 'resource-planning', name: '资源规划', description: '多人服务器的资源分配和规划' },
        { id: 'base-planning', name: '基地规划', description: '共同基地的设计和建设规划' },
        { id: 'task-distribution', name: '任务分工', description: '大型项目的任务分配和协调' },
        { id: 'progress-sync', name: '进度同步', description: '团队成员间的进度协调' }
      ]
    },
    {
      id: 'community-projects',
      name: '社区项目',
      description: '社区共同参与的大型项目',
      icon: 'Trophy',
      subcategories: [
        { id: 'mega-builds', name: '巨型建筑', description: '需要多人协作的大型建筑项目' },
        { id: 'documentation', name: '文档编写', description: '共同编写游戏指南和文档' },
        { id: 'mod-development', name: '模组开发', description: '社区模组的开发和测试' },
        { id: 'wiki-contribution', name: 'Wiki贡献', description: '共同完善游戏Wiki内容' }
      ]
    }
  ]
};

export const communitySections: CommunitySection[] = [
  progressionSection,
  technicalSection,
  resourcesSection,
  guidanceSection,
  showcaseSection,
  collaborationSection
];

// 生成用于CreatePostForm的选项
export interface PostPartitionOption {
  value: string;
  label: string;
  section: string;
  category?: string;
}

export const generatePostPartitionOptions = (): PostPartitionOption[] => {
  const options: PostPartitionOption[] = [];

  communitySections.forEach(section => {
    section.categories.forEach(category => {
      category.subcategories.forEach(subcategory => {
        options.push({
          value: `${section.id}|${category.id}|${subcategory.id}`,
          label: `[${section.name}] ${category.name} - ${subcategory.name}`,
          section: section.name,
          category: category.name
        });
      });
    });
  });

  return options;
};
