'use client';

import './search.css';
import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  User,
  FileText,
  BookOpen,
  Loader2,
  Sparkles,
  TrendingUp,
  Clock,
  Filter,
  SortDesc,
  Grid3X3,
  List,
  ChevronDown,
  Calendar,
  Star,
  MessageCircle,
  Users
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu';
import { globalSearch } from '@/services/api';
import { GlobalSearchResult } from '@/types/GlobalSearchResult';
import Link from 'next/link';
import AvatarDisplay from '@/components/AvatarDisplay';
import { cn } from '@/lib/utils';

const SearchResultsContent: React.FC = () => {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('q') || '';

  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<GlobalSearchResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'relevance' | 'date-desc' | 'date-asc' | 'popularity' | 'alphabetical'>('relevance');
  const [filterBy, setFilterBy] = useState<{
    timeRange: 'all' | 'today' | 'week' | 'month' | 'year';
    contentType: 'all' | 'users' | 'posts' | 'articles';
    userStatus: 'all' | 'online' | 'offline';
    hasReplies: 'all' | 'with-replies' | 'no-replies';
  }>({
    timeRange: 'all',
    contentType: 'all',
    userStatus: 'all',
    hasReplies: 'all'
  });

  // 执行搜索
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setError(null);
    
    try {
      const searchResults = await globalSearch(searchQuery.trim());
      setResults(searchResults);
    } catch (err) {
      console.error('搜索失败:', err);
      setError('搜索失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 初始搜索
  useEffect(() => {
    if (initialQuery) {
      performSearch(initialQuery);
    }
  }, [initialQuery]);

  // 处理搜索提交
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      performSearch(query);
      // 更新URL
      const url = new URL(window.location.href);
      url.searchParams.set('q', query.trim());
      window.history.pushState({}, '', url.toString());
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 排序逻辑
  const sortResults = (items: any[], type: 'users' | 'posts' | 'articles') => {
    if (!items || items.length === 0) return items;

    return [...items].sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.createdAt || b.updatedAt || 0).getTime() - new Date(a.createdAt || a.updatedAt || 0).getTime();
        case 'date-asc':
          return new Date(a.createdAt || a.updatedAt || 0).getTime() - new Date(b.createdAt || b.updatedAt || 0).getTime();
        case 'popularity':
          if (type === 'posts') {
            const aPopularity = (a._count?.likes || a.likesCount || 0) + (a._count?.comments || a.commentsCount || 0);
            const bPopularity = (b._count?.likes || b.likesCount || 0) + (b._count?.comments || b.commentsCount || 0);
            return bPopularity - aPopularity;
          }
          return 0;
        case 'alphabetical':
          const aName = type === 'users' ? a.username : a.title;
          const bName = type === 'users' ? b.username : b.title;
          return aName.localeCompare(bName, 'zh-CN');
        case 'relevance':
        default:
          return 0; // 保持原始顺序（相关性）
      }
    });
  };

  // 筛选逻辑
  const filterResults = (items: any[], type: 'users' | 'posts' | 'articles') => {
    if (!items || items.length === 0) return items;

    return items.filter(item => {
      // 时间范围筛选
      if (filterBy.timeRange !== 'all') {
        const itemDate = new Date(item.createdAt || item.updatedAt);
        const now = new Date();
        const diffTime = now.getTime() - itemDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        switch (filterBy.timeRange) {
          case 'today':
            if (diffDays > 1) return false;
            break;
          case 'week':
            if (diffDays > 7) return false;
            break;
          case 'month':
            if (diffDays > 30) return false;
            break;
          case 'year':
            if (diffDays > 365) return false;
            break;
        }
      }

      // 内容类型筛选
      if (filterBy.contentType !== 'all' && filterBy.contentType !== type) {
        return false;
      }

      // 帖子特定筛选
      if (type === 'posts' && filterBy.hasReplies !== 'all') {
        const hasComments = (item._count?.comments || item.commentsCount || 0) > 0;
        if (filterBy.hasReplies === 'with-replies' && !hasComments) return false;
        if (filterBy.hasReplies === 'no-replies' && hasComments) return false;
      }

      return true;
    });
  };

  // 获取处理后的结果
  const getProcessedResults = () => {
    if (!results) return null;

    return {
      ...results,
      users: sortResults(filterResults(results.users, 'users'), 'users'),
      posts: sortResults(filterResults(results.posts, 'posts'), 'posts'),
      articles: sortResults(filterResults(results.articles, 'articles'), 'articles'),
    };
  };

  const processedResults = getProcessedResults();

  return (
    <div className="min-h-screen relative">
      {/* 背景图片 */}
      <div
        className="fixed inset-0 z-0"
        style={{
          backgroundImage: 'url(/images/background/Search.webp)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* 深色遮罩层 */}
        <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-4 pt-24 pb-8 max-w-6xl">
        {/* 搜索头部区域 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-flex items-center gap-3 mb-4"
            >
              <div className="p-3 rounded-full bg-gradient-to-r from-gray-600/20 to-gray-500/20 backdrop-blur-sm border border-gray-600/30">
                <Search className="h-8 w-8 text-gray-300" />
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                社区搜索
              </h1>
            </motion.div>
            <p className="text-gray-400 text-lg">格雷科技创造未来，发现你想要的知识</p>
          </div>

          {/* 搜索输入框 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="max-w-2xl mx-auto"
          >
            <form onSubmit={handleSearch} className="relative">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-gray-600/20 to-gray-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <div className="relative flex items-center bg-gray-800/90 backdrop-blur-xl border border-gray-600/50 rounded-2xl p-2 group-hover:border-gray-500/70 transition-all duration-300">
                  <div className="flex items-center pl-4 pr-2">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="搜索用户、帖子、文章..."
                    className="flex-1 bg-transparent border-0 text-gray-100 placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0 text-lg"
                  />
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-gray-700 hover:bg-gray-600 text-gray-100 border-0 rounded-xl px-6 py-2 transition-all duration-300 hover:scale-105"
                  >
                    {isLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <>
                        <Search className="h-5 w-5 mr-2" />
                        搜索
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </motion.div>
        </motion.div>

        {/* 加载状态 */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex flex-col items-center justify-center py-20"
            >
              <div className="relative">
                <div className="w-16 h-16 border-4 border-gray-600 border-t-blue-500 rounded-full animate-spin" />
                <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-purple-500 rounded-full animate-spin animate-reverse" style={{ animationDelay: '0.5s' }} />
              </div>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-6 text-gray-300 text-lg"
              >
                正在搜索中...
              </motion.p>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="flex items-center gap-2 mt-2 text-gray-500"
              >
                <Sparkles className="h-4 w-4" />
                <span className="text-sm">为您找到最相关的内容</span>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 错误状态 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="max-w-md mx-auto"
            >
              <Card className="bg-red-900/20 border-red-500/30 backdrop-blur-sm">
                <CardContent className="pt-6 text-center">
                  <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="h-6 w-6 text-red-400" />
                  </div>
                  <p className="text-red-300 mb-2">搜索遇到问题</p>
                  <p className="text-gray-400 text-sm">{error}</p>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 搜索结果 */}
        <AnimatePresence>
          {results && !isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {/* 搜索统计和控制栏 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-6 bg-gray-800/60 backdrop-blur-sm border border-gray-600/40 rounded-2xl">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-300">
                        搜索 "<span className="font-semibold text-gray-100">{results.query}</span>"
                      </span>
                    </div>
                    <Badge variant="secondary" className="bg-gray-600/30 text-gray-300 border-gray-500/40">
                      {processedResults ? (processedResults.users.length + processedResults.posts.length + processedResults.articles.length) : 0} 个结果
                    </Badge>
                  </div>

                  {/* 控制按钮 */}
                  <div className="flex items-center gap-2">
                    <div className="flex items-center bg-gray-700/60 rounded-lg p-1">
                      <Button
                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('grid')}
                        className={cn(
                          "h-8 w-8 p-0",
                          viewMode === 'grid'
                            ? "bg-gray-600 hover:bg-gray-500 text-gray-100"
                            : "text-gray-400 hover:text-gray-200 hover:bg-gray-600/50"
                        )}
                      >
                        <Grid3X3 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('list')}
                        className={cn(
                          "h-8 w-8 p-0",
                          viewMode === 'list'
                            ? "bg-gray-600 hover:bg-gray-500 text-gray-100"
                            : "text-gray-400 hover:text-gray-200 hover:bg-gray-600/50"
                        )}
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* 排序下拉菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="bg-gray-700/60 border-gray-500/50 text-gray-300 hover:bg-gray-600/60 hover:text-gray-200">
                          <SortDesc className="h-4 w-4 mr-2" />
                          排序
                          <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-gray-800/95 backdrop-blur-sm border-gray-600/50 text-gray-100">
                        <DropdownMenuLabel className="text-gray-300">排序方式</DropdownMenuLabel>
                        <DropdownMenuSeparator className="bg-gray-600/50" />
                        <DropdownMenuItem
                          onClick={() => setSortBy('relevance')}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            sortBy === 'relevance' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <Star className="h-4 w-4 mr-2" />
                          相关性
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setSortBy('date-desc')}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            sortBy === 'date-desc' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          最新发布
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setSortBy('date-asc')}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            sortBy === 'date-asc' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          最早发布
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setSortBy('popularity')}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            sortBy === 'popularity' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <TrendingUp className="h-4 w-4 mr-2" />
                          热度排序
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setSortBy('alphabetical')}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            sortBy === 'alphabetical' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          字母排序
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {/* 筛选下拉菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="bg-gray-700/60 border-gray-500/50 text-gray-300 hover:bg-gray-600/60 hover:text-gray-200">
                          <Filter className="h-4 w-4 mr-2" />
                          筛选
                          <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-gray-800/95 backdrop-blur-sm border-gray-600/50 text-gray-100 w-48">
                        <DropdownMenuLabel className="text-gray-300">时间范围</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, timeRange: 'all' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.timeRange === 'all' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          全部时间
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, timeRange: 'today' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.timeRange === 'today' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          今天
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, timeRange: 'week' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.timeRange === 'week' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          本周
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, timeRange: 'month' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.timeRange === 'month' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          本月
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, timeRange: 'year' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.timeRange === 'year' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          本年
                        </DropdownMenuItem>

                        <DropdownMenuSeparator className="bg-gray-600/50" />
                        <DropdownMenuLabel className="text-gray-300">帖子筛选</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, hasReplies: 'all' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.hasReplies === 'all' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          全部帖子
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, hasReplies: 'with-replies' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.hasReplies === 'with-replies' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          有回复
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterBy(prev => ({ ...prev, hasReplies: 'no-replies' }))}
                          className={cn(
                            "hover:bg-gray-700/50 focus:bg-gray-700/50",
                            filterBy.hasReplies === 'no-replies' && "bg-gray-700/70 text-gray-100"
                          )}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          无回复
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </motion.div>

              {processedResults && (processedResults.users.length + processedResults.posts.length + processedResults.articles.length) === 0 ? (
                // 无结果状态
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-center py-20"
                >
                  <div className="max-w-md mx-auto">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.4, type: "spring", stiffness: 200 }}
                      className="w-24 h-24 bg-gradient-to-br from-gray-700 to-gray-800 rounded-full flex items-center justify-center mx-auto mb-6"
                    >
                      <Search className="h-12 w-12 text-gray-400" />
                    </motion.div>

                    <motion.h3
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                      className="text-2xl font-bold text-white mb-3"
                    >
                      未找到相关结果
                    </motion.h3>

                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="text-gray-400 mb-8"
                    >
                      尝试使用不同的关键词或检查拼写
                    </motion.p>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.7 }}
                      className="bg-gray-900/60 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
                    >
                      <h4 className="text-white font-medium mb-4 flex items-center gap-2">
                        <Sparkles className="h-4 w-4 text-blue-400" />
                        搜索建议
                      </h4>
                      <ul className="space-y-2 text-gray-400 text-sm">
                        <li className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-blue-400 rounded-full" />
                          使用更简单的关键词
                        </li>
                        <li className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full" />
                          检查拼写是否正确
                        </li>
                        <li className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                          尝试使用同义词
                        </li>
                      </ul>
                    </motion.div>
                  </div>
                </motion.div>
              ) : (
                // 搜索结果标签页
                <Tabs defaultValue="all" className="w-full">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <TabsList className="grid w-full grid-cols-4 bg-gray-800/70 backdrop-blur-sm border border-gray-600/50 rounded-xl p-1 mb-8">
                      <TabsTrigger
                        value="all"
                        className="data-[state=active]:bg-gray-600 data-[state=active]:text-gray-100 text-gray-400 hover:text-gray-300 rounded-lg transition-all duration-300"
                      >
                        <div className="flex items-center gap-2">
                          <Sparkles className="h-4 w-4" />
                          <span>全部</span>
                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 text-xs border-gray-600/40">
                            {processedResults ? (processedResults.users.length + processedResults.posts.length + processedResults.articles.length) : 0}
                          </Badge>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="users"
                        className="data-[state=active]:bg-gray-600 data-[state=active]:text-gray-100 text-gray-400 hover:text-gray-300 rounded-lg transition-all duration-300"
                      >
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>用户</span>
                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 text-xs border-gray-600/40">
                            {processedResults?.users.length || 0}
                          </Badge>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="posts"
                        className="data-[state=active]:bg-gray-600 data-[state=active]:text-gray-100 text-gray-400 hover:text-gray-300 rounded-lg transition-all duration-300"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span>帖子</span>
                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 text-xs border-gray-600/40">
                            {processedResults?.posts.length || 0}
                          </Badge>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="articles"
                        className="data-[state=active]:bg-gray-600 data-[state=active]:text-gray-100 text-gray-400 hover:text-gray-300 rounded-lg transition-all duration-300"
                      >
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4" />
                          <span>文章</span>
                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 text-xs border-gray-600/40">
                            {processedResults?.articles.length || 0}
                          </Badge>
                        </div>
                      </TabsTrigger>
                    </TabsList>
                  </motion.div>

                  {/* 全部结果 */}
                  <TabsContent value="all" className="space-y-8">
                    {/* 用户结果 */}
                    {processedResults && processedResults.users.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <div className="flex items-center gap-3 mb-6">
                          <div className="p-2 bg-gradient-to-r from-gray-600/30 to-gray-500/30 rounded-lg">
                            <User className="h-5 w-5 text-gray-400" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-100">用户</h3>
                          <Badge variant="outline" className="border-gray-500/40 text-gray-400">
                            {processedResults.users.length} 个结果
                          </Badge>
                        </div>

                        <div className={cn(
                          "grid gap-4",
                          viewMode === 'grid' ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"
                        )}>
                          {processedResults.users.slice(0, 6).map((user, index) => (
                            <motion.div
                              key={user.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.6 + index * 0.1 }}
                            >
                              <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                                <CardContent className="p-6">
                                  <Link href={`/profile/${user.username}`} className="block">
                                    <div className="flex items-center gap-4">
                                      <div className="relative">
                                        <AvatarDisplay
                                          avatarUrl={user.fullAvatarUrl}
                                          serialNumber={user.serialNumber}
                                          avatarVersion={user.avatarVersion}
                                          size={48}
                                          className="h-12 w-12 rounded-full ring-2 ring-gray-600 group-hover:ring-gray-500/70 transition-all duration-300"
                                        />
                                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gray-500 rounded-full border-2 border-gray-800" />
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <p className="font-semibold text-gray-100 group-hover:text-gray-200 transition-colors duration-300">
                                          {user.username}
                                        </p>
                                        {user.bio && (
                                          <p className="text-sm text-gray-400 line-clamp-2 mt-1">
                                            {user.bio}
                                          </p>
                                        )}
                                        <div className="flex items-center gap-2 mt-2">
                                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 text-xs border-gray-600/40">
                                            用户
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </Link>
                                </CardContent>
                              </Card>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}

                    {/* 帖子结果 */}
                    {processedResults && processedResults.posts.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                      >
                        <div className="flex items-center gap-3 mb-6">
                          <div className="p-2 bg-gradient-to-r from-gray-600/30 to-gray-500/30 rounded-lg">
                            <FileText className="h-5 w-5 text-gray-400" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-100">社区帖子</h3>
                          <Badge variant="outline" className="border-gray-500/40 text-gray-400">
                            {processedResults.posts.length} 个结果
                          </Badge>
                        </div>

                        <div className={cn(
                          "grid gap-4",
                          viewMode === 'grid' ? "grid-cols-1 lg:grid-cols-2" : "grid-cols-1"
                        )}>
                          {processedResults.posts.slice(0, 6).map((post, index) => (
                            <motion.div
                              key={post.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.8 + index * 0.1 }}
                            >
                              <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                                <CardContent className="p-6">
                                  <Link href={`/community/post/${post.id}`} className="block">
                                    <div className="flex items-start gap-4">
                                      <div className="p-3 bg-gradient-to-br from-gray-600/30 to-gray-500/30 rounded-lg shrink-0">
                                        <FileText className="h-5 w-5 text-gray-400" />
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <h4 className="font-semibold text-gray-100 group-hover:text-gray-200 transition-colors duration-300 line-clamp-2 mb-3">
                                          {post.title}
                                        </h4>
                                        <p className="text-gray-400 line-clamp-3 mb-4 leading-relaxed">
                                          {post.content}
                                        </p>
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-3 text-sm text-gray-500">
                                            <div className="flex items-center gap-1">
                                              <User className="h-3 w-3" />
                                              <span>{post.author?.username}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                              <Clock className="h-3 w-3" />
                                              <span>{formatDate(post.createdAt)}</span>
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-4 text-sm text-gray-500">
                                            <span>{post._count?.likes || post.likesCount || post.likes || 0} 点赞</span>
                                            <span>{post._count?.comments || post.commentsCount || post.comments?.length || 0} 评论</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </Link>
                                </CardContent>
                              </Card>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}

                    {/* 文章结果 */}
                    {processedResults && processedResults.articles.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.9 }}
                      >
                        <div className="flex items-center gap-3 mb-6">
                          <div className="p-2 bg-gradient-to-r from-gray-600/30 to-gray-500/30 rounded-lg">
                            <BookOpen className="h-5 w-5 text-gray-400" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-100">知识库文章</h3>
                          <Badge variant="outline" className="border-gray-500/40 text-gray-400">
                            {processedResults.articles.length} 个结果
                          </Badge>
                        </div>

                        <div className={cn(
                          "grid gap-4",
                          viewMode === 'grid' ? "grid-cols-1 lg:grid-cols-2" : "grid-cols-1"
                        )}>
                          {processedResults.articles.slice(0, 6).map((article, index) => (
                            <motion.div
                              key={article.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 1.0 + index * 0.1 }}
                            >
                              <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                                <CardContent className="p-6">
                                  <Link href={`/kb/${article.id}`} className="block">
                                    <div className="flex items-start gap-4">
                                      <div className="p-3 bg-gradient-to-br from-gray-600/30 to-gray-500/30 rounded-lg shrink-0">
                                        <BookOpen className="h-5 w-5 text-gray-400" />
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <h4 className="font-semibold text-gray-100 group-hover:text-gray-200 transition-colors duration-300 line-clamp-2 mb-3">
                                          {article.title}
                                        </h4>
                                        <p className="text-gray-400 line-clamp-3 mb-4 leading-relaxed">
                                          {article.content}
                                        </p>
                                        <div className="flex items-center gap-3 text-sm text-gray-500">
                                          <div className="flex items-center gap-1">
                                            <Clock className="h-3 w-3" />
                                            <span>{formatDate(article.createdAt)}</span>
                                          </div>
                                          <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 border-gray-600/40 text-xs">
                                            知识库
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </Link>
                                </CardContent>
                              </Card>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </TabsContent>

                  {/* 用户标签页 */}
                  <TabsContent value="users">
                    {!processedResults || processedResults.users.length === 0 ? (
                      <div className="text-center py-20">
                        <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                          <User className="h-8 w-8 text-gray-500" />
                        </div>
                        <p className="text-gray-400">未找到相关用户</p>
                      </div>
                    ) : (
                      <div className={cn(
                        "grid gap-4",
                        viewMode === 'grid' ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"
                      )}>
                        {processedResults.users.map((user, index) => (
                          <motion.div
                            key={user.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                              <CardContent className="p-6">
                                <Link href={`/profile/${user.username}`} className="block">
                                  <div className="flex items-center gap-4">
                                    <div className="relative">
                                      <AvatarDisplay
                                        avatarUrl={user.fullAvatarUrl}
                                        serialNumber={user.serialNumber}
                                        avatarVersion={user.avatarVersion}
                                        size={56}
                                        className="h-14 w-14 rounded-full ring-2 ring-gray-600 group-hover:ring-gray-500/70 transition-all duration-300"
                                      />
                                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gray-500 rounded-full border-2 border-gray-800" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <p className="font-semibold text-gray-100 group-hover:text-gray-200 transition-colors duration-300 text-lg">
                                        {user.username}
                                      </p>
                                      {user.bio && (
                                        <p className="text-gray-400 mt-2 line-clamp-2">
                                          {user.bio}
                                        </p>
                                      )}
                                      <div className="flex items-center gap-2 mt-3">
                                        <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 border-gray-600/40">
                                          用户
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </Link>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  {/* 帖子标签页 */}
                  <TabsContent value="posts">
                    {!processedResults || processedResults.posts.length === 0 ? (
                      <div className="text-center py-20">
                        <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                          <FileText className="h-8 w-8 text-gray-500" />
                        </div>
                        <p className="text-gray-400">未找到相关帖子</p>
                      </div>
                    ) : (
                      <div className={cn(
                        "grid gap-6",
                        viewMode === 'grid' ? "grid-cols-1 lg:grid-cols-2" : "grid-cols-1"
                      )}>
                        {processedResults.posts.map((post, index) => (
                          <motion.div
                            key={post.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                              <CardContent className="p-6">
                                <Link href={`/community/post/${post.id}`} className="block">
                                  <div className="flex items-start gap-4">
                                    <div className="p-3 bg-gradient-to-br from-gray-600/30 to-gray-500/30 rounded-lg shrink-0">
                                      <FileText className="h-6 w-6 text-gray-400" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-bold text-gray-100 group-hover:text-gray-200 transition-colors duration-300 text-xl mb-3 line-clamp-2">
                                        {post.title}
                                      </h4>
                                      <p className="text-gray-400 mb-6 line-clamp-4 leading-relaxed">
                                        {post.content}
                                      </p>
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-4 text-sm text-gray-500">
                                          <div className="flex items-center gap-2">
                                            <User className="h-4 w-4" />
                                            <span>{post.author?.username}</span>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <Clock className="h-4 w-4" />
                                            <span>{formatDate(post.createdAt)}</span>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-6 text-sm text-gray-500">
                                          <div className="flex items-center gap-1">
                                            <TrendingUp className="h-4 w-4" />
                                            <span>{post._count?.likes || post.likesCount || post.likes || 0}</span>
                                          </div>
                                          <div className="flex items-center gap-1">
                                            <FileText className="h-4 w-4" />
                                            <span>{post._count?.comments || post.commentsCount || post.comments?.length || 0}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </Link>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  {/* 文章标签页 */}
                  <TabsContent value="articles">
                    {!processedResults || processedResults.articles.length === 0 ? (
                      <div className="text-center py-20">
                        <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                          <BookOpen className="h-8 w-8 text-gray-500" />
                        </div>
                        <p className="text-gray-400">未找到相关文章</p>
                      </div>
                    ) : (
                      <div className={cn(
                        "grid gap-6",
                        viewMode === 'grid' ? "grid-cols-1 lg:grid-cols-2" : "grid-cols-1"
                      )}>
                        {processedResults.articles.map((article, index) => (
                          <motion.div
                            key={article.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card className="group bg-gray-800/70 backdrop-blur-sm border-gray-600/50 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-gray-500/20">
                              <CardContent className="p-6">
                                <Link href={`/kb/${article.id}`} className="block">
                                  <div className="flex items-start gap-4">
                                    <div className="p-3 bg-gradient-to-br from-gray-600/30 to-gray-500/30 rounded-lg shrink-0">
                                      <BookOpen className="h-6 w-6 text-gray-400" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-bold text-gray-100 group-hover:text-gray-200 transition-colors duration-300 text-xl mb-3 line-clamp-2">
                                        {article.title}
                                      </h4>
                                      <p className="text-gray-400 mb-6 line-clamp-4 leading-relaxed">
                                        {article.content}
                                      </p>
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2 text-sm text-gray-500">
                                          <Clock className="h-4 w-4" />
                                          <span>{formatDate(article.createdAt)}</span>
                                        </div>
                                        <Badge variant="secondary" className="bg-gray-700/60 text-gray-300 border-gray-600/40">
                                          知识库
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </Link>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

const SearchPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen relative">
        {/* 背景图片 */}
        <div
          className="fixed inset-0 z-0"
          style={{
            backgroundImage: 'url(/images/background/Search.webp)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          <div className="absolute inset-0 bg-black/70 backdrop-blur-sm" />
        </div>

        {/* 加载内容 */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="relative mb-6">
              <div className="w-16 h-16 border-4 border-gray-600 border-t-gray-400 rounded-full animate-spin mx-auto" />
              <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-gray-500 rounded-full animate-spin animate-reverse mx-auto" style={{ animationDelay: '0.5s' }} />
            </div>
            <p className="text-gray-300 text-lg">加载搜索页面...</p>
          </div>
        </div>
      </div>
    }>
      <SearchResultsContent />
    </Suspense>
  );
};

export default SearchPage;
