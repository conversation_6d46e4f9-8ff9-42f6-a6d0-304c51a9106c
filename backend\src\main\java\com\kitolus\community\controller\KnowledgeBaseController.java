package com.kitolus.community.controller;

import com.kitolus.community.entity.KnowledgeBaseArticle;
import com.kitolus.community.entity.SearchResult;
import com.kitolus.community.service.KnowledgeBaseService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/kb")
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;

    public KnowledgeBaseController(KnowledgeBaseService knowledgeBaseService) {
        this.knowledgeBaseService = knowledgeBaseService;
    }

    @GetMapping("/all")
    public ResponseEntity<List<KnowledgeBaseArticle>> getAllArticles() {
        return ResponseEntity.ok(knowledgeBaseService.getAllArticles());
    }

    @GetMapping("/all/grouped")
    public ResponseEntity<Map<String, List<KnowledgeBaseArticle>>> getAllArticlesGrouped() {
        return ResponseEntity.ok(knowledgeBaseService.getAllArticlesGroupedByDate());
    }

    @GetMapping("/stats")
    public ResponseEntity<Map<String, Long>> getStats() {
        long count = knowledgeBaseService.countArticles();
        return ResponseEntity.ok(Collections.singletonMap("totalArticles", count));
    }

    @GetMapping("/search")
    public ResponseEntity<SearchResult> searchArticles(@RequestParam(value = "query") String query) {
        return ResponseEntity.ok(knowledgeBaseService.searchArticles(query));
    }

    @PostMapping("/upload")
    @PreAuthorize("hasAnyRole('EDITOR', 'ADMIN')")
    public ResponseEntity<?> uploadArticle(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("message", "请选择要上传的文件！"));
        }
        try {
            KnowledgeBaseArticle article = knowledgeBaseService.storeArticleFromMarkdownFile(file);
            return ResponseEntity.ok(article);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "文件上传失败: " + e.getMessage()));
        }
    }

    @GetMapping("/random")
    public ResponseEntity<List<KnowledgeBaseArticle>> getRandomArticles(@RequestParam(value = "count", defaultValue = "4") int count) {
        return ResponseEntity.ok(knowledgeBaseService.getRandomArticles(count));
    }

    @GetMapping("/{id}")
    public ResponseEntity<KnowledgeBaseArticle> getArticleById(@PathVariable String id) {
        KnowledgeBaseArticle article = knowledgeBaseService.getArticleById(id);
        if (article != null) {
            return ResponseEntity.ok(article);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 