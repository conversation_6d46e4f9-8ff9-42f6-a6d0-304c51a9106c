import log from 'loglevel';

// Set default log level based on environment
const defaultLevel = process.env.NODE_ENV === 'development' ? 'trace' : 'warn';

// Allow overriding level via localStorage for debugging on staging/production
let level = defaultLevel;
if (typeof window !== 'undefined') {
  const storedLevel = window.localStorage.getItem('loglevel');
  if (storedLevel) {
    level = storedLevel;
  }
}

log.setLevel(level as log.LogLevelDesc);

export default log; 