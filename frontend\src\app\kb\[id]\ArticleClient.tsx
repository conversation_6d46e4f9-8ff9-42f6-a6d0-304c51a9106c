'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeSlug from 'rehype-slug';
import { CodeBlock } from '@/components/ui/code-block.tsx';
import React from 'react';
import { KnowledgeBaseArticle } from '@/types/KnowledgeBaseArticle';

interface TableOfContentsItem {
    id: string;
    text: string;
    level: number;
}

export default function ArticleClient({ article }: { article: KnowledgeBaseArticle }) {
    const articleRef = useRef<HTMLDivElement>(null);
    const [toc, setToc] = useState<TableOfContentsItem[]>([]);
    const [activeId, setActiveId] = useState<string>('');

    useEffect(() => {
        if (article && articleRef.current) {
            // Use a short delay to ensure DOM is fully painted after hydration
            const timer = setTimeout(() => {
                const headings = Array.from(
                    articleRef.current!.querySelectorAll('h1, h2, h3')
                ).map((elem) => ({
                    id: elem.id,
                    text: elem.textContent || '',
                    level: parseInt(elem.tagName.substring(1), 10),
                }));
                if (headings.length > 0) {
                    setToc(headings);
                }
            }, 100);
            return () => clearTimeout(timer);
        }
    }, [article]);

    const handleScroll = useCallback(() => {
        let currentId = '';
        const headingElements = toc.map(item => document.getElementById(item.id)).filter(Boolean);

        for (const elem of headingElements) {
            if (elem!.getBoundingClientRect().top < 150) { // 150px from top
                currentId = elem!.id;
            } else {
                break;
            }
        }
        
        setActiveId(currentId);
        
    }, [toc]);
    
    useEffect(() => {
        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => window.removeEventListener('scroll', handleScroll);
    }, [handleScroll]);

    const handleTocClick = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
        e.preventDefault();
        const element = document.getElementById(id);
        if (element) {
            const yOffset = -120; // Offset for fixed header
            const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
            window.scrollTo({ top: y, behavior: 'smooth' });
        }
    };

    return (
        <>
            {/* Table of Contents (Left, Fixed) */}
            <aside className="hidden xl:block fixed left-8 top-28 w-72 h-[calc(100vh-8rem)]">
                <div className="bg-card/50 backdrop-blur-lg rounded-xl p-4 h-full overflow-y-auto">
                    <h3 className="text-lg font-semibold mb-4 text-foreground">目录</h3>
                    {toc.length > 0 ? (
                        <nav className="space-y-2 border-l-2 border-border/50">
                            {toc.map((item) => (
                                <a
                                    key={item.id}
                                    href={`#${item.id}`}
                                    onClick={(e) => handleTocClick(e, item.id)}
                                    className={`-ml-0.5 block border-l-2 py-1 transition-colors duration-200 text-sm ${
                                        item.level === 2 ? 'pl-6' : 'pl-3'
                                    } ${
                                        item.level === 3 ? 'pl-9' : ''
                                    } ${
                                        activeId === item.id
                                            ? 'text-primary border-primary font-semibold'
                                            : 'text-muted-foreground hover:text-foreground border-transparent hover:border-muted-foreground/50'
                                    }`}
                                >
                                    {item.text}
                                </a>
                            ))}
                        </nav>
                    ) : (
                        <div className="text-sm text-muted-foreground pt-2">这篇文章没有目录。</div>
                    )}
                </div>
            </aside>

            {/* Main Content (Centered Independently) */}
            <main ref={articleRef} className="mx-auto max-w-4xl">
                <div className="bg-card/50 backdrop-blur-lg rounded-xl p-8 md:p-12">
                    <article className="prose prose-invert max-w-none 
                                    prose-h1:text-4xl prose-h1:font-bold prose-h1:mb-8 prose-h1:text-center
                                    prose-h2:mt-12 prose-h2:mb-4 prose-h2:border-b prose-h2:border-border/50 prose-h2:pb-2 prose-h2:text-center
                                    prose-h3:text-center
                                    prose-h4:text-center
                                    prose-p:text-center
                                    prose-a:text-primary hover:prose-a:underline 
                                    prose-img:rounded-lg prose-img:mx-auto
                                    prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic
                                    prose-pre:bg-transparent prose-pre:p-0
                                    prose-table:mx-auto
                                    prose-ul:text-left
                                    prose-ol:text-left">
                        <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            rehypePlugins={[rehypeSlug]}
                            components={{
                                code: CodeBlock,
                                img: ({node, ...props}) => (
                                    // eslint-disable-next-line @next/next/no-img-element
                                    <img 
                                        alt={props.alt || ''}
                                        {...props} 
                                        className="rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                        onClick={() => window.open(props.src, '_blank')}
                                    />
                                ),
                            }}
                        >
                            {article.content || ''}
                        </ReactMarkdown>
                    </article>
                    <div className="mt-12 pt-6 border-t border-border/50 text-center text-sm text-muted-foreground">
                        <p>知识库部分内容整理自：</p>
                        <a href="https://gtnh.huijiwiki.com/wiki/%E9%A6%96%E9%A1%B5" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline mx-2">
                            GTNH 中文维基
                        </a>
                        <span>•</span>
                        <a href="https://gtnh.miraheze.org/wiki/Main_Page" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline mx-2">
                            Official GTNH Wiki
                        </a>
                    </div>
                </div>
            </main>
        </>
    );
} 