package com.kitolus.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kitolus.community.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    @Results(id = "userResultMap", value = {
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "avatarUrl", column = "avatar_url"),
        @Result(property = "serialNumber", column = "serial_number"),
        // Map other necessary fields if needed, but these are the core ones for display.
        @Result(property = "role", column = "role"),
        @Result(property = "email", column = "email")
    })
    @Select("SELECT * FROM user WHERE id = -1") // This is a dummy query to anchor the Results
    User _dummyUser();

    /**
     * 根据用户名查找用户。
     * 这是一个手动编写SQL的示例。
     *
     * @param username 用户名
     * @return 用户实体
     */
    @Select("SELECT * FROM `user` WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    @Select("SELECT COUNT(*) FROM user WHERE role = #{role}")
    Long countByRole(@Param("role") String role);

    @Select("SELECT COUNT(*) FROM user WHERE DATE(created_at) = CURDATE()")
    long countNewUsersToday();

    @Select("SELECT * FROM `user` WHERE email = #{email}")
    User findByEmail(@Param("email") String email);
} 