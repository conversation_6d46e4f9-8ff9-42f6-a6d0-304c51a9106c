'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

const ParallaxBackground = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const { clientX, clientY } = event;
      const x = (clientX / window.innerWidth - 0.5) * 2;
      const y = (clientY / window.innerHeight - 0.5) * 2;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  const layers = [
    { strength: 0.005, delay: '0s' },
    { strength: 0.01, delay: '2s' },
    { strength: 0.02, delay: '4s' },
    { strength: 0.04, delay: '1s' },
    { strength: 0.08, delay: '3s' },
  ];

  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden -z-10">
      {layers.map((layer, i) => (
        <div
          key={i}
          className="absolute inset-0"
          style={{
            transform: `translate(${mousePosition.x * 100 * layer.strength}px, ${mousePosition.y * 100 * layer.strength}px)`,
            transition: 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          }}
        >
          <div
            className={cn(
              'absolute inset-0 w-full h-full bg-cover bg-center animate-subtle-zoom'
            )}
            style={{
              backgroundImage: `url('/images/background/MainPage.webp')`,
              backgroundSize: `${110 + i * 5}%`,
              backgroundPosition: `calc(50% + ${-i * 5}px) calc(50% + ${-i * 5}px)`,
              animationDelay: layer.delay,
            }}
          />
        </div>
      ))}
      <div className="absolute inset-0 w-full h-full bg-[radial-gradient(ellipse_at_center,transparent_65%,rgba(0,0,0,0.4))]"></div>
    </div>
  );
};

export default ParallaxBackground;