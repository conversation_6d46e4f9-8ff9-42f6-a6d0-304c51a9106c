'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    // 检查是否是AnimatePresence相关的错误
    if (error.message.includes('AnimatePresence is not defined')) {
      console.warn('捕获到AnimatePresence错误，使用fallback渲染:', error.message);
      return { hasError: true, error };
    }
    
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary捕获到错误:', error, errorInfo);
    
    // 如果是AnimatePresence错误，尝试修复
    if (error.message.includes('AnimatePresence is not defined')) {
      // 尝试重新加载framer-motion
      try {
        import('framer-motion').then(({ AnimatePresence, motion }) => {
          (window as any).AnimatePresence = AnimatePresence;
          (window as any).motion = motion;
          console.log('重新加载framer-motion成功');
          
          // 重置错误状态
          setTimeout(() => {
            this.setState({ hasError: false, error: undefined });
          }, 100);
        });
      } catch (e) {
        console.error('重新加载framer-motion失败:', e);
      }
    }
  }

  public render() {
    if (this.state.hasError) {
      // 如果是AnimatePresence错误，渲染子组件但不使用动画
      if (this.state.error?.message.includes('AnimatePresence is not defined')) {
        return this.props.fallback || (
          <div className="error-fallback">
            {/* 渲染子组件但禁用动画 */}
            <div style={{ opacity: 1 }}>
              {this.props.children}
            </div>
          </div>
        );
      }
      
      // 其他错误显示错误信息
      return this.props.fallback || (
        <div className="error-boundary p-4 border border-red-500 rounded bg-red-50 text-red-800">
          <h2 className="text-lg font-semibold mb-2">出现了一个错误</h2>
          <details className="text-sm">
            <summary className="cursor-pointer">错误详情</summary>
            <pre className="mt-2 whitespace-pre-wrap">
              {this.state.error?.message}
            </pre>
          </details>
          <button 
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm"
            onClick={() => this.setState({ hasError: false, error: undefined })}
          >
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
