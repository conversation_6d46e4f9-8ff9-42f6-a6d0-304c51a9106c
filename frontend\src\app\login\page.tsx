'use client';

import { Suspense, useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import { motion, AnimatePresence } from 'framer-motion';
import apiService from '@/services/api';
import { Loader2, User, Lock } from 'lucide-react';

function LoginFormComponent() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get('registered') === 'true') {
      setSuccessMessage('注册成功！请登录。');
      // A trick to remove the query param from the URL without a full page reload.
      router.replace('/login', { scroll: false });
    }
  }, [searchParams, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setIsLoading(true);

    try {
      const response = await apiService.post('/api/auth/login', { username, password });
      const { token } = response.data;
      await login(token);
    } catch (err: any) {
      if (err.response) {
        const errorMessage = err.response.data?.message || err.response.data?.error;
        if (err.response.status === 401) {
          setError('用户名或密码错误');
        } else if (err.response.status === 403) {
          setError('账号已被禁用，请联系管理员');
        } else {
          setError(errorMessage || '登录失败，请稍后重试');
        }
      } else if (err.request) {
        setError('无法连接到服务器，请检查网络连接');
      } else {
        setError('登录请求失败，请稍后重试');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative w-full">
      <div className="relative w-full flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-[400px]"
        >
          <div className="bg-[#1a1a1a]/90 backdrop-blur-md rounded-2xl p-10 shadow-2xl border border-[#2a2a2a] w-full">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-8"
            >
              <h1 className="text-3xl font-bold text-gray-100">
                GTNH 社区
              </h1>
              <p className="text-gray-400 mt-2">欢迎回来，请登录您的账号</p>
            </motion.div>

            <AnimatePresence>
              {successMessage && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="p-3 mb-4 text-sm text-gray-300 bg-[#2a2a2a]/50 rounded-lg border border-[#3a3a3a]"
                  role="alert"
                >
                  {successMessage}
                </motion.div>
              )}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="p-3 mb-4 text-sm text-gray-300 bg-[#2a2a2a]/50 rounded-lg border border-[#3a3a3a]"
                  role="alert"
                >
                  {error}
                </motion.div>
              )}
            </AnimatePresence>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                  用户名或邮箱
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isLoading}
                    className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                    placeholder="请输入用户名或邮箱"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                  密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    className="block w-full pl-10 pr-3 py-2 bg-[#2a2a2a]/50 border border-[#3a3a3a] rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#3a3a3a] focus:border-transparent transition-all duration-200"
                    placeholder="请输入密码"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  {/* Placeholder for "Remember me" if needed */}
                </div>
                <div className="text-sm">
                  <a href="/forgot-password" // TODO: Change to Link component once the page is ready
                    className="font-medium text-gray-400 hover:text-gray-200 transition-colors duration-200">
                    忘记密码？
                  </a>
                </div>
              </div>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center items-center py-2 px-4 border border-[#3a3a3a] rounded-lg shadow-sm text-sm font-medium text-gray-100 bg-[#2a2a2a] hover:bg-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3a3a3a] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  '登录'
                )}
              </motion.button>

              <div className="text-center">
                <p className="text-sm text-gray-400">
                  还没有账号？{' '}
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    href="/register"
                    className="font-medium text-gray-300 hover:text-gray-200 transition-colors duration-200"
                  >
                    立即注册
                  </motion.a>
                </p>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <main className="min-h-screen relative">
      <div className="fixed inset-0 bg-[url('/images/background/background.webp')] bg-cover bg-bottom opacity-50"></div>
      <div className="fixed inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60"></div>
      <div className="relative h-full w-full">
        <Header />
        <div className="h-[calc(100vh-64px)] relative w-full">
          <div className="relative h-full w-full flex items-center justify-center p-4 pt-6">
            <Suspense fallback={<div>Loading...</div>}>
              <LoginFormComponent />
            </Suspense>
          </div>
        </div>
      </div>
    </main>
  );
} 