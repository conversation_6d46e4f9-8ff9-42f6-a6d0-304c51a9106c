# =================================================================
# Spring Boot 开发环境特定配置 (application-dev.yml)
# =================================================================

# 在开发环境中，我们需要让Java后端自己提供静态资源，
# 因为没有Nginx作为反向代理。

# 覆盖 app.base-url 以便在开发环境中生成正确的本地URL
app:
  base-url: "http://localhost:8080"

file-storage:
  # 在Windows开发机上，文件存储的根目录。
  # 使用正斜杠 /，Java可以很好地跨平台处理。
  storage-base-path: "E:/GTNH Projects/GTNH Website/backend/storage"

# CORS allowed origins for development
cors:
  allowed-origins: http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:8080

frontend:
  # 前端项目 public 目录的绝对路径，用于提供背景图等静态资源。
  # 同样，请根据您的实际情况修改。
  public-path: "E:/GTNH Projects/GTNH Website/frontend/public"

spring:
  datasource:
    # 开发环境使用 kitolus_community 数据库，与生产环境保持一致
    # 请确保您本地MySQL中存在名为 kitolus_community 的数据库
    url: ******************************************************************************************************************
    username: Kitolus
    password: dsbz233S
    driver-class-name: com.mysql.cj.jdbc.Driver
  mail:
    # 在开发环境中, 可以将邮件发送功能关闭, 避免不必要的邮件发送和报错
    # 如果需要测试邮件功能, 请取消下面的注释并提供有效的凭证
    # host: smtpdm.aliyun.com
    # port: 465
    # username: "<EMAIL>"
    # password: "your-password"
    # properties:
    #   mail:
    #     smtp:
    #       auth: true
    #       ssl:
    #         enable: true
    # host: 

# LantuPay Configuration
lantu:
  payment:
    mch_id: "1719309092" # LantuPay Merchant ID
    secret_key: "bf5135a2ed7c84a385bc15056b5c73d1" # LantuPay Secret Key
    notify_url: "https://kitolus.top/api/payment/notify" # LantuPay Notify URL
    api_url: "https://api.ltzf.cn/api/wxpay/native"

# MyBatis-Plus 开发环境配置
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted  # 全局逻辑删除字段名
      logic-delete-value: true     # 逻辑已删除值(使用boolean true)
      logic-not-delete-value: false # 逻辑未删除值(使用boolean false)