'use client';

import React, { useState, useMemo, useCallback, useRef } from 'react';
import Link from 'next/link';
import { getGroupedArticles, searchKb } from '@/services/api';
import { KnowledgeBaseArticle } from '@/types/KnowledgeBaseArticle';
import { SearchResult } from '@/types/SearchResult';
import debounce from 'lodash/debounce';
import useSWR from 'swr';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Search, Loader2, ServerCrash, ArrowLeft, BookCopy } from 'lucide-react';

export default function KnowledgeBaseAllClient() {
    // SWR for data fetching
    const { data: groupedArticles, error, isLoading } = useSWR('grouped-articles', getGroupedArticles);

    const [isSearching, setIsSearching] = useState(false);
    const [searchError, setSearchError] = useState<string | null>(null);
    const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
    const [activeSearchTerm, setActiveSearchTerm] = useState('');
    
    const inputRef = useRef<HTMLInputElement>(null);

    const debouncedSearch = useMemo(
        () =>
            debounce(async (query: string) => {
                if (!query.trim()) {
                    setSearchResults(null);
                    setIsSearching(false);
                    return;
                }
                setIsSearching(true);
                setSearchError(null);
                try {
                    const results = await searchKb(query);
                    setSearchResults(results);
                } catch (error) {
                    console.error('Search failed:', error);
                    setSearchError('搜索失败，请稍后重试。');
                    setSearchResults(null);
                } finally {
                    setIsSearching(false);
                }
            }, 300),
        []
    );

    const handleSearch = (query: string) => {
        setActiveSearchTerm(query);
        debouncedSearch(query);
    };
    
    const renderGroupedArticles = () => {
        if (!groupedArticles || Object.keys(groupedArticles).length === 0) {
             return (
                <div className="text-center p-8 text-gray-500">
                   当前没有任何知识库文章。
               </div>
            );
        }

        return (
             <Accordion type="multiple" defaultValue={Object.keys(groupedArticles).slice(0, 3)} className="w-full max-w-5xl mx-auto">
                {Object.entries(groupedArticles).map(([date, articlesOnDate]) => (
                    <AccordionItem value={date} key={date}>
                        <AccordionTrigger className="text-xl font-medium hover:no-underline">
                            <div className="flex items-center gap-3">
                                <span>{date}</span>
                                <span className="text-sm font-normal text-muted-foreground bg-muted px-2 py-1 rounded-md">
                                    {articlesOnDate.length} 篇
                                </span>
                            </div>
                        </AccordionTrigger>
                        <AccordionContent>
                           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 pt-4">
                                {articlesOnDate.map((article) => (
                                    <Link href={`/kb/${article.id}`} key={article.id}>
                                        <Card className="bg-background/50 border-border h-full hover:shadow-lg hover:border-primary transition-all duration-300 transform hover:-translate-y-1">
                                            <CardHeader>
                                                <CardTitle className="flex items-start gap-3 text-base font-semibold">
                                                    <BookCopy className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                                                    <span>{article.title}</span>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-muted-foreground text-sm line-clamp-3">
                                                    {article.summary}
                                                </p>
                                            </CardContent>
                                        </Card>
                                    </Link>
                                ))}
                            </div>
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        );
    };

    const renderArticleList = (articles: KnowledgeBaseArticle[] | undefined, title: string) => {
        if (!articles || articles.length === 0) {
            return null;
        }
        return (
            <div className="mt-8">
                <h3 className="text-2xl font-semibold text-foreground border-b pb-3 mb-6">{title}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {articles.map((article) => (
                        <Link href={`/kb/${article.id}`} key={article.id}>
                            <Card className="bg-background/50 border-border h-full hover:shadow-lg hover:border-primary transition-all duration-300 transform hover:-translate-y-1">
                                <CardHeader>
                                    <CardTitle className="flex items-start gap-3">
                                        <BookCopy className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                                        <span>{article.title}</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground text-sm line-clamp-3">
                                        {article.summary}
                                    </p>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>
        );
    };

    const noResultsFound = !isSearching && 
                           activeSearchTerm &&
                           searchResults &&
                           (searchResults.titleMatches?.length === 0) && 
                           (searchResults.contentMatches?.length === 0);

    return (
        <main className="flex-grow container mx-auto px-4 py-32 md:py-40">
            <div className="max-w-3xl mx-auto text-center mb-16">
                <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">所有文章</h1>
                <p className="text-muted-foreground">浏览或搜索社区分享的所有指南、教程和技术蓝图。</p>
                <div className="mt-8 flex gap-2">
                    <div className="relative flex-grow">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                        <Input
                            ref={inputRef}
                            type="search"
                            placeholder="在所有文章中即时搜索..."
                            className="w-full pl-10"
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>
                </div>
            </div>

            {isSearching && (
                <div className="text-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                    <p className="mt-4">搜索中...</p>
                </div>
            )}
            {searchError && (
                <div className="text-center p-8 text-red-500 flex flex-col items-center">
                    <ServerCrash className="h-12 w-12 mb-4" />
                    <p>{searchError}</p>
                </div>
            )}
            
            {activeSearchTerm.trim() && !isSearching && (
                <div className="mt-8">
                    {renderArticleList(searchResults?.titleMatches, '标题匹配的结果')}
                    {renderArticleList(searchResults?.contentMatches, '内容匹配的结果')}
                    
                    {noResultsFound && (
                         <div className="text-center p-8 text-gray-500">
                            未找到与 "{activeSearchTerm}" 相关的内容。
                        </div>
                    )}
                </div>
            )}
            
            {!activeSearchTerm.trim() && (
                <>
                    {isLoading && (
                        <div className="text-center p-8">
                            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                            <p className="mt-4">正在加载所有文章...</p>
                        </div>
                    )}
                    {error && (
                        <div className="text-center p-8 text-red-500 flex flex-col items-center">
                            <ServerCrash className="h-12 w-12 mb-4" />
                            <p>{error.message || '无法加载文章列表，请稍后再试。'}</p>
                        </div>
                    )}
                    {!isLoading && !error && renderGroupedArticles()}
                </>
            )}

            <div className="text-center mt-16">
              <Link href="/kb" className="inline-flex items-center text-blue-600 hover:underline">
                <ArrowLeft className="mr-2 h-4 w-4"/>
                返回知识库主页
              </Link>
            </div>
        </main>
    );
}